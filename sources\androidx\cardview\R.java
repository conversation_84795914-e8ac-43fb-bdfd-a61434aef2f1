package androidx.cardview;

/* loaded from: classes.dex */
public final class R {

    public static final class attr {
        public static final int cardBackgroundColor = 0x7f0400bb;
        public static final int cardCornerRadius = 0x7f0400bc;
        public static final int cardElevation = 0x7f0400bd;
        public static final int cardMaxElevation = 0x7f0400bf;
        public static final int cardPreventCornerOverlap = 0x7f0400c0;
        public static final int cardUseCompatPadding = 0x7f0400c1;
        public static final int cardViewStyle = 0x7f0400c2;
        public static final int contentPadding = 0x7f040287;
        public static final int contentPaddingBottom = 0x7f040288;
        public static final int contentPaddingLeft = 0x7f04028a;
        public static final int contentPaddingRight = 0x7f04028b;
        public static final int contentPaddingTop = 0x7f04028d;

        private attr() {
        }
    }

    public static final class color {
        public static final int cardview_dark_background = 0x7f06002f;
        public static final int cardview_light_background = 0x7f060030;
        public static final int cardview_shadow_end_color = 0x7f060031;
        public static final int cardview_shadow_start_color = 0x7f060032;

        private color() {
        }
    }

    public static final class dimen {
        public static final int cardview_compat_inset_shadow = 0x7f070053;
        public static final int cardview_default_elevation = 0x7f070054;
        public static final int cardview_default_radius = 0x7f070055;

        private dimen() {
        }
    }

    public static final class style {
        public static final int Base_CardView = 0x7f120011;
        public static final int CardView = 0x7f120148;
        public static final int CardView_Dark = 0x7f120149;
        public static final int CardView_Light = 0x7f12014a;

        private style() {
        }
    }

    public static final class styleable {
        public static final int[] CardView = {android.R.attr.minWidth, android.R.attr.minHeight, com.incall.apps.softmanager.R.attr.cardBackgroundColor, com.incall.apps.softmanager.R.attr.cardCornerRadius, com.incall.apps.softmanager.R.attr.cardElevation, com.incall.apps.softmanager.R.attr.cardMaxElevation, com.incall.apps.softmanager.R.attr.cardPreventCornerOverlap, com.incall.apps.softmanager.R.attr.cardUseCompatPadding, com.incall.apps.softmanager.R.attr.contentPadding, com.incall.apps.softmanager.R.attr.contentPaddingBottom, com.incall.apps.softmanager.R.attr.contentPaddingLeft, com.incall.apps.softmanager.R.attr.contentPaddingRight, com.incall.apps.softmanager.R.attr.contentPaddingTop};
        public static final int CardView_android_minHeight = 0x00000001;
        public static final int CardView_android_minWidth = 0x00000000;
        public static final int CardView_cardBackgroundColor = 0x00000002;
        public static final int CardView_cardCornerRadius = 0x00000003;
        public static final int CardView_cardElevation = 0x00000004;
        public static final int CardView_cardMaxElevation = 0x00000005;
        public static final int CardView_cardPreventCornerOverlap = 0x00000006;
        public static final int CardView_cardUseCompatPadding = 0x00000007;
        public static final int CardView_contentPadding = 0x00000008;
        public static final int CardView_contentPaddingBottom = 0x00000009;
        public static final int CardView_contentPaddingLeft = 0x0000000a;
        public static final int CardView_contentPaddingRight = 0x0000000b;
        public static final int CardView_contentPaddingTop = 0x0000000c;

        private styleable() {
        }
    }

    private R() {
    }
}
