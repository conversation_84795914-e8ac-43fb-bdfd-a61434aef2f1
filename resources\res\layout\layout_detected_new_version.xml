<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:tag="layout/layout_detected_new_version_0"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">
    <ImageView
        android:id="@+id/img_car"
        android:layout_width="1240dp"
        android:layout_height="1240dp"
        android:layout_marginBottom="67dp"
        android:src="@drawable/fota_normal_model"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <View
        android:tag="binding_1"
        android:layout_width="1044dp"
        android:layout_height="820dp"
        android:layout_marginTop="185dp"
        android:layout_marginBottom="204dp"
        android:layout_marginStart="1412dp"
        android:layout_marginEnd="104dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.incall.apps.caui.layout.CAUITextView
        android:textSize="24sp"
        android:textColor="@color/caui_config_text_color_secondary"
        android:gravity="center"
        android:id="@+id/new_version_tip"
        android:background="@color/caui_config_tag_bg_green"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginTop="40dp"
        android:text="@string/new_version_tip"
        android:paddingStart="24dp"
        android:paddingEnd="24dp"
        android:layout_marginStart="104dp"
        app:caui_radius="@dimen/caui_config_corner_radius_middle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="56sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/tv_download_upgrade_title"
        android:tag="binding_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginStart="104dp"
        android:textFontWeight="600"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/new_version_tip"/>
    <TextView
        android:textSize="40sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/download_tip"
        android:tag="binding_3"
        android:layout_width="1260dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginStart="104dp"
        android:textFontWeight="400"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_download_upgrade_title"/>
    <com.incall.apps.caui.widget.button.processbutton.CAUIProcessButton
        android:textSize="36sp"
        android:textColor="@color/fota_white"
        android:gravity="center"
        android:id="@+id/btn_start_deploy"
        android:tag="binding_4"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="136dp"
        android:contentDescription="下载升级包"
        android:layout_marginStart="104dp"
        app:caui_pb_cornerRadius="@dimen/caui_config_corner_radius_x_small"
        app:caui_pb_normal_bg_color="@color/caui_config_theme_color_normal"
        app:caui_pb_state="NORMAL"
        app:caui_shape_radius="@dimen/caui_config_corner_radius_x_small"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/download_tip"/>
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="1260dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="80dp"
        android:layout_marginStart="104dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btn_start_deploy">
        <com.incall.apps.softmanager.base.view.AutoDownloadSwitchBar
            android:id="@+id/auto_download_sw_detach_new_version_page"
            android:tag="binding_5"
            android:layout_width="1260dp"
            android:layout_height="wrap_content"/>
        <com.incall.apps.softmanager.base.view.AutoUpdateSwitchBar
            android:id="@+id/auto_sw_detach_new_version_page"
            android:tag="binding_6"
            android:layout_width="1260dp"
            android:layout_height="wrap_content"/>
        <View
            android:background="@color/caui_config_divider_color_primary"
            android:layout_width="1260dp"
            android:layout_height="2dp"
            android:layout_marginTop="16dp"/>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
