<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    android:sharedUserId="android.uid.system"
    android:versionCode="298"
    android:versionName="V1206.1302"
    android:compileSdkVersion="31"
    android:compileSdkVersionCodename="12"
    package="com.incall.apps.softmanager"
    platformBuildVersionCode="31"
    platformBuildVersionName="12">
    <uses-sdk
        android:minSdkVersion="29"
        android:targetSdkVersion="29"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.SET_TIME_ZONE"/>
    <uses-permission android:name="android.permission.WRITE_SETTINGS"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.CALL_PHONE"/>
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.SEND_BROADCAST"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.REBOOT"/>
    <uses-permission android:name="android.permission.INSTALL_PACKAGES"/>
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/>
    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES"/>
    <uses-permission android:name="android.permission.DELETE_PACKAGES"/>
    <protected-broadcast android:name="com.cpms.remove"/>
    <permission
        android:name="com.incall.apps.softmanager.libautotestOTA5.aidl"
        android:protectionLevel="signature"/>
    <permission android:name="com.incall.apps.libdispatchcenter.aidl"/>
    <permission android:name="com.incall.apps.libaftersale.aidl"/>
    <permission
        android:name="com.incall.apps.libautodiagnosis.aidl"
        android:protectionLevel="signature"/>
    <application
        android:theme="@style/AppTheme.softmanager"
        android:label="@string/app_name"
        android:icon="@mipmap/ic_launcher"
        android:name="com.incall.apps.softmanager.FotaApplication"
        android:persistent="false"
        android:allowBackup="true"
        android:supportsRtl="true"
        android:extractNativeLibs="true"
        android:usesCleartextTraffic="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory">
        <meta-data
            android:name="sotaCode"
            android:value="9211815-ES01"/>
        <activity
            android:name="com.incall.apps.softmanager.FactoryActivity"
            android:configChanges="uiMode"/>
        <activity
            android:name="com.incall.apps.softmanager.MainActivity"
            android:launchMode="singleInstance"
            android:configChanges="uiMode">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.LAUNCHER"/>
                <data
                    android:scheme="changan"
                    android:host="sda"
                    android:path="/hmi/softmanager"/>
            </intent-filter>
        </activity>
        <receiver
            android:name="com.incall.apps.softmanager.server.BootReceiver"
            android:permission="true">
            <intent-filter android:priority="2147483647">
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
                <action android:name="android.intent.action.ACTION_BOOT_HU"/>
                <action android:name="fota.action.restart"/>
                <action android:name="com.changan.ota.factorymode"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.incall.apps.softmanager.server.FontSizeChangeReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="android.intent.action.CONFIGURATION_CHANGED"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.incall.apps.libbase.proxy.CarStatusReceiver"
            android:permission="true">
            <intent-filter android:priority="1000">
                <action android:name="ota.intent.action.ALARM_STATUS"/>
                <action android:name="ota.intent.action.POWER_STATUS"/>
                <action android:name="ota.intent.action.GEAR_LEVEL"/>
                <action android:name="ota.intent.action.EPB_STATUS"/>
                <action android:name="ota.intent.action.VEHICLE_SPEED"/>
                <action android:name="ota.intent.action.SAFE_BELT_STATUS"/>
                <action android:name="ota.intent.action.TEST_POWER_API"/>
                <action android:name="ota.intent.action.LOCK_STATUS"/>
                <action android:name="ota.intent.action.CHARGE_STATUS"/>
                <action android:name="ota.intent.action.VIDEO_SAVING_STATUS"/>
                <action android:name="ota.intent.action.BATTERY_POWER"/>
                <action android:name="ota.intent.action.BATTERY_VOLTAGE"/>
                <action android:name="ota.intent.action.OUT_TEMPERATURE"/>
                <action android:name="ota.intent.action.DRIVE_MODE"/>
                <action android:name="ota.intent.action.DIS_CHARGE_STATUS"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.incall.apps.softmanager.server.InstallReceiver"
            android:permission="true"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.PACKAGE_ADDED"/>
                <action android:name="android.intent.action.PACKAGE_REPLACED"/>
                <action android:name="android.intent.action.PACKAGE_REMOVED"/>
                <data android:scheme="package"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.incall.apps.upgradeproxy.independent.service.install.InstallResultBroadcastReceiver"
            android:permission="true"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.incall.apps.upgradeproxy.ACTION_INSTALL_RESULT"/>
            </intent-filter>
        </receiver>
        <service android:name="com.incall.apps.softmanager.server.OtaService"/>
        <receiver
            android:name="com.incall.apps.softmanager.cpms.common.upgrade.UpdateResultReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <data android:scheme="package"/>
            </intent-filter>
        </receiver>
        <receiver android:name="com.incall.apps.softmanager.server.LocaleChangeReceiver">
            <intent-filter>
                <action android:name="android.intent.action.LOCALE_CHANGED"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.incall.apps.master.service.usbtask.usb.UsbReceiver"
            android:permission="true">
            <intent-filter>
                <action android:name="android.intent.action.MEDIA_UNMOUNTABLE"/>
                <action android:name="android.intent.action.MEDIA_MOUNTED"/>
                <action android:name="android.intent.action.MEDIA_EJECT"/>
                <data android:scheme="file"/>
            </intent-filter>
        </receiver>
        <receiver android:name="com.incall.apps.master.service.autotest.AutoTestReceiver">
            <intent-filter>
                <action android:name="action.auto.upgrade.START_UPGRADE"/>
                <action android:name="action.ota.preinstall.START"/>
                <action android:name="action.ota.preinstall.STOP"/>
                <action android:name="ota.intent.action.CHANGE_CAR_STATUS_MANAGER"/>
            </intent-filter>
        </receiver>
        <service
            android:name="com.incall.apps.softmanager.libautotestOTA5.AutotestService"
            android:permission="com.incall.apps.softmanager.libautotestOTA5.aidl"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com_incall_apps_softmanager_libautotest"/>
            </intent-filter>
        </service>
        <service
            android:name="com.incall.apps.libpdi.PdiService"
            android:permission="com.incall.apps.PdiService.aidl"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com_incall_apps_PdiService"/>
            </intent-filter>
        </service>
        <service
            android:name="com.incall.apps.libdispatchcenter.DispatchCenterService"
            android:permission="com.incall.apps.libdispatchcenter.aidl"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com_incall_apps_libdispatchcenter"/>
            </intent-filter>
        </service>
        <service
            android:name="com.incall.apps.libaftersale.AfterSaleService"
            android:permission="com.incall.apps.libaftersale.aidl"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com_incall_apps_libdispatchcenter"/>
            </intent-filter>
        </service>
        <service
            android:name="com.incall.apps.libautodiagnosis.AutoDiagnosisService"
            android:permission="com.incall.apps.libautodiagnosis.aidl"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com_incall_apps_libautodiagnosis"/>
            </intent-filter>
        </service>
        <service
            android:name="com.incall.apps.libbascotamaster.BascOtaMasterService"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com_incall_apps_libbascotamaster"/>
            </intent-filter>
        </service>
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:exported="false"
            android:authorities="com.incall.apps.softmanager.androidx-startup">
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup"/>
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup"/>
        </provider>
    </application>
</manifest>
