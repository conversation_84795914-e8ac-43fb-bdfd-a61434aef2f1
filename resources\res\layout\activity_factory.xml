<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:apps="http://schemas.android.com/apk/res-auto"
    android:id="@+id/fragment_demo"
    android:tag="layout/activity_factory_0"
    android:background="@color/caui_config_content_bg_color_primary"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <View
        android:id="@+id/left_border_divider"
        android:background="@color/caui_config_btn_bg_action_positive_normal_color"
        android:layout_width="3dp"
        android:layout_height="match_parent"
        apps:layout_constraintBottom_toBottomOf="parent"
        apps:layout_constraintStart_toStartOf="parent"
        apps:layout_constraintTop_toTopOf="parent"/>
    <View
        android:id="@+id/left_divider"
        android:background="@color/caui_config_btn_bg_action_positive_normal_color"
        android:layout_width="3dp"
        android:layout_height="match_parent"
        android:layout_marginStart="270dp"
        apps:layout_constraintBottom_toBottomOf="parent"
        apps:layout_constraintStart_toStartOf="parent"
        apps:layout_constraintTop_toTopOf="parent"/>
    <View
        android:id="@+id/right_divider"
        android:background="@color/caui_config_btn_bg_action_positive_normal_color"
        android:layout_width="3dp"
        android:layout_height="match_parent"
        android:layout_marginStart="1100dp"
        apps:layout_constraintBottom_toBottomOf="parent"
        apps:layout_constraintStart_toEndOf="@+id/left_divider"
        apps:layout_constraintTop_toTopOf="parent"/>
    <View
        android:background="@color/caui_config_btn_bg_action_positive_normal_color"
        android:layout_width="0dp"
        android:layout_height="3dp"
        apps:layout_constraintEnd_toEndOf="parent"
        apps:layout_constraintStart_toStartOf="@+id/right_divider"
        apps:layout_constraintTop_toBottomOf="@+id/console_title_tx"/>
    <View
        android:background="@color/caui_config_btn_bg_action_positive_normal_color"
        android:layout_width="0dp"
        android:layout_height="3dp"
        apps:layout_constraintBottom_toBottomOf="parent"
        apps:layout_constraintEnd_toEndOf="parent"
        apps:layout_constraintStart_toStartOf="@+id/right_divider"/>
    <View
        android:id="@+id/right_border_divider"
        android:background="@color/caui_config_btn_bg_action_positive_normal_color"
        android:layout_width="3dp"
        android:layout_height="match_parent"
        apps:layout_constraintBottom_toBottomOf="parent"
        apps:layout_constraintEnd_toEndOf="parent"
        apps:layout_constraintTop_toTopOf="parent"/>
    <ScrollView
        android:id="@+id/factory_scroll_view"
        android:scrollbars="vertical"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:fadeScrollbars="false"
        apps:layout_constraintBottom_toBottomOf="parent"
        apps:layout_constraintEnd_toEndOf="@+id/right_divider"
        apps:layout_constraintStart_toStartOf="parent"
        apps:layout_constraintTop_toTopOf="parent">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <View
                android:background="@color/caui_config_btn_bg_action_positive_normal_color"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                apps:layout_constraintEnd_toEndOf="parent"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toTopOf="parent"/>
            <View
                android:background="@color/caui_config_btn_bg_action_positive_normal_color"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                apps:layout_constraintBottom_toBottomOf="@+id/ninth_tx"
                apps:layout_constraintEnd_toEndOf="parent"
                apps:layout_constraintStart_toStartOf="parent"/>
            <View
                android:background="@color/caui_config_btn_bg_action_positive_normal_color"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                apps:layout_constraintBottom_toBottomOf="@+id/first_tx"
                apps:layout_constraintEnd_toEndOf="parent"
                apps:layout_constraintStart_toStartOf="parent"/>
            <View
                android:background="@color/caui_config_btn_bg_action_positive_normal_color"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                apps:layout_constraintBottom_toBottomOf="@+id/second_tx"
                apps:layout_constraintEnd_toEndOf="parent"
                apps:layout_constraintStart_toStartOf="parent"/>
            <View
                android:background="@color/caui_config_btn_bg_action_positive_normal_color"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                apps:layout_constraintBottom_toBottomOf="@+id/third_tx"
                apps:layout_constraintEnd_toEndOf="parent"
                apps:layout_constraintStart_toStartOf="parent"/>
            <View
                android:background="@color/caui_config_btn_bg_action_positive_normal_color"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                apps:layout_constraintBottom_toBottomOf="@+id/fourth_tx"
                apps:layout_constraintEnd_toEndOf="parent"
                apps:layout_constraintStart_toStartOf="parent"/>
            <View
                android:background="@color/caui_config_btn_bg_action_positive_normal_color"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                apps:layout_constraintBottom_toBottomOf="@+id/fifth_tx"
                apps:layout_constraintEnd_toEndOf="parent"
                apps:layout_constraintStart_toStartOf="parent"/>
            <View
                android:background="@color/caui_config_btn_bg_action_positive_normal_color"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                apps:layout_constraintBottom_toBottomOf="@+id/sixth_tx"
                apps:layout_constraintEnd_toEndOf="parent"
                apps:layout_constraintStart_toStartOf="parent"/>
            <View
                android:background="@color/caui_config_btn_bg_action_positive_normal_color"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                apps:layout_constraintBottom_toBottomOf="@+id/seventh_tx"
                apps:layout_constraintEnd_toEndOf="parent"
                apps:layout_constraintStart_toStartOf="parent"/>
            <View
                android:background="@color/caui_config_btn_bg_action_positive_normal_color"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                apps:layout_constraintBottom_toBottomOf="@+id/eighth_tx"
                apps:layout_constraintEnd_toEndOf="parent"
                apps:layout_constraintStart_toStartOf="parent"/>
            <View
                android:background="@color/caui_config_btn_bg_action_positive_normal_color"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                apps:layout_constraintBottom_toBottomOf="@+id/tenth_tx"
                apps:layout_constraintEnd_toEndOf="parent"
                apps:layout_constraintStart_toStartOf="parent"/>
            <TextView
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/caui_config_text_color_primary"
                android:gravity="center"
                android:id="@+id/ninth_tx"
                android:layout_width="250dp"
                android:layout_height="100dp"
                android:text="退出"
                android:layout_marginStart="10dp"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toTopOf="parent"/>
            <TextView
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/caui_config_text_color_primary"
                android:gravity="center"
                android:id="@+id/first_tx"
                android:layout_width="250dp"
                android:layout_height="370dp"
                android:text="获取车辆状态"
                android:layout_marginStart="10dp"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toBottomOf="@+id/ninth_tx"/>
            <TextView
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/caui_config_text_color_primary"
                android:gravity="center"
                android:id="@+id/second_tx"
                android:layout_width="250dp"
                android:layout_height="370dp"
                android:text="设置车辆状态"
                android:layout_marginStart="10dp"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toBottomOf="@+id/first_tx"/>
            <TextView
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/caui_config_text_color_primary"
                android:gravity="center"
                android:id="@+id/third_tx"
                android:layout_width="250dp"
                android:layout_height="100dp"
                android:text="收集版本"
                android:layout_marginStart="10dp"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toBottomOf="@+id/second_tx"/>
            <TextView
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/caui_config_text_color_primary"
                android:gravity="center"
                android:id="@+id/fourth_tx"
                android:layout_width="250dp"
                android:layout_height="200dp"
                android:text="跳过OTA升级条件检查"
                android:layout_marginStart="10dp"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toBottomOf="@+id/third_tx"/>
            <TextView
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/caui_config_text_color_primary"
                android:gravity="center"
                android:id="@+id/fifth_tx"
                android:layout_width="250dp"
                android:layout_height="100dp"
                android:text="试验车模式"
                android:layout_marginStart="10dp"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toBottomOf="@+id/fourth_tx"/>
            <TextView
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/caui_config_text_color_primary"
                android:gravity="center"
                android:id="@+id/sixth_tx"
                android:layout_width="250dp"
                android:layout_height="200dp"
                android:text="SDA工程模式"
                android:layout_marginStart="10dp"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toBottomOf="@+id/fifth_tx"/>
            <TextView
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/caui_config_text_color_primary"
                android:gravity="center"
                android:id="@+id/seventh_tx"
                android:layout_width="250dp"
                android:layout_height="100dp"
                android:text="重启"
                android:layout_marginStart="10dp"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toBottomOf="@+id/sixth_tx"/>
            <TextView
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/caui_config_text_color_primary"
                android:gravity="center"
                android:id="@+id/eighth_tx"
                android:layout_width="250dp"
                android:layout_height="100dp"
                android:text="获取升级信息"
                android:layout_marginStart="10dp"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toBottomOf="@+id/seventh_tx"/>
            <TextView
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/caui_config_text_color_primary"
                android:gravity="center"
                android:id="@+id/tenth_tx"
                android:layout_width="250dp"
                android:layout_height="100dp"
                android:text="PDI模式"
                android:layout_marginStart="10dp"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toBottomOf="@+id/eighth_tx"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:id="@+id/btn_exit_1"
                android:tag="binding_1"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="返回FOTA界面"
                android:layout_marginStart="290dp"
                apps:layout_constraintBaseline_toBaselineOf="@+id/ninth_tx"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toTopOf="parent"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_2"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="清空控制台"
                android:layout_marginStart="20dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintBaseline_toBaselineOf="@+id/ninth_tx"
                apps:layout_constraintStart_toEndOf="@+id/btn_exit_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_exit_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:id="@+id/btn_get_car_status_1"
                android:tag="binding_3"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="15dp"
                android:text="车速"
                android:layout_marginStart="290dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toTopOf="@+id/first_tx"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_4"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="手刹状态"
                android:layout_marginStart="20dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toEndOf="@+id/btn_get_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_get_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_5"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="动力电池电量"
                android:layout_marginStart="290dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toEndOf="@+id/btn_get_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_get_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_6"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="电源挡位"
                android:layout_marginStart="560dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toEndOf="@+id/btn_get_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_get_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_7"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="90dp"
                android:text="防盗状态"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_get_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_get_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_8"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="90dp"
                android:text="安全带卡口状态"
                android:layout_marginStart="270dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_get_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_get_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_9"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="90dp"
                android:text="蓄电池电压"
                android:layout_marginStart="540dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_get_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_get_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_10"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="90dp"
                android:text="哨塔模式录制状态"
                android:layout_marginStart="810dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_get_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_get_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_11"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="180dp"
                android:text="驻车挡位"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_get_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_get_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_12"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="180dp"
                android:text="放电状态"
                android:layout_marginStart="270dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_get_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_get_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_13"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="180dp"
                android:text="充电状态"
                android:layout_marginStart="540dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_get_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_get_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_14"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="180dp"
                android:text="超级省电模式"
                android:layout_marginStart="810dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_get_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_get_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_15"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="270dp"
                android:text="环境温度"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_get_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_get_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_16"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="270dp"
                android:text="获取所有状态"
                android:layout_marginStart="270dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_get_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_get_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_17"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="270dp"
                android:text="获取定位信息"
                android:layout_marginStart="540dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_get_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_get_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:id="@+id/btn_set_car_status_1"
                android:tag="binding_18"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="15dp"
                android:text="设置车速满足"
                android:layout_marginStart="290dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toTopOf="@+id/second_tx"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_19"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="设置驻车挡位满足"
                android:layout_marginStart="270dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_set_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_set_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_20"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="设置手刹状态满足"
                android:layout_marginStart="540dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_set_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_set_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_21"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="设置电池电压满足"
                android:layout_marginStart="810dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_set_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_set_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_22"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="90dp"
                android:text="设置电池电量满足"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_set_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_set_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_23"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="90dp"
                android:text="设置防盗状态满足"
                android:layout_marginStart="270dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_set_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_set_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_24"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="90dp"
                android:text="切换电源挡位ON/OFF"
                android:layout_marginStart="540dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_set_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_set_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_25"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="90dp"
                android:text="设置安全带状态满足"
                android:layout_marginStart="810dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_set_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_set_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_26"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="180dp"
                android:text="设置充电状态满足"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_set_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_set_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_27"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="180dp"
                android:text="设置放电状态满足"
                android:layout_marginStart="270dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_set_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_set_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_28"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="180dp"
                android:text="设置超级省电满足"
                android:layout_marginStart="540dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_set_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_set_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_29"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="180dp"
                android:text="设置环境温度满足"
                android:layout_marginStart="810dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_set_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_set_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_30"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="270dp"
                android:text="设置哨塔模式满足"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_set_car_status_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_set_car_status_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:id="@+id/btn_version_collect_1"
                android:tag="binding_31"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="收集版本"
                android:layout_marginStart="290dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintBaseline_toBaselineOf="@+id/third_tx"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toTopOf="parent"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_32"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="查看版本信息"
                android:layout_marginStart="270dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintBaseline_toBaselineOf="@+id/third_tx"
                apps:layout_constraintStart_toStartOf="@+id/btn_version_collect_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_version_collect_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_33"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="查看补充后版本信息"
                android:layout_marginStart="540dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintBaseline_toBaselineOf="@+id/third_tx"
                apps:layout_constraintStart_toStartOf="@+id/btn_version_collect_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_version_collect_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_34"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="查看自升级版本信息"
                android:layout_marginStart="810dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintBaseline_toBaselineOf="@+id/third_tx"
                apps:layout_constraintStart_toStartOf="@+id/btn_version_collect_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_version_collect_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:id="@+id/btn_skip_condition_1"
                android:tag="binding_35"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="20dp"
                android:text="跳过升级条件检测"
                android:layout_marginStart="290dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toTopOf="@+id/fourth_tx"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_36"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="不跳过升级条件检测"
                android:layout_marginStart="270dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_skip_condition_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_skip_condition_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:id="@+id/btn_skip_conflict"
                android:tag="binding_37"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="110dp"
                android:text="跳过诊断冲突"
                android:layout_marginStart="290dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toTopOf="@+id/fourth_tx"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_38"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="不跳过诊断冲突"
                android:layout_marginStart="270dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_skip_conflict"
                apps:layout_constraintTop_toTopOf="@+id/btn_skip_conflict"/>
            <TextView
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/caui_config_text_color_primary"
                android:gravity="center"
                android:id="@+id/tx_ignore_deploy_speed_limit"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="忽略部署限速策略："
                android:layout_marginStart="540dp"
                apps:layout_constraintStart_toStartOf="@+id/btn_skip_condition_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_skip_condition_1"/>
            <com.incall.apps.caui.widget.button.switchbutton.SwitchButton
                android:id="@+id/st_ignore_deploy_speed_limit"
                android:layout_width="150dp"
                android:layout_height="70dp"
                android:layout_marginStart="810dp"
                apps:layout_constraintStart_toStartOf="@+id/btn_skip_condition_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_skip_condition_1"
                style="@style/SwitchButtonDefault"/>
            <TextView
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/caui_config_text_color_primary"
                android:gravity="center"
                android:id="@+id/tx_ignore_usb_package_encrypt"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="忽略USB加密检查："
                android:layout_marginStart="540dp"
                apps:layout_constraintStart_toStartOf="@+id/btn_skip_conflict"
                apps:layout_constraintTop_toTopOf="@+id/btn_skip_conflict"/>
            <com.incall.apps.caui.widget.button.switchbutton.SwitchButton
                android:id="@+id/st_ignore_usb_package_encrypt"
                android:layout_width="150dp"
                android:layout_height="70dp"
                android:layout_marginStart="810dp"
                apps:layout_constraintStart_toStartOf="@+id/btn_skip_conflict"
                apps:layout_constraintTop_toTopOf="@+id/btn_skip_conflict"
                style="@style/SwitchButtonDefault"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:id="@+id/btn_test_vehicle_1"
                android:tag="binding_39"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="切换试验车/普通车"
                android:layout_marginStart="290dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintBaseline_toBaselineOf="@+id/fifth_tx"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toTopOf="parent"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_40"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="车辆软件信息导出"
                android:layout_marginStart="270dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintBaseline_toBaselineOf="@+id/fifth_tx"
                apps:layout_constraintStart_toStartOf="@+id/btn_test_vehicle_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_test_vehicle_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_41"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="进入试验车模式"
                android:layout_marginStart="540dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintBaseline_toBaselineOf="@+id/fifth_tx"
                apps:layout_constraintStart_toStartOf="@+id/btn_test_vehicle_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_test_vehicle_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_42"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="退出测试"
                android:layout_marginStart="810dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintBaseline_toBaselineOf="@+id/fifth_tx"
                apps:layout_constraintStart_toStartOf="@+id/btn_test_vehicle_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_test_vehicle_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:id="@+id/btn_sda_factory_1"
                android:tag="binding_43"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="15dp"
                android:text="识别USB2.0"
                android:layout_marginStart="290dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toTopOf="@+id/sixth_tx"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_44"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="独立升级"
                android:layout_marginStart="270dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_sda_factory_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_sda_factory_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_45"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="快捷方式"
                android:layout_marginStart="540dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_sda_factory_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_sda_factory_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_46"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="打开子任务视图"
                android:layout_marginStart="810dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_sda_factory_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_sda_factory_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:id="@+id/btn_sda_factory_2"
                android:tag="binding_47"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:layout_marginTop="100dp"
                android:text="关闭子任务视图"
                android:layout_marginStart="290dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toTopOf="@+id/sixth_tx"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_48"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="清除subMaster文件"
                android:layout_marginStart="270dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_sda_factory_2"
                apps:layout_constraintTop_toTopOf="@+id/btn_sda_factory_2"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_49"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="清除大版本号"
                android:layout_marginStart="540dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintStart_toStartOf="@+id/btn_sda_factory_2"
                apps:layout_constraintTop_toTopOf="@+id/btn_sda_factory_2"/>
            <TextView
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/caui_config_text_color_primary"
                android:gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="70dp"
                android:text="测试模式"
                android:layout_marginStart="810dp"
                apps:layout_constraintStart_toStartOf="@+id/btn_sda_factory_2"
                apps:layout_constraintTop_toTopOf="@+id/btn_sda_factory_2"/>
            <com.incall.apps.caui.widget.button.switchbutton.SwitchButton
                android:gravity="center"
                android:id="@+id/st_ota_test_mode"
                android:layout_width="150dp"
                android:layout_height="70dp"
                android:layout_marginStart="900dp"
                apps:layout_constraintStart_toStartOf="@+id/btn_sda_factory_2"
                apps:layout_constraintTop_toTopOf="@+id/btn_sda_factory_2"
                style="@style/SwitchButtonSmallDefault"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:id="@+id/btn_reboot_1"
                android:tag="binding_50"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="重启车机"
                android:layout_marginStart="290dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintBaseline_toBaselineOf="@+id/seventh_tx"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toTopOf="parent"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_51"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="重启FOTA"
                android:layout_marginStart="270dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintBaseline_toBaselineOf="@+id/seventh_tx"
                apps:layout_constraintStart_toStartOf="@+id/btn_reboot_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_reboot_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_52"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="请求C2进入低功耗"
                android:layout_marginStart="540dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintBaseline_toBaselineOf="@+id/seventh_tx"
                apps:layout_constraintStart_toStartOf="@+id/btn_reboot_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_reboot_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_53"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="请求C2退出低功耗"
                android:layout_marginStart="810dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintBaseline_toBaselineOf="@+id/seventh_tx"
                apps:layout_constraintStart_toStartOf="@+id/btn_reboot_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_reboot_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:id="@+id/btn_get_info_1"
                android:tag="binding_54"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="获取实时升级信息"
                android:layout_marginStart="290dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintBaseline_toBaselineOf="@+id/eighth_tx"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toTopOf="parent"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_55"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="U盘导出日志"
                android:layout_marginStart="270dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintBaseline_toBaselineOf="@+id/eighth_tx"
                apps:layout_constraintStart_toStartOf="@+id/btn_get_info_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_get_info_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_56"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="清除升级任务记录"
                android:layout_marginStart="540dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintBaseline_toBaselineOf="@+id/eighth_tx"
                apps:layout_constraintStart_toStartOf="@+id/btn_get_info_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_get_info_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:tag="binding_57"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="上传日志到云端"
                android:layout_marginStart="810dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintBaseline_toBaselineOf="@+id/eighth_tx"
                apps:layout_constraintStart_toStartOf="@+id/btn_get_info_1"
                apps:layout_constraintTop_toTopOf="@+id/btn_get_info_1"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:id="@+id/btn_enter_pdi"
                android:tag="binding_58"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="进入PDI模式"
                android:layout_marginStart="290dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintBaseline_toBaselineOf="@+id/tenth_tx"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toTopOf="parent"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:id="@+id/btn_exit_pdi"
                android:tag="binding_59"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="退出PDI模式"
                android:layout_marginStart="270dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintBaseline_toBaselineOf="@+id/tenth_tx"
                apps:layout_constraintStart_toStartOf="@+id/btn_get_info_1"
                apps:layout_constraintTop_toTopOf="@+id/tenth_tx"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:id="@+id/btn_allow_usb"
                android:tag="binding_60"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="允许U盘升级"
                android:layout_marginStart="540dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintBaseline_toBaselineOf="@+id/tenth_tx"
                apps:layout_constraintStart_toStartOf="@+id/btn_get_info_1"
                apps:layout_constraintTop_toTopOf="@+id/tenth_tx"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="24sp"
                android:textStyle="bold"
                android:gravity="center"
                android:id="@+id/btn_refuse_usb"
                android:tag="binding_61"
                android:layout_width="250dp"
                android:layout_height="70dp"
                android:text="不允许U盘升级"
                android:layout_marginStart="810dp"
                apps:caui_round_radius="@dimen/caui_config_corner_radius_small"
                apps:layout_constraintBaseline_toBaselineOf="@+id/tenth_tx"
                apps:layout_constraintStart_toStartOf="@+id/btn_get_info_1"
                apps:layout_constraintTop_toTopOf="@+id/tenth_tx"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
    <TextView
        android:textSize="32sp"
        android:textStyle="bold"
        android:textColor="@color/fota_white"
        android:gravity="center"
        android:id="@+id/console_title_tx"
        android:background="@color/caui_config_btn_bg_action_positive_normal_color"
        android:layout_width="0dp"
        android:layout_height="100dp"
        android:text="控制台显示界面"
        apps:layout_constraintEnd_toEndOf="parent"
        apps:layout_constraintHorizontal_bias="1"
        apps:layout_constraintStart_toStartOf="@+id/right_divider"
        apps:layout_constraintTop_toTopOf="parent"/>
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/console_info_rv"
        android:scrollbars="vertical"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="12dp"
        apps:layout_constraintBottom_toBottomOf="parent"
        apps:layout_constraintEnd_toEndOf="parent"
        apps:layout_constraintStart_toStartOf="@+id/right_divider"
        apps:layout_constraintTop_toBottomOf="@+id/console_title_tx"/>
</androidx.constraintlayout.widget.ConstraintLayout>
