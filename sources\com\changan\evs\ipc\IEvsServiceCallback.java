package com.changan.evs.ipc;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface IEvsServiceCallback extends IInterface {

    public static class Default implements IEvsServiceCallback {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.changan.evs.ipc.IEvsServiceCallback
        public void onBroadcastMessageReceive(EvsMessage evsMessage) throws RemoteException {
        }

        @Override // com.changan.evs.ipc.IEvsServiceCallback
        public void onPrivateMessageReceive(EvsMessage evsMessage) throws RemoteException {
        }

        @Override // com.changan.evs.ipc.IEvsServiceCallback
        public void onResponseEvent(EvsMessage evsMessage) throws RemoteException {
        }
    }

    void onBroadcastMessageReceive(EvsMessage evsMessage) throws RemoteException;

    void onPrivateMessageReceive(EvsMessage evsMessage) throws RemoteException;

    void onResponseEvent(EvsMessage evsMessage) throws RemoteException;

    public static abstract class Stub extends Binder implements IEvsServiceCallback {
        private static final String DESCRIPTOR = "com.changan.evs.ipc.IEvsServiceCallback";
        static final int TRANSACTION_onBroadcastMessageReceive = 1;
        static final int TRANSACTION_onPrivateMessageReceive = 3;
        static final int TRANSACTION_onResponseEvent = 2;

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static IEvsServiceCallback asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof IEvsServiceCallback)) {
                return (IEvsServiceCallback) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i == 1) {
                parcel.enforceInterface(DESCRIPTOR);
                onBroadcastMessageReceive(parcel.readInt() != 0 ? EvsMessage.CREATOR.createFromParcel(parcel) : null);
                return true;
            }
            if (i == 2) {
                parcel.enforceInterface(DESCRIPTOR);
                onResponseEvent(parcel.readInt() != 0 ? EvsMessage.CREATOR.createFromParcel(parcel) : null);
                return true;
            }
            if (i == 3) {
                parcel.enforceInterface(DESCRIPTOR);
                onPrivateMessageReceive(parcel.readInt() != 0 ? EvsMessage.CREATOR.createFromParcel(parcel) : null);
                return true;
            }
            if (i == 1598968902) {
                parcel2.writeString(DESCRIPTOR);
                return true;
            }
            return super.onTransact(i, parcel, parcel2, i2);
        }

        private static class Proxy implements IEvsServiceCallback {
            public static IEvsServiceCallback sDefaultImpl;
            private IBinder mRemote;

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            @Override // com.changan.evs.ipc.IEvsServiceCallback
            public void onBroadcastMessageReceive(EvsMessage evsMessage) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (evsMessage != null) {
                        obtain.writeInt(1);
                        evsMessage.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    if (this.mRemote.transact(1, obtain, null, 1) || Stub.getDefaultImpl() == null) {
                        return;
                    }
                    Stub.getDefaultImpl().onBroadcastMessageReceive(evsMessage);
                } finally {
                    obtain.recycle();
                }
            }

            @Override // com.changan.evs.ipc.IEvsServiceCallback
            public void onResponseEvent(EvsMessage evsMessage) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (evsMessage != null) {
                        obtain.writeInt(1);
                        evsMessage.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    if (this.mRemote.transact(2, obtain, null, 1) || Stub.getDefaultImpl() == null) {
                        return;
                    }
                    Stub.getDefaultImpl().onResponseEvent(evsMessage);
                } finally {
                    obtain.recycle();
                }
            }

            @Override // com.changan.evs.ipc.IEvsServiceCallback
            public void onPrivateMessageReceive(EvsMessage evsMessage) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (evsMessage != null) {
                        obtain.writeInt(1);
                        evsMessage.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    if (this.mRemote.transact(3, obtain, null, 1) || Stub.getDefaultImpl() == null) {
                        return;
                    }
                    Stub.getDefaultImpl().onPrivateMessageReceive(evsMessage);
                } finally {
                    obtain.recycle();
                }
            }
        }

        public static boolean setDefaultImpl(IEvsServiceCallback iEvsServiceCallback) {
            if (Proxy.sDefaultImpl != null) {
                throw new IllegalStateException("setDefaultImpl() called twice");
            }
            if (iEvsServiceCallback == null) {
                return false;
            }
            Proxy.sDefaultImpl = iEvsServiceCallback;
            return true;
        }

        public static IEvsServiceCallback getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }
    }
}
