<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/transparent"
    android:layout_width="960dp"
    android:layout_height="960dp">
    <com.incall.apps.caui.shape.CAUIShapeConstraintLayout
        android:layout_gravity="center"
        android:background="@color/transparent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:minWidth="960dp"
        app:caui_shape_radius="@dimen/caui_config_corner_radius_middle">
        <com.incall.apps.caui.layout.CAUITextView
            android:textSize="44sp"
            android:textStyle="bold"
            android:textColor="@color/caui_config_text_color_primary"
            android:gravity="center"
            android:id="@+id/lv_version_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="@string/version_dialog_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <com.incall.apps.caui.layout.CAUITextView
            android:textSize="22sp"
            android:textColor="@color/caui_config_text_color_primary"
            android:gravity="center"
            android:id="@+id/tx_big_version"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/lv_version_title"/>
        <com.incall.apps.caui.layout.CAUITextView
            android:textSize="22sp"
            android:textColor="@color/caui_config_text_color_primary"
            android:gravity="center"
            android:id="@+id/tx_big_display_version"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tx_big_version"/>
        <androidx.recyclerview.widget.RecyclerView
            android:scrollbarThumbVertical="@drawable/fota_scroll_thumb"
            android:scrollbarStyle="outsideOverlay"
            android:orientation="vertical"
            android:id="@+id/lv_version_list"
            android:scrollbars="vertical"
            android:layout_width="960dp"
            android:layout_height="642dp"
            android:layout_marginBottom="40dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tx_big_display_version"/>
    </com.incall.apps.caui.shape.CAUIShapeConstraintLayout>
</FrameLayout>
