<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:tag="layout/fragment_main_0"
    android:background="@color/caui_config_content_bg_color_primary"
    android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_constraintHorizontal_chainStyle="spread">
    <com.incall.apps.caui.widget.title.CAUITopBar
        android:id="@+id/menu_bar"
        android:layout_width="match_parent"
        android:layout_height="136dp"
        android:layout_marginStart="32dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="40sp"
        android:textColor="@color/caui_config_text_color_secondary"
        android:id="@+id/pre_title_tx"
        android:layout_width="wrap_content"
        android:layout_height="55dp"
        android:layout_marginTop="40dp"
        android:layout_marginStart="216dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <include
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/menu_bar"
        layout="@layout/layout_main"/>
    <include
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/menu_bar"
        layout="@layout/layout_error"/>
    <include
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/menu_bar"
        layout="@layout/layout_network_unavailable"/>
    <include
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/menu_bar"
        layout="@layout/layout_detected_new_version"/>
    <include
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/menu_bar"
        layout="@layout/layout_deploy"/>
    <include
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/menu_bar"
        layout="@layout/layout_task_detail"/>
    <include
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/menu_bar"
        layout="@layout/layout_condition_check"/>
    <include
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/menu_bar"
        layout="@layout/layout_condition_check_fail"/>
    <include
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/menu_bar"
        layout="@layout/layout_condition_check_count_down"/>
    <include
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/menu_bar"
        layout="@layout/layout_entry_check"/>
    <include
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/menu_bar"
        layout="@layout/layout_pre_install_mask"/>
    <include
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/menu_bar"
        layout="@layout/layout_eject_usb"/>
    <include
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/menu_bar"
        layout="@layout/layout_ota_abnormal"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="26sp"
        android:gravity="center"
        android:id="@+id/menu_tx_test_vehicle"
        android:tag="binding_1"
        android:layout_width="300dp"
        android:layout_height="100dp"
        android:layout_marginTop="20dp"
        android:text="@string/menu_bar_test_vehicle"
        android:layout_marginEnd="20dp"
        app:caui_round_btn_type="secondary"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
