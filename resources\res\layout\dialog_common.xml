<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_gravity="center"
        android:background="@color/caui_config_content_bg_color_primary"
        android:paddingTop="55dp"
        android:layout_width="880dp"
        android:layout_height="wrap_content">
        <TextView
            android:textSize="44sp"
            android:textColor="@color/caui_config_text_color_primary"
            android:gravity="center"
            android:id="@+id/title"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"/>
        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/content_layout"
            android:layout_width="752dp"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toTopOf="@+id/btn_layout"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title">
            <TextView
                android:textSize="36sp"
                android:textColor="@color/caui_config_text_color_secondary"
                android:id="@+id/content"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="22dp"/>
            <EditText
                android:textSize="36sp"
                android:textColor="@color/caui_config_text_color_third"
                android:id="@+id/dialog_content_edit"
                android:background="@null"
                android:padding="16dp"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="90dp"
                android:layout_marginTop="22dp"
                android:hint="请输入工号获取创建信息"/>
            <TextView
                android:textSize="26sp"
                android:textColor="@color/caui_config_theme_color_normal"
                android:layout_gravity="bottom|right"
                android:id="@+id/more_operate"
                android:padding="12dp"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="22dp"
                android:text="@string/check_details"/>
        </LinearLayout>
        <LinearLayout
            android:orientation="horizontal"
            android:id="@+id/btn_layout"
            android:paddingTop="64dp"
            android:paddingBottom="64dp"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/content_layout">
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="36sp"
                android:textColor="@color/caui_config_color_white"
                android:gravity="center"
                android:id="@+id/btn_left"
                android:visibility="gone"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:maxWidth="440dp"
                android:text="@string/notify_dialog_button_confirm"
                android:layout_weight="1"
                android:layout_marginStart="64dp"
                android:layout_marginEnd="64dp"
                app:caui_round_btn_type="main"
                app:caui_round_radius="@dimen/caui_config_corner_radius_middle"/>
            <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
                android:textSize="36sp"
                android:gravity="center"
                android:id="@+id/btn_right"
                android:visibility="gone"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:text="@string/notify_dialog_button_ignore"
                android:layout_weight="1"
                android:layout_marginEnd="64dp"
                app:caui_round_btn_type="secondary"
                app:caui_round_radius="@dimen/caui_config_corner_radius_middle"/>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
