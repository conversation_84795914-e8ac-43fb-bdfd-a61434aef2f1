package com.changan.lh;

/* loaded from: classes.dex */
public final class R {

    public static final class anim {
        public static final int abc_fade_in = 0x7f010000;
        public static final int abc_fade_out = 0x7f010001;
        public static final int abc_grow_fade_in_from_bottom = 0x7f010002;
        public static final int abc_popup_enter = 0x7f010003;
        public static final int abc_popup_exit = 0x7f010004;
        public static final int abc_shrink_fade_out_from_bottom = 0x7f010005;
        public static final int abc_slide_in_bottom = 0x7f010006;
        public static final int abc_slide_in_top = 0x7f010007;
        public static final int abc_slide_out_bottom = 0x7f010008;
        public static final int abc_slide_out_top = 0x7f010009;
        public static final int abc_tooltip_enter = 0x7f01000a;
        public static final int abc_tooltip_exit = 0x7f01000b;
        public static final int btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c;
        public static final int btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d;
        public static final int btn_checkbox_to_checked_icon_null_animation = 0x7f01000e;
        public static final int btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f;
        public static final int btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010;
        public static final int btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011;
        public static final int btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012;
        public static final int btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013;
        public static final int btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014;
        public static final int btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015;
        public static final int btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016;
        public static final int btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017;
        public static final int design_bottom_sheet_slide_in = 0x7f01001e;
        public static final int design_bottom_sheet_slide_out = 0x7f01001f;
        public static final int design_snackbar_in = 0x7f010020;
        public static final int design_snackbar_out = 0x7f010021;
        public static final int mtrl_bottom_sheet_slide_in = 0x7f010024;
        public static final int mtrl_bottom_sheet_slide_out = 0x7f010025;
        public static final int mtrl_card_lowers_interpolator = 0x7f010026;

        private anim() {
        }
    }

    public static final class animator {
        public static final int design_appbar_state_list_animator = 0x7f020000;
        public static final int design_fab_hide_motion_spec = 0x7f020001;
        public static final int design_fab_show_motion_spec = 0x7f020002;
        public static final int mtrl_btn_state_list_anim = 0x7f020013;
        public static final int mtrl_btn_unelevated_state_list_anim = 0x7f020014;
        public static final int mtrl_card_state_list_anim = 0x7f020015;
        public static final int mtrl_chip_state_list_anim = 0x7f020016;
        public static final int mtrl_extended_fab_hide_motion_spec = 0x7f020019;
        public static final int mtrl_extended_fab_show_motion_spec = 0x7f02001a;
        public static final int mtrl_extended_fab_state_list_animator = 0x7f02001b;
        public static final int mtrl_fab_hide_motion_spec = 0x7f02001c;
        public static final int mtrl_fab_show_motion_spec = 0x7f02001d;
        public static final int mtrl_fab_transformation_sheet_collapse_spec = 0x7f02001e;
        public static final int mtrl_fab_transformation_sheet_expand_spec = 0x7f02001f;

        private animator() {
        }
    }

    public static final class attr {
        public static final int actionBarDivider = 0x7f040011;
        public static final int actionBarItemBackground = 0x7f040012;
        public static final int actionBarPopupTheme = 0x7f040013;
        public static final int actionBarSize = 0x7f040014;
        public static final int actionBarSplitStyle = 0x7f040015;
        public static final int actionBarStyle = 0x7f040016;
        public static final int actionBarTabBarStyle = 0x7f040017;
        public static final int actionBarTabStyle = 0x7f040018;
        public static final int actionBarTabTextStyle = 0x7f040019;
        public static final int actionBarTheme = 0x7f04001a;
        public static final int actionBarWidgetTheme = 0x7f04001b;
        public static final int actionButtonStyle = 0x7f04001c;
        public static final int actionDropDownStyle = 0x7f04001d;
        public static final int actionLayout = 0x7f04001e;
        public static final int actionMenuTextAppearance = 0x7f04001f;
        public static final int actionMenuTextColor = 0x7f040020;
        public static final int actionModeBackground = 0x7f040021;
        public static final int actionModeCloseButtonStyle = 0x7f040022;
        public static final int actionModeCloseDrawable = 0x7f040024;
        public static final int actionModeCopyDrawable = 0x7f040025;
        public static final int actionModeCutDrawable = 0x7f040026;
        public static final int actionModeFindDrawable = 0x7f040027;
        public static final int actionModePasteDrawable = 0x7f040028;
        public static final int actionModePopupWindowStyle = 0x7f040029;
        public static final int actionModeSelectAllDrawable = 0x7f04002a;
        public static final int actionModeShareDrawable = 0x7f04002b;
        public static final int actionModeSplitBackground = 0x7f04002c;
        public static final int actionModeStyle = 0x7f04002d;
        public static final int actionModeWebSearchDrawable = 0x7f04002f;
        public static final int actionOverflowButtonStyle = 0x7f040030;
        public static final int actionOverflowMenuStyle = 0x7f040031;
        public static final int actionProviderClass = 0x7f040032;
        public static final int actionTextColorAlpha = 0x7f040033;
        public static final int actionViewClass = 0x7f040034;
        public static final int activityChooserViewStyle = 0x7f040035;
        public static final int alertDialogButtonGroupStyle = 0x7f040036;
        public static final int alertDialogCenterButtons = 0x7f040037;
        public static final int alertDialogStyle = 0x7f040038;
        public static final int alertDialogTheme = 0x7f040039;
        public static final int allowStacking = 0x7f04003a;
        public static final int alpha = 0x7f04003b;
        public static final int alphabeticModifiers = 0x7f04003c;
        public static final int animationMode = 0x7f040040;
        public static final int appBarLayoutStyle = 0x7f040041;
        public static final int arrowHeadLength = 0x7f040044;
        public static final int arrowShaftLength = 0x7f040045;
        public static final int autoCompleteTextViewStyle = 0x7f040048;
        public static final int autoSizeMaxTextSize = 0x7f040049;
        public static final int autoSizeMinTextSize = 0x7f04004a;
        public static final int autoSizePresetSizes = 0x7f04004b;
        public static final int autoSizeStepGranularity = 0x7f04004c;
        public static final int autoSizeTextType = 0x7f04004d;
        public static final int background = 0x7f04004f;
        public static final int backgroundColor = 0x7f040050;
        public static final int backgroundInsetBottom = 0x7f040051;
        public static final int backgroundInsetEnd = 0x7f040052;
        public static final int backgroundInsetStart = 0x7f040053;
        public static final int backgroundInsetTop = 0x7f040054;
        public static final int backgroundOverlayColorAlpha = 0x7f040055;
        public static final int backgroundSplit = 0x7f040056;
        public static final int backgroundStacked = 0x7f040057;
        public static final int backgroundTint = 0x7f040058;
        public static final int backgroundTintMode = 0x7f040059;
        public static final int badgeGravity = 0x7f04005a;
        public static final int badgeStyle = 0x7f04005c;
        public static final int badgeTextColor = 0x7f04005d;
        public static final int barLength = 0x7f040073;
        public static final int behavior_autoHide = 0x7f040077;
        public static final int behavior_autoShrink = 0x7f040078;
        public static final int behavior_draggable = 0x7f040079;
        public static final int behavior_expandedOffset = 0x7f04007a;
        public static final int behavior_fitToContents = 0x7f04007b;
        public static final int behavior_halfExpandedRatio = 0x7f04007c;
        public static final int behavior_hideable = 0x7f04007d;
        public static final int behavior_overlapTop = 0x7f04007e;
        public static final int behavior_peekHeight = 0x7f04007f;
        public static final int behavior_saveFlags = 0x7f040080;
        public static final int behavior_skipCollapsed = 0x7f040081;
        public static final int borderWidth = 0x7f040091;
        public static final int borderlessButtonStyle = 0x7f040092;
        public static final int bottomAppBarStyle = 0x7f040093;
        public static final int bottomNavigationStyle = 0x7f040095;
        public static final int bottomSheetDialogTheme = 0x7f040096;
        public static final int bottomSheetStyle = 0x7f040097;
        public static final int boxBackgroundColor = 0x7f040098;
        public static final int boxBackgroundMode = 0x7f040099;
        public static final int boxCollapsedPaddingTop = 0x7f04009a;
        public static final int boxCornerRadiusBottomEnd = 0x7f04009b;
        public static final int boxCornerRadiusBottomStart = 0x7f04009c;
        public static final int boxCornerRadiusTopEnd = 0x7f04009d;
        public static final int boxCornerRadiusTopStart = 0x7f04009e;
        public static final int boxStrokeColor = 0x7f04009f;
        public static final int boxStrokeErrorColor = 0x7f0400a0;
        public static final int boxStrokeWidth = 0x7f0400a1;
        public static final int boxStrokeWidthFocused = 0x7f0400a2;
        public static final int buttonBarButtonStyle = 0x7f0400ad;
        public static final int buttonBarNegativeButtonStyle = 0x7f0400ae;
        public static final int buttonBarNeutralButtonStyle = 0x7f0400af;
        public static final int buttonBarPositiveButtonStyle = 0x7f0400b0;
        public static final int buttonBarStyle = 0x7f0400b1;
        public static final int buttonCompat = 0x7f0400b2;
        public static final int buttonGravity = 0x7f0400b3;
        public static final int buttonIconDimen = 0x7f0400b4;
        public static final int buttonPanelSideLayout = 0x7f0400b5;
        public static final int buttonStyle = 0x7f0400b6;
        public static final int buttonStyleSmall = 0x7f0400b7;
        public static final int buttonTint = 0x7f0400b8;
        public static final int buttonTintMode = 0x7f0400b9;
        public static final int cardBackgroundColor = 0x7f0400bb;
        public static final int cardCornerRadius = 0x7f0400bc;
        public static final int cardElevation = 0x7f0400bd;
        public static final int cardForegroundColor = 0x7f0400be;
        public static final int cardMaxElevation = 0x7f0400bf;
        public static final int cardPreventCornerOverlap = 0x7f0400c0;
        public static final int cardUseCompatPadding = 0x7f0400c1;
        public static final int cardViewStyle = 0x7f0400c2;
        public static final int checkboxStyle = 0x7f040205;
        public static final int checkedButton = 0x7f040206;
        public static final int checkedChip = 0x7f040207;
        public static final int checkedIcon = 0x7f040208;
        public static final int checkedIconEnabled = 0x7f040209;
        public static final int checkedIconTint = 0x7f04020d;
        public static final int checkedIconVisible = 0x7f04020e;
        public static final int checkedTextViewStyle = 0x7f04020f;
        public static final int chipBackgroundColor = 0x7f040210;
        public static final int chipCornerRadius = 0x7f040211;
        public static final int chipEndPadding = 0x7f040212;
        public static final int chipGroupStyle = 0x7f040213;
        public static final int chipIcon = 0x7f040214;
        public static final int chipIconEnabled = 0x7f040215;
        public static final int chipIconSize = 0x7f040216;
        public static final int chipIconTint = 0x7f040217;
        public static final int chipIconVisible = 0x7f040218;
        public static final int chipMinHeight = 0x7f040219;
        public static final int chipMinTouchTargetSize = 0x7f04021a;
        public static final int chipSpacing = 0x7f04021b;
        public static final int chipSpacingHorizontal = 0x7f04021c;
        public static final int chipSpacingVertical = 0x7f04021d;
        public static final int chipStandaloneStyle = 0x7f04021e;
        public static final int chipStartPadding = 0x7f04021f;
        public static final int chipStrokeColor = 0x7f040220;
        public static final int chipStrokeWidth = 0x7f040221;
        public static final int chipStyle = 0x7f040222;
        public static final int chipSurfaceColor = 0x7f040223;
        public static final int closeIcon = 0x7f040231;
        public static final int closeIconEnabled = 0x7f040232;
        public static final int closeIconEndPadding = 0x7f040233;
        public static final int closeIconSize = 0x7f040234;
        public static final int closeIconStartPadding = 0x7f040235;
        public static final int closeIconTint = 0x7f040236;
        public static final int closeIconVisible = 0x7f040237;
        public static final int closeItemLayout = 0x7f040238;
        public static final int collapseContentDescription = 0x7f040239;
        public static final int collapseIcon = 0x7f04023a;
        public static final int collapsedTitleGravity = 0x7f04023c;
        public static final int collapsedTitleTextAppearance = 0x7f04023d;
        public static final int color = 0x7f040244;
        public static final int colorAccent = 0x7f040245;
        public static final int colorBackgroundFloating = 0x7f040246;
        public static final int colorButtonNormal = 0x7f040247;
        public static final int colorControlActivated = 0x7f040249;
        public static final int colorControlHighlight = 0x7f04024a;
        public static final int colorControlNormal = 0x7f04024b;
        public static final int colorError = 0x7f04024c;
        public static final int colorOnBackground = 0x7f04024e;
        public static final int colorOnError = 0x7f040250;
        public static final int colorOnPrimary = 0x7f040252;
        public static final int colorOnPrimarySurface = 0x7f040254;
        public static final int colorOnSecondary = 0x7f040255;
        public static final int colorOnSurface = 0x7f040257;
        public static final int colorPrimary = 0x7f04025d;
        public static final int colorPrimaryDark = 0x7f04025f;
        public static final int colorPrimarySurface = 0x7f040261;
        public static final int colorPrimaryVariant = 0x7f040262;
        public static final int colorSecondary = 0x7f040263;
        public static final int colorSecondaryVariant = 0x7f040265;
        public static final int colorSurface = 0x7f040271;
        public static final int colorSwitchThumbNormal = 0x7f040274;
        public static final int commitIcon = 0x7f040277;
        public static final int contentDescription = 0x7f040280;
        public static final int contentInsetEnd = 0x7f040281;
        public static final int contentInsetEndWithActions = 0x7f040282;
        public static final int contentInsetLeft = 0x7f040283;
        public static final int contentInsetRight = 0x7f040284;
        public static final int contentInsetStart = 0x7f040285;
        public static final int contentInsetStartWithNavigation = 0x7f040286;
        public static final int contentPadding = 0x7f040287;
        public static final int contentPaddingBottom = 0x7f040288;
        public static final int contentPaddingLeft = 0x7f04028a;
        public static final int contentPaddingRight = 0x7f04028b;
        public static final int contentPaddingTop = 0x7f04028d;
        public static final int contentScrim = 0x7f04028e;
        public static final int controlBackground = 0x7f040290;
        public static final int coordinatorLayoutStyle = 0x7f040297;
        public static final int cornerFamily = 0x7f040298;
        public static final int cornerFamilyBottomLeft = 0x7f040299;
        public static final int cornerFamilyBottomRight = 0x7f04029a;
        public static final int cornerFamilyTopLeft = 0x7f04029b;
        public static final int cornerFamilyTopRight = 0x7f04029c;
        public static final int cornerRadius = 0x7f04029d;
        public static final int cornerSize = 0x7f04029e;
        public static final int cornerSizeBottomLeft = 0x7f04029f;
        public static final int cornerSizeBottomRight = 0x7f0402a0;
        public static final int cornerSizeTopLeft = 0x7f0402a1;
        public static final int cornerSizeTopRight = 0x7f0402a2;
        public static final int counterEnabled = 0x7f0402a3;
        public static final int counterMaxLength = 0x7f0402a4;
        public static final int counterOverflowTextAppearance = 0x7f0402a5;
        public static final int counterOverflowTextColor = 0x7f0402a6;
        public static final int counterTextAppearance = 0x7f0402a7;
        public static final int counterTextColor = 0x7f0402a8;
        public static final int customNavigationLayout = 0x7f0402b2;
        public static final int dayInvalidStyle = 0x7f0402b6;
        public static final int daySelectedStyle = 0x7f0402b7;
        public static final int dayStyle = 0x7f0402b8;
        public static final int dayTodayStyle = 0x7f0402b9;
        public static final int defaultQueryHint = 0x7f0402bb;
        public static final int dialogCornerRadius = 0x7f0402c0;
        public static final int dialogPreferredPadding = 0x7f0402c1;
        public static final int dialogTheme = 0x7f0402c2;
        public static final int displayOptions = 0x7f0402c3;
        public static final int divider = 0x7f0402c4;
        public static final int dividerHorizontal = 0x7f0402c6;
        public static final int dividerPadding = 0x7f0402c9;
        public static final int dividerVertical = 0x7f0402cb;
        public static final int drawableBottomCompat = 0x7f0402d0;
        public static final int drawableEndCompat = 0x7f0402d1;
        public static final int drawableLeftCompat = 0x7f0402d2;
        public static final int drawableRightCompat = 0x7f0402d3;
        public static final int drawableSize = 0x7f0402d4;
        public static final int drawableStartCompat = 0x7f0402d5;
        public static final int drawableTint = 0x7f0402d6;
        public static final int drawableTintMode = 0x7f0402d7;
        public static final int drawableTopCompat = 0x7f0402d8;
        public static final int drawerArrowStyle = 0x7f0402d9;
        public static final int dropDownListViewStyle = 0x7f0402dc;
        public static final int dropdownListPreferredItemHeight = 0x7f0402dd;
        public static final int editTextBackground = 0x7f0402e9;
        public static final int editTextColor = 0x7f0402ea;
        public static final int editTextStyle = 0x7f0402eb;
        public static final int elevation = 0x7f0402ec;
        public static final int elevationOverlayColor = 0x7f0402ee;
        public static final int elevationOverlayEnabled = 0x7f0402ef;
        public static final int endIconCheckable = 0x7f0402f2;
        public static final int endIconContentDescription = 0x7f0402f3;
        public static final int endIconDrawable = 0x7f0402f4;
        public static final int endIconMode = 0x7f0402f5;
        public static final int endIconTint = 0x7f0402f6;
        public static final int endIconTintMode = 0x7f0402f7;
        public static final int enforceMaterialTheme = 0x7f0402f8;
        public static final int enforceTextAppearance = 0x7f0402f9;
        public static final int ensureMinTouchTargetSize = 0x7f0402fa;
        public static final int errorContentDescription = 0x7f0402fb;
        public static final int errorEnabled = 0x7f0402fc;
        public static final int errorIconDrawable = 0x7f0402fd;
        public static final int errorIconTint = 0x7f0402fe;
        public static final int errorIconTintMode = 0x7f0402ff;
        public static final int errorTextAppearance = 0x7f040300;
        public static final int errorTextColor = 0x7f040301;
        public static final int expandActivityOverflowButtonDrawable = 0x7f040302;
        public static final int expanded = 0x7f040303;
        public static final int expandedTitleGravity = 0x7f040305;
        public static final int expandedTitleMargin = 0x7f040306;
        public static final int expandedTitleMarginBottom = 0x7f040307;
        public static final int expandedTitleMarginEnd = 0x7f040308;
        public static final int expandedTitleMarginStart = 0x7f040309;
        public static final int expandedTitleMarginTop = 0x7f04030a;
        public static final int expandedTitleTextAppearance = 0x7f04030b;
        public static final int extendMotionSpec = 0x7f04030d;
        public static final int extendedFloatingActionButtonStyle = 0x7f040310;
        public static final int fabAlignmentMode = 0x7f040314;
        public static final int fabAnimationMode = 0x7f040315;
        public static final int fabCradleMargin = 0x7f040316;
        public static final int fabCradleRoundedCornerRadius = 0x7f040317;
        public static final int fabCradleVerticalOffset = 0x7f040318;
        public static final int fabCustomSize = 0x7f040319;
        public static final int fabSize = 0x7f04031a;
        public static final int fastScrollEnabled = 0x7f04031b;
        public static final int fastScrollHorizontalThumbDrawable = 0x7f04031c;
        public static final int fastScrollHorizontalTrackDrawable = 0x7f04031d;
        public static final int fastScrollVerticalThumbDrawable = 0x7f04031e;
        public static final int fastScrollVerticalTrackDrawable = 0x7f04031f;
        public static final int firstBaselineToTopHeight = 0x7f040320;
        public static final int floatingActionButtonStyle = 0x7f040328;
        public static final int font = 0x7f04033e;
        public static final int fontFamily = 0x7f04033f;
        public static final int fontProviderAuthority = 0x7f040340;
        public static final int fontProviderCerts = 0x7f040341;
        public static final int fontProviderFetchStrategy = 0x7f040342;
        public static final int fontProviderFetchTimeout = 0x7f040343;
        public static final int fontProviderPackage = 0x7f040344;
        public static final int fontProviderQuery = 0x7f040345;
        public static final int fontStyle = 0x7f040347;
        public static final int fontVariationSettings = 0x7f040348;
        public static final int fontWeight = 0x7f040349;
        public static final int foregroundInsidePadding = 0x7f04034b;
        public static final int gapBetweenBars = 0x7f04034d;
        public static final int gestureInsetBottomIgnored = 0x7f04034e;
        public static final int goIcon = 0x7f04034f;
        public static final int haloColor = 0x7f040351;
        public static final int haloRadius = 0x7f040352;
        public static final int headerLayout = 0x7f040353;
        public static final int height = 0x7f040354;
        public static final int helperText = 0x7f040355;
        public static final int helperTextEnabled = 0x7f040356;
        public static final int helperTextTextAppearance = 0x7f040357;
        public static final int helperTextTextColor = 0x7f040358;
        public static final int hideMotionSpec = 0x7f04035a;
        public static final int hideOnContentScroll = 0x7f04035b;
        public static final int hideOnScroll = 0x7f04035c;
        public static final int hintAnimationEnabled = 0x7f04035d;
        public static final int hintEnabled = 0x7f04035e;
        public static final int hintTextAppearance = 0x7f04035f;
        public static final int hintTextColor = 0x7f040360;
        public static final int homeAsUpIndicator = 0x7f040361;
        public static final int homeLayout = 0x7f040362;
        public static final int horizontalOffset = 0x7f040363;
        public static final int hoveredFocusedTranslationZ = 0x7f040365;
        public static final int icon = 0x7f040366;
        public static final int iconEndPadding = 0x7f040367;
        public static final int iconGravity = 0x7f040368;
        public static final int iconPadding = 0x7f040369;
        public static final int iconSize = 0x7f04036a;
        public static final int iconStartPadding = 0x7f04036b;
        public static final int iconTint = 0x7f04036c;
        public static final int iconTintMode = 0x7f04036d;
        public static final int iconifiedByDefault = 0x7f04036e;
        public static final int imageButtonStyle = 0x7f040371;
        public static final int indeterminateProgressStyle = 0x7f040377;
        public static final int initialActivityCount = 0x7f040387;
        public static final int insetForeground = 0x7f040388;
        public static final int isLightTheme = 0x7f040389;
        public static final int isMaterialTheme = 0x7f04038b;
        public static final int itemBackground = 0x7f04038d;
        public static final int itemFillColor = 0x7f04038e;
        public static final int itemHorizontalPadding = 0x7f04038f;
        public static final int itemHorizontalTranslationEnabled = 0x7f040390;
        public static final int itemIconPadding = 0x7f040391;
        public static final int itemIconSize = 0x7f040392;
        public static final int itemIconTint = 0x7f040393;
        public static final int itemMaxLines = 0x7f040394;
        public static final int itemPadding = 0x7f040396;
        public static final int itemRippleColor = 0x7f040399;
        public static final int itemShapeAppearance = 0x7f04039a;
        public static final int itemShapeAppearanceOverlay = 0x7f04039b;
        public static final int itemShapeFillColor = 0x7f04039c;
        public static final int itemShapeInsetBottom = 0x7f04039d;
        public static final int itemShapeInsetEnd = 0x7f04039e;
        public static final int itemShapeInsetStart = 0x7f04039f;
        public static final int itemShapeInsetTop = 0x7f0403a0;
        public static final int itemSpacing = 0x7f0403a1;
        public static final int itemStrokeColor = 0x7f0403a2;
        public static final int itemStrokeWidth = 0x7f0403a3;
        public static final int itemTextAppearance = 0x7f0403a4;
        public static final int itemTextAppearanceActive = 0x7f0403a5;
        public static final int itemTextAppearanceInactive = 0x7f0403a6;
        public static final int itemTextColor = 0x7f0403a7;
        public static final int keylines = 0x7f0403ab;
        public static final int labelBehavior = 0x7f0403ad;
        public static final int labelStyle = 0x7f0403ae;
        public static final int labelVisibilityMode = 0x7f0403af;
        public static final int lastBaselineToBottomHeight = 0x7f0403b0;
        public static final int layout = 0x7f0403b2;
        public static final int layoutManager = 0x7f0403b5;
        public static final int layout_anchor = 0x7f0403b6;
        public static final int layout_anchorGravity = 0x7f0403b7;
        public static final int layout_behavior = 0x7f0403b8;
        public static final int layout_collapseMode = 0x7f0403b9;
        public static final int layout_collapseParallaxMultiplier = 0x7f0403ba;
        public static final int layout_dodgeInsetEdges = 0x7f0403e9;
        public static final int layout_insetEdge = 0x7f0403f3;
        public static final int layout_keyline = 0x7f0403f4;
        public static final int layout_scrollFlags = 0x7f0403f8;
        public static final int layout_scrollInterpolator = 0x7f0403f9;
        public static final int liftOnScroll = 0x7f0403fd;
        public static final int liftOnScrollTargetViewId = 0x7f0403fe;
        public static final int lineHeight = 0x7f040400;
        public static final int lineSpacing = 0x7f040401;
        public static final int listChoiceBackgroundIndicator = 0x7f040403;
        public static final int listChoiceIndicatorMultipleAnimated = 0x7f040404;
        public static final int listChoiceIndicatorSingleAnimated = 0x7f040405;
        public static final int listDividerAlertDialog = 0x7f040406;
        public static final int listItemLayout = 0x7f040407;
        public static final int listLayout = 0x7f040408;
        public static final int listMenuViewStyle = 0x7f040409;
        public static final int listPopupWindowStyle = 0x7f04040a;
        public static final int listPreferredItemHeight = 0x7f04040b;
        public static final int listPreferredItemHeightLarge = 0x7f04040c;
        public static final int listPreferredItemHeightSmall = 0x7f04040d;
        public static final int listPreferredItemPaddingEnd = 0x7f04040e;
        public static final int listPreferredItemPaddingLeft = 0x7f04040f;
        public static final int listPreferredItemPaddingRight = 0x7f040410;
        public static final int listPreferredItemPaddingStart = 0x7f040411;
        public static final int logo = 0x7f040412;
        public static final int logoDescription = 0x7f040414;
        public static final int materialAlertDialogBodyTextStyle = 0x7f04042c;
        public static final int materialAlertDialogTheme = 0x7f04042e;
        public static final int materialAlertDialogTitleIconStyle = 0x7f04042f;
        public static final int materialAlertDialogTitlePanelStyle = 0x7f040430;
        public static final int materialAlertDialogTitleTextStyle = 0x7f040431;
        public static final int materialButtonOutlinedStyle = 0x7f040432;
        public static final int materialButtonStyle = 0x7f040433;
        public static final int materialButtonToggleGroupStyle = 0x7f040434;
        public static final int materialCalendarDay = 0x7f040435;
        public static final int materialCalendarFullscreenTheme = 0x7f040437;
        public static final int materialCalendarHeaderConfirmButton = 0x7f040439;
        public static final int materialCalendarHeaderDivider = 0x7f04043a;
        public static final int materialCalendarHeaderLayout = 0x7f04043b;
        public static final int materialCalendarHeaderSelection = 0x7f04043c;
        public static final int materialCalendarHeaderTitle = 0x7f04043d;
        public static final int materialCalendarHeaderToggleButton = 0x7f04043e;
        public static final int materialCalendarStyle = 0x7f040441;
        public static final int materialCalendarTheme = 0x7f040442;
        public static final int materialCardViewStyle = 0x7f040447;
        public static final int materialThemeOverlay = 0x7f04044d;
        public static final int maxActionInlineWidth = 0x7f040452;
        public static final int maxButtonHeight = 0x7f040453;
        public static final int maxCharacterCount = 0x7f040454;
        public static final int maxImageSize = 0x7f040456;
        public static final int maxLines = 0x7f040457;
        public static final int measureWithLargestChild = 0x7f04045a;
        public static final int menu = 0x7f04045b;
        public static final int minTouchTargetSize = 0x7f040461;
        public static final int multiChoiceItemLayout = 0x7f040490;
        public static final int navigationContentDescription = 0x7f040491;
        public static final int navigationIcon = 0x7f040492;
        public static final int navigationMode = 0x7f040494;
        public static final int navigationViewStyle = 0x7f040496;
        public static final int number = 0x7f04049a;
        public static final int numericModifiers = 0x7f04049b;
        public static final int overlapAnchor = 0x7f0404a3;
        public static final int paddingBottomNoButtons = 0x7f0404a5;
        public static final int paddingBottomSystemWindowInsets = 0x7f0404a6;
        public static final int paddingEnd = 0x7f0404a7;
        public static final int paddingLeftSystemWindowInsets = 0x7f0404a8;
        public static final int paddingRightSystemWindowInsets = 0x7f0404a9;
        public static final int paddingStart = 0x7f0404aa;
        public static final int paddingTopNoTitle = 0x7f0404ab;
        public static final int panelBackground = 0x7f0404ad;
        public static final int panelMenuListTheme = 0x7f0404ae;
        public static final int panelMenuListWidth = 0x7f0404af;
        public static final int passwordToggleContentDescription = 0x7f0404b0;
        public static final int passwordToggleDrawable = 0x7f0404b1;
        public static final int passwordToggleEnabled = 0x7f0404b2;
        public static final int passwordToggleTint = 0x7f0404b3;
        public static final int passwordToggleTintMode = 0x7f0404b4;
        public static final int placeholderText = 0x7f0404bd;
        public static final int placeholderTextAppearance = 0x7f0404be;
        public static final int placeholderTextColor = 0x7f0404bf;
        public static final int popupMenuBackground = 0x7f0404c2;
        public static final int popupMenuStyle = 0x7f0404c3;
        public static final int popupTheme = 0x7f0404c4;
        public static final int popupWindowStyle = 0x7f0404c5;
        public static final int prefixText = 0x7f0404c6;
        public static final int prefixTextAppearance = 0x7f0404c7;
        public static final int prefixTextColor = 0x7f0404c8;
        public static final int preserveIconSpacing = 0x7f0404c9;
        public static final int pressedTranslationZ = 0x7f0404ca;
        public static final int progressBarPadding = 0x7f0404cb;
        public static final int progressBarStyle = 0x7f0404cc;
        public static final int queryBackground = 0x7f0404d0;
        public static final int queryHint = 0x7f0404d1;
        public static final int radioButtonStyle = 0x7f0404d3;
        public static final int rangeFillColor = 0x7f0404d4;
        public static final int ratingBarStyle = 0x7f0404d5;
        public static final int ratingBarStyleIndicator = 0x7f0404d6;
        public static final int ratingBarStyleSmall = 0x7f0404d7;
        public static final int recyclerViewStyle = 0x7f0404dc;
        public static final int reverseLayout = 0x7f0404e1;
        public static final int rippleColor = 0x7f0404e2;
        public static final int scrimAnimationDuration = 0x7f040516;
        public static final int scrimBackground = 0x7f040517;
        public static final int scrimVisibleHeightTrigger = 0x7f040518;
        public static final int searchHintIcon = 0x7f040519;
        public static final int searchIcon = 0x7f04051a;
        public static final int searchViewStyle = 0x7f04051b;
        public static final int seekBarStyle = 0x7f04051c;
        public static final int selectableItemBackground = 0x7f04051d;
        public static final int selectableItemBackgroundBorderless = 0x7f04051e;
        public static final int selectionRequired = 0x7f04051f;
        public static final int shapeAppearance = 0x7f040522;
        public static final int shapeAppearanceLargeComponent = 0x7f040523;
        public static final int shapeAppearanceMediumComponent = 0x7f040524;
        public static final int shapeAppearanceOverlay = 0x7f040525;
        public static final int shapeAppearanceSmallComponent = 0x7f040526;
        public static final int showAsAction = 0x7f040529;
        public static final int showDividers = 0x7f04052b;
        public static final int showMotionSpec = 0x7f04052c;
        public static final int showText = 0x7f04052e;
        public static final int showTitle = 0x7f04052f;
        public static final int shrinkMotionSpec = 0x7f040530;
        public static final int singleChoiceItemLayout = 0x7f040533;
        public static final int singleLine = 0x7f040534;
        public static final int singleSelection = 0x7f040535;
        public static final int sliderStyle = 0x7f04053e;
        public static final int snackbarButtonStyle = 0x7f040556;
        public static final int snackbarStyle = 0x7f040557;
        public static final int snackbarTextViewStyle = 0x7f040558;
        public static final int spanCount = 0x7f040559;
        public static final int spinBars = 0x7f04055a;
        public static final int spinnerDropDownItemStyle = 0x7f04055b;
        public static final int spinnerStyle = 0x7f04055c;
        public static final int splitTrack = 0x7f04055d;
        public static final int srcCompat = 0x7f040563;
        public static final int stackFromEnd = 0x7f040596;
        public static final int startIconCheckable = 0x7f040598;
        public static final int startIconContentDescription = 0x7f040599;
        public static final int startIconDrawable = 0x7f04059a;
        public static final int startIconTint = 0x7f04059b;
        public static final int startIconTintMode = 0x7f04059c;
        public static final int state_above_anchor = 0x7f04059d;
        public static final int state_collapsed = 0x7f04059e;
        public static final int state_collapsible = 0x7f04059f;
        public static final int state_dragged = 0x7f0405a0;
        public static final int state_liftable = 0x7f0405a1;
        public static final int state_lifted = 0x7f0405a2;
        public static final int statusBarBackground = 0x7f0405a3;
        public static final int statusBarForeground = 0x7f0405a4;
        public static final int statusBarScrim = 0x7f0405a5;
        public static final int strokeColor = 0x7f0405a6;
        public static final int strokeWidth = 0x7f0405a7;
        public static final int subMenuArrow = 0x7f0405a8;
        public static final int submitBackground = 0x7f0405ad;
        public static final int subtitle = 0x7f0405ae;
        public static final int subtitleTextAppearance = 0x7f0405b0;
        public static final int subtitleTextColor = 0x7f0405b1;
        public static final int subtitleTextStyle = 0x7f0405b2;
        public static final int suffixText = 0x7f0405b3;
        public static final int suffixTextAppearance = 0x7f0405b4;
        public static final int suffixTextColor = 0x7f0405b5;
        public static final int suggestionRowLayout = 0x7f0405b6;
        public static final int switchMinWidth = 0x7f0405d0;
        public static final int switchPadding = 0x7f0405d1;
        public static final int switchStyle = 0x7f0405d2;
        public static final int switchTextAppearance = 0x7f0405d3;
        public static final int tabBackground = 0x7f0405d4;
        public static final int tabContentStart = 0x7f0405d5;
        public static final int tabGravity = 0x7f0405d6;
        public static final int tabIconTint = 0x7f0405d7;
        public static final int tabIconTintMode = 0x7f0405d8;
        public static final int tabIndicator = 0x7f0405d9;
        public static final int tabIndicatorAnimationDuration = 0x7f0405da;
        public static final int tabIndicatorColor = 0x7f0405dc;
        public static final int tabIndicatorFullWidth = 0x7f0405dd;
        public static final int tabIndicatorGravity = 0x7f0405de;
        public static final int tabIndicatorHeight = 0x7f0405df;
        public static final int tabInlineLabel = 0x7f0405e0;
        public static final int tabMaxWidth = 0x7f0405e1;
        public static final int tabMinWidth = 0x7f0405e2;
        public static final int tabMode = 0x7f0405e3;
        public static final int tabPadding = 0x7f0405e4;
        public static final int tabPaddingBottom = 0x7f0405e5;
        public static final int tabPaddingEnd = 0x7f0405e6;
        public static final int tabPaddingStart = 0x7f0405e7;
        public static final int tabPaddingTop = 0x7f0405e8;
        public static final int tabRippleColor = 0x7f0405e9;
        public static final int tabSelectedTextColor = 0x7f0405eb;
        public static final int tabStyle = 0x7f0405ec;
        public static final int tabTextAppearance = 0x7f0405ed;
        public static final int tabTextColor = 0x7f0405ee;
        public static final int tabUnboundedRipple = 0x7f0405ef;
        public static final int textAllCaps = 0x7f040604;
        public static final int textAppearanceBody1 = 0x7f040605;
        public static final int textAppearanceBody2 = 0x7f040606;
        public static final int textAppearanceButton = 0x7f04060a;
        public static final int textAppearanceCaption = 0x7f04060b;
        public static final int textAppearanceHeadline1 = 0x7f04060f;
        public static final int textAppearanceHeadline2 = 0x7f040610;
        public static final int textAppearanceHeadline3 = 0x7f040611;
        public static final int textAppearanceHeadline4 = 0x7f040612;
        public static final int textAppearanceHeadline5 = 0x7f040613;
        public static final int textAppearanceHeadline6 = 0x7f040614;
        public static final int textAppearanceLargePopupMenu = 0x7f04061b;
        public static final int textAppearanceLineHeightEnabled = 0x7f04061c;
        public static final int textAppearanceListItem = 0x7f04061d;
        public static final int textAppearanceListItemSecondary = 0x7f04061e;
        public static final int textAppearanceListItemSmall = 0x7f04061f;
        public static final int textAppearanceOverline = 0x7f040620;
        public static final int textAppearancePopupMenuHeader = 0x7f040621;
        public static final int textAppearanceSearchResultSubtitle = 0x7f040622;
        public static final int textAppearanceSearchResultTitle = 0x7f040623;
        public static final int textAppearanceSmallPopupMenu = 0x7f040624;
        public static final int textAppearanceSubtitle1 = 0x7f040625;
        public static final int textAppearanceSubtitle2 = 0x7f040626;
        public static final int textColorAlertDialogListItem = 0x7f04062f;
        public static final int textColorSearchUrl = 0x7f040630;
        public static final int textEndPadding = 0x7f040631;
        public static final int textInputLayoutFocusedRectEnabled = 0x7f040636;
        public static final int textInputStyle = 0x7f04063a;
        public static final int textLocale = 0x7f04063b;
        public static final int textStartPadding = 0x7f040641;
        public static final int theme = 0x7f040646;
        public static final int themeLineHeight = 0x7f040647;
        public static final int thickness = 0x7f040648;
        public static final int thumbColor = 0x7f040649;
        public static final int thumbElevation = 0x7f04064a;
        public static final int thumbRadius = 0x7f04064b;
        public static final int thumbTextPadding = 0x7f04064e;
        public static final int thumbTint = 0x7f04064f;
        public static final int thumbTintMode = 0x7f040650;
        public static final int tickColor = 0x7f040651;
        public static final int tickColorActive = 0x7f040652;
        public static final int tickColorInactive = 0x7f040653;
        public static final int tickMark = 0x7f040654;
        public static final int tickMarkTint = 0x7f040655;
        public static final int tickMarkTintMode = 0x7f040656;
        public static final int tint = 0x7f040658;
        public static final int tintMode = 0x7f040659;
        public static final int title = 0x7f04066f;
        public static final int titleEnabled = 0x7f040672;
        public static final int titleMargin = 0x7f040673;
        public static final int titleMarginBottom = 0x7f040674;
        public static final int titleMarginEnd = 0x7f040675;
        public static final int titleMarginStart = 0x7f040676;
        public static final int titleMarginTop = 0x7f040677;
        public static final int titleMargins = 0x7f040678;
        public static final int titleTextAppearance = 0x7f04067a;
        public static final int titleTextColor = 0x7f04067b;
        public static final int titleTextStyle = 0x7f04067c;
        public static final int toolbarId = 0x7f04067d;
        public static final int toolbarNavigationButtonStyle = 0x7f04067e;
        public static final int toolbarStyle = 0x7f04067f;
        public static final int tooltipForegroundColor = 0x7f040681;
        public static final int tooltipFrameBackground = 0x7f040682;
        public static final int tooltipStyle = 0x7f040683;
        public static final int tooltipText = 0x7f040684;
        public static final int track = 0x7f040689;
        public static final int trackColor = 0x7f04068a;
        public static final int trackColorActive = 0x7f04068b;
        public static final int trackColorInactive = 0x7f04068c;
        public static final int trackHeight = 0x7f04068e;
        public static final int trackTint = 0x7f040690;
        public static final int trackTintMode = 0x7f040691;
        public static final int transitionShapeAppearance = 0x7f040697;
        public static final int ttcIndex = 0x7f04069b;
        public static final int useCompatPadding = 0x7f04069d;
        public static final int useMaterialThemeColors = 0x7f04069e;
        public static final int values = 0x7f04069f;
        public static final int verticalOffset = 0x7f0406ac;
        public static final int viewInflaterClass = 0x7f0406ae;
        public static final int voiceIcon = 0x7f0406b4;
        public static final int windowActionBar = 0x7f0406bf;
        public static final int windowActionBarOverlay = 0x7f0406c0;
        public static final int windowActionModeOverlay = 0x7f0406c1;
        public static final int windowFixedHeightMajor = 0x7f0406c2;
        public static final int windowFixedHeightMinor = 0x7f0406c3;
        public static final int windowFixedWidthMajor = 0x7f0406c4;
        public static final int windowFixedWidthMinor = 0x7f0406c5;
        public static final int windowMinWidthMajor = 0x7f0406c6;
        public static final int windowMinWidthMinor = 0x7f0406c7;
        public static final int windowNoTitle = 0x7f0406c8;
        public static final int yearSelectedStyle = 0x7f0406c9;
        public static final int yearStyle = 0x7f0406ca;
        public static final int yearTodayStyle = 0x7f0406cb;

        private attr() {
        }
    }

    public static final class bool {
        public static final int abc_action_bar_embed_tabs = 0x7f050000;
        public static final int abc_config_actionMenuItemAllCaps = 0x7f050001;
        public static final int mtrl_btn_textappearance_all_caps = 0x7f050002;

        private bool() {
        }
    }

    public static final class color {
        public static final int abc_background_cache_hint_selector_material_dark = 0x7f060000;
        public static final int abc_background_cache_hint_selector_material_light = 0x7f060001;
        public static final int abc_btn_colored_borderless_text_material = 0x7f060002;
        public static final int abc_btn_colored_text_material = 0x7f060003;
        public static final int abc_color_highlight_material = 0x7f060004;
        public static final int abc_decor_view_status_guard = 0x7f060005;
        public static final int abc_decor_view_status_guard_light = 0x7f060006;
        public static final int abc_hint_foreground_material_dark = 0x7f060007;
        public static final int abc_hint_foreground_material_light = 0x7f060008;
        public static final int abc_primary_text_disable_only_material_dark = 0x7f060009;
        public static final int abc_primary_text_disable_only_material_light = 0x7f06000a;
        public static final int abc_primary_text_material_dark = 0x7f06000b;
        public static final int abc_primary_text_material_light = 0x7f06000c;
        public static final int abc_search_url_text = 0x7f06000d;
        public static final int abc_search_url_text_normal = 0x7f06000e;
        public static final int abc_search_url_text_pressed = 0x7f06000f;
        public static final int abc_search_url_text_selected = 0x7f060010;
        public static final int abc_secondary_text_material_dark = 0x7f060011;
        public static final int abc_secondary_text_material_light = 0x7f060012;
        public static final int abc_tint_btn_checkable = 0x7f060013;
        public static final int abc_tint_default = 0x7f060014;
        public static final int abc_tint_edittext = 0x7f060015;
        public static final int abc_tint_seek_thumb = 0x7f060016;
        public static final int abc_tint_spinner = 0x7f060017;
        public static final int abc_tint_switch_track = 0x7f060018;
        public static final int accent_material_dark = 0x7f060019;
        public static final int accent_material_light = 0x7f06001a;
        public static final int androidx_core_ripple_material_light = 0x7f06001b;
        public static final int androidx_core_secondary_text_default_material_light = 0x7f06001c;
        public static final int background_floating_material_dark = 0x7f06001d;
        public static final int background_floating_material_light = 0x7f06001e;
        public static final int background_material_dark = 0x7f06001f;
        public static final int background_material_light = 0x7f060020;
        public static final int bright_foreground_disabled_material_dark = 0x7f060027;
        public static final int bright_foreground_disabled_material_light = 0x7f060028;
        public static final int bright_foreground_inverse_material_dark = 0x7f060029;
        public static final int bright_foreground_inverse_material_light = 0x7f06002a;
        public static final int bright_foreground_material_dark = 0x7f06002b;
        public static final int bright_foreground_material_light = 0x7f06002c;
        public static final int button_material_dark = 0x7f06002d;
        public static final int button_material_light = 0x7f06002e;
        public static final int cardview_dark_background = 0x7f06002f;
        public static final int cardview_light_background = 0x7f060030;
        public static final int cardview_shadow_end_color = 0x7f060031;
        public static final int cardview_shadow_start_color = 0x7f060032;
        public static final int checkbox_themeable_attribute_color = 0x7f0600db;
        public static final int design_bottom_navigation_shadow_color = 0x7f0600e1;
        public static final int design_box_stroke_color = 0x7f0600e2;
        public static final int design_dark_default_color_background = 0x7f0600e3;
        public static final int design_dark_default_color_error = 0x7f0600e4;
        public static final int design_dark_default_color_on_background = 0x7f0600e5;
        public static final int design_dark_default_color_on_error = 0x7f0600e6;
        public static final int design_dark_default_color_on_primary = 0x7f0600e7;
        public static final int design_dark_default_color_on_secondary = 0x7f0600e8;
        public static final int design_dark_default_color_on_surface = 0x7f0600e9;
        public static final int design_dark_default_color_primary = 0x7f0600ea;
        public static final int design_dark_default_color_primary_dark = 0x7f0600eb;
        public static final int design_dark_default_color_primary_variant = 0x7f0600ec;
        public static final int design_dark_default_color_secondary = 0x7f0600ed;
        public static final int design_dark_default_color_secondary_variant = 0x7f0600ee;
        public static final int design_dark_default_color_surface = 0x7f0600ef;
        public static final int design_default_color_background = 0x7f0600f0;
        public static final int design_default_color_error = 0x7f0600f1;
        public static final int design_default_color_on_background = 0x7f0600f2;
        public static final int design_default_color_on_error = 0x7f0600f3;
        public static final int design_default_color_on_primary = 0x7f0600f4;
        public static final int design_default_color_on_secondary = 0x7f0600f5;
        public static final int design_default_color_on_surface = 0x7f0600f6;
        public static final int design_default_color_primary = 0x7f0600f7;
        public static final int design_default_color_primary_dark = 0x7f0600f8;
        public static final int design_default_color_primary_variant = 0x7f0600f9;
        public static final int design_default_color_secondary = 0x7f0600fa;
        public static final int design_default_color_secondary_variant = 0x7f0600fb;
        public static final int design_default_color_surface = 0x7f0600fc;
        public static final int design_error = 0x7f0600fd;
        public static final int design_fab_shadow_end_color = 0x7f0600fe;
        public static final int design_fab_shadow_mid_color = 0x7f0600ff;
        public static final int design_fab_shadow_start_color = 0x7f060100;
        public static final int design_fab_stroke_end_inner_color = 0x7f060101;
        public static final int design_fab_stroke_end_outer_color = 0x7f060102;
        public static final int design_fab_stroke_top_inner_color = 0x7f060103;
        public static final int design_fab_stroke_top_outer_color = 0x7f060104;
        public static final int design_icon_tint = 0x7f060105;
        public static final int design_snackbar_background_color = 0x7f060106;
        public static final int dim_foreground_disabled_material_dark = 0x7f060132;
        public static final int dim_foreground_disabled_material_light = 0x7f060133;
        public static final int dim_foreground_material_dark = 0x7f060134;
        public static final int dim_foreground_material_light = 0x7f060135;
        public static final int error_color_material_dark = 0x7f060136;
        public static final int error_color_material_light = 0x7f060137;
        public static final int foreground_material_dark = 0x7f060138;
        public static final int foreground_material_light = 0x7f060139;
        public static final int highlighted_text_material_dark = 0x7f060151;
        public static final int highlighted_text_material_light = 0x7f060152;
        public static final int material_blue_grey_800 = 0x7f060294;
        public static final int material_blue_grey_900 = 0x7f060295;
        public static final int material_blue_grey_950 = 0x7f060296;
        public static final int material_deep_teal_200 = 0x7f060298;
        public static final int material_deep_teal_500 = 0x7f060299;
        public static final int material_grey_100 = 0x7f0602dc;
        public static final int material_grey_300 = 0x7f0602dd;
        public static final int material_grey_50 = 0x7f0602de;
        public static final int material_grey_600 = 0x7f0602df;
        public static final int material_grey_800 = 0x7f0602e0;
        public static final int material_grey_850 = 0x7f0602e1;
        public static final int material_grey_900 = 0x7f0602e2;
        public static final int material_on_background_disabled = 0x7f0602e7;
        public static final int material_on_background_emphasis_high_type = 0x7f0602e8;
        public static final int material_on_background_emphasis_medium = 0x7f0602e9;
        public static final int material_on_primary_disabled = 0x7f0602ea;
        public static final int material_on_primary_emphasis_high_type = 0x7f0602eb;
        public static final int material_on_primary_emphasis_medium = 0x7f0602ec;
        public static final int material_on_surface_disabled = 0x7f0602ed;
        public static final int material_on_surface_emphasis_high_type = 0x7f0602ee;
        public static final int material_on_surface_emphasis_medium = 0x7f0602ef;
        public static final int material_on_surface_stroke = 0x7f0602f0;
        public static final int material_slider_active_tick_marks_color = 0x7f0602f1;
        public static final int material_slider_active_track_color = 0x7f0602f2;
        public static final int material_slider_halo_color = 0x7f0602f3;
        public static final int material_slider_inactive_tick_marks_color = 0x7f0602f4;
        public static final int material_slider_inactive_track_color = 0x7f0602f5;
        public static final int material_slider_thumb_color = 0x7f0602f6;
        public static final int mtrl_btn_bg_color_selector = 0x7f0602fc;
        public static final int mtrl_btn_ripple_color = 0x7f0602fd;
        public static final int mtrl_btn_stroke_color_selector = 0x7f0602fe;
        public static final int mtrl_btn_text_btn_bg_color_selector = 0x7f0602ff;
        public static final int mtrl_btn_text_btn_ripple_color = 0x7f060300;
        public static final int mtrl_btn_text_color_disabled = 0x7f060301;
        public static final int mtrl_btn_text_color_selector = 0x7f060302;
        public static final int mtrl_btn_transparent_bg_color = 0x7f060303;
        public static final int mtrl_calendar_item_stroke_color = 0x7f060304;
        public static final int mtrl_calendar_selected_range = 0x7f060305;
        public static final int mtrl_card_view_foreground = 0x7f060306;
        public static final int mtrl_card_view_ripple = 0x7f060307;
        public static final int mtrl_chip_background_color = 0x7f060308;
        public static final int mtrl_chip_close_icon_tint = 0x7f060309;
        public static final int mtrl_chip_surface_color = 0x7f06030a;
        public static final int mtrl_chip_text_color = 0x7f06030b;
        public static final int mtrl_choice_chip_background_color = 0x7f06030c;
        public static final int mtrl_choice_chip_ripple_color = 0x7f06030d;
        public static final int mtrl_choice_chip_text_color = 0x7f06030e;
        public static final int mtrl_error = 0x7f06030f;
        public static final int mtrl_fab_bg_color_selector = 0x7f060310;
        public static final int mtrl_fab_icon_text_color_selector = 0x7f060311;
        public static final int mtrl_fab_ripple_color = 0x7f060312;
        public static final int mtrl_filled_background_color = 0x7f060313;
        public static final int mtrl_filled_icon_tint = 0x7f060314;
        public static final int mtrl_filled_stroke_color = 0x7f060315;
        public static final int mtrl_indicator_text_color = 0x7f060316;
        public static final int mtrl_navigation_item_background_color = 0x7f06031b;
        public static final int mtrl_navigation_item_icon_tint = 0x7f06031c;
        public static final int mtrl_navigation_item_text_color = 0x7f06031d;
        public static final int mtrl_on_primary_text_btn_text_color_selector = 0x7f06031e;
        public static final int mtrl_outlined_icon_tint = 0x7f060320;
        public static final int mtrl_outlined_stroke_color = 0x7f060321;
        public static final int mtrl_popupmenu_overlay_color = 0x7f060322;
        public static final int mtrl_scrim_color = 0x7f060323;
        public static final int mtrl_tabs_colored_ripple_color = 0x7f060324;
        public static final int mtrl_tabs_icon_color_selector = 0x7f060325;
        public static final int mtrl_tabs_icon_color_selector_colored = 0x7f060326;
        public static final int mtrl_tabs_legacy_text_color_selector = 0x7f060327;
        public static final int mtrl_tabs_ripple_color = 0x7f060328;
        public static final int mtrl_text_btn_text_color_selector = 0x7f060329;
        public static final int mtrl_textinput_default_box_stroke_color = 0x7f06032a;
        public static final int mtrl_textinput_disabled_color = 0x7f06032b;
        public static final int mtrl_textinput_filled_box_default_background_color = 0x7f06032c;
        public static final int mtrl_textinput_focused_box_stroke_color = 0x7f06032d;
        public static final int mtrl_textinput_hovered_box_stroke_color = 0x7f06032e;
        public static final int notification_action_color_filter = 0x7f06032f;
        public static final int notification_icon_bg_color = 0x7f060330;
        public static final int primary_dark_material_dark = 0x7f060334;
        public static final int primary_dark_material_light = 0x7f060335;
        public static final int primary_material_dark = 0x7f060336;
        public static final int primary_material_light = 0x7f060337;
        public static final int primary_text_default_material_dark = 0x7f060338;
        public static final int primary_text_default_material_light = 0x7f060339;
        public static final int primary_text_disabled_material_dark = 0x7f06033a;
        public static final int primary_text_disabled_material_light = 0x7f06033b;
        public static final int radiobutton_themeable_attribute_color = 0x7f060341;
        public static final int ripple_material_dark = 0x7f060342;
        public static final int ripple_material_light = 0x7f060343;
        public static final int secondary_text_default_material_dark = 0x7f060344;
        public static final int secondary_text_default_material_light = 0x7f060345;
        public static final int secondary_text_disabled_material_dark = 0x7f060346;
        public static final int secondary_text_disabled_material_light = 0x7f060347;
        public static final int switch_thumb_disabled_material_dark = 0x7f060348;
        public static final int switch_thumb_disabled_material_light = 0x7f060349;
        public static final int switch_thumb_material_dark = 0x7f06034a;
        public static final int switch_thumb_material_light = 0x7f06034b;
        public static final int switch_thumb_normal_material_dark = 0x7f06034c;
        public static final int switch_thumb_normal_material_light = 0x7f06034d;
        public static final int test_mtrl_calendar_day = 0x7f060351;
        public static final int test_mtrl_calendar_day_selected = 0x7f060352;
        public static final int tooltip_background_dark = 0x7f060354;
        public static final int tooltip_background_light = 0x7f060355;

        private color() {
        }
    }

    public static final class dimen {
        public static final int abc_action_bar_content_inset_material = 0x7f070000;
        public static final int abc_action_bar_content_inset_with_nav = 0x7f070001;
        public static final int abc_action_bar_default_height_material = 0x7f070002;
        public static final int abc_action_bar_default_padding_end_material = 0x7f070003;
        public static final int abc_action_bar_default_padding_start_material = 0x7f070004;
        public static final int abc_action_bar_elevation_material = 0x7f070005;
        public static final int abc_action_bar_icon_vertical_padding_material = 0x7f070006;
        public static final int abc_action_bar_overflow_padding_end_material = 0x7f070007;
        public static final int abc_action_bar_overflow_padding_start_material = 0x7f070008;
        public static final int abc_action_bar_stacked_max_height = 0x7f070009;
        public static final int abc_action_bar_stacked_tab_max_width = 0x7f07000a;
        public static final int abc_action_bar_subtitle_bottom_margin_material = 0x7f07000b;
        public static final int abc_action_bar_subtitle_top_margin_material = 0x7f07000c;
        public static final int abc_action_button_min_height_material = 0x7f07000d;
        public static final int abc_action_button_min_width_material = 0x7f07000e;
        public static final int abc_action_button_min_width_overflow_material = 0x7f07000f;
        public static final int abc_alert_dialog_button_bar_height = 0x7f070010;
        public static final int abc_alert_dialog_button_dimen = 0x7f070011;
        public static final int abc_button_inset_horizontal_material = 0x7f070012;
        public static final int abc_button_inset_vertical_material = 0x7f070013;
        public static final int abc_button_padding_horizontal_material = 0x7f070014;
        public static final int abc_button_padding_vertical_material = 0x7f070015;
        public static final int abc_cascading_menus_min_smallest_width = 0x7f070016;
        public static final int abc_config_prefDialogWidth = 0x7f070017;
        public static final int abc_control_corner_material = 0x7f070018;
        public static final int abc_control_inset_material = 0x7f070019;
        public static final int abc_control_padding_material = 0x7f07001a;
        public static final int abc_dialog_corner_radius_material = 0x7f07001b;
        public static final int abc_dialog_fixed_height_major = 0x7f07001c;
        public static final int abc_dialog_fixed_height_minor = 0x7f07001d;
        public static final int abc_dialog_fixed_width_major = 0x7f07001e;
        public static final int abc_dialog_fixed_width_minor = 0x7f07001f;
        public static final int abc_dialog_list_padding_bottom_no_buttons = 0x7f070020;
        public static final int abc_dialog_list_padding_top_no_title = 0x7f070021;
        public static final int abc_dialog_min_width_major = 0x7f070022;
        public static final int abc_dialog_min_width_minor = 0x7f070023;
        public static final int abc_dialog_padding_material = 0x7f070024;
        public static final int abc_dialog_padding_top_material = 0x7f070025;
        public static final int abc_dialog_title_divider_material = 0x7f070026;
        public static final int abc_disabled_alpha_material_dark = 0x7f070027;
        public static final int abc_disabled_alpha_material_light = 0x7f070028;
        public static final int abc_dropdownitem_icon_width = 0x7f070029;
        public static final int abc_dropdownitem_text_padding_left = 0x7f07002a;
        public static final int abc_dropdownitem_text_padding_right = 0x7f07002b;
        public static final int abc_edit_text_inset_bottom_material = 0x7f07002c;
        public static final int abc_edit_text_inset_horizontal_material = 0x7f07002d;
        public static final int abc_edit_text_inset_top_material = 0x7f07002e;
        public static final int abc_floating_window_z = 0x7f07002f;
        public static final int abc_list_item_height_large_material = 0x7f070030;
        public static final int abc_list_item_height_material = 0x7f070031;
        public static final int abc_list_item_height_small_material = 0x7f070032;
        public static final int abc_list_item_padding_horizontal_material = 0x7f070033;
        public static final int abc_panel_menu_list_width = 0x7f070034;
        public static final int abc_progress_bar_height_material = 0x7f070035;
        public static final int abc_search_view_preferred_height = 0x7f070036;
        public static final int abc_search_view_preferred_width = 0x7f070037;
        public static final int abc_seekbar_track_background_height_material = 0x7f070038;
        public static final int abc_seekbar_track_progress_height_material = 0x7f070039;
        public static final int abc_select_dialog_padding_start_material = 0x7f07003a;
        public static final int abc_switch_padding = 0x7f07003e;
        public static final int abc_text_size_body_1_material = 0x7f07003f;
        public static final int abc_text_size_body_2_material = 0x7f070040;
        public static final int abc_text_size_button_material = 0x7f070041;
        public static final int abc_text_size_caption_material = 0x7f070042;
        public static final int abc_text_size_display_1_material = 0x7f070043;
        public static final int abc_text_size_display_2_material = 0x7f070044;
        public static final int abc_text_size_display_3_material = 0x7f070045;
        public static final int abc_text_size_display_4_material = 0x7f070046;
        public static final int abc_text_size_headline_material = 0x7f070047;
        public static final int abc_text_size_large_material = 0x7f070048;
        public static final int abc_text_size_medium_material = 0x7f070049;
        public static final int abc_text_size_menu_header_material = 0x7f07004a;
        public static final int abc_text_size_menu_material = 0x7f07004b;
        public static final int abc_text_size_small_material = 0x7f07004c;
        public static final int abc_text_size_subhead_material = 0x7f07004d;
        public static final int abc_text_size_subtitle_material_toolbar = 0x7f07004e;
        public static final int abc_text_size_title_material = 0x7f07004f;
        public static final int abc_text_size_title_material_toolbar = 0x7f070050;
        public static final int action_bar_size = 0x7f070051;
        public static final int appcompat_dialog_background_inset = 0x7f070052;
        public static final int cardview_compat_inset_shadow = 0x7f070053;
        public static final int cardview_default_elevation = 0x7f070054;
        public static final int cardview_default_radius = 0x7f070055;
        public static final int compat_button_inset_horizontal_material = 0x7f070075;
        public static final int compat_button_inset_vertical_material = 0x7f070076;
        public static final int compat_button_padding_horizontal_material = 0x7f070077;
        public static final int compat_button_padding_vertical_material = 0x7f070078;
        public static final int compat_control_corner_material = 0x7f070079;
        public static final int compat_notification_large_icon_max_height = 0x7f07007a;
        public static final int compat_notification_large_icon_max_width = 0x7f07007b;
        public static final int default_dimension = 0x7f07007d;
        public static final int design_appbar_elevation = 0x7f07007e;
        public static final int design_bottom_navigation_active_item_max_width = 0x7f07007f;
        public static final int design_bottom_navigation_active_item_min_width = 0x7f070080;
        public static final int design_bottom_navigation_active_text_size = 0x7f070081;
        public static final int design_bottom_navigation_elevation = 0x7f070082;
        public static final int design_bottom_navigation_height = 0x7f070083;
        public static final int design_bottom_navigation_icon_size = 0x7f070084;
        public static final int design_bottom_navigation_item_max_width = 0x7f070085;
        public static final int design_bottom_navigation_item_min_width = 0x7f070086;
        public static final int design_bottom_navigation_margin = 0x7f070088;
        public static final int design_bottom_navigation_shadow_height = 0x7f070089;
        public static final int design_bottom_navigation_text_size = 0x7f07008a;
        public static final int design_bottom_sheet_elevation = 0x7f07008b;
        public static final int design_bottom_sheet_modal_elevation = 0x7f07008c;
        public static final int design_bottom_sheet_peek_height_min = 0x7f07008d;
        public static final int design_fab_border_width = 0x7f07008e;
        public static final int design_fab_elevation = 0x7f07008f;
        public static final int design_fab_image_size = 0x7f070090;
        public static final int design_fab_size_mini = 0x7f070091;
        public static final int design_fab_size_normal = 0x7f070092;
        public static final int design_fab_translation_z_hovered_focused = 0x7f070093;
        public static final int design_fab_translation_z_pressed = 0x7f070094;
        public static final int design_navigation_elevation = 0x7f070095;
        public static final int design_navigation_icon_padding = 0x7f070096;
        public static final int design_navigation_icon_size = 0x7f070097;
        public static final int design_navigation_item_horizontal_padding = 0x7f070098;
        public static final int design_navigation_item_icon_padding = 0x7f070099;
        public static final int design_navigation_max_width = 0x7f07009b;
        public static final int design_navigation_padding_bottom = 0x7f07009c;
        public static final int design_navigation_separator_vertical_padding = 0x7f07009d;
        public static final int design_snackbar_action_inline_max_width = 0x7f07009e;
        public static final int design_snackbar_action_text_color_alpha = 0x7f07009f;
        public static final int design_snackbar_background_corner_radius = 0x7f0700a0;
        public static final int design_snackbar_elevation = 0x7f0700a1;
        public static final int design_snackbar_extra_spacing_horizontal = 0x7f0700a2;
        public static final int design_snackbar_max_width = 0x7f0700a3;
        public static final int design_snackbar_min_width = 0x7f0700a4;
        public static final int design_snackbar_padding_horizontal = 0x7f0700a5;
        public static final int design_snackbar_padding_vertical = 0x7f0700a6;
        public static final int design_snackbar_padding_vertical_2lines = 0x7f0700a7;
        public static final int design_snackbar_text_size = 0x7f0700a8;
        public static final int design_tab_max_width = 0x7f0700a9;
        public static final int design_tab_scrollable_min_width = 0x7f0700aa;
        public static final int design_tab_text_size = 0x7f0700ab;
        public static final int design_tab_text_size_2line = 0x7f0700ac;
        public static final int design_textinput_caption_translate_y = 0x7f0700ad;
        public static final int disabled_alpha_material_dark = 0x7f0700b8;
        public static final int disabled_alpha_material_light = 0x7f0700b9;
        public static final int fastscroll_default_thickness = 0x7f0700ba;
        public static final int fastscroll_margin = 0x7f0700bb;
        public static final int fastscroll_minimum_range = 0x7f0700bc;
        public static final int highlight_alpha_material_colored = 0x7f0700bd;
        public static final int highlight_alpha_material_dark = 0x7f0700be;
        public static final int highlight_alpha_material_light = 0x7f0700bf;
        public static final int hint_alpha_material_dark = 0x7f0700c0;
        public static final int hint_alpha_material_light = 0x7f0700c1;
        public static final int hint_pressed_alpha_material_dark = 0x7f0700c2;
        public static final int hint_pressed_alpha_material_light = 0x7f0700c3;
        public static final int item_touch_helper_max_drag_scroll_per_frame = 0x7f0700c4;
        public static final int item_touch_helper_swipe_escape_max_velocity = 0x7f0700c5;
        public static final int item_touch_helper_swipe_escape_velocity = 0x7f0700c6;
        public static final int material_emphasis_disabled = 0x7f070176;
        public static final int material_emphasis_high_type = 0x7f070178;
        public static final int material_emphasis_medium = 0x7f070179;
        public static final int material_text_view_test_line_height = 0x7f070186;
        public static final int material_text_view_test_line_height_override = 0x7f070187;
        public static final int mtrl_alert_dialog_background_inset_bottom = 0x7f07018e;
        public static final int mtrl_alert_dialog_background_inset_end = 0x7f07018f;
        public static final int mtrl_alert_dialog_background_inset_start = 0x7f070190;
        public static final int mtrl_alert_dialog_background_inset_top = 0x7f070191;
        public static final int mtrl_alert_dialog_picker_background_inset = 0x7f070192;
        public static final int mtrl_badge_horizontal_edge_offset = 0x7f070193;
        public static final int mtrl_badge_long_text_horizontal_padding = 0x7f070194;
        public static final int mtrl_badge_radius = 0x7f070195;
        public static final int mtrl_badge_text_horizontal_edge_offset = 0x7f070196;
        public static final int mtrl_badge_text_size = 0x7f070197;
        public static final int mtrl_badge_with_text_radius = 0x7f07019a;
        public static final int mtrl_bottomappbar_fabOffsetEndMode = 0x7f07019b;
        public static final int mtrl_bottomappbar_fab_bottom_margin = 0x7f07019c;
        public static final int mtrl_bottomappbar_fab_cradle_margin = 0x7f07019d;
        public static final int mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f07019e;
        public static final int mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f07019f;
        public static final int mtrl_bottomappbar_height = 0x7f0701a0;
        public static final int mtrl_btn_corner_radius = 0x7f0701a1;
        public static final int mtrl_btn_dialog_btn_min_width = 0x7f0701a2;
        public static final int mtrl_btn_disabled_elevation = 0x7f0701a3;
        public static final int mtrl_btn_disabled_z = 0x7f0701a4;
        public static final int mtrl_btn_elevation = 0x7f0701a5;
        public static final int mtrl_btn_focused_z = 0x7f0701a6;
        public static final int mtrl_btn_hovered_z = 0x7f0701a7;
        public static final int mtrl_btn_icon_btn_padding_left = 0x7f0701a8;
        public static final int mtrl_btn_icon_padding = 0x7f0701a9;
        public static final int mtrl_btn_inset = 0x7f0701aa;
        public static final int mtrl_btn_letter_spacing = 0x7f0701ab;
        public static final int mtrl_btn_padding_bottom = 0x7f0701ad;
        public static final int mtrl_btn_padding_left = 0x7f0701ae;
        public static final int mtrl_btn_padding_right = 0x7f0701af;
        public static final int mtrl_btn_padding_top = 0x7f0701b0;
        public static final int mtrl_btn_pressed_z = 0x7f0701b1;
        public static final int mtrl_btn_stroke_size = 0x7f0701b3;
        public static final int mtrl_btn_text_btn_icon_padding = 0x7f0701b4;
        public static final int mtrl_btn_text_btn_padding_left = 0x7f0701b5;
        public static final int mtrl_btn_text_btn_padding_right = 0x7f0701b6;
        public static final int mtrl_btn_text_size = 0x7f0701b7;
        public static final int mtrl_btn_z = 0x7f0701b8;
        public static final int mtrl_calendar_action_height = 0x7f0701ba;
        public static final int mtrl_calendar_action_padding = 0x7f0701bb;
        public static final int mtrl_calendar_bottom_padding = 0x7f0701bc;
        public static final int mtrl_calendar_content_padding = 0x7f0701bd;
        public static final int mtrl_calendar_day_corner = 0x7f0701be;
        public static final int mtrl_calendar_day_height = 0x7f0701bf;
        public static final int mtrl_calendar_day_horizontal_padding = 0x7f0701c0;
        public static final int mtrl_calendar_day_today_stroke = 0x7f0701c1;
        public static final int mtrl_calendar_day_vertical_padding = 0x7f0701c2;
        public static final int mtrl_calendar_day_width = 0x7f0701c3;
        public static final int mtrl_calendar_days_of_week_height = 0x7f0701c4;
        public static final int mtrl_calendar_dialog_background_inset = 0x7f0701c5;
        public static final int mtrl_calendar_header_content_padding = 0x7f0701c6;
        public static final int mtrl_calendar_header_content_padding_fullscreen = 0x7f0701c7;
        public static final int mtrl_calendar_header_divider_thickness = 0x7f0701c8;
        public static final int mtrl_calendar_header_height = 0x7f0701c9;
        public static final int mtrl_calendar_header_height_fullscreen = 0x7f0701ca;
        public static final int mtrl_calendar_header_selection_line_height = 0x7f0701cb;
        public static final int mtrl_calendar_header_text_padding = 0x7f0701cc;
        public static final int mtrl_calendar_header_toggle_margin_bottom = 0x7f0701cd;
        public static final int mtrl_calendar_header_toggle_margin_top = 0x7f0701ce;
        public static final int mtrl_calendar_landscape_header_width = 0x7f0701cf;
        public static final int mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f0701d0;
        public static final int mtrl_calendar_month_horizontal_padding = 0x7f0701d1;
        public static final int mtrl_calendar_month_vertical_padding = 0x7f0701d2;
        public static final int mtrl_calendar_navigation_bottom_padding = 0x7f0701d3;
        public static final int mtrl_calendar_navigation_height = 0x7f0701d4;
        public static final int mtrl_calendar_navigation_top_padding = 0x7f0701d5;
        public static final int mtrl_calendar_pre_l_text_clip_padding = 0x7f0701d6;
        public static final int mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f0701d7;
        public static final int mtrl_calendar_selection_text_baseline_to_bottom = 0x7f0701d8;
        public static final int mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f0701d9;
        public static final int mtrl_calendar_selection_text_baseline_to_top = 0x7f0701da;
        public static final int mtrl_calendar_text_input_padding_top = 0x7f0701db;
        public static final int mtrl_calendar_title_baseline_to_top = 0x7f0701dc;
        public static final int mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f0701dd;
        public static final int mtrl_calendar_year_corner = 0x7f0701de;
        public static final int mtrl_calendar_year_height = 0x7f0701df;
        public static final int mtrl_calendar_year_horizontal_padding = 0x7f0701e0;
        public static final int mtrl_calendar_year_vertical_padding = 0x7f0701e1;
        public static final int mtrl_calendar_year_width = 0x7f0701e2;
        public static final int mtrl_card_checked_icon_margin = 0x7f0701e3;
        public static final int mtrl_card_checked_icon_size = 0x7f0701e4;
        public static final int mtrl_card_corner_radius = 0x7f0701e5;
        public static final int mtrl_card_dragged_z = 0x7f0701e6;
        public static final int mtrl_card_elevation = 0x7f0701e7;
        public static final int mtrl_card_spacing = 0x7f0701e8;
        public static final int mtrl_chip_pressed_translation_z = 0x7f0701e9;
        public static final int mtrl_chip_text_size = 0x7f0701ea;
        public static final int mtrl_exposed_dropdown_menu_popup_elevation = 0x7f0701eb;
        public static final int mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f0701ec;
        public static final int mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f0701ed;
        public static final int mtrl_extended_fab_bottom_padding = 0x7f0701ee;
        public static final int mtrl_extended_fab_corner_radius = 0x7f0701ef;
        public static final int mtrl_extended_fab_disabled_elevation = 0x7f0701f0;
        public static final int mtrl_extended_fab_disabled_translation_z = 0x7f0701f1;
        public static final int mtrl_extended_fab_elevation = 0x7f0701f2;
        public static final int mtrl_extended_fab_end_padding = 0x7f0701f3;
        public static final int mtrl_extended_fab_end_padding_icon = 0x7f0701f4;
        public static final int mtrl_extended_fab_icon_size = 0x7f0701f5;
        public static final int mtrl_extended_fab_icon_text_spacing = 0x7f0701f6;
        public static final int mtrl_extended_fab_min_height = 0x7f0701f7;
        public static final int mtrl_extended_fab_min_width = 0x7f0701f8;
        public static final int mtrl_extended_fab_start_padding = 0x7f0701f9;
        public static final int mtrl_extended_fab_start_padding_icon = 0x7f0701fa;
        public static final int mtrl_extended_fab_top_padding = 0x7f0701fb;
        public static final int mtrl_extended_fab_translation_z_base = 0x7f0701fc;
        public static final int mtrl_extended_fab_translation_z_hovered_focused = 0x7f0701fd;
        public static final int mtrl_extended_fab_translation_z_pressed = 0x7f0701fe;
        public static final int mtrl_fab_elevation = 0x7f0701ff;
        public static final int mtrl_fab_min_touch_target = 0x7f070200;
        public static final int mtrl_fab_translation_z_hovered_focused = 0x7f070201;
        public static final int mtrl_fab_translation_z_pressed = 0x7f070202;
        public static final int mtrl_high_ripple_default_alpha = 0x7f070203;
        public static final int mtrl_high_ripple_focused_alpha = 0x7f070204;
        public static final int mtrl_high_ripple_hovered_alpha = 0x7f070205;
        public static final int mtrl_high_ripple_pressed_alpha = 0x7f070206;
        public static final int mtrl_large_touch_target = 0x7f070207;
        public static final int mtrl_low_ripple_default_alpha = 0x7f070208;
        public static final int mtrl_low_ripple_focused_alpha = 0x7f070209;
        public static final int mtrl_low_ripple_hovered_alpha = 0x7f07020a;
        public static final int mtrl_low_ripple_pressed_alpha = 0x7f07020b;
        public static final int mtrl_min_touch_target_size = 0x7f07020c;
        public static final int mtrl_navigation_elevation = 0x7f07020f;
        public static final int mtrl_navigation_item_horizontal_padding = 0x7f070210;
        public static final int mtrl_navigation_item_icon_padding = 0x7f070211;
        public static final int mtrl_navigation_item_icon_size = 0x7f070212;
        public static final int mtrl_navigation_item_shape_horizontal_margin = 0x7f070213;
        public static final int mtrl_navigation_item_shape_vertical_margin = 0x7f070214;
        public static final int mtrl_shape_corner_size_large_component = 0x7f07022c;
        public static final int mtrl_shape_corner_size_medium_component = 0x7f07022d;
        public static final int mtrl_shape_corner_size_small_component = 0x7f07022e;
        public static final int mtrl_slider_halo_radius = 0x7f07022f;
        public static final int mtrl_slider_label_padding = 0x7f070230;
        public static final int mtrl_slider_label_radius = 0x7f070231;
        public static final int mtrl_slider_label_square_side = 0x7f070232;
        public static final int mtrl_slider_thumb_elevation = 0x7f070233;
        public static final int mtrl_slider_thumb_radius = 0x7f070234;
        public static final int mtrl_slider_track_height = 0x7f070235;
        public static final int mtrl_slider_track_side_padding = 0x7f070236;
        public static final int mtrl_slider_track_top = 0x7f070237;
        public static final int mtrl_slider_widget_height = 0x7f070238;
        public static final int mtrl_snackbar_action_text_color_alpha = 0x7f070239;
        public static final int mtrl_snackbar_background_corner_radius = 0x7f07023a;
        public static final int mtrl_snackbar_background_overlay_color_alpha = 0x7f07023b;
        public static final int mtrl_snackbar_margin = 0x7f07023c;
        public static final int mtrl_switch_thumb_elevation = 0x7f07023f;
        public static final int mtrl_textinput_box_corner_radius_medium = 0x7f070240;
        public static final int mtrl_textinput_box_corner_radius_small = 0x7f070241;
        public static final int mtrl_textinput_box_label_cutout_padding = 0x7f070242;
        public static final int mtrl_textinput_box_stroke_width_default = 0x7f070243;
        public static final int mtrl_textinput_box_stroke_width_focused = 0x7f070244;
        public static final int mtrl_textinput_counter_margin_start = 0x7f070245;
        public static final int mtrl_textinput_end_icon_margin_start = 0x7f070246;
        public static final int mtrl_textinput_outline_box_expanded_padding = 0x7f070247;
        public static final int mtrl_textinput_start_icon_margin_end = 0x7f070248;
        public static final int mtrl_toolbar_default_height = 0x7f070249;
        public static final int mtrl_tooltip_arrowSize = 0x7f07024a;
        public static final int mtrl_tooltip_cornerSize = 0x7f07024b;
        public static final int mtrl_tooltip_minHeight = 0x7f07024c;
        public static final int mtrl_tooltip_minWidth = 0x7f07024d;
        public static final int mtrl_tooltip_padding = 0x7f07024e;
        public static final int mtrl_transition_shared_axis_slide_distance = 0x7f07024f;
        public static final int notification_action_icon_size = 0x7f070250;
        public static final int notification_action_text_size = 0x7f070251;
        public static final int notification_big_circle_margin = 0x7f070252;
        public static final int notification_content_margin_start = 0x7f070253;
        public static final int notification_large_icon_height = 0x7f070254;
        public static final int notification_large_icon_width = 0x7f070255;
        public static final int notification_main_column_padding_top = 0x7f070256;
        public static final int notification_media_narrow_margin = 0x7f070257;
        public static final int notification_right_icon_size = 0x7f070258;
        public static final int notification_right_side_padding_top = 0x7f070259;
        public static final int notification_small_icon_background_padding = 0x7f07025a;
        public static final int notification_small_icon_size_as_large = 0x7f07025b;
        public static final int notification_subtext_size = 0x7f07025c;
        public static final int notification_top_pad = 0x7f07025d;
        public static final int notification_top_pad_large_text = 0x7f07025e;
        public static final int test_mtrl_calendar_day_cornerSize = 0x7f070261;
        public static final int tooltip_corner_radius = 0x7f07026d;
        public static final int tooltip_horizontal_padding = 0x7f07026e;
        public static final int tooltip_margin = 0x7f07026f;
        public static final int tooltip_precise_anchor_extra_offset = 0x7f070270;
        public static final int tooltip_precise_anchor_threshold = 0x7f070271;
        public static final int tooltip_vertical_padding = 0x7f070272;
        public static final int tooltip_y_offset_non_touch = 0x7f070273;
        public static final int tooltip_y_offset_touch = 0x7f070274;

        private dimen() {
        }
    }

    public static final class drawable {
        public static final int abc_ab_share_pack_mtrl_alpha = 0x7f080008;
        public static final int abc_action_bar_item_background_material = 0x7f080009;
        public static final int abc_btn_borderless_material = 0x7f08000a;
        public static final int abc_btn_check_material = 0x7f08000b;
        public static final int abc_btn_check_material_anim = 0x7f08000c;
        public static final int abc_btn_check_to_on_mtrl_000 = 0x7f08000d;
        public static final int abc_btn_check_to_on_mtrl_015 = 0x7f08000e;
        public static final int abc_btn_colored_material = 0x7f08000f;
        public static final int abc_btn_default_mtrl_shape = 0x7f080010;
        public static final int abc_btn_radio_material = 0x7f080011;
        public static final int abc_btn_radio_material_anim = 0x7f080012;
        public static final int abc_btn_radio_to_on_mtrl_000 = 0x7f080013;
        public static final int abc_btn_radio_to_on_mtrl_015 = 0x7f080014;
        public static final int abc_btn_switch_to_on_mtrl_00001 = 0x7f080015;
        public static final int abc_btn_switch_to_on_mtrl_00012 = 0x7f080016;
        public static final int abc_cab_background_internal_bg = 0x7f080017;
        public static final int abc_cab_background_top_material = 0x7f080018;
        public static final int abc_cab_background_top_mtrl_alpha = 0x7f080019;
        public static final int abc_control_background_material = 0x7f08001a;
        public static final int abc_dialog_material_background = 0x7f08001b;
        public static final int abc_edit_text_material = 0x7f08001c;
        public static final int abc_ic_ab_back_material = 0x7f08001d;
        public static final int abc_ic_arrow_drop_right_black_24dp = 0x7f08001e;
        public static final int abc_ic_clear_material = 0x7f08001f;
        public static final int abc_ic_commit_search_api_mtrl_alpha = 0x7f080020;
        public static final int abc_ic_go_search_api_material = 0x7f080021;
        public static final int abc_ic_menu_copy_mtrl_am_alpha = 0x7f080022;
        public static final int abc_ic_menu_cut_mtrl_alpha = 0x7f080023;
        public static final int abc_ic_menu_overflow_material = 0x7f080024;
        public static final int abc_ic_menu_paste_mtrl_am_alpha = 0x7f080025;
        public static final int abc_ic_menu_selectall_mtrl_alpha = 0x7f080026;
        public static final int abc_ic_menu_share_mtrl_alpha = 0x7f080027;
        public static final int abc_ic_search_api_material = 0x7f080028;
        public static final int abc_ic_voice_search_api_material = 0x7f080029;
        public static final int abc_item_background_holo_dark = 0x7f08002a;
        public static final int abc_item_background_holo_light = 0x7f08002b;
        public static final int abc_list_divider_material = 0x7f08002c;
        public static final int abc_list_divider_mtrl_alpha = 0x7f08002d;
        public static final int abc_list_focused_holo = 0x7f08002e;
        public static final int abc_list_longpressed_holo = 0x7f08002f;
        public static final int abc_list_pressed_holo_dark = 0x7f080030;
        public static final int abc_list_pressed_holo_light = 0x7f080031;
        public static final int abc_list_selector_background_transition_holo_dark = 0x7f080032;
        public static final int abc_list_selector_background_transition_holo_light = 0x7f080033;
        public static final int abc_list_selector_disabled_holo_dark = 0x7f080034;
        public static final int abc_list_selector_disabled_holo_light = 0x7f080035;
        public static final int abc_list_selector_holo_dark = 0x7f080036;
        public static final int abc_list_selector_holo_light = 0x7f080037;
        public static final int abc_menu_hardkey_panel_mtrl_mult = 0x7f080038;
        public static final int abc_popup_background_mtrl_mult = 0x7f080039;
        public static final int abc_ratingbar_indicator_material = 0x7f08003a;
        public static final int abc_ratingbar_material = 0x7f08003b;
        public static final int abc_ratingbar_small_material = 0x7f08003c;
        public static final int abc_scrubber_control_off_mtrl_alpha = 0x7f08003d;
        public static final int abc_scrubber_control_to_pressed_mtrl_000 = 0x7f08003e;
        public static final int abc_scrubber_control_to_pressed_mtrl_005 = 0x7f08003f;
        public static final int abc_scrubber_primary_mtrl_alpha = 0x7f080040;
        public static final int abc_scrubber_track_mtrl_alpha = 0x7f080041;
        public static final int abc_seekbar_thumb_material = 0x7f080042;
        public static final int abc_seekbar_tick_mark_material = 0x7f080043;
        public static final int abc_seekbar_track_material = 0x7f080044;
        public static final int abc_spinner_mtrl_am_alpha = 0x7f080045;
        public static final int abc_spinner_textfield_background_material = 0x7f080046;
        public static final int abc_switch_thumb_material = 0x7f080049;
        public static final int abc_switch_track_mtrl_alpha = 0x7f08004a;
        public static final int abc_tab_indicator_material = 0x7f08004b;
        public static final int abc_tab_indicator_mtrl_alpha = 0x7f08004c;
        public static final int abc_text_cursor_material = 0x7f08004d;
        public static final int abc_textfield_activated_mtrl_alpha = 0x7f080051;
        public static final int abc_textfield_default_mtrl_alpha = 0x7f080052;
        public static final int abc_textfield_search_activated_mtrl_alpha = 0x7f080053;
        public static final int abc_textfield_search_default_mtrl_alpha = 0x7f080054;
        public static final int abc_textfield_search_material = 0x7f080055;
        public static final int abc_vector_test = 0x7f080056;
        public static final int avd_hide_password = 0x7f080057;
        public static final int avd_show_password = 0x7f080058;
        public static final int btn_checkbox_checked_mtrl = 0x7f080068;
        public static final int btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f080069;
        public static final int btn_checkbox_unchecked_mtrl = 0x7f08006a;
        public static final int btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f08006b;
        public static final int btn_radio_off_mtrl = 0x7f08006d;
        public static final int btn_radio_off_to_on_mtrl_animation = 0x7f08006e;
        public static final int btn_radio_on_mtrl = 0x7f08006f;
        public static final int btn_radio_on_to_off_mtrl_animation = 0x7f080070;
        public static final int design_fab_background = 0x7f0800b3;
        public static final int design_ic_visibility = 0x7f0800b4;
        public static final int design_ic_visibility_off = 0x7f0800b5;
        public static final int design_password_eye = 0x7f0800b6;
        public static final int design_snackbar_background = 0x7f0800b7;
        public static final int ic_mtrl_checked_circle = 0x7f0800e9;
        public static final int ic_mtrl_chip_checked_black = 0x7f0800ea;
        public static final int ic_mtrl_chip_checked_circle = 0x7f0800eb;
        public static final int ic_mtrl_chip_close_circle = 0x7f0800ec;
        public static final int material_ic_calendar_black_24dp = 0x7f0800fe;
        public static final int material_ic_clear_black_24dp = 0x7f0800ff;
        public static final int material_ic_edit_black_24dp = 0x7f080100;
        public static final int material_ic_keyboard_arrow_left_black_24dp = 0x7f080101;
        public static final int material_ic_keyboard_arrow_right_black_24dp = 0x7f080104;
        public static final int material_ic_menu_arrow_down_black_24dp = 0x7f080105;
        public static final int material_ic_menu_arrow_up_black_24dp = 0x7f080106;
        public static final int mtrl_dialog_background = 0x7f080108;
        public static final int mtrl_dropdown_arrow = 0x7f080109;
        public static final int mtrl_ic_arrow_drop_down = 0x7f08010a;
        public static final int mtrl_ic_arrow_drop_up = 0x7f08010b;
        public static final int mtrl_ic_cancel = 0x7f08010c;
        public static final int mtrl_ic_error = 0x7f08010d;
        public static final int mtrl_popupmenu_background = 0x7f08010f;
        public static final int mtrl_tabs_default_indicator = 0x7f080111;
        public static final int navigation_empty_icon = 0x7f080112;
        public static final int notification_action_background = 0x7f080113;
        public static final int notification_bg = 0x7f080114;
        public static final int notification_bg_low = 0x7f080115;
        public static final int notification_bg_low_normal = 0x7f080116;
        public static final int notification_bg_low_pressed = 0x7f080117;
        public static final int notification_bg_normal = 0x7f080118;
        public static final int notification_bg_normal_pressed = 0x7f080119;
        public static final int notification_icon_background = 0x7f08011a;
        public static final int notification_template_icon_bg = 0x7f08011b;
        public static final int notification_template_icon_low_bg = 0x7f08011c;
        public static final int notification_tile_bg = 0x7f08011d;
        public static final int notify_panel_notification_icon_bg = 0x7f08011e;
        public static final int test_custom_background = 0x7f080125;
        public static final int tooltip_frame_dark = 0x7f080127;
        public static final int tooltip_frame_light = 0x7f080128;

        private drawable() {
        }
    }

    public static final class id {
        public static final int BOTTOM_END = 0x7f0a0002;
        public static final int BOTTOM_START = 0x7f0a0003;
        public static final int TOP_END = 0x7f0a0018;
        public static final int TOP_START = 0x7f0a0019;
        public static final int accessibility_action_clickable_span = 0x7f0a001c;
        public static final int accessibility_custom_action_0 = 0x7f0a001d;
        public static final int accessibility_custom_action_1 = 0x7f0a001e;
        public static final int accessibility_custom_action_10 = 0x7f0a001f;
        public static final int accessibility_custom_action_11 = 0x7f0a0020;
        public static final int accessibility_custom_action_12 = 0x7f0a0021;
        public static final int accessibility_custom_action_13 = 0x7f0a0022;
        public static final int accessibility_custom_action_14 = 0x7f0a0023;
        public static final int accessibility_custom_action_15 = 0x7f0a0024;
        public static final int accessibility_custom_action_16 = 0x7f0a0025;
        public static final int accessibility_custom_action_17 = 0x7f0a0026;
        public static final int accessibility_custom_action_18 = 0x7f0a0027;
        public static final int accessibility_custom_action_19 = 0x7f0a0028;
        public static final int accessibility_custom_action_2 = 0x7f0a0029;
        public static final int accessibility_custom_action_20 = 0x7f0a002a;
        public static final int accessibility_custom_action_21 = 0x7f0a002b;
        public static final int accessibility_custom_action_22 = 0x7f0a002c;
        public static final int accessibility_custom_action_23 = 0x7f0a002d;
        public static final int accessibility_custom_action_24 = 0x7f0a002e;
        public static final int accessibility_custom_action_25 = 0x7f0a002f;
        public static final int accessibility_custom_action_26 = 0x7f0a0030;
        public static final int accessibility_custom_action_27 = 0x7f0a0031;
        public static final int accessibility_custom_action_28 = 0x7f0a0032;
        public static final int accessibility_custom_action_29 = 0x7f0a0033;
        public static final int accessibility_custom_action_3 = 0x7f0a0034;
        public static final int accessibility_custom_action_30 = 0x7f0a0035;
        public static final int accessibility_custom_action_31 = 0x7f0a0036;
        public static final int accessibility_custom_action_4 = 0x7f0a0037;
        public static final int accessibility_custom_action_5 = 0x7f0a0038;
        public static final int accessibility_custom_action_6 = 0x7f0a0039;
        public static final int accessibility_custom_action_7 = 0x7f0a003a;
        public static final int accessibility_custom_action_8 = 0x7f0a003b;
        public static final int accessibility_custom_action_9 = 0x7f0a003c;
        public static final int action_bar = 0x7f0a0040;
        public static final int action_bar_activity_content = 0x7f0a0041;
        public static final int action_bar_container = 0x7f0a0042;
        public static final int action_bar_root = 0x7f0a0043;
        public static final int action_bar_spinner = 0x7f0a0044;
        public static final int action_bar_subtitle = 0x7f0a0045;
        public static final int action_bar_title = 0x7f0a0046;
        public static final int action_container = 0x7f0a0047;
        public static final int action_context_bar = 0x7f0a0048;
        public static final int action_divider = 0x7f0a0049;
        public static final int action_image = 0x7f0a004a;
        public static final int action_menu_divider = 0x7f0a004b;
        public static final int action_menu_presenter = 0x7f0a004c;
        public static final int action_mode_bar = 0x7f0a004d;
        public static final int action_mode_bar_stub = 0x7f0a004e;
        public static final int action_mode_close_button = 0x7f0a004f;
        public static final int action_text = 0x7f0a0050;
        public static final int actions = 0x7f0a0051;
        public static final int activity_chooser_view_content = 0x7f0a0052;
        public static final int add = 0x7f0a0053;
        public static final int alertTitle = 0x7f0a0054;
        public static final int async = 0x7f0a0062;
        public static final int auto = 0x7f0a0063;
        public static final int blocking = 0x7f0a0082;
        public static final int bottom = 0x7f0a0083;
        public static final int buttonPanel = 0x7f0a00c0;
        public static final int cancel_button = 0x7f0a00ca;
        public static final int center = 0x7f0a00e4;
        public static final int checkbox = 0x7f0a00ee;
        public static final int checked = 0x7f0a00ef;
        public static final int chip = 0x7f0a00f1;
        public static final int chip1 = 0x7f0a00f2;
        public static final int chip2 = 0x7f0a00f3;
        public static final int chip3 = 0x7f0a00f4;
        public static final int chip_group = 0x7f0a00f5;
        public static final int chronometer = 0x7f0a00f6;
        public static final int clear_text = 0x7f0a00fa;
        public static final int confirm_button = 0x7f0a0117;
        public static final int container = 0x7f0a0122;
        public static final int content = 0x7f0a0123;
        public static final int contentPanel = 0x7f0a0124;
        public static final int coordinator = 0x7f0a012c;
        public static final int custom = 0x7f0a0133;
        public static final int customPanel = 0x7f0a0134;
        public static final int cut = 0x7f0a0135;
        public static final int date_picker_actions = 0x7f0a0139;
        public static final int decor_content_parent = 0x7f0a013d;
        public static final int default_activity_button = 0x7f0a013e;
        public static final int design_bottom_sheet = 0x7f0a0142;
        public static final int design_menu_item_action_area = 0x7f0a0143;
        public static final int design_menu_item_action_area_stub = 0x7f0a0144;
        public static final int design_menu_item_text = 0x7f0a0145;
        public static final int design_navigation_view = 0x7f0a0146;
        public static final int dialog_button = 0x7f0a014a;
        public static final int dropdown_menu = 0x7f0a0164;
        public static final int edit_query = 0x7f0a016e;
        public static final int end = 0x7f0a0174;
        public static final int expand_activities_button = 0x7f0a017c;
        public static final int expanded_menu = 0x7f0a017d;
        public static final int fade = 0x7f0a017f;
        public static final int fill = 0x7f0a0181;
        public static final int filled = 0x7f0a0184;
        public static final int fixed = 0x7f0a018b;
        public static final int floating = 0x7f0a018d;
        public static final int forever = 0x7f0a018f;
        public static final int ghost_view = 0x7f0a0198;
        public static final int ghost_view_holder = 0x7f0a0199;
        public static final int gone = 0x7f0a019b;
        public static final int group_divider = 0x7f0a019e;
        public static final int home = 0x7f0a01ab;
        public static final int icon = 0x7f0a01b5;
        public static final int icon_group = 0x7f0a01b6;
        public static final int image = 0x7f0a01ba;
        public static final int info = 0x7f0a01c1;
        public static final int italic = 0x7f0a01c9;
        public static final int item_touch_helper_previous_elevation = 0x7f0a01d0;
        public static final int labeled = 0x7f0a01dc;
        public static final int left = 0x7f0a01e8;
        public static final int line1 = 0x7f0a01ef;
        public static final int line3 = 0x7f0a01f0;
        public static final int listMode = 0x7f0a01f4;
        public static final int list_item = 0x7f0a01f5;
        public static final int masked = 0x7f0a0208;
        public static final int message = 0x7f0a0221;
        public static final int mini = 0x7f0a0223;
        public static final int month_grid = 0x7f0a022a;
        public static final int month_navigation_bar = 0x7f0a022b;
        public static final int month_navigation_fragment_toggle = 0x7f0a022c;
        public static final int month_navigation_next = 0x7f0a022d;
        public static final int month_navigation_previous = 0x7f0a022e;
        public static final int month_title = 0x7f0a022f;
        public static final int mtrl_calendar_day_selector_frame = 0x7f0a0234;
        public static final int mtrl_calendar_days_of_week = 0x7f0a0235;
        public static final int mtrl_calendar_frame = 0x7f0a0236;
        public static final int mtrl_calendar_main_pane = 0x7f0a0237;
        public static final int mtrl_calendar_months = 0x7f0a0238;
        public static final int mtrl_calendar_selection_frame = 0x7f0a0239;
        public static final int mtrl_calendar_text_input_frame = 0x7f0a023a;
        public static final int mtrl_calendar_year_selector_frame = 0x7f0a023b;
        public static final int mtrl_card_checked_layer_id = 0x7f0a023c;
        public static final int mtrl_child_content_container = 0x7f0a023d;
        public static final int mtrl_internal_children_alpha_tag = 0x7f0a023e;
        public static final int mtrl_motion_snapshot_view = 0x7f0a023f;
        public static final int mtrl_picker_fullscreen = 0x7f0a0240;
        public static final int mtrl_picker_header = 0x7f0a0241;
        public static final int mtrl_picker_header_selection_text = 0x7f0a0242;
        public static final int mtrl_picker_header_title_and_selection = 0x7f0a0243;
        public static final int mtrl_picker_header_toggle = 0x7f0a0244;
        public static final int mtrl_picker_text_input_date = 0x7f0a0245;
        public static final int mtrl_picker_text_input_range_end = 0x7f0a0246;
        public static final int mtrl_picker_text_input_range_start = 0x7f0a0247;
        public static final int mtrl_picker_title_text = 0x7f0a0248;
        public static final int multiply = 0x7f0a024e;
        public static final int navigation_header_container = 0x7f0a0255;
        public static final int none = 0x7f0a0262;
        public static final int normal = 0x7f0a0263;
        public static final int notification_background = 0x7f0a0265;
        public static final int notification_main_column = 0x7f0a0266;
        public static final int notification_main_column_container = 0x7f0a0267;
        public static final int off = 0x7f0a026a;
        public static final int on = 0x7f0a026b;
        public static final int outline = 0x7f0a0275;
        public static final int parallax = 0x7f0a0279;
        public static final int parentPanel = 0x7f0a027b;
        public static final int parent_matrix = 0x7f0a027d;
        public static final int password_toggle = 0x7f0a027e;
        public static final int pin = 0x7f0a0285;
        public static final int progress_circular = 0x7f0a028e;
        public static final int progress_horizontal = 0x7f0a028f;
        public static final int radio = 0x7f0a029a;
        public static final int right = 0x7f0a02a8;
        public static final int right_icon = 0x7f0a02ac;
        public static final int right_side = 0x7f0a02ad;
        public static final int rounded = 0x7f0a02b1;
        public static final int row_index_key = 0x7f0a02b2;
        public static final int save_non_transition_alpha = 0x7f0a02bb;
        public static final int save_overlay_view = 0x7f0a02bc;
        public static final int scale = 0x7f0a02be;
        public static final int screen = 0x7f0a02bf;
        public static final int scrollIndicatorDown = 0x7f0a02c1;
        public static final int scrollIndicatorUp = 0x7f0a02c2;
        public static final int scrollView = 0x7f0a02c3;
        public static final int scrollable = 0x7f0a02c4;
        public static final int search_badge = 0x7f0a02c5;
        public static final int search_bar = 0x7f0a02c6;
        public static final int search_button = 0x7f0a02c7;
        public static final int search_close_btn = 0x7f0a02c8;
        public static final int search_edit_frame = 0x7f0a02c9;
        public static final int search_go_btn = 0x7f0a02ca;
        public static final int search_mag_icon = 0x7f0a02cb;
        public static final int search_plate = 0x7f0a02cc;
        public static final int search_src_text = 0x7f0a02cd;
        public static final int search_voice_btn = 0x7f0a02ce;
        public static final int select_dialog_listview = 0x7f0a02d1;
        public static final int selected = 0x7f0a02d2;
        public static final int shortcut = 0x7f0a02d7;
        public static final int slide = 0x7f0a02e2;
        public static final int snackbar_action = 0x7f0a02e7;
        public static final int snackbar_text = 0x7f0a02e8;
        public static final int spacer = 0x7f0a02ee;
        public static final int split_action_bar = 0x7f0a02f1;
        public static final int src_atop = 0x7f0a02f6;
        public static final int src_in = 0x7f0a02f7;
        public static final int src_over = 0x7f0a02f8;
        public static final int start = 0x7f0a02fd;
        public static final int stretch = 0x7f0a0306;
        public static final int submenuarrow = 0x7f0a0307;
        public static final int submit_area = 0x7f0a0308;
        public static final int tabMode = 0x7f0a030c;
        public static final int tag_accessibility_actions = 0x7f0a030d;
        public static final int tag_accessibility_clickable_spans = 0x7f0a030e;
        public static final int tag_accessibility_heading = 0x7f0a030f;
        public static final int tag_accessibility_pane_title = 0x7f0a0310;
        public static final int tag_screen_reader_focusable = 0x7f0a0314;
        public static final int tag_transition_group = 0x7f0a0316;
        public static final int tag_unhandled_key_event_manager = 0x7f0a0317;
        public static final int tag_unhandled_key_listeners = 0x7f0a0318;
        public static final int test_checkbox_android_button_tint = 0x7f0a0321;
        public static final int test_checkbox_app_button_tint = 0x7f0a0322;
        public static final int test_radiobutton_android_button_tint = 0x7f0a0323;
        public static final int test_radiobutton_app_button_tint = 0x7f0a0324;
        public static final int text = 0x7f0a0326;
        public static final int text2 = 0x7f0a0327;
        public static final int textSpacerNoButtons = 0x7f0a0329;
        public static final int textSpacerNoTitle = 0x7f0a032a;
        public static final int text_input_end_icon = 0x7f0a032e;
        public static final int text_input_start_icon = 0x7f0a0330;
        public static final int textinput_counter = 0x7f0a0331;
        public static final int textinput_error = 0x7f0a0332;
        public static final int textinput_helper_text = 0x7f0a0333;
        public static final int textinput_placeholder = 0x7f0a0334;
        public static final int textinput_prefix_text = 0x7f0a0335;
        public static final int textinput_suffix_text = 0x7f0a0336;
        public static final int time = 0x7f0a0339;
        public static final int title = 0x7f0a0345;
        public static final int titleDividerNoCustom = 0x7f0a0346;
        public static final int title_template = 0x7f0a0347;
        public static final int top = 0x7f0a034a;
        public static final int topPanel = 0x7f0a034b;
        public static final int touch_outside = 0x7f0a034e;
        public static final int transition_current_scene = 0x7f0a0351;
        public static final int transition_layout_save = 0x7f0a0352;
        public static final int transition_position = 0x7f0a0353;
        public static final int transition_scene_layoutid_cache = 0x7f0a0354;
        public static final int transition_transform = 0x7f0a0355;
        public static final int unchecked = 0x7f0a039d;
        public static final int uniform = 0x7f0a039e;
        public static final int unlabeled = 0x7f0a039f;
        public static final int up = 0x7f0a03a0;
        public static final int view_offset_helper = 0x7f0a03b3;
        public static final int visible = 0x7f0a03ba;
        public static final int withinBounds = 0x7f0a03d2;
        public static final int wrap_content = 0x7f0a03d5;
        public static final int zero_corner_chip = 0x7f0a03db;

        private id() {
        }
    }

    public static final class integer {
        public static final int abc_config_activityDefaultDur = 0x7f0b0000;
        public static final int abc_config_activityShortDur = 0x7f0b0001;
        public static final int app_bar_elevation_anim_duration = 0x7f0b0002;
        public static final int bottom_sheet_slide_duration = 0x7f0b0003;
        public static final int cancel_button_image_alpha = 0x7f0b0004;
        public static final int config_tooltipAnimTime = 0x7f0b0005;
        public static final int design_snackbar_text_max_lines = 0x7f0b0006;
        public static final int design_tab_indicator_anim_duration_ms = 0x7f0b0007;
        public static final int hide_password_duration = 0x7f0b0008;
        public static final int mtrl_badge_max_character_count = 0x7f0b0026;
        public static final int mtrl_btn_anim_delay_ms = 0x7f0b0027;
        public static final int mtrl_btn_anim_duration_ms = 0x7f0b0028;
        public static final int mtrl_calendar_header_orientation = 0x7f0b0029;
        public static final int mtrl_calendar_selection_text_lines = 0x7f0b002a;
        public static final int mtrl_calendar_year_selector_span = 0x7f0b002b;
        public static final int mtrl_card_anim_delay_ms = 0x7f0b002c;
        public static final int mtrl_card_anim_duration_ms = 0x7f0b002d;
        public static final int mtrl_chip_anim_duration = 0x7f0b002e;
        public static final int mtrl_tab_indicator_anim_duration_ms = 0x7f0b002f;
        public static final int show_password_duration = 0x7f0b0033;
        public static final int status_bar_notification_info_maxnum = 0x7f0b0034;

        private integer() {
        }
    }

    public static final class interpolator {
        public static final int btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0c0000;
        public static final int btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0c0001;
        public static final int btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0c0002;
        public static final int btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0c0003;
        public static final int btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0c0004;
        public static final int btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0c0005;
        public static final int fast_out_slow_in = 0x7f0c0006;
        public static final int mtrl_fast_out_linear_in = 0x7f0c0007;
        public static final int mtrl_fast_out_slow_in = 0x7f0c0008;
        public static final int mtrl_linear = 0x7f0c0009;
        public static final int mtrl_linear_out_slow_in = 0x7f0c000a;

        private interpolator() {
        }
    }

    public static final class layout {
        public static final int abc_action_bar_title_item = 0x7f0d0000;
        public static final int abc_action_bar_up_container = 0x7f0d0001;
        public static final int abc_action_menu_item_layout = 0x7f0d0002;
        public static final int abc_action_menu_layout = 0x7f0d0003;
        public static final int abc_action_mode_bar = 0x7f0d0004;
        public static final int abc_action_mode_close_item_material = 0x7f0d0005;
        public static final int abc_activity_chooser_view = 0x7f0d0006;
        public static final int abc_activity_chooser_view_list_item = 0x7f0d0007;
        public static final int abc_alert_dialog_button_bar_material = 0x7f0d0008;
        public static final int abc_alert_dialog_material = 0x7f0d0009;
        public static final int abc_alert_dialog_title_material = 0x7f0d000a;
        public static final int abc_cascading_menu_item_layout = 0x7f0d000b;
        public static final int abc_dialog_title_material = 0x7f0d000c;
        public static final int abc_expanded_menu_layout = 0x7f0d000d;
        public static final int abc_list_menu_item_checkbox = 0x7f0d000e;
        public static final int abc_list_menu_item_icon = 0x7f0d000f;
        public static final int abc_list_menu_item_layout = 0x7f0d0010;
        public static final int abc_list_menu_item_radio = 0x7f0d0011;
        public static final int abc_popup_menu_header_item_layout = 0x7f0d0012;
        public static final int abc_popup_menu_item_layout = 0x7f0d0013;
        public static final int abc_screen_content_include = 0x7f0d0014;
        public static final int abc_screen_simple = 0x7f0d0015;
        public static final int abc_screen_simple_overlay_action_mode = 0x7f0d0016;
        public static final int abc_screen_toolbar = 0x7f0d0017;
        public static final int abc_search_dropdown_item_icons_2line = 0x7f0d0018;
        public static final int abc_search_view = 0x7f0d0019;
        public static final int abc_select_dialog_material = 0x7f0d001a;
        public static final int abc_tooltip = 0x7f0d001b;
        public static final int custom_dialog = 0x7f0d0046;
        public static final int design_bottom_navigation_item = 0x7f0d0047;
        public static final int design_bottom_sheet_dialog = 0x7f0d0048;
        public static final int design_layout_snackbar = 0x7f0d0049;
        public static final int design_layout_snackbar_include = 0x7f0d004a;
        public static final int design_layout_tab_icon = 0x7f0d004b;
        public static final int design_layout_tab_text = 0x7f0d004c;
        public static final int design_menu_item_action_area = 0x7f0d004d;
        public static final int design_navigation_item = 0x7f0d004e;
        public static final int design_navigation_item_header = 0x7f0d004f;
        public static final int design_navigation_item_separator = 0x7f0d0050;
        public static final int design_navigation_item_subheader = 0x7f0d0051;
        public static final int design_navigation_menu = 0x7f0d0052;
        public static final int design_navigation_menu_item = 0x7f0d0053;
        public static final int design_text_input_end_icon = 0x7f0d0054;
        public static final int design_text_input_start_icon = 0x7f0d0055;
        public static final int mtrl_alert_dialog = 0x7f0d00b1;
        public static final int mtrl_alert_dialog_actions = 0x7f0d00b2;
        public static final int mtrl_alert_dialog_title = 0x7f0d00b3;
        public static final int mtrl_alert_select_dialog_item = 0x7f0d00b4;
        public static final int mtrl_alert_select_dialog_multichoice = 0x7f0d00b5;
        public static final int mtrl_alert_select_dialog_singlechoice = 0x7f0d00b6;
        public static final int mtrl_calendar_day = 0x7f0d00b8;
        public static final int mtrl_calendar_day_of_week = 0x7f0d00b9;
        public static final int mtrl_calendar_days_of_week = 0x7f0d00ba;
        public static final int mtrl_calendar_horizontal = 0x7f0d00bb;
        public static final int mtrl_calendar_month = 0x7f0d00bc;
        public static final int mtrl_calendar_month_labeled = 0x7f0d00bd;
        public static final int mtrl_calendar_month_navigation = 0x7f0d00be;
        public static final int mtrl_calendar_months = 0x7f0d00bf;
        public static final int mtrl_calendar_vertical = 0x7f0d00c0;
        public static final int mtrl_calendar_year = 0x7f0d00c1;
        public static final int mtrl_layout_snackbar = 0x7f0d00c2;
        public static final int mtrl_layout_snackbar_include = 0x7f0d00c3;
        public static final int mtrl_picker_actions = 0x7f0d00c5;
        public static final int mtrl_picker_dialog = 0x7f0d00c6;
        public static final int mtrl_picker_fullscreen = 0x7f0d00c7;
        public static final int mtrl_picker_header_dialog = 0x7f0d00c8;
        public static final int mtrl_picker_header_fullscreen = 0x7f0d00c9;
        public static final int mtrl_picker_header_selection_text = 0x7f0d00ca;
        public static final int mtrl_picker_header_title_text = 0x7f0d00cb;
        public static final int mtrl_picker_header_toggle = 0x7f0d00cc;
        public static final int mtrl_picker_text_input_date = 0x7f0d00cd;
        public static final int mtrl_picker_text_input_date_range = 0x7f0d00ce;
        public static final int notification_action = 0x7f0d00d0;
        public static final int notification_action_tombstone = 0x7f0d00d1;
        public static final int notification_template_custom_big = 0x7f0d00d2;
        public static final int notification_template_icon_group = 0x7f0d00d3;
        public static final int notification_template_part_chronometer = 0x7f0d00d4;
        public static final int notification_template_part_time = 0x7f0d00d5;
        public static final int select_dialog_item_material = 0x7f0d00d6;
        public static final int select_dialog_multichoice_material = 0x7f0d00d7;
        public static final int select_dialog_singlechoice_material = 0x7f0d00d8;
        public static final int support_simple_spinner_dropdown_item = 0x7f0d00d9;
        public static final int test_action_chip = 0x7f0d00da;
        public static final int test_chip_zero_corner_radius = 0x7f0d00db;
        public static final int test_design_checkbox = 0x7f0d00dc;
        public static final int test_design_radiobutton = 0x7f0d00dd;
        public static final int test_reflow_chipgroup = 0x7f0d00e0;
        public static final int test_toolbar = 0x7f0d00e1;
        public static final int test_toolbar_custom_background = 0x7f0d00e2;
        public static final int test_toolbar_elevation = 0x7f0d00e3;
        public static final int test_toolbar_surface = 0x7f0d00e4;
        public static final int text_view_with_line_height_from_appearance = 0x7f0d00e5;
        public static final int text_view_with_line_height_from_layout = 0x7f0d00e6;
        public static final int text_view_with_line_height_from_style = 0x7f0d00e7;
        public static final int text_view_with_theme_line_height = 0x7f0d00e8;
        public static final int text_view_without_line_height = 0x7f0d00e9;

        private layout() {
        }
    }

    public static final class plurals {
        public static final int mtrl_badge_content_description = 0x7f100000;

        private plurals() {
        }
    }

    public static final class string {
        public static final int abc_action_bar_home_description = 0x7f110000;
        public static final int abc_action_bar_up_description = 0x7f110001;
        public static final int abc_action_menu_overflow_description = 0x7f110002;
        public static final int abc_action_mode_done = 0x7f110003;
        public static final int abc_activity_chooser_view_see_all = 0x7f110004;
        public static final int abc_activitychooserview_choose_application = 0x7f110005;
        public static final int abc_capital_off = 0x7f110006;
        public static final int abc_capital_on = 0x7f110007;
        public static final int abc_menu_alt_shortcut_label = 0x7f110008;
        public static final int abc_menu_ctrl_shortcut_label = 0x7f110009;
        public static final int abc_menu_delete_shortcut_label = 0x7f11000a;
        public static final int abc_menu_enter_shortcut_label = 0x7f11000b;
        public static final int abc_menu_function_shortcut_label = 0x7f11000c;
        public static final int abc_menu_meta_shortcut_label = 0x7f11000d;
        public static final int abc_menu_shift_shortcut_label = 0x7f11000e;
        public static final int abc_menu_space_shortcut_label = 0x7f11000f;
        public static final int abc_menu_sym_shortcut_label = 0x7f110010;
        public static final int abc_prepend_shortcut_label = 0x7f110011;
        public static final int abc_search_hint = 0x7f110012;
        public static final int abc_searchview_description_clear = 0x7f110013;
        public static final int abc_searchview_description_query = 0x7f110014;
        public static final int abc_searchview_description_search = 0x7f110015;
        public static final int abc_searchview_description_submit = 0x7f110016;
        public static final int abc_searchview_description_voice = 0x7f110017;
        public static final int abc_shareactionprovider_share_with = 0x7f110018;
        public static final int abc_shareactionprovider_share_with_application = 0x7f110019;
        public static final int abc_toolbar_collapse_description = 0x7f11001a;
        public static final int appbar_scrolling_view_behavior = 0x7f110022;
        public static final int bottom_sheet_behavior = 0x7f110029;
        public static final int character_counter_content_description = 0x7f110057;
        public static final int character_counter_overflowed_content_description = 0x7f110058;
        public static final int character_counter_pattern = 0x7f110059;
        public static final int chip_text = 0x7f110061;
        public static final int clear_text_end_icon_content_description = 0x7f110062;
        public static final int error_icon_content_description = 0x7f1100d5;
        public static final int exposed_dropdown_menu_content_description = 0x7f1100ec;
        public static final int fab_transformation_scrim_behavior = 0x7f1100ed;
        public static final int fab_transformation_sheet_behavior = 0x7f1100ee;
        public static final int hide_bottom_view_on_scroll_behavior = 0x7f1100f3;
        public static final int icon_content_description = 0x7f1100fa;
        public static final int item_view_role_description = 0x7f110130;
        public static final int material_slider_range_end = 0x7f11015e;
        public static final int material_slider_range_start = 0x7f11015f;
        public static final int mtrl_badge_numberless_content_description = 0x7f11016e;
        public static final int mtrl_chip_close_icon_content_description = 0x7f11016f;
        public static final int mtrl_exceed_max_badge_number_content_description = 0x7f110170;
        public static final int mtrl_exceed_max_badge_number_suffix = 0x7f110171;
        public static final int mtrl_picker_a11y_next_month = 0x7f110172;
        public static final int mtrl_picker_a11y_prev_month = 0x7f110173;
        public static final int mtrl_picker_announce_current_selection = 0x7f110174;
        public static final int mtrl_picker_cancel = 0x7f110175;
        public static final int mtrl_picker_confirm = 0x7f110176;
        public static final int mtrl_picker_date_header_selected = 0x7f110177;
        public static final int mtrl_picker_date_header_title = 0x7f110178;
        public static final int mtrl_picker_date_header_unselected = 0x7f110179;
        public static final int mtrl_picker_day_of_week_column_header = 0x7f11017a;
        public static final int mtrl_picker_invalid_format = 0x7f11017b;
        public static final int mtrl_picker_invalid_format_example = 0x7f11017c;
        public static final int mtrl_picker_invalid_format_use = 0x7f11017d;
        public static final int mtrl_picker_invalid_range = 0x7f11017e;
        public static final int mtrl_picker_navigate_to_year_description = 0x7f11017f;
        public static final int mtrl_picker_out_of_range = 0x7f110180;
        public static final int mtrl_picker_range_header_only_end_selected = 0x7f110181;
        public static final int mtrl_picker_range_header_only_start_selected = 0x7f110182;
        public static final int mtrl_picker_range_header_selected = 0x7f110183;
        public static final int mtrl_picker_range_header_title = 0x7f110184;
        public static final int mtrl_picker_range_header_unselected = 0x7f110185;
        public static final int mtrl_picker_save = 0x7f110186;
        public static final int mtrl_picker_text_input_date_hint = 0x7f110187;
        public static final int mtrl_picker_text_input_date_range_end_hint = 0x7f110188;
        public static final int mtrl_picker_text_input_date_range_start_hint = 0x7f110189;
        public static final int mtrl_picker_text_input_day_abbr = 0x7f11018a;
        public static final int mtrl_picker_text_input_month_abbr = 0x7f11018b;
        public static final int mtrl_picker_text_input_year_abbr = 0x7f11018c;
        public static final int mtrl_picker_toggle_to_calendar_input_mode = 0x7f11018d;
        public static final int mtrl_picker_toggle_to_day_selection = 0x7f11018e;
        public static final int mtrl_picker_toggle_to_text_input_mode = 0x7f11018f;
        public static final int mtrl_picker_toggle_to_year_selection = 0x7f110190;
        public static final int password_toggle_content_description = 0x7f1101be;
        public static final int path_password_eye = 0x7f1101bf;
        public static final int path_password_eye_mask_strike_through = 0x7f1101c0;
        public static final int path_password_eye_mask_visible = 0x7f1101c1;
        public static final int path_password_strike_through = 0x7f1101c2;
        public static final int search_menu_title = 0x7f1101cc;
        public static final int status_bar_notification_info_overflow = 0x7f1101da;

        private string() {
        }
    }

    public static final class style {
        public static final int AlertDialog_AppCompat = 0x7f120000;
        public static final int AlertDialog_AppCompat_Light = 0x7f120001;
        public static final int AndroidThemeColorAccentYellow = 0x7f120002;
        public static final int Animation_AppCompat_Dialog = 0x7f120003;
        public static final int Animation_AppCompat_DropDownUp = 0x7f120004;
        public static final int Animation_AppCompat_Tooltip = 0x7f120005;
        public static final int Animation_Design_BottomSheetDialog = 0x7f120006;
        public static final int Animation_MaterialComponents_BottomSheetDialog = 0x7f120007;
        public static final int Base_AlertDialog_AppCompat = 0x7f12000c;
        public static final int Base_AlertDialog_AppCompat_Light = 0x7f12000d;
        public static final int Base_Animation_AppCompat_Dialog = 0x7f12000e;
        public static final int Base_Animation_AppCompat_DropDownUp = 0x7f12000f;
        public static final int Base_Animation_AppCompat_Tooltip = 0x7f120010;
        public static final int Base_CardView = 0x7f120011;
        public static final int Base_DialogWindowTitleBackground_AppCompat = 0x7f120013;
        public static final int Base_DialogWindowTitle_AppCompat = 0x7f120012;
        public static final int Base_MaterialAlertDialog_MaterialComponents_Title_Icon = 0x7f120014;
        public static final int Base_MaterialAlertDialog_MaterialComponents_Title_Panel = 0x7f120015;
        public static final int Base_MaterialAlertDialog_MaterialComponents_Title_Text = 0x7f120016;
        public static final int Base_TextAppearance_AppCompat = 0x7f120017;
        public static final int Base_TextAppearance_AppCompat_Body1 = 0x7f120018;
        public static final int Base_TextAppearance_AppCompat_Body2 = 0x7f120019;
        public static final int Base_TextAppearance_AppCompat_Button = 0x7f12001a;
        public static final int Base_TextAppearance_AppCompat_Caption = 0x7f12001b;
        public static final int Base_TextAppearance_AppCompat_Display1 = 0x7f12001c;
        public static final int Base_TextAppearance_AppCompat_Display2 = 0x7f12001d;
        public static final int Base_TextAppearance_AppCompat_Display3 = 0x7f12001e;
        public static final int Base_TextAppearance_AppCompat_Display4 = 0x7f12001f;
        public static final int Base_TextAppearance_AppCompat_Headline = 0x7f120020;
        public static final int Base_TextAppearance_AppCompat_Inverse = 0x7f120021;
        public static final int Base_TextAppearance_AppCompat_Large = 0x7f120022;
        public static final int Base_TextAppearance_AppCompat_Large_Inverse = 0x7f120023;
        public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f120024;
        public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f120025;
        public static final int Base_TextAppearance_AppCompat_Medium = 0x7f120026;
        public static final int Base_TextAppearance_AppCompat_Medium_Inverse = 0x7f120027;
        public static final int Base_TextAppearance_AppCompat_Menu = 0x7f120028;
        public static final int Base_TextAppearance_AppCompat_SearchResult = 0x7f120029;
        public static final int Base_TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f12002a;
        public static final int Base_TextAppearance_AppCompat_SearchResult_Title = 0x7f12002b;
        public static final int Base_TextAppearance_AppCompat_Small = 0x7f12002c;
        public static final int Base_TextAppearance_AppCompat_Small_Inverse = 0x7f12002d;
        public static final int Base_TextAppearance_AppCompat_Subhead = 0x7f12002e;
        public static final int Base_TextAppearance_AppCompat_Subhead_Inverse = 0x7f12002f;
        public static final int Base_TextAppearance_AppCompat_Title = 0x7f120030;
        public static final int Base_TextAppearance_AppCompat_Title_Inverse = 0x7f120031;
        public static final int Base_TextAppearance_AppCompat_Tooltip = 0x7f120032;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f120033;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f120034;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f120035;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f120036;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f120037;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f120038;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f120039;
        public static final int Base_TextAppearance_AppCompat_Widget_Button = 0x7f12003a;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f12003b;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Colored = 0x7f12003c;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f12003d;
        public static final int Base_TextAppearance_AppCompat_Widget_DropDownItem = 0x7f12003e;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f12003f;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f120040;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f120041;
        public static final int Base_TextAppearance_AppCompat_Widget_Switch = 0x7f120042;
        public static final int Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f120043;
        public static final int Base_TextAppearance_MaterialComponents_Badge = 0x7f120044;
        public static final int Base_TextAppearance_MaterialComponents_Button = 0x7f120045;
        public static final int Base_TextAppearance_MaterialComponents_Headline6 = 0x7f120046;
        public static final int Base_TextAppearance_MaterialComponents_Subtitle2 = 0x7f120047;
        public static final int Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f120048;
        public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f120049;
        public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f12004a;
        public static final int Base_ThemeOverlay_AppCompat = 0x7f120072;
        public static final int Base_ThemeOverlay_AppCompat_ActionBar = 0x7f120073;
        public static final int Base_ThemeOverlay_AppCompat_Dark = 0x7f120074;
        public static final int Base_ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f120075;
        public static final int Base_ThemeOverlay_AppCompat_Dialog = 0x7f120076;
        public static final int Base_ThemeOverlay_AppCompat_Dialog_Alert = 0x7f120077;
        public static final int Base_ThemeOverlay_AppCompat_Light = 0x7f120078;
        public static final int Base_ThemeOverlay_MaterialComponents_Dialog = 0x7f12007d;
        public static final int Base_ThemeOverlay_MaterialComponents_Dialog_Alert = 0x7f12007e;
        public static final int Base_ThemeOverlay_MaterialComponents_Dialog_Alert_Framework = 0x7f12007f;
        public static final int Base_ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework = 0x7f120080;
        public static final int Base_ThemeOverlay_MaterialComponents_MaterialAlertDialog = 0x7f120081;
        public static final int Base_Theme_AppCompat = 0x7f12004b;
        public static final int Base_Theme_AppCompat_CompactMenu = 0x7f12004c;
        public static final int Base_Theme_AppCompat_Dialog = 0x7f12004d;
        public static final int Base_Theme_AppCompat_DialogWhenLarge = 0x7f120051;
        public static final int Base_Theme_AppCompat_Dialog_Alert = 0x7f12004e;
        public static final int Base_Theme_AppCompat_Dialog_FixedSize = 0x7f12004f;
        public static final int Base_Theme_AppCompat_Dialog_MinWidth = 0x7f120050;
        public static final int Base_Theme_AppCompat_Light = 0x7f120052;
        public static final int Base_Theme_AppCompat_Light_DarkActionBar = 0x7f120053;
        public static final int Base_Theme_AppCompat_Light_Dialog = 0x7f120054;
        public static final int Base_Theme_AppCompat_Light_DialogWhenLarge = 0x7f120058;
        public static final int Base_Theme_AppCompat_Light_Dialog_Alert = 0x7f120055;
        public static final int Base_Theme_AppCompat_Light_Dialog_FixedSize = 0x7f120056;
        public static final int Base_Theme_AppCompat_Light_Dialog_MinWidth = 0x7f120057;
        public static final int Base_Theme_MaterialComponents = 0x7f12005f;
        public static final int Base_Theme_MaterialComponents_Bridge = 0x7f120060;
        public static final int Base_Theme_MaterialComponents_CompactMenu = 0x7f120061;
        public static final int Base_Theme_MaterialComponents_Dialog = 0x7f120062;
        public static final int Base_Theme_MaterialComponents_DialogWhenLarge = 0x7f120067;
        public static final int Base_Theme_MaterialComponents_Dialog_Alert = 0x7f120063;
        public static final int Base_Theme_MaterialComponents_Dialog_Bridge = 0x7f120064;
        public static final int Base_Theme_MaterialComponents_Dialog_FixedSize = 0x7f120065;
        public static final int Base_Theme_MaterialComponents_Dialog_MinWidth = 0x7f120066;
        public static final int Base_Theme_MaterialComponents_Light = 0x7f120068;
        public static final int Base_Theme_MaterialComponents_Light_Bridge = 0x7f120069;
        public static final int Base_Theme_MaterialComponents_Light_DarkActionBar = 0x7f12006a;
        public static final int Base_Theme_MaterialComponents_Light_DarkActionBar_Bridge = 0x7f12006b;
        public static final int Base_Theme_MaterialComponents_Light_Dialog = 0x7f12006c;
        public static final int Base_Theme_MaterialComponents_Light_DialogWhenLarge = 0x7f120071;
        public static final int Base_Theme_MaterialComponents_Light_Dialog_Alert = 0x7f12006d;
        public static final int Base_Theme_MaterialComponents_Light_Dialog_Bridge = 0x7f12006e;
        public static final int Base_Theme_MaterialComponents_Light_Dialog_FixedSize = 0x7f12006f;
        public static final int Base_Theme_MaterialComponents_Light_Dialog_MinWidth = 0x7f120070;
        public static final int Base_V14_ThemeOverlay_MaterialComponents_Dialog = 0x7f120093;
        public static final int Base_V14_ThemeOverlay_MaterialComponents_Dialog_Alert = 0x7f120094;
        public static final int Base_V14_ThemeOverlay_MaterialComponents_MaterialAlertDialog = 0x7f120095;
        public static final int Base_V14_Theme_MaterialComponents = 0x7f120088;
        public static final int Base_V14_Theme_MaterialComponents_Bridge = 0x7f120089;
        public static final int Base_V14_Theme_MaterialComponents_Dialog = 0x7f12008a;
        public static final int Base_V14_Theme_MaterialComponents_Dialog_Bridge = 0x7f12008b;
        public static final int Base_V14_Theme_MaterialComponents_Light = 0x7f12008c;
        public static final int Base_V14_Theme_MaterialComponents_Light_Bridge = 0x7f12008d;
        public static final int Base_V14_Theme_MaterialComponents_Light_DarkActionBar_Bridge = 0x7f12008e;
        public static final int Base_V14_Theme_MaterialComponents_Light_Dialog = 0x7f12008f;
        public static final int Base_V14_Theme_MaterialComponents_Light_Dialog_Bridge = 0x7f120090;
        public static final int Base_V21_ThemeOverlay_AppCompat_Dialog = 0x7f12009e;
        public static final int Base_V21_Theme_AppCompat = 0x7f120096;
        public static final int Base_V21_Theme_AppCompat_Dialog = 0x7f120097;
        public static final int Base_V21_Theme_AppCompat_Light = 0x7f120098;
        public static final int Base_V21_Theme_AppCompat_Light_Dialog = 0x7f120099;
        public static final int Base_V21_Theme_MaterialComponents = 0x7f12009a;
        public static final int Base_V21_Theme_MaterialComponents_Dialog = 0x7f12009b;
        public static final int Base_V21_Theme_MaterialComponents_Light = 0x7f12009c;
        public static final int Base_V21_Theme_MaterialComponents_Light_Dialog = 0x7f12009d;
        public static final int Base_V22_Theme_AppCompat = 0x7f1200a1;
        public static final int Base_V22_Theme_AppCompat_Light = 0x7f1200a2;
        public static final int Base_V23_Theme_AppCompat = 0x7f1200a3;
        public static final int Base_V23_Theme_AppCompat_Light = 0x7f1200a4;
        public static final int Base_V26_Theme_AppCompat = 0x7f1200a9;
        public static final int Base_V26_Theme_AppCompat_Light = 0x7f1200aa;
        public static final int Base_V26_Widget_AppCompat_Toolbar = 0x7f1200ab;
        public static final int Base_V28_Theme_AppCompat = 0x7f1200ac;
        public static final int Base_V28_Theme_AppCompat_Light = 0x7f1200ad;
        public static final int Base_V7_ThemeOverlay_AppCompat_Dialog = 0x7f1200b2;
        public static final int Base_V7_Theme_AppCompat = 0x7f1200ae;
        public static final int Base_V7_Theme_AppCompat_Dialog = 0x7f1200af;
        public static final int Base_V7_Theme_AppCompat_Light = 0x7f1200b0;
        public static final int Base_V7_Theme_AppCompat_Light_Dialog = 0x7f1200b1;
        public static final int Base_V7_Widget_AppCompat_AutoCompleteTextView = 0x7f1200b3;
        public static final int Base_V7_Widget_AppCompat_EditText = 0x7f1200b4;
        public static final int Base_V7_Widget_AppCompat_Toolbar = 0x7f1200b5;
        public static final int Base_Widget_AppCompat_ActionBar = 0x7f1200b6;
        public static final int Base_Widget_AppCompat_ActionBar_Solid = 0x7f1200b7;
        public static final int Base_Widget_AppCompat_ActionBar_TabBar = 0x7f1200b8;
        public static final int Base_Widget_AppCompat_ActionBar_TabText = 0x7f1200b9;
        public static final int Base_Widget_AppCompat_ActionBar_TabView = 0x7f1200ba;
        public static final int Base_Widget_AppCompat_ActionButton = 0x7f1200bb;
        public static final int Base_Widget_AppCompat_ActionButton_CloseMode = 0x7f1200bc;
        public static final int Base_Widget_AppCompat_ActionButton_Overflow = 0x7f1200bd;
        public static final int Base_Widget_AppCompat_ActionMode = 0x7f1200be;
        public static final int Base_Widget_AppCompat_ActivityChooserView = 0x7f1200bf;
        public static final int Base_Widget_AppCompat_AutoCompleteTextView = 0x7f1200c0;
        public static final int Base_Widget_AppCompat_Button = 0x7f1200c1;
        public static final int Base_Widget_AppCompat_ButtonBar = 0x7f1200c7;
        public static final int Base_Widget_AppCompat_ButtonBar_AlertDialog = 0x7f1200c8;
        public static final int Base_Widget_AppCompat_Button_Borderless = 0x7f1200c2;
        public static final int Base_Widget_AppCompat_Button_Borderless_Colored = 0x7f1200c3;
        public static final int Base_Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f1200c4;
        public static final int Base_Widget_AppCompat_Button_Colored = 0x7f1200c5;
        public static final int Base_Widget_AppCompat_Button_Small = 0x7f1200c6;
        public static final int Base_Widget_AppCompat_CompoundButton_CheckBox = 0x7f1200c9;
        public static final int Base_Widget_AppCompat_CompoundButton_RadioButton = 0x7f1200ca;
        public static final int Base_Widget_AppCompat_CompoundButton_Switch = 0x7f1200cb;
        public static final int Base_Widget_AppCompat_DrawerArrowToggle = 0x7f1200cc;
        public static final int Base_Widget_AppCompat_DrawerArrowToggle_Common = 0x7f1200cd;
        public static final int Base_Widget_AppCompat_DropDownItem_Spinner = 0x7f1200ce;
        public static final int Base_Widget_AppCompat_EditText = 0x7f1200cf;
        public static final int Base_Widget_AppCompat_ImageButton = 0x7f1200d0;
        public static final int Base_Widget_AppCompat_Light_ActionBar = 0x7f1200d1;
        public static final int Base_Widget_AppCompat_Light_ActionBar_Solid = 0x7f1200d2;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabBar = 0x7f1200d3;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabText = 0x7f1200d4;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f1200d5;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabView = 0x7f1200d6;
        public static final int Base_Widget_AppCompat_Light_PopupMenu = 0x7f1200d7;
        public static final int Base_Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f1200d8;
        public static final int Base_Widget_AppCompat_ListMenuView = 0x7f1200d9;
        public static final int Base_Widget_AppCompat_ListPopupWindow = 0x7f1200da;
        public static final int Base_Widget_AppCompat_ListView = 0x7f1200db;
        public static final int Base_Widget_AppCompat_ListView_DropDown = 0x7f1200dc;
        public static final int Base_Widget_AppCompat_ListView_Menu = 0x7f1200dd;
        public static final int Base_Widget_AppCompat_PopupMenu = 0x7f1200de;
        public static final int Base_Widget_AppCompat_PopupMenu_Overflow = 0x7f1200df;
        public static final int Base_Widget_AppCompat_PopupWindow = 0x7f1200e0;
        public static final int Base_Widget_AppCompat_ProgressBar = 0x7f1200e1;
        public static final int Base_Widget_AppCompat_ProgressBar_Horizontal = 0x7f1200e2;
        public static final int Base_Widget_AppCompat_RatingBar = 0x7f1200e3;
        public static final int Base_Widget_AppCompat_RatingBar_Indicator = 0x7f1200e4;
        public static final int Base_Widget_AppCompat_RatingBar_Small = 0x7f1200e5;
        public static final int Base_Widget_AppCompat_SearchView = 0x7f1200e6;
        public static final int Base_Widget_AppCompat_SearchView_ActionBar = 0x7f1200e7;
        public static final int Base_Widget_AppCompat_SeekBar = 0x7f1200e8;
        public static final int Base_Widget_AppCompat_SeekBar_Discrete = 0x7f1200e9;
        public static final int Base_Widget_AppCompat_Spinner = 0x7f1200ea;
        public static final int Base_Widget_AppCompat_Spinner_Underlined = 0x7f1200eb;
        public static final int Base_Widget_AppCompat_TextView = 0x7f1200ec;
        public static final int Base_Widget_AppCompat_TextView_SpinnerItem = 0x7f1200ed;
        public static final int Base_Widget_AppCompat_Toolbar = 0x7f1200ee;
        public static final int Base_Widget_AppCompat_Toolbar_Button_Navigation = 0x7f1200ef;
        public static final int Base_Widget_Design_TabLayout = 0x7f1200f0;
        public static final int Base_Widget_MaterialComponents_AutoCompleteTextView = 0x7f120103;
        public static final int Base_Widget_MaterialComponents_CheckedTextView = 0x7f120104;
        public static final int Base_Widget_MaterialComponents_Chip = 0x7f120105;
        public static final int Base_Widget_MaterialComponents_PopupMenu = 0x7f120108;
        public static final int Base_Widget_MaterialComponents_PopupMenu_ContextMenu = 0x7f120109;
        public static final int Base_Widget_MaterialComponents_PopupMenu_ListPopupWindow = 0x7f12010a;
        public static final int Base_Widget_MaterialComponents_PopupMenu_Overflow = 0x7f12010b;
        public static final int Base_Widget_MaterialComponents_Slider = 0x7f12010c;
        public static final int Base_Widget_MaterialComponents_TextInputEditText = 0x7f12010e;
        public static final int Base_Widget_MaterialComponents_TextInputLayout = 0x7f12010f;
        public static final int Base_Widget_MaterialComponents_TextView = 0x7f120110;
        public static final int CardView = 0x7f120148;
        public static final int CardView_Dark = 0x7f120149;
        public static final int CardView_Light = 0x7f12014a;
        public static final int EmptyTheme = 0x7f12014e;
        public static final int MaterialAlertDialog_MaterialComponents = 0x7f120158;
        public static final int MaterialAlertDialog_MaterialComponents_Body_Text = 0x7f120159;
        public static final int MaterialAlertDialog_MaterialComponents_Picker_Date_Calendar = 0x7f12015a;
        public static final int MaterialAlertDialog_MaterialComponents_Picker_Date_Spinner = 0x7f12015b;
        public static final int MaterialAlertDialog_MaterialComponents_Title_Icon = 0x7f12015c;
        public static final int MaterialAlertDialog_MaterialComponents_Title_Icon_CenterStacked = 0x7f12015d;
        public static final int MaterialAlertDialog_MaterialComponents_Title_Panel = 0x7f12015e;
        public static final int MaterialAlertDialog_MaterialComponents_Title_Panel_CenterStacked = 0x7f12015f;
        public static final int MaterialAlertDialog_MaterialComponents_Title_Text = 0x7f120160;
        public static final int MaterialAlertDialog_MaterialComponents_Title_Text_CenterStacked = 0x7f120161;
        public static final int Platform_AppCompat = 0x7f120164;
        public static final int Platform_AppCompat_Light = 0x7f120165;
        public static final int Platform_MaterialComponents = 0x7f120166;
        public static final int Platform_MaterialComponents_Dialog = 0x7f120167;
        public static final int Platform_MaterialComponents_Light = 0x7f120168;
        public static final int Platform_MaterialComponents_Light_Dialog = 0x7f120169;
        public static final int Platform_ThemeOverlay_AppCompat = 0x7f12016a;
        public static final int Platform_ThemeOverlay_AppCompat_Dark = 0x7f12016b;
        public static final int Platform_ThemeOverlay_AppCompat_Light = 0x7f12016c;
        public static final int Platform_V21_AppCompat = 0x7f12016d;
        public static final int Platform_V21_AppCompat_Light = 0x7f12016e;
        public static final int Platform_V25_AppCompat = 0x7f12016f;
        public static final int Platform_V25_AppCompat_Light = 0x7f120170;
        public static final int Platform_Widget_AppCompat_Spinner = 0x7f120171;
        public static final int RtlOverlay_DialogWindowTitle_AppCompat = 0x7f120173;
        public static final int RtlOverlay_Widget_AppCompat_ActionBar_TitleItem = 0x7f120174;
        public static final int RtlOverlay_Widget_AppCompat_DialogTitle_Icon = 0x7f120175;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem = 0x7f120176;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup = 0x7f120177;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut = 0x7f120178;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow = 0x7f120179;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Text = 0x7f12017a;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Title = 0x7f12017b;
        public static final int RtlOverlay_Widget_AppCompat_SearchView_MagIcon = 0x7f120181;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown = 0x7f12017c;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 = 0x7f12017d;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 = 0x7f12017e;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Query = 0x7f12017f;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Text = 0x7f120180;
        public static final int RtlUnderlay_Widget_AppCompat_ActionButton = 0x7f120182;
        public static final int RtlUnderlay_Widget_AppCompat_ActionButton_Overflow = 0x7f120183;
        public static final int ShapeAppearanceOverlay = 0x7f1201a2;
        public static final int ShapeAppearanceOverlay_BottomLeftDifferentCornerSize = 0x7f1201a3;
        public static final int ShapeAppearanceOverlay_BottomRightCut = 0x7f1201a4;
        public static final int ShapeAppearanceOverlay_Cut = 0x7f1201a5;
        public static final int ShapeAppearanceOverlay_DifferentCornerSize = 0x7f1201a6;
        public static final int ShapeAppearanceOverlay_MaterialComponents_BottomSheet = 0x7f1201ad;
        public static final int ShapeAppearanceOverlay_MaterialComponents_Chip = 0x7f1201ae;
        public static final int ShapeAppearanceOverlay_MaterialComponents_ExtendedFloatingActionButton = 0x7f1201af;
        public static final int ShapeAppearanceOverlay_MaterialComponents_FloatingActionButton = 0x7f1201b0;
        public static final int ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day = 0x7f1201b1;
        public static final int ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Window_Fullscreen = 0x7f1201b2;
        public static final int ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Year = 0x7f1201b3;
        public static final int ShapeAppearanceOverlay_MaterialComponents_TextInputLayout_FilledBox = 0x7f1201b4;
        public static final int ShapeAppearanceOverlay_TopLeftCut = 0x7f1201b5;
        public static final int ShapeAppearanceOverlay_TopRightDifferentCornerSize = 0x7f1201b6;
        public static final int ShapeAppearance_MaterialComponents = 0x7f12019c;
        public static final int ShapeAppearance_MaterialComponents_LargeComponent = 0x7f12019d;
        public static final int ShapeAppearance_MaterialComponents_MediumComponent = 0x7f12019e;
        public static final int ShapeAppearance_MaterialComponents_SmallComponent = 0x7f12019f;
        public static final int ShapeAppearance_MaterialComponents_Test = 0x7f1201a0;
        public static final int ShapeAppearance_MaterialComponents_Tooltip = 0x7f1201a1;
        public static final int TestStyleWithLineHeight = 0x7f1201c3;
        public static final int TestStyleWithLineHeightAppearance = 0x7f1201c4;
        public static final int TestStyleWithThemeLineHeightAttribute = 0x7f1201c5;
        public static final int TestStyleWithoutLineHeight = 0x7f1201c6;
        public static final int TestThemeWithLineHeight = 0x7f1201c7;
        public static final int TestThemeWithLineHeightDisabled = 0x7f1201c8;
        public static final int Test_ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day = 0x7f1201be;
        public static final int Test_Theme_MaterialComponents_MaterialCalendar = 0x7f1201bf;
        public static final int Test_Widget_MaterialComponents_MaterialCalendar = 0x7f1201c0;
        public static final int Test_Widget_MaterialComponents_MaterialCalendar_Day = 0x7f1201c1;
        public static final int Test_Widget_MaterialComponents_MaterialCalendar_Day_Selected = 0x7f1201c2;
        public static final int TextAppearance_AppCompat = 0x7f1201c9;
        public static final int TextAppearance_AppCompat_Body1 = 0x7f1201ca;
        public static final int TextAppearance_AppCompat_Body2 = 0x7f1201cb;
        public static final int TextAppearance_AppCompat_Button = 0x7f1201cc;
        public static final int TextAppearance_AppCompat_Caption = 0x7f1201cd;
        public static final int TextAppearance_AppCompat_Display1 = 0x7f1201ce;
        public static final int TextAppearance_AppCompat_Display2 = 0x7f1201cf;
        public static final int TextAppearance_AppCompat_Display3 = 0x7f1201d0;
        public static final int TextAppearance_AppCompat_Display4 = 0x7f1201d1;
        public static final int TextAppearance_AppCompat_Headline = 0x7f1201d2;
        public static final int TextAppearance_AppCompat_Inverse = 0x7f1201d3;
        public static final int TextAppearance_AppCompat_Large = 0x7f1201d4;
        public static final int TextAppearance_AppCompat_Large_Inverse = 0x7f1201d5;
        public static final int TextAppearance_AppCompat_Light_SearchResult_Subtitle = 0x7f1201d6;
        public static final int TextAppearance_AppCompat_Light_SearchResult_Title = 0x7f1201d7;
        public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f1201d8;
        public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f1201d9;
        public static final int TextAppearance_AppCompat_Medium = 0x7f1201da;
        public static final int TextAppearance_AppCompat_Medium_Inverse = 0x7f1201db;
        public static final int TextAppearance_AppCompat_Menu = 0x7f1201dc;
        public static final int TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f1201dd;
        public static final int TextAppearance_AppCompat_SearchResult_Title = 0x7f1201de;
        public static final int TextAppearance_AppCompat_Small = 0x7f1201df;
        public static final int TextAppearance_AppCompat_Small_Inverse = 0x7f1201e0;
        public static final int TextAppearance_AppCompat_Subhead = 0x7f1201e1;
        public static final int TextAppearance_AppCompat_Subhead_Inverse = 0x7f1201e2;
        public static final int TextAppearance_AppCompat_Title = 0x7f1201e3;
        public static final int TextAppearance_AppCompat_Title_Inverse = 0x7f1201e4;
        public static final int TextAppearance_AppCompat_Tooltip = 0x7f1201e5;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f1201e6;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f1201e7;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f1201e8;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f1201e9;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f1201ea;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f1201eb;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse = 0x7f1201ec;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f1201ed;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse = 0x7f1201ee;
        public static final int TextAppearance_AppCompat_Widget_Button = 0x7f1201ef;
        public static final int TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f1201f0;
        public static final int TextAppearance_AppCompat_Widget_Button_Colored = 0x7f1201f1;
        public static final int TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f1201f2;
        public static final int TextAppearance_AppCompat_Widget_DropDownItem = 0x7f1201f3;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f1201f4;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f1201f5;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f1201f6;
        public static final int TextAppearance_AppCompat_Widget_Switch = 0x7f1201f7;
        public static final int TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f1201f8;
        public static final int TextAppearance_Compat_Notification = 0x7f1201f9;
        public static final int TextAppearance_Compat_Notification_Info = 0x7f1201fa;
        public static final int TextAppearance_Compat_Notification_Line2 = 0x7f1201fb;
        public static final int TextAppearance_Compat_Notification_Time = 0x7f1201fc;
        public static final int TextAppearance_Compat_Notification_Title = 0x7f1201fd;
        public static final int TextAppearance_Design_CollapsingToolbar_Expanded = 0x7f1201fe;
        public static final int TextAppearance_Design_Counter = 0x7f1201ff;
        public static final int TextAppearance_Design_Counter_Overflow = 0x7f120200;
        public static final int TextAppearance_Design_Error = 0x7f120201;
        public static final int TextAppearance_Design_HelperText = 0x7f120202;
        public static final int TextAppearance_Design_Hint = 0x7f120203;
        public static final int TextAppearance_Design_Placeholder = 0x7f120204;
        public static final int TextAppearance_Design_Prefix = 0x7f120205;
        public static final int TextAppearance_Design_Snackbar_Message = 0x7f120206;
        public static final int TextAppearance_Design_Suffix = 0x7f120207;
        public static final int TextAppearance_Design_Tab = 0x7f120208;
        public static final int TextAppearance_MaterialComponents_Badge = 0x7f12022a;
        public static final int TextAppearance_MaterialComponents_Body1 = 0x7f12022b;
        public static final int TextAppearance_MaterialComponents_Body2 = 0x7f12022c;
        public static final int TextAppearance_MaterialComponents_Button = 0x7f12022d;
        public static final int TextAppearance_MaterialComponents_Caption = 0x7f12022e;
        public static final int TextAppearance_MaterialComponents_Chip = 0x7f12022f;
        public static final int TextAppearance_MaterialComponents_Headline1 = 0x7f120230;
        public static final int TextAppearance_MaterialComponents_Headline2 = 0x7f120231;
        public static final int TextAppearance_MaterialComponents_Headline3 = 0x7f120232;
        public static final int TextAppearance_MaterialComponents_Headline4 = 0x7f120233;
        public static final int TextAppearance_MaterialComponents_Headline5 = 0x7f120234;
        public static final int TextAppearance_MaterialComponents_Headline6 = 0x7f120235;
        public static final int TextAppearance_MaterialComponents_Overline = 0x7f120236;
        public static final int TextAppearance_MaterialComponents_Subtitle1 = 0x7f120237;
        public static final int TextAppearance_MaterialComponents_Subtitle2 = 0x7f120238;
        public static final int TextAppearance_MaterialComponents_Tooltip = 0x7f12023a;
        public static final int TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f12023e;
        public static final int TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f12023f;
        public static final int TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f120240;
        public static final int ThemeOverlayColorAccentRed = 0x7f12030c;
        public static final int ThemeOverlay_AppCompat = 0x7f1202a9;
        public static final int ThemeOverlay_AppCompat_ActionBar = 0x7f1202aa;
        public static final int ThemeOverlay_AppCompat_Dark = 0x7f1202ab;
        public static final int ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f1202ac;
        public static final int ThemeOverlay_AppCompat_DayNight = 0x7f1202ad;
        public static final int ThemeOverlay_AppCompat_DayNight_ActionBar = 0x7f1202ae;
        public static final int ThemeOverlay_AppCompat_Dialog = 0x7f1202af;
        public static final int ThemeOverlay_AppCompat_Dialog_Alert = 0x7f1202b0;
        public static final int ThemeOverlay_AppCompat_Light = 0x7f1202b1;
        public static final int ThemeOverlay_Design_TextInputEditText = 0x7f1202b2;
        public static final int ThemeOverlay_MaterialComponents = 0x7f1202e4;
        public static final int ThemeOverlay_MaterialComponents_ActionBar = 0x7f1202e5;
        public static final int ThemeOverlay_MaterialComponents_ActionBar_Primary = 0x7f1202e6;
        public static final int ThemeOverlay_MaterialComponents_ActionBar_Surface = 0x7f1202e7;
        public static final int ThemeOverlay_MaterialComponents_AutoCompleteTextView = 0x7f1202e8;
        public static final int ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox = 0x7f1202e9;
        public static final int ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox_Dense = 0x7f1202ea;
        public static final int ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox = 0x7f1202eb;
        public static final int ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense = 0x7f1202ec;
        public static final int ThemeOverlay_MaterialComponents_BottomAppBar_Primary = 0x7f1202ed;
        public static final int ThemeOverlay_MaterialComponents_BottomAppBar_Surface = 0x7f1202ee;
        public static final int ThemeOverlay_MaterialComponents_BottomSheetDialog = 0x7f1202ef;
        public static final int ThemeOverlay_MaterialComponents_Dark = 0x7f1202f0;
        public static final int ThemeOverlay_MaterialComponents_Dark_ActionBar = 0x7f1202f1;
        public static final int ThemeOverlay_MaterialComponents_DayNight_BottomSheetDialog = 0x7f1202f2;
        public static final int ThemeOverlay_MaterialComponents_Dialog = 0x7f1202f3;
        public static final int ThemeOverlay_MaterialComponents_Dialog_Alert = 0x7f1202f4;
        public static final int ThemeOverlay_MaterialComponents_Dialog_Alert_Framework = 0x7f1202f5;
        public static final int ThemeOverlay_MaterialComponents_Light = 0x7f1202f6;
        public static final int ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework = 0x7f1202f7;
        public static final int ThemeOverlay_MaterialComponents_MaterialAlertDialog = 0x7f1202f8;
        public static final int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Centered = 0x7f1202f9;
        public static final int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date = 0x7f1202fa;
        public static final int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Calendar = 0x7f1202fb;
        public static final int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text = 0x7f1202fc;
        public static final int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text_Day = 0x7f1202fd;
        public static final int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Spinner = 0x7f1202fe;
        public static final int ThemeOverlay_MaterialComponents_MaterialCalendar = 0x7f1202ff;
        public static final int ThemeOverlay_MaterialComponents_MaterialCalendar_Fullscreen = 0x7f120300;
        public static final int ThemeOverlay_MaterialComponents_TextInputEditText = 0x7f120301;
        public static final int ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox = 0x7f120302;
        public static final int ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox_Dense = 0x7f120303;
        public static final int ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox = 0x7f120304;
        public static final int ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox_Dense = 0x7f120305;
        public static final int ThemeOverlay_MaterialComponents_Toolbar_Primary = 0x7f12030a;
        public static final int ThemeOverlay_MaterialComponents_Toolbar_Surface = 0x7f12030b;
        public static final int Theme_AppCompat = 0x7f120241;
        public static final int Theme_AppCompat_CompactMenu = 0x7f120242;
        public static final int Theme_AppCompat_DayNight = 0x7f120243;
        public static final int Theme_AppCompat_DayNight_DarkActionBar = 0x7f120244;
        public static final int Theme_AppCompat_DayNight_Dialog = 0x7f120245;
        public static final int Theme_AppCompat_DayNight_DialogWhenLarge = 0x7f120248;
        public static final int Theme_AppCompat_DayNight_Dialog_Alert = 0x7f120246;
        public static final int Theme_AppCompat_DayNight_Dialog_MinWidth = 0x7f120247;
        public static final int Theme_AppCompat_DayNight_NoActionBar = 0x7f120249;
        public static final int Theme_AppCompat_Dialog = 0x7f12024a;
        public static final int Theme_AppCompat_DialogWhenLarge = 0x7f12024d;
        public static final int Theme_AppCompat_Dialog_Alert = 0x7f12024b;
        public static final int Theme_AppCompat_Dialog_MinWidth = 0x7f12024c;
        public static final int Theme_AppCompat_Empty = 0x7f12024e;
        public static final int Theme_AppCompat_Light = 0x7f12024f;
        public static final int Theme_AppCompat_Light_DarkActionBar = 0x7f120250;
        public static final int Theme_AppCompat_Light_Dialog = 0x7f120251;
        public static final int Theme_AppCompat_Light_DialogWhenLarge = 0x7f120254;
        public static final int Theme_AppCompat_Light_Dialog_Alert = 0x7f120252;
        public static final int Theme_AppCompat_Light_Dialog_MinWidth = 0x7f120253;
        public static final int Theme_AppCompat_Light_NoActionBar = 0x7f120255;
        public static final int Theme_AppCompat_NoActionBar = 0x7f120256;
        public static final int Theme_Design = 0x7f120258;
        public static final int Theme_Design_BottomSheetDialog = 0x7f120259;
        public static final int Theme_Design_Light = 0x7f12025a;
        public static final int Theme_Design_Light_BottomSheetDialog = 0x7f12025b;
        public static final int Theme_Design_Light_NoActionBar = 0x7f12025c;
        public static final int Theme_Design_NoActionBar = 0x7f12025d;
        public static final int Theme_MaterialComponents = 0x7f120277;
        public static final int Theme_MaterialComponents_BottomSheetDialog = 0x7f120278;
        public static final int Theme_MaterialComponents_Bridge = 0x7f120279;
        public static final int Theme_MaterialComponents_CompactMenu = 0x7f12027a;
        public static final int Theme_MaterialComponents_DayNight = 0x7f12027b;
        public static final int Theme_MaterialComponents_DayNight_BottomSheetDialog = 0x7f12027c;
        public static final int Theme_MaterialComponents_DayNight_Bridge = 0x7f12027d;
        public static final int Theme_MaterialComponents_DayNight_DarkActionBar = 0x7f12027e;
        public static final int Theme_MaterialComponents_DayNight_DarkActionBar_Bridge = 0x7f12027f;
        public static final int Theme_MaterialComponents_DayNight_Dialog = 0x7f120280;
        public static final int Theme_MaterialComponents_DayNight_DialogWhenLarge = 0x7f120288;
        public static final int Theme_MaterialComponents_DayNight_Dialog_Alert = 0x7f120281;
        public static final int Theme_MaterialComponents_DayNight_Dialog_Alert_Bridge = 0x7f120282;
        public static final int Theme_MaterialComponents_DayNight_Dialog_Bridge = 0x7f120283;
        public static final int Theme_MaterialComponents_DayNight_Dialog_FixedSize = 0x7f120284;
        public static final int Theme_MaterialComponents_DayNight_Dialog_FixedSize_Bridge = 0x7f120285;
        public static final int Theme_MaterialComponents_DayNight_Dialog_MinWidth = 0x7f120286;
        public static final int Theme_MaterialComponents_DayNight_Dialog_MinWidth_Bridge = 0x7f120287;
        public static final int Theme_MaterialComponents_DayNight_NoActionBar = 0x7f120289;
        public static final int Theme_MaterialComponents_DayNight_NoActionBar_Bridge = 0x7f12028a;
        public static final int Theme_MaterialComponents_Dialog = 0x7f12028b;
        public static final int Theme_MaterialComponents_DialogWhenLarge = 0x7f120293;
        public static final int Theme_MaterialComponents_Dialog_Alert = 0x7f12028c;
        public static final int Theme_MaterialComponents_Dialog_Alert_Bridge = 0x7f12028d;
        public static final int Theme_MaterialComponents_Dialog_Bridge = 0x7f12028e;
        public static final int Theme_MaterialComponents_Dialog_FixedSize = 0x7f12028f;
        public static final int Theme_MaterialComponents_Dialog_FixedSize_Bridge = 0x7f120290;
        public static final int Theme_MaterialComponents_Dialog_MinWidth = 0x7f120291;
        public static final int Theme_MaterialComponents_Dialog_MinWidth_Bridge = 0x7f120292;
        public static final int Theme_MaterialComponents_Light = 0x7f120294;
        public static final int Theme_MaterialComponents_Light_BarSize = 0x7f120295;
        public static final int Theme_MaterialComponents_Light_BottomSheetDialog = 0x7f120296;
        public static final int Theme_MaterialComponents_Light_Bridge = 0x7f120297;
        public static final int Theme_MaterialComponents_Light_DarkActionBar = 0x7f120298;
        public static final int Theme_MaterialComponents_Light_DarkActionBar_Bridge = 0x7f120299;
        public static final int Theme_MaterialComponents_Light_Dialog = 0x7f12029a;
        public static final int Theme_MaterialComponents_Light_DialogWhenLarge = 0x7f1202a2;
        public static final int Theme_MaterialComponents_Light_Dialog_Alert = 0x7f12029b;
        public static final int Theme_MaterialComponents_Light_Dialog_Alert_Bridge = 0x7f12029c;
        public static final int Theme_MaterialComponents_Light_Dialog_Bridge = 0x7f12029d;
        public static final int Theme_MaterialComponents_Light_Dialog_FixedSize = 0x7f12029e;
        public static final int Theme_MaterialComponents_Light_Dialog_FixedSize_Bridge = 0x7f12029f;
        public static final int Theme_MaterialComponents_Light_Dialog_MinWidth = 0x7f1202a0;
        public static final int Theme_MaterialComponents_Light_Dialog_MinWidth_Bridge = 0x7f1202a1;
        public static final int Theme_MaterialComponents_Light_LargeTouch = 0x7f1202a3;
        public static final int Theme_MaterialComponents_Light_NoActionBar = 0x7f1202a4;
        public static final int Theme_MaterialComponents_Light_NoActionBar_Bridge = 0x7f1202a5;
        public static final int Theme_MaterialComponents_NoActionBar = 0x7f1202a6;
        public static final int Theme_MaterialComponents_NoActionBar_Bridge = 0x7f1202a7;
        public static final int Widget_AppCompat_ActionBar = 0x7f120316;
        public static final int Widget_AppCompat_ActionBar_Solid = 0x7f120317;
        public static final int Widget_AppCompat_ActionBar_TabBar = 0x7f120318;
        public static final int Widget_AppCompat_ActionBar_TabText = 0x7f120319;
        public static final int Widget_AppCompat_ActionBar_TabView = 0x7f12031a;
        public static final int Widget_AppCompat_ActionButton = 0x7f12031b;
        public static final int Widget_AppCompat_ActionButton_CloseMode = 0x7f12031c;
        public static final int Widget_AppCompat_ActionButton_Overflow = 0x7f12031d;
        public static final int Widget_AppCompat_ActionMode = 0x7f12031e;
        public static final int Widget_AppCompat_ActivityChooserView = 0x7f12031f;
        public static final int Widget_AppCompat_AutoCompleteTextView = 0x7f120320;
        public static final int Widget_AppCompat_Button = 0x7f120321;
        public static final int Widget_AppCompat_ButtonBar = 0x7f120327;
        public static final int Widget_AppCompat_ButtonBar_AlertDialog = 0x7f120328;
        public static final int Widget_AppCompat_Button_Borderless = 0x7f120322;
        public static final int Widget_AppCompat_Button_Borderless_Colored = 0x7f120323;
        public static final int Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f120324;
        public static final int Widget_AppCompat_Button_Colored = 0x7f120325;
        public static final int Widget_AppCompat_Button_Small = 0x7f120326;
        public static final int Widget_AppCompat_CompoundButton_CheckBox = 0x7f120329;
        public static final int Widget_AppCompat_CompoundButton_RadioButton = 0x7f12032a;
        public static final int Widget_AppCompat_CompoundButton_Switch = 0x7f12032b;
        public static final int Widget_AppCompat_DrawerArrowToggle = 0x7f12032c;
        public static final int Widget_AppCompat_DropDownItem_Spinner = 0x7f12032d;
        public static final int Widget_AppCompat_EditText = 0x7f12032e;
        public static final int Widget_AppCompat_ImageButton = 0x7f12032f;
        public static final int Widget_AppCompat_Light_ActionBar = 0x7f120330;
        public static final int Widget_AppCompat_Light_ActionBar_Solid = 0x7f120331;
        public static final int Widget_AppCompat_Light_ActionBar_Solid_Inverse = 0x7f120332;
        public static final int Widget_AppCompat_Light_ActionBar_TabBar = 0x7f120333;
        public static final int Widget_AppCompat_Light_ActionBar_TabBar_Inverse = 0x7f120334;
        public static final int Widget_AppCompat_Light_ActionBar_TabText = 0x7f120335;
        public static final int Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f120336;
        public static final int Widget_AppCompat_Light_ActionBar_TabView = 0x7f120337;
        public static final int Widget_AppCompat_Light_ActionBar_TabView_Inverse = 0x7f120338;
        public static final int Widget_AppCompat_Light_ActionButton = 0x7f120339;
        public static final int Widget_AppCompat_Light_ActionButton_CloseMode = 0x7f12033a;
        public static final int Widget_AppCompat_Light_ActionButton_Overflow = 0x7f12033b;
        public static final int Widget_AppCompat_Light_ActionMode_Inverse = 0x7f12033c;
        public static final int Widget_AppCompat_Light_ActivityChooserView = 0x7f12033d;
        public static final int Widget_AppCompat_Light_AutoCompleteTextView = 0x7f12033e;
        public static final int Widget_AppCompat_Light_DropDownItem_Spinner = 0x7f12033f;
        public static final int Widget_AppCompat_Light_ListPopupWindow = 0x7f120340;
        public static final int Widget_AppCompat_Light_ListView_DropDown = 0x7f120341;
        public static final int Widget_AppCompat_Light_PopupMenu = 0x7f120342;
        public static final int Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f120343;
        public static final int Widget_AppCompat_Light_SearchView = 0x7f120344;
        public static final int Widget_AppCompat_Light_Spinner_DropDown_ActionBar = 0x7f120345;
        public static final int Widget_AppCompat_ListMenuView = 0x7f120346;
        public static final int Widget_AppCompat_ListPopupWindow = 0x7f120347;
        public static final int Widget_AppCompat_ListView = 0x7f120348;
        public static final int Widget_AppCompat_ListView_DropDown = 0x7f120349;
        public static final int Widget_AppCompat_ListView_Menu = 0x7f12034a;
        public static final int Widget_AppCompat_PopupMenu = 0x7f12034b;
        public static final int Widget_AppCompat_PopupMenu_Overflow = 0x7f12034c;
        public static final int Widget_AppCompat_PopupWindow = 0x7f12034d;
        public static final int Widget_AppCompat_ProgressBar = 0x7f12034e;
        public static final int Widget_AppCompat_ProgressBar_Horizontal = 0x7f12034f;
        public static final int Widget_AppCompat_RatingBar = 0x7f120350;
        public static final int Widget_AppCompat_RatingBar_Indicator = 0x7f120351;
        public static final int Widget_AppCompat_RatingBar_Small = 0x7f120352;
        public static final int Widget_AppCompat_SearchView = 0x7f120353;
        public static final int Widget_AppCompat_SearchView_ActionBar = 0x7f120354;
        public static final int Widget_AppCompat_SeekBar = 0x7f120355;
        public static final int Widget_AppCompat_SeekBar_Discrete = 0x7f120356;
        public static final int Widget_AppCompat_Spinner = 0x7f120357;
        public static final int Widget_AppCompat_Spinner_DropDown = 0x7f120358;
        public static final int Widget_AppCompat_Spinner_DropDown_ActionBar = 0x7f120359;
        public static final int Widget_AppCompat_Spinner_Underlined = 0x7f12035a;
        public static final int Widget_AppCompat_TextView = 0x7f12035b;
        public static final int Widget_AppCompat_TextView_SpinnerItem = 0x7f12035c;
        public static final int Widget_AppCompat_Toolbar = 0x7f12035d;
        public static final int Widget_AppCompat_Toolbar_Button_Navigation = 0x7f12035e;
        public static final int Widget_Compat_NotificationActionContainer = 0x7f120365;
        public static final int Widget_Compat_NotificationActionText = 0x7f120366;
        public static final int Widget_Design_AppBarLayout = 0x7f120367;
        public static final int Widget_Design_BottomNavigationView = 0x7f120368;
        public static final int Widget_Design_BottomSheet_Modal = 0x7f120369;
        public static final int Widget_Design_CollapsingToolbar = 0x7f12036a;
        public static final int Widget_Design_FloatingActionButton = 0x7f12036b;
        public static final int Widget_Design_NavigationView = 0x7f12036c;
        public static final int Widget_Design_ScrimInsetsFrameLayout = 0x7f12036d;
        public static final int Widget_Design_Snackbar = 0x7f12036e;
        public static final int Widget_Design_TabLayout = 0x7f12036f;
        public static final int Widget_Design_TextInputEditText = 0x7f120370;
        public static final int Widget_Design_TextInputLayout = 0x7f120371;
        public static final int Widget_MaterialComponents_ActionBar_Primary = 0x7f1203fa;
        public static final int Widget_MaterialComponents_ActionBar_PrimarySurface = 0x7f1203fb;
        public static final int Widget_MaterialComponents_ActionBar_Solid = 0x7f1203fc;
        public static final int Widget_MaterialComponents_ActionBar_Surface = 0x7f1203fd;
        public static final int Widget_MaterialComponents_AppBarLayout_Primary = 0x7f1203ff;
        public static final int Widget_MaterialComponents_AppBarLayout_PrimarySurface = 0x7f120400;
        public static final int Widget_MaterialComponents_AppBarLayout_Surface = 0x7f120401;
        public static final int Widget_MaterialComponents_AutoCompleteTextView_FilledBox = 0x7f120402;
        public static final int Widget_MaterialComponents_AutoCompleteTextView_FilledBox_Dense = 0x7f120403;
        public static final int Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox = 0x7f120404;
        public static final int Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense = 0x7f120405;
        public static final int Widget_MaterialComponents_Badge = 0x7f120406;
        public static final int Widget_MaterialComponents_BottomAppBar = 0x7f120407;
        public static final int Widget_MaterialComponents_BottomAppBar_Colored = 0x7f120408;
        public static final int Widget_MaterialComponents_BottomAppBar_PrimarySurface = 0x7f120409;
        public static final int Widget_MaterialComponents_BottomNavigationView = 0x7f12040a;
        public static final int Widget_MaterialComponents_BottomNavigationView_Colored = 0x7f12040b;
        public static final int Widget_MaterialComponents_BottomNavigationView_PrimarySurface = 0x7f12040c;
        public static final int Widget_MaterialComponents_BottomSheet = 0x7f12040d;
        public static final int Widget_MaterialComponents_BottomSheet_Modal = 0x7f12040e;
        public static final int Widget_MaterialComponents_Button = 0x7f12040f;
        public static final int Widget_MaterialComponents_Button_Icon = 0x7f120410;
        public static final int Widget_MaterialComponents_Button_OutlinedButton = 0x7f120411;
        public static final int Widget_MaterialComponents_Button_OutlinedButton_Icon = 0x7f120412;
        public static final int Widget_MaterialComponents_Button_TextButton = 0x7f120413;
        public static final int Widget_MaterialComponents_Button_TextButton_Dialog = 0x7f120414;
        public static final int Widget_MaterialComponents_Button_TextButton_Dialog_Flush = 0x7f120415;
        public static final int Widget_MaterialComponents_Button_TextButton_Dialog_Icon = 0x7f120416;
        public static final int Widget_MaterialComponents_Button_TextButton_Icon = 0x7f120417;
        public static final int Widget_MaterialComponents_Button_TextButton_Snackbar = 0x7f120418;
        public static final int Widget_MaterialComponents_Button_UnelevatedButton = 0x7f120419;
        public static final int Widget_MaterialComponents_Button_UnelevatedButton_Icon = 0x7f12041a;
        public static final int Widget_MaterialComponents_CardView = 0x7f12041b;
        public static final int Widget_MaterialComponents_CheckedTextView = 0x7f12041c;
        public static final int Widget_MaterialComponents_ChipGroup = 0x7f120421;
        public static final int Widget_MaterialComponents_Chip_Action = 0x7f12041d;
        public static final int Widget_MaterialComponents_Chip_Choice = 0x7f12041e;
        public static final int Widget_MaterialComponents_Chip_Entry = 0x7f12041f;
        public static final int Widget_MaterialComponents_Chip_Filter = 0x7f120420;
        public static final int Widget_MaterialComponents_CompoundButton_CheckBox = 0x7f120427;
        public static final int Widget_MaterialComponents_CompoundButton_RadioButton = 0x7f120428;
        public static final int Widget_MaterialComponents_CompoundButton_Switch = 0x7f120429;
        public static final int Widget_MaterialComponents_ExtendedFloatingActionButton = 0x7f12042a;
        public static final int Widget_MaterialComponents_ExtendedFloatingActionButton_Icon = 0x7f12042b;
        public static final int Widget_MaterialComponents_FloatingActionButton = 0x7f12042c;
        public static final int Widget_MaterialComponents_Light_ActionBar_Solid = 0x7f12042d;
        public static final int Widget_MaterialComponents_MaterialButtonToggleGroup = 0x7f12042f;
        public static final int Widget_MaterialComponents_MaterialCalendar = 0x7f120430;
        public static final int Widget_MaterialComponents_MaterialCalendar_Day = 0x7f120431;
        public static final int Widget_MaterialComponents_MaterialCalendar_DayTextView = 0x7f120436;
        public static final int Widget_MaterialComponents_MaterialCalendar_Day_Invalid = 0x7f120432;
        public static final int Widget_MaterialComponents_MaterialCalendar_Day_Selected = 0x7f120433;
        public static final int Widget_MaterialComponents_MaterialCalendar_Day_Today = 0x7f120434;
        public static final int Widget_MaterialComponents_MaterialCalendar_Fullscreen = 0x7f120437;
        public static final int Widget_MaterialComponents_MaterialCalendar_HeaderConfirmButton = 0x7f120439;
        public static final int Widget_MaterialComponents_MaterialCalendar_HeaderDivider = 0x7f12043a;
        public static final int Widget_MaterialComponents_MaterialCalendar_HeaderLayout = 0x7f12043b;
        public static final int Widget_MaterialComponents_MaterialCalendar_HeaderSelection = 0x7f12043c;
        public static final int Widget_MaterialComponents_MaterialCalendar_HeaderSelection_Fullscreen = 0x7f12043d;
        public static final int Widget_MaterialComponents_MaterialCalendar_HeaderTitle = 0x7f12043e;
        public static final int Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton = 0x7f12043f;
        public static final int Widget_MaterialComponents_MaterialCalendar_Item = 0x7f120440;
        public static final int Widget_MaterialComponents_MaterialCalendar_Year = 0x7f120443;
        public static final int Widget_MaterialComponents_MaterialCalendar_Year_Selected = 0x7f120444;
        public static final int Widget_MaterialComponents_MaterialCalendar_Year_Today = 0x7f120445;
        public static final int Widget_MaterialComponents_NavigationView = 0x7f12044d;
        public static final int Widget_MaterialComponents_PopupMenu = 0x7f12044e;
        public static final int Widget_MaterialComponents_PopupMenu_ContextMenu = 0x7f12044f;
        public static final int Widget_MaterialComponents_PopupMenu_ListPopupWindow = 0x7f120450;
        public static final int Widget_MaterialComponents_PopupMenu_Overflow = 0x7f120451;
        public static final int Widget_MaterialComponents_ShapeableImageView = 0x7f120453;
        public static final int Widget_MaterialComponents_Slider = 0x7f120454;
        public static final int Widget_MaterialComponents_Snackbar = 0x7f120455;
        public static final int Widget_MaterialComponents_Snackbar_FullWidth = 0x7f120456;
        public static final int Widget_MaterialComponents_Snackbar_TextView = 0x7f120457;
        public static final int Widget_MaterialComponents_TabLayout = 0x7f120458;
        public static final int Widget_MaterialComponents_TabLayout_Colored = 0x7f120459;
        public static final int Widget_MaterialComponents_TabLayout_PrimarySurface = 0x7f12045a;
        public static final int Widget_MaterialComponents_TextInputEditText_FilledBox = 0x7f12045b;
        public static final int Widget_MaterialComponents_TextInputEditText_FilledBox_Dense = 0x7f12045c;
        public static final int Widget_MaterialComponents_TextInputEditText_OutlinedBox = 0x7f12045d;
        public static final int Widget_MaterialComponents_TextInputEditText_OutlinedBox_Dense = 0x7f12045e;
        public static final int Widget_MaterialComponents_TextInputLayout_FilledBox = 0x7f12045f;
        public static final int Widget_MaterialComponents_TextInputLayout_FilledBox_Dense = 0x7f120460;
        public static final int Widget_MaterialComponents_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu = 0x7f120461;
        public static final int Widget_MaterialComponents_TextInputLayout_FilledBox_ExposedDropdownMenu = 0x7f120462;
        public static final int Widget_MaterialComponents_TextInputLayout_OutlinedBox = 0x7f120463;
        public static final int Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense = 0x7f120464;
        public static final int Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu = 0x7f120465;
        public static final int Widget_MaterialComponents_TextInputLayout_OutlinedBox_ExposedDropdownMenu = 0x7f120466;
        public static final int Widget_MaterialComponents_TextView = 0x7f120467;
        public static final int Widget_MaterialComponents_Toolbar = 0x7f120472;
        public static final int Widget_MaterialComponents_Toolbar_Primary = 0x7f120473;
        public static final int Widget_MaterialComponents_Toolbar_PrimarySurface = 0x7f120474;
        public static final int Widget_MaterialComponents_Toolbar_Surface = 0x7f120475;
        public static final int Widget_MaterialComponents_Tooltip = 0x7f120476;
        public static final int Widget_Support_CoordinatorLayout = 0x7f120477;

        private style() {
        }
    }

    public static final class styleable {
        public static final int ActionBarLayout_android_layout_gravity = 0x00000000;
        public static final int ActionBar_background = 0x00000000;
        public static final int ActionBar_backgroundSplit = 0x00000001;
        public static final int ActionBar_backgroundStacked = 0x00000002;
        public static final int ActionBar_contentInsetEnd = 0x00000003;
        public static final int ActionBar_contentInsetEndWithActions = 0x00000004;
        public static final int ActionBar_contentInsetLeft = 0x00000005;
        public static final int ActionBar_contentInsetRight = 0x00000006;
        public static final int ActionBar_contentInsetStart = 0x00000007;
        public static final int ActionBar_contentInsetStartWithNavigation = 0x00000008;
        public static final int ActionBar_customNavigationLayout = 0x00000009;
        public static final int ActionBar_displayOptions = 0x0000000a;
        public static final int ActionBar_divider = 0x0000000b;
        public static final int ActionBar_elevation = 0x0000000c;
        public static final int ActionBar_height = 0x0000000d;
        public static final int ActionBar_hideOnContentScroll = 0x0000000e;
        public static final int ActionBar_homeAsUpIndicator = 0x0000000f;
        public static final int ActionBar_homeLayout = 0x00000010;
        public static final int ActionBar_icon = 0x00000011;
        public static final int ActionBar_indeterminateProgressStyle = 0x00000012;
        public static final int ActionBar_itemPadding = 0x00000013;
        public static final int ActionBar_logo = 0x00000014;
        public static final int ActionBar_navigationMode = 0x00000015;
        public static final int ActionBar_popupTheme = 0x00000016;
        public static final int ActionBar_progressBarPadding = 0x00000017;
        public static final int ActionBar_progressBarStyle = 0x00000018;
        public static final int ActionBar_subtitle = 0x00000019;
        public static final int ActionBar_subtitleTextStyle = 0x0000001a;
        public static final int ActionBar_title = 0x0000001b;
        public static final int ActionBar_titleTextStyle = 0x0000001c;
        public static final int ActionMenuItemView_android_minWidth = 0x00000000;
        public static final int ActionMode_background = 0x00000000;
        public static final int ActionMode_backgroundSplit = 0x00000001;
        public static final int ActionMode_closeItemLayout = 0x00000002;
        public static final int ActionMode_height = 0x00000003;
        public static final int ActionMode_subtitleTextStyle = 0x00000004;
        public static final int ActionMode_titleTextStyle = 0x00000005;
        public static final int ActivityChooserView_expandActivityOverflowButtonDrawable = 0x00000000;
        public static final int ActivityChooserView_initialActivityCount = 0x00000001;
        public static final int AlertDialog_android_layout = 0x00000000;
        public static final int AlertDialog_buttonIconDimen = 0x00000001;
        public static final int AlertDialog_buttonPanelSideLayout = 0x00000002;
        public static final int AlertDialog_listItemLayout = 0x00000003;
        public static final int AlertDialog_listLayout = 0x00000004;
        public static final int AlertDialog_multiChoiceItemLayout = 0x00000005;
        public static final int AlertDialog_showTitle = 0x00000006;
        public static final int AlertDialog_singleChoiceItemLayout = 0x00000007;
        public static final int AnimatedStateListDrawableCompat_android_constantSize = 0x00000003;
        public static final int AnimatedStateListDrawableCompat_android_dither = 0x00000000;
        public static final int AnimatedStateListDrawableCompat_android_enterFadeDuration = 0x00000004;
        public static final int AnimatedStateListDrawableCompat_android_exitFadeDuration = 0x00000005;
        public static final int AnimatedStateListDrawableCompat_android_variablePadding = 0x00000002;
        public static final int AnimatedStateListDrawableCompat_android_visible = 0x00000001;
        public static final int AnimatedStateListDrawableItem_android_drawable = 0x00000001;
        public static final int AnimatedStateListDrawableItem_android_id = 0x00000000;
        public static final int AnimatedStateListDrawableTransition_android_drawable = 0x00000000;
        public static final int AnimatedStateListDrawableTransition_android_fromId = 0x00000002;
        public static final int AnimatedStateListDrawableTransition_android_reversible = 0x00000003;
        public static final int AnimatedStateListDrawableTransition_android_toId = 0x00000001;
        public static final int AppBarLayoutStates_state_collapsed = 0x00000000;
        public static final int AppBarLayoutStates_state_collapsible = 0x00000001;
        public static final int AppBarLayoutStates_state_liftable = 0x00000002;
        public static final int AppBarLayoutStates_state_lifted = 0x00000003;
        public static final int AppBarLayout_Layout_layout_scrollEffect = 0x00000000;
        public static final int AppBarLayout_Layout_layout_scrollFlags = 0x00000001;
        public static final int AppBarLayout_Layout_layout_scrollInterpolator = 0x00000002;
        public static final int AppBarLayout_android_background = 0x00000000;
        public static final int AppBarLayout_android_keyboardNavigationCluster = 0x00000002;
        public static final int AppBarLayout_android_touchscreenBlocksFocus = 0x00000001;
        public static final int AppBarLayout_elevation = 0x00000003;
        public static final int AppBarLayout_expanded = 0x00000004;
        public static final int AppBarLayout_liftOnScroll = 0x00000005;
        public static final int AppBarLayout_liftOnScrollTargetViewId = 0x00000006;
        public static final int AppBarLayout_statusBarForeground = 0x00000007;
        public static final int AppCompatImageView_android_src = 0x00000000;
        public static final int AppCompatImageView_srcCompat = 0x00000001;
        public static final int AppCompatImageView_tint = 0x00000002;
        public static final int AppCompatImageView_tintMode = 0x00000003;
        public static final int AppCompatSeekBar_android_thumb = 0x00000000;
        public static final int AppCompatSeekBar_tickMark = 0x00000001;
        public static final int AppCompatSeekBar_tickMarkTint = 0x00000002;
        public static final int AppCompatSeekBar_tickMarkTintMode = 0x00000003;
        public static final int AppCompatTextHelper_android_drawableBottom = 0x00000002;
        public static final int AppCompatTextHelper_android_drawableEnd = 0x00000006;
        public static final int AppCompatTextHelper_android_drawableLeft = 0x00000003;
        public static final int AppCompatTextHelper_android_drawableRight = 0x00000004;
        public static final int AppCompatTextHelper_android_drawableStart = 0x00000005;
        public static final int AppCompatTextHelper_android_drawableTop = 0x00000001;
        public static final int AppCompatTextHelper_android_textAppearance = 0x00000000;
        public static final int AppCompatTextView_android_textAppearance = 0x00000000;
        public static final int AppCompatTextView_autoSizeMaxTextSize = 0x00000001;
        public static final int AppCompatTextView_autoSizeMinTextSize = 0x00000002;
        public static final int AppCompatTextView_autoSizePresetSizes = 0x00000003;
        public static final int AppCompatTextView_autoSizeStepGranularity = 0x00000004;
        public static final int AppCompatTextView_autoSizeTextType = 0x00000005;
        public static final int AppCompatTextView_drawableBottomCompat = 0x00000006;
        public static final int AppCompatTextView_drawableEndCompat = 0x00000007;
        public static final int AppCompatTextView_drawableLeftCompat = 0x00000008;
        public static final int AppCompatTextView_drawableRightCompat = 0x00000009;
        public static final int AppCompatTextView_drawableStartCompat = 0x0000000a;
        public static final int AppCompatTextView_drawableTint = 0x0000000b;
        public static final int AppCompatTextView_drawableTintMode = 0x0000000c;
        public static final int AppCompatTextView_drawableTopCompat = 0x0000000d;
        public static final int AppCompatTextView_emojiCompatEnabled = 0x0000000e;
        public static final int AppCompatTextView_firstBaselineToTopHeight = 0x0000000f;
        public static final int AppCompatTextView_fontFamily = 0x00000010;
        public static final int AppCompatTextView_fontVariationSettings = 0x00000011;
        public static final int AppCompatTextView_lastBaselineToBottomHeight = 0x00000012;
        public static final int AppCompatTextView_lineHeight = 0x00000013;
        public static final int AppCompatTextView_textAllCaps = 0x00000014;
        public static final int AppCompatTextView_textLocale = 0x00000015;
        public static final int AppCompatTheme_actionBarDivider = 0x00000002;
        public static final int AppCompatTheme_actionBarItemBackground = 0x00000003;
        public static final int AppCompatTheme_actionBarPopupTheme = 0x00000004;
        public static final int AppCompatTheme_actionBarSize = 0x00000005;
        public static final int AppCompatTheme_actionBarSplitStyle = 0x00000006;
        public static final int AppCompatTheme_actionBarStyle = 0x00000007;
        public static final int AppCompatTheme_actionBarTabBarStyle = 0x00000008;
        public static final int AppCompatTheme_actionBarTabStyle = 0x00000009;
        public static final int AppCompatTheme_actionBarTabTextStyle = 0x0000000a;
        public static final int AppCompatTheme_actionBarTheme = 0x0000000b;
        public static final int AppCompatTheme_actionBarWidgetTheme = 0x0000000c;
        public static final int AppCompatTheme_actionButtonStyle = 0x0000000d;
        public static final int AppCompatTheme_actionDropDownStyle = 0x0000000e;
        public static final int AppCompatTheme_actionMenuTextAppearance = 0x0000000f;
        public static final int AppCompatTheme_actionMenuTextColor = 0x00000010;
        public static final int AppCompatTheme_actionModeBackground = 0x00000011;
        public static final int AppCompatTheme_actionModeCloseButtonStyle = 0x00000012;
        public static final int AppCompatTheme_actionModeCloseContentDescription = 0x00000013;
        public static final int AppCompatTheme_actionModeCloseDrawable = 0x00000014;
        public static final int AppCompatTheme_actionModeCopyDrawable = 0x00000015;
        public static final int AppCompatTheme_actionModeCutDrawable = 0x00000016;
        public static final int AppCompatTheme_actionModeFindDrawable = 0x00000017;
        public static final int AppCompatTheme_actionModePasteDrawable = 0x00000018;
        public static final int AppCompatTheme_actionModePopupWindowStyle = 0x00000019;
        public static final int AppCompatTheme_actionModeSelectAllDrawable = 0x0000001a;
        public static final int AppCompatTheme_actionModeShareDrawable = 0x0000001b;
        public static final int AppCompatTheme_actionModeSplitBackground = 0x0000001c;
        public static final int AppCompatTheme_actionModeStyle = 0x0000001d;
        public static final int AppCompatTheme_actionModeTheme = 0x0000001e;
        public static final int AppCompatTheme_actionModeWebSearchDrawable = 0x0000001f;
        public static final int AppCompatTheme_actionOverflowButtonStyle = 0x00000020;
        public static final int AppCompatTheme_actionOverflowMenuStyle = 0x00000021;
        public static final int AppCompatTheme_activityChooserViewStyle = 0x00000022;
        public static final int AppCompatTheme_alertDialogButtonGroupStyle = 0x00000023;
        public static final int AppCompatTheme_alertDialogCenterButtons = 0x00000024;
        public static final int AppCompatTheme_alertDialogStyle = 0x00000025;
        public static final int AppCompatTheme_alertDialogTheme = 0x00000026;
        public static final int AppCompatTheme_android_windowAnimationStyle = 0x00000001;
        public static final int AppCompatTheme_android_windowIsFloating = 0x00000000;
        public static final int AppCompatTheme_autoCompleteTextViewStyle = 0x00000027;
        public static final int AppCompatTheme_borderlessButtonStyle = 0x00000028;
        public static final int AppCompatTheme_buttonBarButtonStyle = 0x00000029;
        public static final int AppCompatTheme_buttonBarNegativeButtonStyle = 0x0000002a;
        public static final int AppCompatTheme_buttonBarNeutralButtonStyle = 0x0000002b;
        public static final int AppCompatTheme_buttonBarPositiveButtonStyle = 0x0000002c;
        public static final int AppCompatTheme_buttonBarStyle = 0x0000002d;
        public static final int AppCompatTheme_buttonStyle = 0x0000002e;
        public static final int AppCompatTheme_buttonStyleSmall = 0x0000002f;
        public static final int AppCompatTheme_checkboxStyle = 0x00000030;
        public static final int AppCompatTheme_checkedTextViewStyle = 0x00000031;
        public static final int AppCompatTheme_colorAccent = 0x00000032;
        public static final int AppCompatTheme_colorBackgroundFloating = 0x00000033;
        public static final int AppCompatTheme_colorButtonNormal = 0x00000034;
        public static final int AppCompatTheme_colorControlActivated = 0x00000035;
        public static final int AppCompatTheme_colorControlHighlight = 0x00000036;
        public static final int AppCompatTheme_colorControlNormal = 0x00000037;
        public static final int AppCompatTheme_colorError = 0x00000038;
        public static final int AppCompatTheme_colorPrimary = 0x00000039;
        public static final int AppCompatTheme_colorPrimaryDark = 0x0000003a;
        public static final int AppCompatTheme_colorSwitchThumbNormal = 0x0000003b;
        public static final int AppCompatTheme_controlBackground = 0x0000003c;
        public static final int AppCompatTheme_dialogCornerRadius = 0x0000003d;
        public static final int AppCompatTheme_dialogPreferredPadding = 0x0000003e;
        public static final int AppCompatTheme_dialogTheme = 0x0000003f;
        public static final int AppCompatTheme_dividerHorizontal = 0x00000040;
        public static final int AppCompatTheme_dividerVertical = 0x00000041;
        public static final int AppCompatTheme_dropDownListViewStyle = 0x00000042;
        public static final int AppCompatTheme_dropdownListPreferredItemHeight = 0x00000043;
        public static final int AppCompatTheme_editTextBackground = 0x00000044;
        public static final int AppCompatTheme_editTextColor = 0x00000045;
        public static final int AppCompatTheme_editTextStyle = 0x00000046;
        public static final int AppCompatTheme_homeAsUpIndicator = 0x00000047;
        public static final int AppCompatTheme_imageButtonStyle = 0x00000048;
        public static final int AppCompatTheme_listChoiceBackgroundIndicator = 0x00000049;
        public static final int AppCompatTheme_listChoiceIndicatorMultipleAnimated = 0x0000004a;
        public static final int AppCompatTheme_listChoiceIndicatorSingleAnimated = 0x0000004b;
        public static final int AppCompatTheme_listDividerAlertDialog = 0x0000004c;
        public static final int AppCompatTheme_listMenuViewStyle = 0x0000004d;
        public static final int AppCompatTheme_listPopupWindowStyle = 0x0000004e;
        public static final int AppCompatTheme_listPreferredItemHeight = 0x0000004f;
        public static final int AppCompatTheme_listPreferredItemHeightLarge = 0x00000050;
        public static final int AppCompatTheme_listPreferredItemHeightSmall = 0x00000051;
        public static final int AppCompatTheme_listPreferredItemPaddingEnd = 0x00000052;
        public static final int AppCompatTheme_listPreferredItemPaddingLeft = 0x00000053;
        public static final int AppCompatTheme_listPreferredItemPaddingRight = 0x00000054;
        public static final int AppCompatTheme_listPreferredItemPaddingStart = 0x00000055;
        public static final int AppCompatTheme_panelBackground = 0x00000056;
        public static final int AppCompatTheme_panelMenuListTheme = 0x00000057;
        public static final int AppCompatTheme_panelMenuListWidth = 0x00000058;
        public static final int AppCompatTheme_popupMenuStyle = 0x00000059;
        public static final int AppCompatTheme_popupWindowStyle = 0x0000005a;
        public static final int AppCompatTheme_radioButtonStyle = 0x0000005b;
        public static final int AppCompatTheme_ratingBarStyle = 0x0000005c;
        public static final int AppCompatTheme_ratingBarStyleIndicator = 0x0000005d;
        public static final int AppCompatTheme_ratingBarStyleSmall = 0x0000005e;
        public static final int AppCompatTheme_searchViewStyle = 0x0000005f;
        public static final int AppCompatTheme_seekBarStyle = 0x00000060;
        public static final int AppCompatTheme_selectableItemBackground = 0x00000061;
        public static final int AppCompatTheme_selectableItemBackgroundBorderless = 0x00000062;
        public static final int AppCompatTheme_spinnerDropDownItemStyle = 0x00000063;
        public static final int AppCompatTheme_spinnerStyle = 0x00000064;
        public static final int AppCompatTheme_switchStyle = 0x00000065;
        public static final int AppCompatTheme_textAppearanceLargePopupMenu = 0x00000066;
        public static final int AppCompatTheme_textAppearanceListItem = 0x00000067;
        public static final int AppCompatTheme_textAppearanceListItemSecondary = 0x00000068;
        public static final int AppCompatTheme_textAppearanceListItemSmall = 0x00000069;
        public static final int AppCompatTheme_textAppearancePopupMenuHeader = 0x0000006a;
        public static final int AppCompatTheme_textAppearanceSearchResultSubtitle = 0x0000006b;
        public static final int AppCompatTheme_textAppearanceSearchResultTitle = 0x0000006c;
        public static final int AppCompatTheme_textAppearanceSmallPopupMenu = 0x0000006d;
        public static final int AppCompatTheme_textColorAlertDialogListItem = 0x0000006e;
        public static final int AppCompatTheme_textColorSearchUrl = 0x0000006f;
        public static final int AppCompatTheme_toolbarNavigationButtonStyle = 0x00000070;
        public static final int AppCompatTheme_toolbarStyle = 0x00000071;
        public static final int AppCompatTheme_tooltipForegroundColor = 0x00000072;
        public static final int AppCompatTheme_tooltipFrameBackground = 0x00000073;
        public static final int AppCompatTheme_viewInflaterClass = 0x00000074;
        public static final int AppCompatTheme_windowActionBar = 0x00000075;
        public static final int AppCompatTheme_windowActionBarOverlay = 0x00000076;
        public static final int AppCompatTheme_windowActionModeOverlay = 0x00000077;
        public static final int AppCompatTheme_windowFixedHeightMajor = 0x00000078;
        public static final int AppCompatTheme_windowFixedHeightMinor = 0x00000079;
        public static final int AppCompatTheme_windowFixedWidthMajor = 0x0000007a;
        public static final int AppCompatTheme_windowFixedWidthMinor = 0x0000007b;
        public static final int AppCompatTheme_windowMinWidthMajor = 0x0000007c;
        public static final int AppCompatTheme_windowMinWidthMinor = 0x0000007d;
        public static final int AppCompatTheme_windowNoTitle = 0x0000007e;
        public static final int Badge_backgroundColor = 0x00000000;
        public static final int Badge_badgeGravity = 0x00000001;
        public static final int Badge_badgeRadius = 0x00000002;
        public static final int Badge_badgeTextColor = 0x00000003;
        public static final int Badge_badgeWidePadding = 0x00000004;
        public static final int Badge_badgeWithTextRadius = 0x00000005;
        public static final int Badge_horizontalOffset = 0x00000006;
        public static final int Badge_horizontalOffsetWithText = 0x00000007;
        public static final int Badge_maxCharacterCount = 0x00000008;
        public static final int Badge_number = 0x00000009;
        public static final int Badge_verticalOffset = 0x0000000a;
        public static final int Badge_verticalOffsetWithText = 0x0000000b;
        public static final int BottomAppBar_backgroundTint = 0x00000000;
        public static final int BottomAppBar_elevation = 0x00000001;
        public static final int BottomAppBar_fabAlignmentMode = 0x00000002;
        public static final int BottomAppBar_fabAnimationMode = 0x00000003;
        public static final int BottomAppBar_fabCradleMargin = 0x00000004;
        public static final int BottomAppBar_fabCradleRoundedCornerRadius = 0x00000005;
        public static final int BottomAppBar_fabCradleVerticalOffset = 0x00000006;
        public static final int BottomAppBar_hideOnScroll = 0x00000007;
        public static final int BottomAppBar_navigationIconTint = 0x00000008;
        public static final int BottomAppBar_paddingBottomSystemWindowInsets = 0x00000009;
        public static final int BottomAppBar_paddingLeftSystemWindowInsets = 0x0000000a;
        public static final int BottomAppBar_paddingRightSystemWindowInsets = 0x0000000b;
        public static final int BottomNavigationView_android_minHeight = 0x00000000;
        public static final int BottomNavigationView_itemHorizontalTranslationEnabled = 0x00000001;
        public static final int BottomSheetBehavior_Layout_android_elevation = 0x00000002;
        public static final int BottomSheetBehavior_Layout_android_maxHeight = 0x00000001;
        public static final int BottomSheetBehavior_Layout_android_maxWidth = 0x00000000;
        public static final int BottomSheetBehavior_Layout_backgroundTint = 0x00000003;
        public static final int BottomSheetBehavior_Layout_behavior_draggable = 0x00000004;
        public static final int BottomSheetBehavior_Layout_behavior_expandedOffset = 0x00000005;
        public static final int BottomSheetBehavior_Layout_behavior_fitToContents = 0x00000006;
        public static final int BottomSheetBehavior_Layout_behavior_halfExpandedRatio = 0x00000007;
        public static final int BottomSheetBehavior_Layout_behavior_hideable = 0x00000008;
        public static final int BottomSheetBehavior_Layout_behavior_peekHeight = 0x00000009;
        public static final int BottomSheetBehavior_Layout_behavior_saveFlags = 0x0000000a;
        public static final int BottomSheetBehavior_Layout_behavior_skipCollapsed = 0x0000000b;
        public static final int BottomSheetBehavior_Layout_gestureInsetBottomIgnored = 0x0000000c;
        public static final int BottomSheetBehavior_Layout_marginLeftSystemWindowInsets = 0x0000000d;
        public static final int BottomSheetBehavior_Layout_marginRightSystemWindowInsets = 0x0000000e;
        public static final int BottomSheetBehavior_Layout_marginTopSystemWindowInsets = 0x0000000f;
        public static final int BottomSheetBehavior_Layout_paddingBottomSystemWindowInsets = 0x00000010;
        public static final int BottomSheetBehavior_Layout_paddingLeftSystemWindowInsets = 0x00000011;
        public static final int BottomSheetBehavior_Layout_paddingRightSystemWindowInsets = 0x00000012;
        public static final int BottomSheetBehavior_Layout_paddingTopSystemWindowInsets = 0x00000013;
        public static final int BottomSheetBehavior_Layout_shapeAppearance = 0x00000014;
        public static final int BottomSheetBehavior_Layout_shapeAppearanceOverlay = 0x00000015;
        public static final int ButtonBarLayout_allowStacking = 0x00000000;
        public static final int CardView_android_minHeight = 0x00000001;
        public static final int CardView_android_minWidth = 0x00000000;
        public static final int CardView_cardBackgroundColor = 0x00000002;
        public static final int CardView_cardCornerRadius = 0x00000003;
        public static final int CardView_cardElevation = 0x00000004;
        public static final int CardView_cardMaxElevation = 0x00000005;
        public static final int CardView_cardPreventCornerOverlap = 0x00000006;
        public static final int CardView_cardUseCompatPadding = 0x00000007;
        public static final int CardView_contentPadding = 0x00000008;
        public static final int CardView_contentPaddingBottom = 0x00000009;
        public static final int CardView_contentPaddingLeft = 0x0000000a;
        public static final int CardView_contentPaddingRight = 0x0000000b;
        public static final int CardView_contentPaddingTop = 0x0000000c;
        public static final int ChipGroup_checkedChip = 0x00000000;
        public static final int ChipGroup_chipSpacing = 0x00000001;
        public static final int ChipGroup_chipSpacingHorizontal = 0x00000002;
        public static final int ChipGroup_chipSpacingVertical = 0x00000003;
        public static final int ChipGroup_selectionRequired = 0x00000004;
        public static final int ChipGroup_singleLine = 0x00000005;
        public static final int ChipGroup_singleSelection = 0x00000006;
        public static final int Chip_android_checkable = 0x00000006;
        public static final int Chip_android_ellipsize = 0x00000003;
        public static final int Chip_android_maxWidth = 0x00000004;
        public static final int Chip_android_text = 0x00000005;
        public static final int Chip_android_textAppearance = 0x00000000;
        public static final int Chip_android_textColor = 0x00000002;
        public static final int Chip_android_textSize = 0x00000001;
        public static final int Chip_checkedIcon = 0x00000007;
        public static final int Chip_checkedIconEnabled = 0x00000008;
        public static final int Chip_checkedIconTint = 0x00000009;
        public static final int Chip_checkedIconVisible = 0x0000000a;
        public static final int Chip_chipBackgroundColor = 0x0000000b;
        public static final int Chip_chipCornerRadius = 0x0000000c;
        public static final int Chip_chipEndPadding = 0x0000000d;
        public static final int Chip_chipIcon = 0x0000000e;
        public static final int Chip_chipIconEnabled = 0x0000000f;
        public static final int Chip_chipIconSize = 0x00000010;
        public static final int Chip_chipIconTint = 0x00000011;
        public static final int Chip_chipIconVisible = 0x00000012;
        public static final int Chip_chipMinHeight = 0x00000013;
        public static final int Chip_chipMinTouchTargetSize = 0x00000014;
        public static final int Chip_chipStartPadding = 0x00000015;
        public static final int Chip_chipStrokeColor = 0x00000016;
        public static final int Chip_chipStrokeWidth = 0x00000017;
        public static final int Chip_chipSurfaceColor = 0x00000018;
        public static final int Chip_closeIcon = 0x00000019;
        public static final int Chip_closeIconEnabled = 0x0000001a;
        public static final int Chip_closeIconEndPadding = 0x0000001b;
        public static final int Chip_closeIconSize = 0x0000001c;
        public static final int Chip_closeIconStartPadding = 0x0000001d;
        public static final int Chip_closeIconTint = 0x0000001e;
        public static final int Chip_closeIconVisible = 0x0000001f;
        public static final int Chip_ensureMinTouchTargetSize = 0x00000020;
        public static final int Chip_hideMotionSpec = 0x00000021;
        public static final int Chip_iconEndPadding = 0x00000022;
        public static final int Chip_iconStartPadding = 0x00000023;
        public static final int Chip_rippleColor = 0x00000024;
        public static final int Chip_shapeAppearance = 0x00000025;
        public static final int Chip_shapeAppearanceOverlay = 0x00000026;
        public static final int Chip_showMotionSpec = 0x00000027;
        public static final int Chip_textEndPadding = 0x00000028;
        public static final int Chip_textStartPadding = 0x00000029;
        public static final int CollapsingToolbarLayout_Layout_layout_collapseMode = 0x00000000;
        public static final int CollapsingToolbarLayout_Layout_layout_collapseParallaxMultiplier = 0x00000001;
        public static final int CollapsingToolbarLayout_collapsedTitleGravity = 0x00000000;
        public static final int CollapsingToolbarLayout_collapsedTitleTextAppearance = 0x00000001;
        public static final int CollapsingToolbarLayout_collapsedTitleTextColor = 0x00000002;
        public static final int CollapsingToolbarLayout_contentScrim = 0x00000003;
        public static final int CollapsingToolbarLayout_expandedTitleGravity = 0x00000004;
        public static final int CollapsingToolbarLayout_expandedTitleMargin = 0x00000005;
        public static final int CollapsingToolbarLayout_expandedTitleMarginBottom = 0x00000006;
        public static final int CollapsingToolbarLayout_expandedTitleMarginEnd = 0x00000007;
        public static final int CollapsingToolbarLayout_expandedTitleMarginStart = 0x00000008;
        public static final int CollapsingToolbarLayout_expandedTitleMarginTop = 0x00000009;
        public static final int CollapsingToolbarLayout_expandedTitleTextAppearance = 0x0000000a;
        public static final int CollapsingToolbarLayout_expandedTitleTextColor = 0x0000000b;
        public static final int CollapsingToolbarLayout_extraMultilineHeightEnabled = 0x0000000c;
        public static final int CollapsingToolbarLayout_forceApplySystemWindowInsetTop = 0x0000000d;
        public static final int CollapsingToolbarLayout_maxLines = 0x0000000e;
        public static final int CollapsingToolbarLayout_scrimAnimationDuration = 0x0000000f;
        public static final int CollapsingToolbarLayout_scrimVisibleHeightTrigger = 0x00000010;
        public static final int CollapsingToolbarLayout_statusBarScrim = 0x00000011;
        public static final int CollapsingToolbarLayout_title = 0x00000012;
        public static final int CollapsingToolbarLayout_titleCollapseMode = 0x00000013;
        public static final int CollapsingToolbarLayout_titleEnabled = 0x00000014;
        public static final int CollapsingToolbarLayout_titlePositionInterpolator = 0x00000015;
        public static final int CollapsingToolbarLayout_toolbarId = 0x00000016;
        public static final int ColorStateListItem_alpha = 0x00000003;
        public static final int ColorStateListItem_android_alpha = 0x00000001;
        public static final int ColorStateListItem_android_color = 0x00000000;
        public static final int ColorStateListItem_android_lStar = 0x00000002;
        public static final int ColorStateListItem_lStar = 0x00000004;
        public static final int CompoundButton_android_button = 0x00000000;
        public static final int CompoundButton_buttonCompat = 0x00000001;
        public static final int CompoundButton_buttonTint = 0x00000002;
        public static final int CompoundButton_buttonTintMode = 0x00000003;
        public static final int CoordinatorLayout_Layout_android_layout_gravity = 0x00000000;
        public static final int CoordinatorLayout_Layout_layout_anchor = 0x00000001;
        public static final int CoordinatorLayout_Layout_layout_anchorGravity = 0x00000002;
        public static final int CoordinatorLayout_Layout_layout_behavior = 0x00000003;
        public static final int CoordinatorLayout_Layout_layout_dodgeInsetEdges = 0x00000004;
        public static final int CoordinatorLayout_Layout_layout_insetEdge = 0x00000005;
        public static final int CoordinatorLayout_Layout_layout_keyline = 0x00000006;
        public static final int CoordinatorLayout_keylines = 0x00000000;
        public static final int CoordinatorLayout_statusBarBackground = 0x00000001;
        public static final int DrawerArrowToggle_arrowHeadLength = 0x00000000;
        public static final int DrawerArrowToggle_arrowShaftLength = 0x00000001;
        public static final int DrawerArrowToggle_barLength = 0x00000002;
        public static final int DrawerArrowToggle_color = 0x00000003;
        public static final int DrawerArrowToggle_drawableSize = 0x00000004;
        public static final int DrawerArrowToggle_gapBetweenBars = 0x00000005;
        public static final int DrawerArrowToggle_spinBars = 0x00000006;
        public static final int DrawerArrowToggle_thickness = 0x00000007;
        public static final int ExtendedFloatingActionButton_Behavior_Layout_behavior_autoHide = 0x00000000;
        public static final int ExtendedFloatingActionButton_Behavior_Layout_behavior_autoShrink = 0x00000001;
        public static final int ExtendedFloatingActionButton_collapsedSize = 0x00000000;
        public static final int ExtendedFloatingActionButton_elevation = 0x00000001;
        public static final int ExtendedFloatingActionButton_extendMotionSpec = 0x00000002;
        public static final int ExtendedFloatingActionButton_hideMotionSpec = 0x00000003;
        public static final int ExtendedFloatingActionButton_showMotionSpec = 0x00000004;
        public static final int ExtendedFloatingActionButton_shrinkMotionSpec = 0x00000005;
        public static final int FloatingActionButton_Behavior_Layout_behavior_autoHide = 0x00000000;
        public static final int FloatingActionButton_android_enabled = 0x00000000;
        public static final int FloatingActionButton_backgroundTint = 0x00000001;
        public static final int FloatingActionButton_backgroundTintMode = 0x00000002;
        public static final int FloatingActionButton_borderWidth = 0x00000003;
        public static final int FloatingActionButton_elevation = 0x00000004;
        public static final int FloatingActionButton_ensureMinTouchTargetSize = 0x00000005;
        public static final int FloatingActionButton_fabCustomSize = 0x00000006;
        public static final int FloatingActionButton_fabSize = 0x00000007;
        public static final int FloatingActionButton_hideMotionSpec = 0x00000008;
        public static final int FloatingActionButton_hoveredFocusedTranslationZ = 0x00000009;
        public static final int FloatingActionButton_maxImageSize = 0x0000000a;
        public static final int FloatingActionButton_pressedTranslationZ = 0x0000000b;
        public static final int FloatingActionButton_rippleColor = 0x0000000c;
        public static final int FloatingActionButton_shapeAppearance = 0x0000000d;
        public static final int FloatingActionButton_shapeAppearanceOverlay = 0x0000000e;
        public static final int FloatingActionButton_showMotionSpec = 0x0000000f;
        public static final int FloatingActionButton_useCompatPadding = 0x00000010;
        public static final int FlowLayout_itemSpacing = 0x00000000;
        public static final int FlowLayout_lineSpacing = 0x00000001;
        public static final int FontFamilyFont_android_font = 0x00000000;
        public static final int FontFamilyFont_android_fontStyle = 0x00000002;
        public static final int FontFamilyFont_android_fontVariationSettings = 0x00000004;
        public static final int FontFamilyFont_android_fontWeight = 0x00000001;
        public static final int FontFamilyFont_android_ttcIndex = 0x00000003;
        public static final int FontFamilyFont_font = 0x00000005;
        public static final int FontFamilyFont_fontStyle = 0x00000006;
        public static final int FontFamilyFont_fontVariationSettings = 0x00000007;
        public static final int FontFamilyFont_fontWeight = 0x00000008;
        public static final int FontFamilyFont_ttcIndex = 0x00000009;
        public static final int FontFamily_fontProviderAuthority = 0x00000000;
        public static final int FontFamily_fontProviderCerts = 0x00000001;
        public static final int FontFamily_fontProviderFetchStrategy = 0x00000002;
        public static final int FontFamily_fontProviderFetchTimeout = 0x00000003;
        public static final int FontFamily_fontProviderPackage = 0x00000004;
        public static final int FontFamily_fontProviderQuery = 0x00000005;
        public static final int FontFamily_fontProviderSystemFontFamily = 0x00000006;
        public static final int ForegroundLinearLayout_android_foreground = 0x00000000;
        public static final int ForegroundLinearLayout_android_foregroundGravity = 0x00000001;
        public static final int ForegroundLinearLayout_foregroundInsidePadding = 0x00000002;
        public static final int GradientColorItem_android_color = 0x00000000;
        public static final int GradientColorItem_android_offset = 0x00000001;
        public static final int GradientColor_android_centerColor = 0x00000007;
        public static final int GradientColor_android_centerX = 0x00000003;
        public static final int GradientColor_android_centerY = 0x00000004;
        public static final int GradientColor_android_endColor = 0x00000001;
        public static final int GradientColor_android_endX = 0x0000000a;
        public static final int GradientColor_android_endY = 0x0000000b;
        public static final int GradientColor_android_gradientRadius = 0x00000005;
        public static final int GradientColor_android_startColor = 0x00000000;
        public static final int GradientColor_android_startX = 0x00000008;
        public static final int GradientColor_android_startY = 0x00000009;
        public static final int GradientColor_android_tileMode = 0x00000006;
        public static final int GradientColor_android_type = 0x00000002;
        public static final int Insets_marginLeftSystemWindowInsets = 0x00000000;
        public static final int Insets_marginRightSystemWindowInsets = 0x00000001;
        public static final int Insets_marginTopSystemWindowInsets = 0x00000002;
        public static final int Insets_paddingBottomSystemWindowInsets = 0x00000003;
        public static final int Insets_paddingLeftSystemWindowInsets = 0x00000004;
        public static final int Insets_paddingRightSystemWindowInsets = 0x00000005;
        public static final int Insets_paddingTopSystemWindowInsets = 0x00000006;
        public static final int LinearLayoutCompat_Layout_android_layout_gravity = 0x00000000;
        public static final int LinearLayoutCompat_Layout_android_layout_height = 0x00000002;
        public static final int LinearLayoutCompat_Layout_android_layout_weight = 0x00000003;
        public static final int LinearLayoutCompat_Layout_android_layout_width = 0x00000001;
        public static final int LinearLayoutCompat_android_baselineAligned = 0x00000002;
        public static final int LinearLayoutCompat_android_baselineAlignedChildIndex = 0x00000003;
        public static final int LinearLayoutCompat_android_gravity = 0x00000000;
        public static final int LinearLayoutCompat_android_orientation = 0x00000001;
        public static final int LinearLayoutCompat_android_weightSum = 0x00000004;
        public static final int LinearLayoutCompat_divider = 0x00000005;
        public static final int LinearLayoutCompat_dividerPadding = 0x00000006;
        public static final int LinearLayoutCompat_measureWithLargestChild = 0x00000007;
        public static final int LinearLayoutCompat_showDividers = 0x00000008;
        public static final int ListPopupWindow_android_dropDownHorizontalOffset = 0x00000000;
        public static final int ListPopupWindow_android_dropDownVerticalOffset = 0x00000001;
        public static final int MaterialAlertDialogTheme_materialAlertDialogBodyTextStyle = 0x00000000;
        public static final int MaterialAlertDialogTheme_materialAlertDialogButtonSpacerVisibility = 0x00000001;
        public static final int MaterialAlertDialogTheme_materialAlertDialogTheme = 0x00000002;
        public static final int MaterialAlertDialogTheme_materialAlertDialogTitleIconStyle = 0x00000003;
        public static final int MaterialAlertDialogTheme_materialAlertDialogTitlePanelStyle = 0x00000004;
        public static final int MaterialAlertDialogTheme_materialAlertDialogTitleTextStyle = 0x00000005;
        public static final int MaterialAlertDialog_backgroundInsetBottom = 0x00000000;
        public static final int MaterialAlertDialog_backgroundInsetEnd = 0x00000001;
        public static final int MaterialAlertDialog_backgroundInsetStart = 0x00000002;
        public static final int MaterialAlertDialog_backgroundInsetTop = 0x00000003;
        public static final int MaterialAutoCompleteTextView_android_inputType = 0x00000000;
        public static final int MaterialAutoCompleteTextView_simpleItemLayout = 0x00000001;
        public static final int MaterialAutoCompleteTextView_simpleItems = 0x00000002;
        public static final int MaterialButtonToggleGroup_checkedButton = 0x00000000;
        public static final int MaterialButtonToggleGroup_selectionRequired = 0x00000001;
        public static final int MaterialButtonToggleGroup_singleSelection = 0x00000002;
        public static final int MaterialButton_android_background = 0x00000000;
        public static final int MaterialButton_android_checkable = 0x00000005;
        public static final int MaterialButton_android_insetBottom = 0x00000004;
        public static final int MaterialButton_android_insetLeft = 0x00000001;
        public static final int MaterialButton_android_insetRight = 0x00000002;
        public static final int MaterialButton_android_insetTop = 0x00000003;
        public static final int MaterialButton_backgroundTint = 0x00000006;
        public static final int MaterialButton_backgroundTintMode = 0x00000007;
        public static final int MaterialButton_cornerRadius = 0x00000008;
        public static final int MaterialButton_elevation = 0x00000009;
        public static final int MaterialButton_icon = 0x0000000a;
        public static final int MaterialButton_iconGravity = 0x0000000b;
        public static final int MaterialButton_iconPadding = 0x0000000c;
        public static final int MaterialButton_iconSize = 0x0000000d;
        public static final int MaterialButton_iconTint = 0x0000000e;
        public static final int MaterialButton_iconTintMode = 0x0000000f;
        public static final int MaterialButton_rippleColor = 0x00000010;
        public static final int MaterialButton_shapeAppearance = 0x00000011;
        public static final int MaterialButton_shapeAppearanceOverlay = 0x00000012;
        public static final int MaterialButton_strokeColor = 0x00000013;
        public static final int MaterialButton_strokeWidth = 0x00000014;
        public static final int MaterialCalendarItem_android_insetBottom = 0x00000003;
        public static final int MaterialCalendarItem_android_insetLeft = 0x00000000;
        public static final int MaterialCalendarItem_android_insetRight = 0x00000001;
        public static final int MaterialCalendarItem_android_insetTop = 0x00000002;
        public static final int MaterialCalendarItem_itemFillColor = 0x00000004;
        public static final int MaterialCalendarItem_itemShapeAppearance = 0x00000005;
        public static final int MaterialCalendarItem_itemShapeAppearanceOverlay = 0x00000006;
        public static final int MaterialCalendarItem_itemStrokeColor = 0x00000007;
        public static final int MaterialCalendarItem_itemStrokeWidth = 0x00000008;
        public static final int MaterialCalendarItem_itemTextColor = 0x00000009;
        public static final int MaterialCalendar_android_windowFullscreen = 0x00000000;
        public static final int MaterialCalendar_dayInvalidStyle = 0x00000001;
        public static final int MaterialCalendar_daySelectedStyle = 0x00000002;
        public static final int MaterialCalendar_dayStyle = 0x00000003;
        public static final int MaterialCalendar_dayTodayStyle = 0x00000004;
        public static final int MaterialCalendar_nestedScrollable = 0x00000005;
        public static final int MaterialCalendar_rangeFillColor = 0x00000006;
        public static final int MaterialCalendar_yearSelectedStyle = 0x00000007;
        public static final int MaterialCalendar_yearStyle = 0x00000008;
        public static final int MaterialCalendar_yearTodayStyle = 0x00000009;
        public static final int MaterialCardView_android_checkable = 0x00000000;
        public static final int MaterialCardView_cardForegroundColor = 0x00000001;
        public static final int MaterialCardView_checkedIcon = 0x00000002;
        public static final int MaterialCardView_checkedIconGravity = 0x00000003;
        public static final int MaterialCardView_checkedIconMargin = 0x00000004;
        public static final int MaterialCardView_checkedIconSize = 0x00000005;
        public static final int MaterialCardView_checkedIconTint = 0x00000006;
        public static final int MaterialCardView_rippleColor = 0x00000007;
        public static final int MaterialCardView_shapeAppearance = 0x00000008;
        public static final int MaterialCardView_shapeAppearanceOverlay = 0x00000009;
        public static final int MaterialCardView_state_dragged = 0x0000000a;
        public static final int MaterialCardView_strokeColor = 0x0000000b;
        public static final int MaterialCardView_strokeWidth = 0x0000000c;
        public static final int MaterialCheckBox_buttonTint = 0x00000000;
        public static final int MaterialCheckBox_centerIfNoTextEnabled = 0x00000001;
        public static final int MaterialCheckBox_useMaterialThemeColors = 0x00000002;
        public static final int MaterialRadioButton_buttonTint = 0x00000000;
        public static final int MaterialRadioButton_useMaterialThemeColors = 0x00000001;
        public static final int MaterialShape_shapeAppearance = 0x00000000;
        public static final int MaterialShape_shapeAppearanceOverlay = 0x00000001;
        public static final int MaterialTextAppearance_android_letterSpacing = 0x00000000;
        public static final int MaterialTextAppearance_android_lineHeight = 0x00000001;
        public static final int MaterialTextAppearance_lineHeight = 0x00000002;
        public static final int MaterialTextView_android_lineHeight = 0x00000001;
        public static final int MaterialTextView_android_textAppearance = 0x00000000;
        public static final int MaterialTextView_lineHeight = 0x00000002;
        public static final int MenuGroup_android_checkableBehavior = 0x00000005;
        public static final int MenuGroup_android_enabled = 0x00000000;
        public static final int MenuGroup_android_id = 0x00000001;
        public static final int MenuGroup_android_menuCategory = 0x00000003;
        public static final int MenuGroup_android_orderInCategory = 0x00000004;
        public static final int MenuGroup_android_visible = 0x00000002;
        public static final int MenuItem_actionLayout = 0x0000000d;
        public static final int MenuItem_actionProviderClass = 0x0000000e;
        public static final int MenuItem_actionViewClass = 0x0000000f;
        public static final int MenuItem_alphabeticModifiers = 0x00000010;
        public static final int MenuItem_android_alphabeticShortcut = 0x00000009;
        public static final int MenuItem_android_checkable = 0x0000000b;
        public static final int MenuItem_android_checked = 0x00000003;
        public static final int MenuItem_android_enabled = 0x00000001;
        public static final int MenuItem_android_icon = 0x00000000;
        public static final int MenuItem_android_id = 0x00000002;
        public static final int MenuItem_android_menuCategory = 0x00000005;
        public static final int MenuItem_android_numericShortcut = 0x0000000a;
        public static final int MenuItem_android_onClick = 0x0000000c;
        public static final int MenuItem_android_orderInCategory = 0x00000006;
        public static final int MenuItem_android_title = 0x00000007;
        public static final int MenuItem_android_titleCondensed = 0x00000008;
        public static final int MenuItem_android_visible = 0x00000004;
        public static final int MenuItem_contentDescription = 0x00000011;
        public static final int MenuItem_iconTint = 0x00000012;
        public static final int MenuItem_iconTintMode = 0x00000013;
        public static final int MenuItem_numericModifiers = 0x00000014;
        public static final int MenuItem_showAsAction = 0x00000015;
        public static final int MenuItem_tooltipText = 0x00000016;
        public static final int MenuView_android_headerBackground = 0x00000004;
        public static final int MenuView_android_horizontalDivider = 0x00000002;
        public static final int MenuView_android_itemBackground = 0x00000005;
        public static final int MenuView_android_itemIconDisabledAlpha = 0x00000006;
        public static final int MenuView_android_itemTextAppearance = 0x00000001;
        public static final int MenuView_android_verticalDivider = 0x00000003;
        public static final int MenuView_android_windowAnimationStyle = 0x00000000;
        public static final int MenuView_preserveIconSpacing = 0x00000007;
        public static final int MenuView_subMenuArrow = 0x00000008;
        public static final int NavigationView_android_background = 0x00000001;
        public static final int NavigationView_android_fitsSystemWindows = 0x00000002;
        public static final int NavigationView_android_layout_gravity = 0x00000000;
        public static final int NavigationView_android_maxWidth = 0x00000003;
        public static final int NavigationView_bottomInsetScrimEnabled = 0x00000004;
        public static final int NavigationView_dividerInsetEnd = 0x00000005;
        public static final int NavigationView_dividerInsetStart = 0x00000006;
        public static final int NavigationView_drawerLayoutCornerSize = 0x00000007;
        public static final int NavigationView_elevation = 0x00000008;
        public static final int NavigationView_headerLayout = 0x00000009;
        public static final int NavigationView_itemBackground = 0x0000000a;
        public static final int NavigationView_itemHorizontalPadding = 0x0000000b;
        public static final int NavigationView_itemIconPadding = 0x0000000c;
        public static final int NavigationView_itemIconSize = 0x0000000d;
        public static final int NavigationView_itemIconTint = 0x0000000e;
        public static final int NavigationView_itemMaxLines = 0x0000000f;
        public static final int NavigationView_itemRippleColor = 0x00000010;
        public static final int NavigationView_itemShapeAppearance = 0x00000011;
        public static final int NavigationView_itemShapeAppearanceOverlay = 0x00000012;
        public static final int NavigationView_itemShapeFillColor = 0x00000013;
        public static final int NavigationView_itemShapeInsetBottom = 0x00000014;
        public static final int NavigationView_itemShapeInsetEnd = 0x00000015;
        public static final int NavigationView_itemShapeInsetStart = 0x00000016;
        public static final int NavigationView_itemShapeInsetTop = 0x00000017;
        public static final int NavigationView_itemTextAppearance = 0x00000018;
        public static final int NavigationView_itemTextColor = 0x00000019;
        public static final int NavigationView_itemVerticalPadding = 0x0000001a;
        public static final int NavigationView_menu = 0x0000001b;
        public static final int NavigationView_shapeAppearance = 0x0000001c;
        public static final int NavigationView_shapeAppearanceOverlay = 0x0000001d;
        public static final int NavigationView_subheaderColor = 0x0000001e;
        public static final int NavigationView_subheaderInsetEnd = 0x0000001f;
        public static final int NavigationView_subheaderInsetStart = 0x00000020;
        public static final int NavigationView_subheaderTextAppearance = 0x00000021;
        public static final int NavigationView_topInsetScrimEnabled = 0x00000022;
        public static final int PopupWindowBackgroundState_state_above_anchor = 0x00000000;
        public static final int PopupWindow_android_popupAnimationStyle = 0x00000001;
        public static final int PopupWindow_android_popupBackground = 0x00000000;
        public static final int PopupWindow_overlapAnchor = 0x00000002;
        public static final int RangeSlider_minSeparation = 0x00000000;
        public static final int RangeSlider_values = 0x00000001;
        public static final int RecycleListView_paddingBottomNoButtons = 0x00000000;
        public static final int RecycleListView_paddingTopNoTitle = 0x00000001;
        public static final int RecyclerView_android_clipToPadding = 0x00000001;
        public static final int RecyclerView_android_descendantFocusability = 0x00000002;
        public static final int RecyclerView_android_orientation = 0x00000000;
        public static final int RecyclerView_fastScrollEnabled = 0x00000003;
        public static final int RecyclerView_fastScrollHorizontalThumbDrawable = 0x00000004;
        public static final int RecyclerView_fastScrollHorizontalTrackDrawable = 0x00000005;
        public static final int RecyclerView_fastScrollVerticalThumbDrawable = 0x00000006;
        public static final int RecyclerView_fastScrollVerticalTrackDrawable = 0x00000007;
        public static final int RecyclerView_layoutManager = 0x00000008;
        public static final int RecyclerView_reverseLayout = 0x00000009;
        public static final int RecyclerView_spanCount = 0x0000000a;
        public static final int RecyclerView_stackFromEnd = 0x0000000b;
        public static final int ScrimInsetsFrameLayout_insetForeground = 0x00000000;
        public static final int ScrollingViewBehavior_Layout_behavior_overlapTop = 0x00000000;
        public static final int SearchView_android_focusable = 0x00000000;
        public static final int SearchView_android_imeOptions = 0x00000003;
        public static final int SearchView_android_inputType = 0x00000002;
        public static final int SearchView_android_maxWidth = 0x00000001;
        public static final int SearchView_closeIcon = 0x00000004;
        public static final int SearchView_commitIcon = 0x00000005;
        public static final int SearchView_defaultQueryHint = 0x00000006;
        public static final int SearchView_goIcon = 0x00000007;
        public static final int SearchView_iconifiedByDefault = 0x00000008;
        public static final int SearchView_layout = 0x00000009;
        public static final int SearchView_queryBackground = 0x0000000a;
        public static final int SearchView_queryHint = 0x0000000b;
        public static final int SearchView_searchHintIcon = 0x0000000c;
        public static final int SearchView_searchIcon = 0x0000000d;
        public static final int SearchView_submitBackground = 0x0000000e;
        public static final int SearchView_suggestionRowLayout = 0x0000000f;
        public static final int SearchView_voiceIcon = 0x00000010;
        public static final int ShapeAppearance_cornerFamily = 0x00000000;
        public static final int ShapeAppearance_cornerFamilyBottomLeft = 0x00000001;
        public static final int ShapeAppearance_cornerFamilyBottomRight = 0x00000002;
        public static final int ShapeAppearance_cornerFamilyTopLeft = 0x00000003;
        public static final int ShapeAppearance_cornerFamilyTopRight = 0x00000004;
        public static final int ShapeAppearance_cornerSize = 0x00000005;
        public static final int ShapeAppearance_cornerSizeBottomLeft = 0x00000006;
        public static final int ShapeAppearance_cornerSizeBottomRight = 0x00000007;
        public static final int ShapeAppearance_cornerSizeTopLeft = 0x00000008;
        public static final int ShapeAppearance_cornerSizeTopRight = 0x00000009;
        public static final int ShapeableImageView_contentPadding = 0x00000000;
        public static final int ShapeableImageView_contentPaddingBottom = 0x00000001;
        public static final int ShapeableImageView_contentPaddingEnd = 0x00000002;
        public static final int ShapeableImageView_contentPaddingLeft = 0x00000003;
        public static final int ShapeableImageView_contentPaddingRight = 0x00000004;
        public static final int ShapeableImageView_contentPaddingStart = 0x00000005;
        public static final int ShapeableImageView_contentPaddingTop = 0x00000006;
        public static final int ShapeableImageView_shapeAppearance = 0x00000007;
        public static final int ShapeableImageView_shapeAppearanceOverlay = 0x00000008;
        public static final int ShapeableImageView_strokeColor = 0x00000009;
        public static final int ShapeableImageView_strokeWidth = 0x0000000a;
        public static final int Slider_android_enabled = 0x00000000;
        public static final int Slider_android_stepSize = 0x00000002;
        public static final int Slider_android_value = 0x00000001;
        public static final int Slider_android_valueFrom = 0x00000003;
        public static final int Slider_android_valueTo = 0x00000004;
        public static final int Slider_haloColor = 0x00000005;
        public static final int Slider_haloRadius = 0x00000006;
        public static final int Slider_labelBehavior = 0x00000007;
        public static final int Slider_labelStyle = 0x00000008;
        public static final int Slider_thumbColor = 0x00000009;
        public static final int Slider_thumbElevation = 0x0000000a;
        public static final int Slider_thumbRadius = 0x0000000b;
        public static final int Slider_thumbStrokeColor = 0x0000000c;
        public static final int Slider_thumbStrokeWidth = 0x0000000d;
        public static final int Slider_tickColor = 0x0000000e;
        public static final int Slider_tickColorActive = 0x0000000f;
        public static final int Slider_tickColorInactive = 0x00000010;
        public static final int Slider_tickVisible = 0x00000011;
        public static final int Slider_trackColor = 0x00000012;
        public static final int Slider_trackColorActive = 0x00000013;
        public static final int Slider_trackColorInactive = 0x00000014;
        public static final int Slider_trackHeight = 0x00000015;
        public static final int SnackbarLayout_actionTextColorAlpha = 0x00000001;
        public static final int SnackbarLayout_android_maxWidth = 0x00000000;
        public static final int SnackbarLayout_animationMode = 0x00000002;
        public static final int SnackbarLayout_backgroundOverlayColorAlpha = 0x00000003;
        public static final int SnackbarLayout_backgroundTint = 0x00000004;
        public static final int SnackbarLayout_backgroundTintMode = 0x00000005;
        public static final int SnackbarLayout_elevation = 0x00000006;
        public static final int SnackbarLayout_maxActionInlineWidth = 0x00000007;
        public static final int Snackbar_snackbarButtonStyle = 0x00000000;
        public static final int Snackbar_snackbarStyle = 0x00000001;
        public static final int Snackbar_snackbarTextViewStyle = 0x00000002;
        public static final int Spinner_android_dropDownWidth = 0x00000003;
        public static final int Spinner_android_entries = 0x00000000;
        public static final int Spinner_android_popupBackground = 0x00000001;
        public static final int Spinner_android_prompt = 0x00000002;
        public static final int Spinner_popupTheme = 0x00000004;
        public static final int StateListDrawableItem_android_drawable = 0x00000000;
        public static final int StateListDrawable_android_constantSize = 0x00000003;
        public static final int StateListDrawable_android_dither = 0x00000000;
        public static final int StateListDrawable_android_enterFadeDuration = 0x00000004;
        public static final int StateListDrawable_android_exitFadeDuration = 0x00000005;
        public static final int StateListDrawable_android_variablePadding = 0x00000002;
        public static final int StateListDrawable_android_visible = 0x00000001;
        public static final int SwitchCompat_android_textOff = 0x00000001;
        public static final int SwitchCompat_android_textOn = 0x00000000;
        public static final int SwitchCompat_android_thumb = 0x00000002;
        public static final int SwitchCompat_showText = 0x00000003;
        public static final int SwitchCompat_splitTrack = 0x00000004;
        public static final int SwitchCompat_switchMinWidth = 0x00000005;
        public static final int SwitchCompat_switchPadding = 0x00000006;
        public static final int SwitchCompat_switchTextAppearance = 0x00000007;
        public static final int SwitchCompat_thumbTextPadding = 0x00000008;
        public static final int SwitchCompat_thumbTint = 0x00000009;
        public static final int SwitchCompat_thumbTintMode = 0x0000000a;
        public static final int SwitchCompat_track = 0x0000000b;
        public static final int SwitchCompat_trackTint = 0x0000000c;
        public static final int SwitchCompat_trackTintMode = 0x0000000d;
        public static final int SwitchMaterial_useMaterialThemeColors = 0x00000000;
        public static final int TabItem_android_icon = 0x00000000;
        public static final int TabItem_android_layout = 0x00000001;
        public static final int TabItem_android_text = 0x00000002;
        public static final int TabLayout_tabBackground = 0x00000000;
        public static final int TabLayout_tabContentStart = 0x00000001;
        public static final int TabLayout_tabGravity = 0x00000002;
        public static final int TabLayout_tabIconTint = 0x00000003;
        public static final int TabLayout_tabIconTintMode = 0x00000004;
        public static final int TabLayout_tabIndicator = 0x00000005;
        public static final int TabLayout_tabIndicatorAnimationDuration = 0x00000006;
        public static final int TabLayout_tabIndicatorAnimationMode = 0x00000007;
        public static final int TabLayout_tabIndicatorColor = 0x00000008;
        public static final int TabLayout_tabIndicatorFullWidth = 0x00000009;
        public static final int TabLayout_tabIndicatorGravity = 0x0000000a;
        public static final int TabLayout_tabIndicatorHeight = 0x0000000b;
        public static final int TabLayout_tabInlineLabel = 0x0000000c;
        public static final int TabLayout_tabMaxWidth = 0x0000000d;
        public static final int TabLayout_tabMinWidth = 0x0000000e;
        public static final int TabLayout_tabMode = 0x0000000f;
        public static final int TabLayout_tabPadding = 0x00000010;
        public static final int TabLayout_tabPaddingBottom = 0x00000011;
        public static final int TabLayout_tabPaddingEnd = 0x00000012;
        public static final int TabLayout_tabPaddingStart = 0x00000013;
        public static final int TabLayout_tabPaddingTop = 0x00000014;
        public static final int TabLayout_tabRippleColor = 0x00000015;
        public static final int TabLayout_tabSelectedTextColor = 0x00000016;
        public static final int TabLayout_tabTextAppearance = 0x00000017;
        public static final int TabLayout_tabTextColor = 0x00000018;
        public static final int TabLayout_tabUnboundedRipple = 0x00000019;
        public static final int TextAppearance_android_fontFamily = 0x0000000a;
        public static final int TextAppearance_android_shadowColor = 0x00000006;
        public static final int TextAppearance_android_shadowDx = 0x00000007;
        public static final int TextAppearance_android_shadowDy = 0x00000008;
        public static final int TextAppearance_android_shadowRadius = 0x00000009;
        public static final int TextAppearance_android_textColor = 0x00000003;
        public static final int TextAppearance_android_textColorHint = 0x00000004;
        public static final int TextAppearance_android_textColorLink = 0x00000005;
        public static final int TextAppearance_android_textFontWeight = 0x0000000b;
        public static final int TextAppearance_android_textSize = 0x00000000;
        public static final int TextAppearance_android_textStyle = 0x00000002;
        public static final int TextAppearance_android_typeface = 0x00000001;
        public static final int TextAppearance_fontFamily = 0x0000000c;
        public static final int TextAppearance_fontVariationSettings = 0x0000000d;
        public static final int TextAppearance_textAllCaps = 0x0000000e;
        public static final int TextAppearance_textLocale = 0x0000000f;
        public static final int TextInputEditText_textInputLayoutFocusedRectEnabled = 0x00000000;
        public static final int TextInputLayout_android_enabled = 0x00000000;
        public static final int TextInputLayout_android_hint = 0x00000004;
        public static final int TextInputLayout_android_maxEms = 0x00000005;
        public static final int TextInputLayout_android_maxWidth = 0x00000002;
        public static final int TextInputLayout_android_minEms = 0x00000006;
        public static final int TextInputLayout_android_minWidth = 0x00000003;
        public static final int TextInputLayout_android_textColorHint = 0x00000001;
        public static final int TextInputLayout_boxBackgroundColor = 0x00000007;
        public static final int TextInputLayout_boxBackgroundMode = 0x00000008;
        public static final int TextInputLayout_boxCollapsedPaddingTop = 0x00000009;
        public static final int TextInputLayout_boxCornerRadiusBottomEnd = 0x0000000a;
        public static final int TextInputLayout_boxCornerRadiusBottomStart = 0x0000000b;
        public static final int TextInputLayout_boxCornerRadiusTopEnd = 0x0000000c;
        public static final int TextInputLayout_boxCornerRadiusTopStart = 0x0000000d;
        public static final int TextInputLayout_boxStrokeColor = 0x0000000e;
        public static final int TextInputLayout_boxStrokeErrorColor = 0x0000000f;
        public static final int TextInputLayout_boxStrokeWidth = 0x00000010;
        public static final int TextInputLayout_boxStrokeWidthFocused = 0x00000011;
        public static final int TextInputLayout_counterEnabled = 0x00000012;
        public static final int TextInputLayout_counterMaxLength = 0x00000013;
        public static final int TextInputLayout_counterOverflowTextAppearance = 0x00000014;
        public static final int TextInputLayout_counterOverflowTextColor = 0x00000015;
        public static final int TextInputLayout_counterTextAppearance = 0x00000016;
        public static final int TextInputLayout_counterTextColor = 0x00000017;
        public static final int TextInputLayout_endIconCheckable = 0x00000018;
        public static final int TextInputLayout_endIconContentDescription = 0x00000019;
        public static final int TextInputLayout_endIconDrawable = 0x0000001a;
        public static final int TextInputLayout_endIconMode = 0x0000001b;
        public static final int TextInputLayout_endIconTint = 0x0000001c;
        public static final int TextInputLayout_endIconTintMode = 0x0000001d;
        public static final int TextInputLayout_errorContentDescription = 0x0000001e;
        public static final int TextInputLayout_errorEnabled = 0x0000001f;
        public static final int TextInputLayout_errorIconDrawable = 0x00000020;
        public static final int TextInputLayout_errorIconTint = 0x00000021;
        public static final int TextInputLayout_errorIconTintMode = 0x00000022;
        public static final int TextInputLayout_errorTextAppearance = 0x00000023;
        public static final int TextInputLayout_errorTextColor = 0x00000024;
        public static final int TextInputLayout_expandedHintEnabled = 0x00000025;
        public static final int TextInputLayout_helperText = 0x00000026;
        public static final int TextInputLayout_helperTextEnabled = 0x00000027;
        public static final int TextInputLayout_helperTextTextAppearance = 0x00000028;
        public static final int TextInputLayout_helperTextTextColor = 0x00000029;
        public static final int TextInputLayout_hintAnimationEnabled = 0x0000002a;
        public static final int TextInputLayout_hintEnabled = 0x0000002b;
        public static final int TextInputLayout_hintTextAppearance = 0x0000002c;
        public static final int TextInputLayout_hintTextColor = 0x0000002d;
        public static final int TextInputLayout_passwordToggleContentDescription = 0x0000002e;
        public static final int TextInputLayout_passwordToggleDrawable = 0x0000002f;
        public static final int TextInputLayout_passwordToggleEnabled = 0x00000030;
        public static final int TextInputLayout_passwordToggleTint = 0x00000031;
        public static final int TextInputLayout_passwordToggleTintMode = 0x00000032;
        public static final int TextInputLayout_placeholderText = 0x00000033;
        public static final int TextInputLayout_placeholderTextAppearance = 0x00000034;
        public static final int TextInputLayout_placeholderTextColor = 0x00000035;
        public static final int TextInputLayout_prefixText = 0x00000036;
        public static final int TextInputLayout_prefixTextAppearance = 0x00000037;
        public static final int TextInputLayout_prefixTextColor = 0x00000038;
        public static final int TextInputLayout_shapeAppearance = 0x00000039;
        public static final int TextInputLayout_shapeAppearanceOverlay = 0x0000003a;
        public static final int TextInputLayout_startIconCheckable = 0x0000003b;
        public static final int TextInputLayout_startIconContentDescription = 0x0000003c;
        public static final int TextInputLayout_startIconDrawable = 0x0000003d;
        public static final int TextInputLayout_startIconTint = 0x0000003e;
        public static final int TextInputLayout_startIconTintMode = 0x0000003f;
        public static final int TextInputLayout_suffixText = 0x00000040;
        public static final int TextInputLayout_suffixTextAppearance = 0x00000041;
        public static final int TextInputLayout_suffixTextColor = 0x00000042;
        public static final int ThemeEnforcement_android_textAppearance = 0x00000000;
        public static final int ThemeEnforcement_enforceMaterialTheme = 0x00000001;
        public static final int ThemeEnforcement_enforceTextAppearance = 0x00000002;
        public static final int Toolbar_android_gravity = 0x00000000;
        public static final int Toolbar_android_minHeight = 0x00000001;
        public static final int Toolbar_buttonGravity = 0x00000002;
        public static final int Toolbar_collapseContentDescription = 0x00000003;
        public static final int Toolbar_collapseIcon = 0x00000004;
        public static final int Toolbar_contentInsetEnd = 0x00000005;
        public static final int Toolbar_contentInsetEndWithActions = 0x00000006;
        public static final int Toolbar_contentInsetLeft = 0x00000007;
        public static final int Toolbar_contentInsetRight = 0x00000008;
        public static final int Toolbar_contentInsetStart = 0x00000009;
        public static final int Toolbar_contentInsetStartWithNavigation = 0x0000000a;
        public static final int Toolbar_logo = 0x0000000b;
        public static final int Toolbar_logoDescription = 0x0000000c;
        public static final int Toolbar_maxButtonHeight = 0x0000000d;
        public static final int Toolbar_menu = 0x0000000e;
        public static final int Toolbar_navigationContentDescription = 0x0000000f;
        public static final int Toolbar_navigationIcon = 0x00000010;
        public static final int Toolbar_popupTheme = 0x00000011;
        public static final int Toolbar_subtitle = 0x00000012;
        public static final int Toolbar_subtitleTextAppearance = 0x00000013;
        public static final int Toolbar_subtitleTextColor = 0x00000014;
        public static final int Toolbar_title = 0x00000015;
        public static final int Toolbar_titleMargin = 0x00000016;
        public static final int Toolbar_titleMarginBottom = 0x00000017;
        public static final int Toolbar_titleMarginEnd = 0x00000018;
        public static final int Toolbar_titleMarginStart = 0x00000019;
        public static final int Toolbar_titleMarginTop = 0x0000001a;
        public static final int Toolbar_titleMargins = 0x0000001b;
        public static final int Toolbar_titleTextAppearance = 0x0000001c;
        public static final int Toolbar_titleTextColor = 0x0000001d;
        public static final int Tooltip_android_layout_margin = 0x00000003;
        public static final int Tooltip_android_minHeight = 0x00000005;
        public static final int Tooltip_android_minWidth = 0x00000004;
        public static final int Tooltip_android_padding = 0x00000002;
        public static final int Tooltip_android_text = 0x00000006;
        public static final int Tooltip_android_textAppearance = 0x00000000;
        public static final int Tooltip_android_textColor = 0x00000001;
        public static final int Tooltip_backgroundTint = 0x00000007;
        public static final int ViewBackgroundHelper_android_background = 0x00000000;
        public static final int ViewBackgroundHelper_backgroundTint = 0x00000001;
        public static final int ViewBackgroundHelper_backgroundTintMode = 0x00000002;
        public static final int ViewPager2_android_orientation = 0x00000000;
        public static final int ViewStubCompat_android_id = 0x00000000;
        public static final int ViewStubCompat_android_inflatedId = 0x00000002;
        public static final int ViewStubCompat_android_layout = 0x00000001;
        public static final int View_android_focusable = 0x00000001;
        public static final int View_android_theme = 0x00000000;
        public static final int View_paddingEnd = 0x00000002;
        public static final int View_paddingStart = 0x00000003;
        public static final int View_theme = 0x00000004;
        public static final int[] ActionBar = {com.incall.apps.softmanager.R.attr.background, com.incall.apps.softmanager.R.attr.backgroundSplit, com.incall.apps.softmanager.R.attr.backgroundStacked, com.incall.apps.softmanager.R.attr.contentInsetEnd, com.incall.apps.softmanager.R.attr.contentInsetEndWithActions, com.incall.apps.softmanager.R.attr.contentInsetLeft, com.incall.apps.softmanager.R.attr.contentInsetRight, com.incall.apps.softmanager.R.attr.contentInsetStart, com.incall.apps.softmanager.R.attr.contentInsetStartWithNavigation, com.incall.apps.softmanager.R.attr.customNavigationLayout, com.incall.apps.softmanager.R.attr.displayOptions, com.incall.apps.softmanager.R.attr.divider, com.incall.apps.softmanager.R.attr.elevation, com.incall.apps.softmanager.R.attr.height, com.incall.apps.softmanager.R.attr.hideOnContentScroll, com.incall.apps.softmanager.R.attr.homeAsUpIndicator, com.incall.apps.softmanager.R.attr.homeLayout, com.incall.apps.softmanager.R.attr.icon, com.incall.apps.softmanager.R.attr.indeterminateProgressStyle, com.incall.apps.softmanager.R.attr.itemPadding, com.incall.apps.softmanager.R.attr.logo, com.incall.apps.softmanager.R.attr.navigationMode, com.incall.apps.softmanager.R.attr.popupTheme, com.incall.apps.softmanager.R.attr.progressBarPadding, com.incall.apps.softmanager.R.attr.progressBarStyle, com.incall.apps.softmanager.R.attr.subtitle, com.incall.apps.softmanager.R.attr.subtitleTextStyle, com.incall.apps.softmanager.R.attr.title, com.incall.apps.softmanager.R.attr.titleTextStyle};
        public static final int[] ActionBarLayout = {android.R.attr.layout_gravity};
        public static final int[] ActionMenuItemView = {android.R.attr.minWidth};
        public static final int[] ActionMenuView = new int[0];
        public static final int[] ActionMode = {com.incall.apps.softmanager.R.attr.background, com.incall.apps.softmanager.R.attr.backgroundSplit, com.incall.apps.softmanager.R.attr.closeItemLayout, com.incall.apps.softmanager.R.attr.height, com.incall.apps.softmanager.R.attr.subtitleTextStyle, com.incall.apps.softmanager.R.attr.titleTextStyle};
        public static final int[] ActivityChooserView = {com.incall.apps.softmanager.R.attr.expandActivityOverflowButtonDrawable, com.incall.apps.softmanager.R.attr.initialActivityCount};
        public static final int[] AlertDialog = {android.R.attr.layout, com.incall.apps.softmanager.R.attr.buttonIconDimen, com.incall.apps.softmanager.R.attr.buttonPanelSideLayout, com.incall.apps.softmanager.R.attr.listItemLayout, com.incall.apps.softmanager.R.attr.listLayout, com.incall.apps.softmanager.R.attr.multiChoiceItemLayout, com.incall.apps.softmanager.R.attr.showTitle, com.incall.apps.softmanager.R.attr.singleChoiceItemLayout};
        public static final int[] AnimatedStateListDrawableCompat = {android.R.attr.dither, android.R.attr.visible, android.R.attr.variablePadding, android.R.attr.constantSize, android.R.attr.enterFadeDuration, android.R.attr.exitFadeDuration};
        public static final int[] AnimatedStateListDrawableItem = {android.R.attr.id, android.R.attr.drawable};
        public static final int[] AnimatedStateListDrawableTransition = {android.R.attr.drawable, android.R.attr.toId, android.R.attr.fromId, android.R.attr.reversible};
        public static final int[] AppBarLayout = {android.R.attr.background, android.R.attr.touchscreenBlocksFocus, android.R.attr.keyboardNavigationCluster, com.incall.apps.softmanager.R.attr.elevation, com.incall.apps.softmanager.R.attr.expanded, com.incall.apps.softmanager.R.attr.liftOnScroll, com.incall.apps.softmanager.R.attr.liftOnScrollTargetViewId, com.incall.apps.softmanager.R.attr.statusBarForeground};
        public static final int[] AppBarLayoutStates = {com.incall.apps.softmanager.R.attr.state_collapsed, com.incall.apps.softmanager.R.attr.state_collapsible, com.incall.apps.softmanager.R.attr.state_liftable, com.incall.apps.softmanager.R.attr.state_lifted};
        public static final int[] AppBarLayout_Layout = {com.incall.apps.softmanager.R.attr.layout_scrollEffect, com.incall.apps.softmanager.R.attr.layout_scrollFlags, com.incall.apps.softmanager.R.attr.layout_scrollInterpolator};
        public static final int[] AppCompatImageView = {android.R.attr.src, com.incall.apps.softmanager.R.attr.srcCompat, com.incall.apps.softmanager.R.attr.tint, com.incall.apps.softmanager.R.attr.tintMode};
        public static final int[] AppCompatSeekBar = {android.R.attr.thumb, com.incall.apps.softmanager.R.attr.tickMark, com.incall.apps.softmanager.R.attr.tickMarkTint, com.incall.apps.softmanager.R.attr.tickMarkTintMode};
        public static final int[] AppCompatTextHelper = {android.R.attr.textAppearance, android.R.attr.drawableTop, android.R.attr.drawableBottom, android.R.attr.drawableLeft, android.R.attr.drawableRight, android.R.attr.drawableStart, android.R.attr.drawableEnd};
        public static final int[] AppCompatTextView = {android.R.attr.textAppearance, com.incall.apps.softmanager.R.attr.autoSizeMaxTextSize, com.incall.apps.softmanager.R.attr.autoSizeMinTextSize, com.incall.apps.softmanager.R.attr.autoSizePresetSizes, com.incall.apps.softmanager.R.attr.autoSizeStepGranularity, com.incall.apps.softmanager.R.attr.autoSizeTextType, com.incall.apps.softmanager.R.attr.drawableBottomCompat, com.incall.apps.softmanager.R.attr.drawableEndCompat, com.incall.apps.softmanager.R.attr.drawableLeftCompat, com.incall.apps.softmanager.R.attr.drawableRightCompat, com.incall.apps.softmanager.R.attr.drawableStartCompat, com.incall.apps.softmanager.R.attr.drawableTint, com.incall.apps.softmanager.R.attr.drawableTintMode, com.incall.apps.softmanager.R.attr.drawableTopCompat, com.incall.apps.softmanager.R.attr.emojiCompatEnabled, com.incall.apps.softmanager.R.attr.firstBaselineToTopHeight, com.incall.apps.softmanager.R.attr.fontFamily, com.incall.apps.softmanager.R.attr.fontVariationSettings, com.incall.apps.softmanager.R.attr.lastBaselineToBottomHeight, com.incall.apps.softmanager.R.attr.lineHeight, com.incall.apps.softmanager.R.attr.textAllCaps, com.incall.apps.softmanager.R.attr.textLocale};
        public static final int[] AppCompatTheme = {android.R.attr.windowIsFloating, android.R.attr.windowAnimationStyle, com.incall.apps.softmanager.R.attr.actionBarDivider, com.incall.apps.softmanager.R.attr.actionBarItemBackground, com.incall.apps.softmanager.R.attr.actionBarPopupTheme, com.incall.apps.softmanager.R.attr.actionBarSize, com.incall.apps.softmanager.R.attr.actionBarSplitStyle, com.incall.apps.softmanager.R.attr.actionBarStyle, com.incall.apps.softmanager.R.attr.actionBarTabBarStyle, com.incall.apps.softmanager.R.attr.actionBarTabStyle, com.incall.apps.softmanager.R.attr.actionBarTabTextStyle, com.incall.apps.softmanager.R.attr.actionBarTheme, com.incall.apps.softmanager.R.attr.actionBarWidgetTheme, com.incall.apps.softmanager.R.attr.actionButtonStyle, com.incall.apps.softmanager.R.attr.actionDropDownStyle, com.incall.apps.softmanager.R.attr.actionMenuTextAppearance, com.incall.apps.softmanager.R.attr.actionMenuTextColor, com.incall.apps.softmanager.R.attr.actionModeBackground, com.incall.apps.softmanager.R.attr.actionModeCloseButtonStyle, com.incall.apps.softmanager.R.attr.actionModeCloseContentDescription, com.incall.apps.softmanager.R.attr.actionModeCloseDrawable, com.incall.apps.softmanager.R.attr.actionModeCopyDrawable, com.incall.apps.softmanager.R.attr.actionModeCutDrawable, com.incall.apps.softmanager.R.attr.actionModeFindDrawable, com.incall.apps.softmanager.R.attr.actionModePasteDrawable, com.incall.apps.softmanager.R.attr.actionModePopupWindowStyle, com.incall.apps.softmanager.R.attr.actionModeSelectAllDrawable, com.incall.apps.softmanager.R.attr.actionModeShareDrawable, com.incall.apps.softmanager.R.attr.actionModeSplitBackground, com.incall.apps.softmanager.R.attr.actionModeStyle, com.incall.apps.softmanager.R.attr.actionModeTheme, com.incall.apps.softmanager.R.attr.actionModeWebSearchDrawable, com.incall.apps.softmanager.R.attr.actionOverflowButtonStyle, com.incall.apps.softmanager.R.attr.actionOverflowMenuStyle, com.incall.apps.softmanager.R.attr.activityChooserViewStyle, com.incall.apps.softmanager.R.attr.alertDialogButtonGroupStyle, com.incall.apps.softmanager.R.attr.alertDialogCenterButtons, com.incall.apps.softmanager.R.attr.alertDialogStyle, com.incall.apps.softmanager.R.attr.alertDialogTheme, com.incall.apps.softmanager.R.attr.autoCompleteTextViewStyle, com.incall.apps.softmanager.R.attr.borderlessButtonStyle, com.incall.apps.softmanager.R.attr.buttonBarButtonStyle, com.incall.apps.softmanager.R.attr.buttonBarNegativeButtonStyle, com.incall.apps.softmanager.R.attr.buttonBarNeutralButtonStyle, com.incall.apps.softmanager.R.attr.buttonBarPositiveButtonStyle, com.incall.apps.softmanager.R.attr.buttonBarStyle, com.incall.apps.softmanager.R.attr.buttonStyle, com.incall.apps.softmanager.R.attr.buttonStyleSmall, com.incall.apps.softmanager.R.attr.checkboxStyle, com.incall.apps.softmanager.R.attr.checkedTextViewStyle, com.incall.apps.softmanager.R.attr.colorAccent, com.incall.apps.softmanager.R.attr.colorBackgroundFloating, com.incall.apps.softmanager.R.attr.colorButtonNormal, com.incall.apps.softmanager.R.attr.colorControlActivated, com.incall.apps.softmanager.R.attr.colorControlHighlight, com.incall.apps.softmanager.R.attr.colorControlNormal, com.incall.apps.softmanager.R.attr.colorError, com.incall.apps.softmanager.R.attr.colorPrimary, com.incall.apps.softmanager.R.attr.colorPrimaryDark, com.incall.apps.softmanager.R.attr.colorSwitchThumbNormal, com.incall.apps.softmanager.R.attr.controlBackground, com.incall.apps.softmanager.R.attr.dialogCornerRadius, com.incall.apps.softmanager.R.attr.dialogPreferredPadding, com.incall.apps.softmanager.R.attr.dialogTheme, com.incall.apps.softmanager.R.attr.dividerHorizontal, com.incall.apps.softmanager.R.attr.dividerVertical, com.incall.apps.softmanager.R.attr.dropDownListViewStyle, com.incall.apps.softmanager.R.attr.dropdownListPreferredItemHeight, com.incall.apps.softmanager.R.attr.editTextBackground, com.incall.apps.softmanager.R.attr.editTextColor, com.incall.apps.softmanager.R.attr.editTextStyle, com.incall.apps.softmanager.R.attr.homeAsUpIndicator, com.incall.apps.softmanager.R.attr.imageButtonStyle, com.incall.apps.softmanager.R.attr.listChoiceBackgroundIndicator, com.incall.apps.softmanager.R.attr.listChoiceIndicatorMultipleAnimated, com.incall.apps.softmanager.R.attr.listChoiceIndicatorSingleAnimated, com.incall.apps.softmanager.R.attr.listDividerAlertDialog, com.incall.apps.softmanager.R.attr.listMenuViewStyle, com.incall.apps.softmanager.R.attr.listPopupWindowStyle, com.incall.apps.softmanager.R.attr.listPreferredItemHeight, com.incall.apps.softmanager.R.attr.listPreferredItemHeightLarge, com.incall.apps.softmanager.R.attr.listPreferredItemHeightSmall, com.incall.apps.softmanager.R.attr.listPreferredItemPaddingEnd, com.incall.apps.softmanager.R.attr.listPreferredItemPaddingLeft, com.incall.apps.softmanager.R.attr.listPreferredItemPaddingRight, com.incall.apps.softmanager.R.attr.listPreferredItemPaddingStart, com.incall.apps.softmanager.R.attr.panelBackground, com.incall.apps.softmanager.R.attr.panelMenuListTheme, com.incall.apps.softmanager.R.attr.panelMenuListWidth, com.incall.apps.softmanager.R.attr.popupMenuStyle, com.incall.apps.softmanager.R.attr.popupWindowStyle, com.incall.apps.softmanager.R.attr.radioButtonStyle, com.incall.apps.softmanager.R.attr.ratingBarStyle, com.incall.apps.softmanager.R.attr.ratingBarStyleIndicator, com.incall.apps.softmanager.R.attr.ratingBarStyleSmall, com.incall.apps.softmanager.R.attr.searchViewStyle, com.incall.apps.softmanager.R.attr.seekBarStyle, com.incall.apps.softmanager.R.attr.selectableItemBackground, com.incall.apps.softmanager.R.attr.selectableItemBackgroundBorderless, com.incall.apps.softmanager.R.attr.spinnerDropDownItemStyle, com.incall.apps.softmanager.R.attr.spinnerStyle, com.incall.apps.softmanager.R.attr.switchStyle, com.incall.apps.softmanager.R.attr.textAppearanceLargePopupMenu, com.incall.apps.softmanager.R.attr.textAppearanceListItem, com.incall.apps.softmanager.R.attr.textAppearanceListItemSecondary, com.incall.apps.softmanager.R.attr.textAppearanceListItemSmall, com.incall.apps.softmanager.R.attr.textAppearancePopupMenuHeader, com.incall.apps.softmanager.R.attr.textAppearanceSearchResultSubtitle, com.incall.apps.softmanager.R.attr.textAppearanceSearchResultTitle, com.incall.apps.softmanager.R.attr.textAppearanceSmallPopupMenu, com.incall.apps.softmanager.R.attr.textColorAlertDialogListItem, com.incall.apps.softmanager.R.attr.textColorSearchUrl, com.incall.apps.softmanager.R.attr.toolbarNavigationButtonStyle, com.incall.apps.softmanager.R.attr.toolbarStyle, com.incall.apps.softmanager.R.attr.tooltipForegroundColor, com.incall.apps.softmanager.R.attr.tooltipFrameBackground, com.incall.apps.softmanager.R.attr.viewInflaterClass, com.incall.apps.softmanager.R.attr.windowActionBar, com.incall.apps.softmanager.R.attr.windowActionBarOverlay, com.incall.apps.softmanager.R.attr.windowActionModeOverlay, com.incall.apps.softmanager.R.attr.windowFixedHeightMajor, com.incall.apps.softmanager.R.attr.windowFixedHeightMinor, com.incall.apps.softmanager.R.attr.windowFixedWidthMajor, com.incall.apps.softmanager.R.attr.windowFixedWidthMinor, com.incall.apps.softmanager.R.attr.windowMinWidthMajor, com.incall.apps.softmanager.R.attr.windowMinWidthMinor, com.incall.apps.softmanager.R.attr.windowNoTitle};
        public static final int[] Badge = {com.incall.apps.softmanager.R.attr.backgroundColor, com.incall.apps.softmanager.R.attr.badgeGravity, com.incall.apps.softmanager.R.attr.badgeRadius, com.incall.apps.softmanager.R.attr.badgeTextColor, com.incall.apps.softmanager.R.attr.badgeWidePadding, com.incall.apps.softmanager.R.attr.badgeWithTextRadius, com.incall.apps.softmanager.R.attr.horizontalOffset, com.incall.apps.softmanager.R.attr.horizontalOffsetWithText, com.incall.apps.softmanager.R.attr.maxCharacterCount, com.incall.apps.softmanager.R.attr.number, com.incall.apps.softmanager.R.attr.verticalOffset, com.incall.apps.softmanager.R.attr.verticalOffsetWithText};
        public static final int[] BottomAppBar = {com.incall.apps.softmanager.R.attr.backgroundTint, com.incall.apps.softmanager.R.attr.elevation, com.incall.apps.softmanager.R.attr.fabAlignmentMode, com.incall.apps.softmanager.R.attr.fabAnimationMode, com.incall.apps.softmanager.R.attr.fabCradleMargin, com.incall.apps.softmanager.R.attr.fabCradleRoundedCornerRadius, com.incall.apps.softmanager.R.attr.fabCradleVerticalOffset, com.incall.apps.softmanager.R.attr.hideOnScroll, com.incall.apps.softmanager.R.attr.navigationIconTint, com.incall.apps.softmanager.R.attr.paddingBottomSystemWindowInsets, com.incall.apps.softmanager.R.attr.paddingLeftSystemWindowInsets, com.incall.apps.softmanager.R.attr.paddingRightSystemWindowInsets};
        public static final int[] BottomNavigationView = {android.R.attr.minHeight, com.incall.apps.softmanager.R.attr.itemHorizontalTranslationEnabled};
        public static final int[] BottomSheetBehavior_Layout = {android.R.attr.maxWidth, android.R.attr.maxHeight, android.R.attr.elevation, com.incall.apps.softmanager.R.attr.backgroundTint, com.incall.apps.softmanager.R.attr.behavior_draggable, com.incall.apps.softmanager.R.attr.behavior_expandedOffset, com.incall.apps.softmanager.R.attr.behavior_fitToContents, com.incall.apps.softmanager.R.attr.behavior_halfExpandedRatio, com.incall.apps.softmanager.R.attr.behavior_hideable, com.incall.apps.softmanager.R.attr.behavior_peekHeight, com.incall.apps.softmanager.R.attr.behavior_saveFlags, com.incall.apps.softmanager.R.attr.behavior_skipCollapsed, com.incall.apps.softmanager.R.attr.gestureInsetBottomIgnored, com.incall.apps.softmanager.R.attr.marginLeftSystemWindowInsets, com.incall.apps.softmanager.R.attr.marginRightSystemWindowInsets, com.incall.apps.softmanager.R.attr.marginTopSystemWindowInsets, com.incall.apps.softmanager.R.attr.paddingBottomSystemWindowInsets, com.incall.apps.softmanager.R.attr.paddingLeftSystemWindowInsets, com.incall.apps.softmanager.R.attr.paddingRightSystemWindowInsets, com.incall.apps.softmanager.R.attr.paddingTopSystemWindowInsets, com.incall.apps.softmanager.R.attr.shapeAppearance, com.incall.apps.softmanager.R.attr.shapeAppearanceOverlay};
        public static final int[] ButtonBarLayout = {com.incall.apps.softmanager.R.attr.allowStacking};
        public static final int[] CardView = {android.R.attr.minWidth, android.R.attr.minHeight, com.incall.apps.softmanager.R.attr.cardBackgroundColor, com.incall.apps.softmanager.R.attr.cardCornerRadius, com.incall.apps.softmanager.R.attr.cardElevation, com.incall.apps.softmanager.R.attr.cardMaxElevation, com.incall.apps.softmanager.R.attr.cardPreventCornerOverlap, com.incall.apps.softmanager.R.attr.cardUseCompatPadding, com.incall.apps.softmanager.R.attr.contentPadding, com.incall.apps.softmanager.R.attr.contentPaddingBottom, com.incall.apps.softmanager.R.attr.contentPaddingLeft, com.incall.apps.softmanager.R.attr.contentPaddingRight, com.incall.apps.softmanager.R.attr.contentPaddingTop};
        public static final int[] Chip = {android.R.attr.textAppearance, android.R.attr.textSize, android.R.attr.textColor, android.R.attr.ellipsize, android.R.attr.maxWidth, android.R.attr.text, android.R.attr.checkable, com.incall.apps.softmanager.R.attr.checkedIcon, com.incall.apps.softmanager.R.attr.checkedIconEnabled, com.incall.apps.softmanager.R.attr.checkedIconTint, com.incall.apps.softmanager.R.attr.checkedIconVisible, com.incall.apps.softmanager.R.attr.chipBackgroundColor, com.incall.apps.softmanager.R.attr.chipCornerRadius, com.incall.apps.softmanager.R.attr.chipEndPadding, com.incall.apps.softmanager.R.attr.chipIcon, com.incall.apps.softmanager.R.attr.chipIconEnabled, com.incall.apps.softmanager.R.attr.chipIconSize, com.incall.apps.softmanager.R.attr.chipIconTint, com.incall.apps.softmanager.R.attr.chipIconVisible, com.incall.apps.softmanager.R.attr.chipMinHeight, com.incall.apps.softmanager.R.attr.chipMinTouchTargetSize, com.incall.apps.softmanager.R.attr.chipStartPadding, com.incall.apps.softmanager.R.attr.chipStrokeColor, com.incall.apps.softmanager.R.attr.chipStrokeWidth, com.incall.apps.softmanager.R.attr.chipSurfaceColor, com.incall.apps.softmanager.R.attr.closeIcon, com.incall.apps.softmanager.R.attr.closeIconEnabled, com.incall.apps.softmanager.R.attr.closeIconEndPadding, com.incall.apps.softmanager.R.attr.closeIconSize, com.incall.apps.softmanager.R.attr.closeIconStartPadding, com.incall.apps.softmanager.R.attr.closeIconTint, com.incall.apps.softmanager.R.attr.closeIconVisible, com.incall.apps.softmanager.R.attr.ensureMinTouchTargetSize, com.incall.apps.softmanager.R.attr.hideMotionSpec, com.incall.apps.softmanager.R.attr.iconEndPadding, com.incall.apps.softmanager.R.attr.iconStartPadding, com.incall.apps.softmanager.R.attr.rippleColor, com.incall.apps.softmanager.R.attr.shapeAppearance, com.incall.apps.softmanager.R.attr.shapeAppearanceOverlay, com.incall.apps.softmanager.R.attr.showMotionSpec, com.incall.apps.softmanager.R.attr.textEndPadding, com.incall.apps.softmanager.R.attr.textStartPadding};
        public static final int[] ChipGroup = {com.incall.apps.softmanager.R.attr.checkedChip, com.incall.apps.softmanager.R.attr.chipSpacing, com.incall.apps.softmanager.R.attr.chipSpacingHorizontal, com.incall.apps.softmanager.R.attr.chipSpacingVertical, com.incall.apps.softmanager.R.attr.selectionRequired, com.incall.apps.softmanager.R.attr.singleLine, com.incall.apps.softmanager.R.attr.singleSelection};
        public static final int[] CollapsingToolbarLayout = {com.incall.apps.softmanager.R.attr.collapsedTitleGravity, com.incall.apps.softmanager.R.attr.collapsedTitleTextAppearance, com.incall.apps.softmanager.R.attr.collapsedTitleTextColor, com.incall.apps.softmanager.R.attr.contentScrim, com.incall.apps.softmanager.R.attr.expandedTitleGravity, com.incall.apps.softmanager.R.attr.expandedTitleMargin, com.incall.apps.softmanager.R.attr.expandedTitleMarginBottom, com.incall.apps.softmanager.R.attr.expandedTitleMarginEnd, com.incall.apps.softmanager.R.attr.expandedTitleMarginStart, com.incall.apps.softmanager.R.attr.expandedTitleMarginTop, com.incall.apps.softmanager.R.attr.expandedTitleTextAppearance, com.incall.apps.softmanager.R.attr.expandedTitleTextColor, com.incall.apps.softmanager.R.attr.extraMultilineHeightEnabled, com.incall.apps.softmanager.R.attr.forceApplySystemWindowInsetTop, com.incall.apps.softmanager.R.attr.maxLines, com.incall.apps.softmanager.R.attr.scrimAnimationDuration, com.incall.apps.softmanager.R.attr.scrimVisibleHeightTrigger, com.incall.apps.softmanager.R.attr.statusBarScrim, com.incall.apps.softmanager.R.attr.title, com.incall.apps.softmanager.R.attr.titleCollapseMode, com.incall.apps.softmanager.R.attr.titleEnabled, com.incall.apps.softmanager.R.attr.titlePositionInterpolator, com.incall.apps.softmanager.R.attr.toolbarId};
        public static final int[] CollapsingToolbarLayout_Layout = {com.incall.apps.softmanager.R.attr.layout_collapseMode, com.incall.apps.softmanager.R.attr.layout_collapseParallaxMultiplier};
        public static final int[] ColorStateListItem = {android.R.attr.color, android.R.attr.alpha, android.R.attr.lStar, com.incall.apps.softmanager.R.attr.alpha, com.incall.apps.softmanager.R.attr.lStar};
        public static final int[] CompoundButton = {android.R.attr.button, com.incall.apps.softmanager.R.attr.buttonCompat, com.incall.apps.softmanager.R.attr.buttonTint, com.incall.apps.softmanager.R.attr.buttonTintMode};
        public static final int[] CoordinatorLayout = {com.incall.apps.softmanager.R.attr.keylines, com.incall.apps.softmanager.R.attr.statusBarBackground};
        public static final int[] CoordinatorLayout_Layout = {android.R.attr.layout_gravity, com.incall.apps.softmanager.R.attr.layout_anchor, com.incall.apps.softmanager.R.attr.layout_anchorGravity, com.incall.apps.softmanager.R.attr.layout_behavior, com.incall.apps.softmanager.R.attr.layout_dodgeInsetEdges, com.incall.apps.softmanager.R.attr.layout_insetEdge, com.incall.apps.softmanager.R.attr.layout_keyline};
        public static final int[] DrawerArrowToggle = {com.incall.apps.softmanager.R.attr.arrowHeadLength, com.incall.apps.softmanager.R.attr.arrowShaftLength, com.incall.apps.softmanager.R.attr.barLength, com.incall.apps.softmanager.R.attr.color, com.incall.apps.softmanager.R.attr.drawableSize, com.incall.apps.softmanager.R.attr.gapBetweenBars, com.incall.apps.softmanager.R.attr.spinBars, com.incall.apps.softmanager.R.attr.thickness};
        public static final int[] ExtendedFloatingActionButton = {com.incall.apps.softmanager.R.attr.collapsedSize, com.incall.apps.softmanager.R.attr.elevation, com.incall.apps.softmanager.R.attr.extendMotionSpec, com.incall.apps.softmanager.R.attr.hideMotionSpec, com.incall.apps.softmanager.R.attr.showMotionSpec, com.incall.apps.softmanager.R.attr.shrinkMotionSpec};
        public static final int[] ExtendedFloatingActionButton_Behavior_Layout = {com.incall.apps.softmanager.R.attr.behavior_autoHide, com.incall.apps.softmanager.R.attr.behavior_autoShrink};
        public static final int[] FloatingActionButton = {android.R.attr.enabled, com.incall.apps.softmanager.R.attr.backgroundTint, com.incall.apps.softmanager.R.attr.backgroundTintMode, com.incall.apps.softmanager.R.attr.borderWidth, com.incall.apps.softmanager.R.attr.elevation, com.incall.apps.softmanager.R.attr.ensureMinTouchTargetSize, com.incall.apps.softmanager.R.attr.fabCustomSize, com.incall.apps.softmanager.R.attr.fabSize, com.incall.apps.softmanager.R.attr.hideMotionSpec, com.incall.apps.softmanager.R.attr.hoveredFocusedTranslationZ, com.incall.apps.softmanager.R.attr.maxImageSize, com.incall.apps.softmanager.R.attr.pressedTranslationZ, com.incall.apps.softmanager.R.attr.rippleColor, com.incall.apps.softmanager.R.attr.shapeAppearance, com.incall.apps.softmanager.R.attr.shapeAppearanceOverlay, com.incall.apps.softmanager.R.attr.showMotionSpec, com.incall.apps.softmanager.R.attr.useCompatPadding};
        public static final int[] FloatingActionButton_Behavior_Layout = {com.incall.apps.softmanager.R.attr.behavior_autoHide};
        public static final int[] FlowLayout = {com.incall.apps.softmanager.R.attr.itemSpacing, com.incall.apps.softmanager.R.attr.lineSpacing};
        public static final int[] FontFamily = {com.incall.apps.softmanager.R.attr.fontProviderAuthority, com.incall.apps.softmanager.R.attr.fontProviderCerts, com.incall.apps.softmanager.R.attr.fontProviderFetchStrategy, com.incall.apps.softmanager.R.attr.fontProviderFetchTimeout, com.incall.apps.softmanager.R.attr.fontProviderPackage, com.incall.apps.softmanager.R.attr.fontProviderQuery, com.incall.apps.softmanager.R.attr.fontProviderSystemFontFamily};
        public static final int[] FontFamilyFont = {android.R.attr.font, android.R.attr.fontWeight, android.R.attr.fontStyle, android.R.attr.ttcIndex, android.R.attr.fontVariationSettings, com.incall.apps.softmanager.R.attr.font, com.incall.apps.softmanager.R.attr.fontStyle, com.incall.apps.softmanager.R.attr.fontVariationSettings, com.incall.apps.softmanager.R.attr.fontWeight, com.incall.apps.softmanager.R.attr.ttcIndex};
        public static final int[] ForegroundLinearLayout = {android.R.attr.foreground, android.R.attr.foregroundGravity, com.incall.apps.softmanager.R.attr.foregroundInsidePadding};
        public static final int[] GradientColor = {android.R.attr.startColor, android.R.attr.endColor, android.R.attr.type, android.R.attr.centerX, android.R.attr.centerY, android.R.attr.gradientRadius, android.R.attr.tileMode, android.R.attr.centerColor, android.R.attr.startX, android.R.attr.startY, android.R.attr.endX, android.R.attr.endY};
        public static final int[] GradientColorItem = {android.R.attr.color, android.R.attr.offset};
        public static final int[] Insets = {com.incall.apps.softmanager.R.attr.marginLeftSystemWindowInsets, com.incall.apps.softmanager.R.attr.marginRightSystemWindowInsets, com.incall.apps.softmanager.R.attr.marginTopSystemWindowInsets, com.incall.apps.softmanager.R.attr.paddingBottomSystemWindowInsets, com.incall.apps.softmanager.R.attr.paddingLeftSystemWindowInsets, com.incall.apps.softmanager.R.attr.paddingRightSystemWindowInsets, com.incall.apps.softmanager.R.attr.paddingTopSystemWindowInsets};
        public static final int[] LinearLayoutCompat = {android.R.attr.gravity, android.R.attr.orientation, android.R.attr.baselineAligned, android.R.attr.baselineAlignedChildIndex, android.R.attr.weightSum, com.incall.apps.softmanager.R.attr.divider, com.incall.apps.softmanager.R.attr.dividerPadding, com.incall.apps.softmanager.R.attr.measureWithLargestChild, com.incall.apps.softmanager.R.attr.showDividers};
        public static final int[] LinearLayoutCompat_Layout = {android.R.attr.layout_gravity, android.R.attr.layout_width, android.R.attr.layout_height, android.R.attr.layout_weight};
        public static final int[] ListPopupWindow = {android.R.attr.dropDownHorizontalOffset, android.R.attr.dropDownVerticalOffset};
        public static final int[] MaterialAlertDialog = {com.incall.apps.softmanager.R.attr.backgroundInsetBottom, com.incall.apps.softmanager.R.attr.backgroundInsetEnd, com.incall.apps.softmanager.R.attr.backgroundInsetStart, com.incall.apps.softmanager.R.attr.backgroundInsetTop};
        public static final int[] MaterialAlertDialogTheme = {com.incall.apps.softmanager.R.attr.materialAlertDialogBodyTextStyle, com.incall.apps.softmanager.R.attr.materialAlertDialogButtonSpacerVisibility, com.incall.apps.softmanager.R.attr.materialAlertDialogTheme, com.incall.apps.softmanager.R.attr.materialAlertDialogTitleIconStyle, com.incall.apps.softmanager.R.attr.materialAlertDialogTitlePanelStyle, com.incall.apps.softmanager.R.attr.materialAlertDialogTitleTextStyle};
        public static final int[] MaterialAutoCompleteTextView = {android.R.attr.inputType, com.incall.apps.softmanager.R.attr.simpleItemLayout, com.incall.apps.softmanager.R.attr.simpleItems};
        public static final int[] MaterialButton = {android.R.attr.background, android.R.attr.insetLeft, android.R.attr.insetRight, android.R.attr.insetTop, android.R.attr.insetBottom, android.R.attr.checkable, com.incall.apps.softmanager.R.attr.backgroundTint, com.incall.apps.softmanager.R.attr.backgroundTintMode, com.incall.apps.softmanager.R.attr.cornerRadius, com.incall.apps.softmanager.R.attr.elevation, com.incall.apps.softmanager.R.attr.icon, com.incall.apps.softmanager.R.attr.iconGravity, com.incall.apps.softmanager.R.attr.iconPadding, com.incall.apps.softmanager.R.attr.iconSize, com.incall.apps.softmanager.R.attr.iconTint, com.incall.apps.softmanager.R.attr.iconTintMode, com.incall.apps.softmanager.R.attr.rippleColor, com.incall.apps.softmanager.R.attr.shapeAppearance, com.incall.apps.softmanager.R.attr.shapeAppearanceOverlay, com.incall.apps.softmanager.R.attr.strokeColor, com.incall.apps.softmanager.R.attr.strokeWidth};
        public static final int[] MaterialButtonToggleGroup = {com.incall.apps.softmanager.R.attr.checkedButton, com.incall.apps.softmanager.R.attr.selectionRequired, com.incall.apps.softmanager.R.attr.singleSelection};
        public static final int[] MaterialCalendar = {android.R.attr.windowFullscreen, com.incall.apps.softmanager.R.attr.dayInvalidStyle, com.incall.apps.softmanager.R.attr.daySelectedStyle, com.incall.apps.softmanager.R.attr.dayStyle, com.incall.apps.softmanager.R.attr.dayTodayStyle, com.incall.apps.softmanager.R.attr.nestedScrollable, com.incall.apps.softmanager.R.attr.rangeFillColor, com.incall.apps.softmanager.R.attr.yearSelectedStyle, com.incall.apps.softmanager.R.attr.yearStyle, com.incall.apps.softmanager.R.attr.yearTodayStyle};
        public static final int[] MaterialCalendarItem = {android.R.attr.insetLeft, android.R.attr.insetRight, android.R.attr.insetTop, android.R.attr.insetBottom, com.incall.apps.softmanager.R.attr.itemFillColor, com.incall.apps.softmanager.R.attr.itemShapeAppearance, com.incall.apps.softmanager.R.attr.itemShapeAppearanceOverlay, com.incall.apps.softmanager.R.attr.itemStrokeColor, com.incall.apps.softmanager.R.attr.itemStrokeWidth, com.incall.apps.softmanager.R.attr.itemTextColor};
        public static final int[] MaterialCardView = {android.R.attr.checkable, com.incall.apps.softmanager.R.attr.cardForegroundColor, com.incall.apps.softmanager.R.attr.checkedIcon, com.incall.apps.softmanager.R.attr.checkedIconGravity, com.incall.apps.softmanager.R.attr.checkedIconMargin, com.incall.apps.softmanager.R.attr.checkedIconSize, com.incall.apps.softmanager.R.attr.checkedIconTint, com.incall.apps.softmanager.R.attr.rippleColor, com.incall.apps.softmanager.R.attr.shapeAppearance, com.incall.apps.softmanager.R.attr.shapeAppearanceOverlay, com.incall.apps.softmanager.R.attr.state_dragged, com.incall.apps.softmanager.R.attr.strokeColor, com.incall.apps.softmanager.R.attr.strokeWidth};
        public static final int[] MaterialCheckBox = {com.incall.apps.softmanager.R.attr.buttonTint, com.incall.apps.softmanager.R.attr.centerIfNoTextEnabled, com.incall.apps.softmanager.R.attr.useMaterialThemeColors};
        public static final int[] MaterialRadioButton = {com.incall.apps.softmanager.R.attr.buttonTint, com.incall.apps.softmanager.R.attr.useMaterialThemeColors};
        public static final int[] MaterialShape = {com.incall.apps.softmanager.R.attr.shapeAppearance, com.incall.apps.softmanager.R.attr.shapeAppearanceOverlay};
        public static final int[] MaterialTextAppearance = {android.R.attr.letterSpacing, android.R.attr.lineHeight, com.incall.apps.softmanager.R.attr.lineHeight};
        public static final int[] MaterialTextView = {android.R.attr.textAppearance, android.R.attr.lineHeight, com.incall.apps.softmanager.R.attr.lineHeight};
        public static final int[] MenuGroup = {android.R.attr.enabled, android.R.attr.id, android.R.attr.visible, android.R.attr.menuCategory, android.R.attr.orderInCategory, android.R.attr.checkableBehavior};
        public static final int[] MenuItem = {android.R.attr.icon, android.R.attr.enabled, android.R.attr.id, android.R.attr.checked, android.R.attr.visible, android.R.attr.menuCategory, android.R.attr.orderInCategory, android.R.attr.title, android.R.attr.titleCondensed, android.R.attr.alphabeticShortcut, android.R.attr.numericShortcut, android.R.attr.checkable, android.R.attr.onClick, com.incall.apps.softmanager.R.attr.actionLayout, com.incall.apps.softmanager.R.attr.actionProviderClass, com.incall.apps.softmanager.R.attr.actionViewClass, com.incall.apps.softmanager.R.attr.alphabeticModifiers, com.incall.apps.softmanager.R.attr.contentDescription, com.incall.apps.softmanager.R.attr.iconTint, com.incall.apps.softmanager.R.attr.iconTintMode, com.incall.apps.softmanager.R.attr.numericModifiers, com.incall.apps.softmanager.R.attr.showAsAction, com.incall.apps.softmanager.R.attr.tooltipText};
        public static final int[] MenuView = {android.R.attr.windowAnimationStyle, android.R.attr.itemTextAppearance, android.R.attr.horizontalDivider, android.R.attr.verticalDivider, android.R.attr.headerBackground, android.R.attr.itemBackground, android.R.attr.itemIconDisabledAlpha, com.incall.apps.softmanager.R.attr.preserveIconSpacing, com.incall.apps.softmanager.R.attr.subMenuArrow};
        public static final int[] NavigationView = {android.R.attr.layout_gravity, android.R.attr.background, android.R.attr.fitsSystemWindows, android.R.attr.maxWidth, com.incall.apps.softmanager.R.attr.bottomInsetScrimEnabled, com.incall.apps.softmanager.R.attr.dividerInsetEnd, com.incall.apps.softmanager.R.attr.dividerInsetStart, com.incall.apps.softmanager.R.attr.drawerLayoutCornerSize, com.incall.apps.softmanager.R.attr.elevation, com.incall.apps.softmanager.R.attr.headerLayout, com.incall.apps.softmanager.R.attr.itemBackground, com.incall.apps.softmanager.R.attr.itemHorizontalPadding, com.incall.apps.softmanager.R.attr.itemIconPadding, com.incall.apps.softmanager.R.attr.itemIconSize, com.incall.apps.softmanager.R.attr.itemIconTint, com.incall.apps.softmanager.R.attr.itemMaxLines, com.incall.apps.softmanager.R.attr.itemRippleColor, com.incall.apps.softmanager.R.attr.itemShapeAppearance, com.incall.apps.softmanager.R.attr.itemShapeAppearanceOverlay, com.incall.apps.softmanager.R.attr.itemShapeFillColor, com.incall.apps.softmanager.R.attr.itemShapeInsetBottom, com.incall.apps.softmanager.R.attr.itemShapeInsetEnd, com.incall.apps.softmanager.R.attr.itemShapeInsetStart, com.incall.apps.softmanager.R.attr.itemShapeInsetTop, com.incall.apps.softmanager.R.attr.itemTextAppearance, com.incall.apps.softmanager.R.attr.itemTextColor, com.incall.apps.softmanager.R.attr.itemVerticalPadding, com.incall.apps.softmanager.R.attr.menu, com.incall.apps.softmanager.R.attr.shapeAppearance, com.incall.apps.softmanager.R.attr.shapeAppearanceOverlay, com.incall.apps.softmanager.R.attr.subheaderColor, com.incall.apps.softmanager.R.attr.subheaderInsetEnd, com.incall.apps.softmanager.R.attr.subheaderInsetStart, com.incall.apps.softmanager.R.attr.subheaderTextAppearance, com.incall.apps.softmanager.R.attr.topInsetScrimEnabled};
        public static final int[] PopupWindow = {android.R.attr.popupBackground, android.R.attr.popupAnimationStyle, com.incall.apps.softmanager.R.attr.overlapAnchor};
        public static final int[] PopupWindowBackgroundState = {com.incall.apps.softmanager.R.attr.state_above_anchor};
        public static final int[] RangeSlider = {com.incall.apps.softmanager.R.attr.minSeparation, com.incall.apps.softmanager.R.attr.values};
        public static final int[] RecycleListView = {com.incall.apps.softmanager.R.attr.paddingBottomNoButtons, com.incall.apps.softmanager.R.attr.paddingTopNoTitle};
        public static final int[] RecyclerView = {android.R.attr.orientation, android.R.attr.clipToPadding, android.R.attr.descendantFocusability, com.incall.apps.softmanager.R.attr.fastScrollEnabled, com.incall.apps.softmanager.R.attr.fastScrollHorizontalThumbDrawable, com.incall.apps.softmanager.R.attr.fastScrollHorizontalTrackDrawable, com.incall.apps.softmanager.R.attr.fastScrollVerticalThumbDrawable, com.incall.apps.softmanager.R.attr.fastScrollVerticalTrackDrawable, com.incall.apps.softmanager.R.attr.layoutManager, com.incall.apps.softmanager.R.attr.reverseLayout, com.incall.apps.softmanager.R.attr.spanCount, com.incall.apps.softmanager.R.attr.stackFromEnd};
        public static final int[] ScrimInsetsFrameLayout = {com.incall.apps.softmanager.R.attr.insetForeground};
        public static final int[] ScrollingViewBehavior_Layout = {com.incall.apps.softmanager.R.attr.behavior_overlapTop};
        public static final int[] SearchView = {android.R.attr.focusable, android.R.attr.maxWidth, android.R.attr.inputType, android.R.attr.imeOptions, com.incall.apps.softmanager.R.attr.closeIcon, com.incall.apps.softmanager.R.attr.commitIcon, com.incall.apps.softmanager.R.attr.defaultQueryHint, com.incall.apps.softmanager.R.attr.goIcon, com.incall.apps.softmanager.R.attr.iconifiedByDefault, com.incall.apps.softmanager.R.attr.layout, com.incall.apps.softmanager.R.attr.queryBackground, com.incall.apps.softmanager.R.attr.queryHint, com.incall.apps.softmanager.R.attr.searchHintIcon, com.incall.apps.softmanager.R.attr.searchIcon, com.incall.apps.softmanager.R.attr.submitBackground, com.incall.apps.softmanager.R.attr.suggestionRowLayout, com.incall.apps.softmanager.R.attr.voiceIcon};
        public static final int[] ShapeAppearance = {com.incall.apps.softmanager.R.attr.cornerFamily, com.incall.apps.softmanager.R.attr.cornerFamilyBottomLeft, com.incall.apps.softmanager.R.attr.cornerFamilyBottomRight, com.incall.apps.softmanager.R.attr.cornerFamilyTopLeft, com.incall.apps.softmanager.R.attr.cornerFamilyTopRight, com.incall.apps.softmanager.R.attr.cornerSize, com.incall.apps.softmanager.R.attr.cornerSizeBottomLeft, com.incall.apps.softmanager.R.attr.cornerSizeBottomRight, com.incall.apps.softmanager.R.attr.cornerSizeTopLeft, com.incall.apps.softmanager.R.attr.cornerSizeTopRight};
        public static final int[] ShapeableImageView = {com.incall.apps.softmanager.R.attr.contentPadding, com.incall.apps.softmanager.R.attr.contentPaddingBottom, com.incall.apps.softmanager.R.attr.contentPaddingEnd, com.incall.apps.softmanager.R.attr.contentPaddingLeft, com.incall.apps.softmanager.R.attr.contentPaddingRight, com.incall.apps.softmanager.R.attr.contentPaddingStart, com.incall.apps.softmanager.R.attr.contentPaddingTop, com.incall.apps.softmanager.R.attr.shapeAppearance, com.incall.apps.softmanager.R.attr.shapeAppearanceOverlay, com.incall.apps.softmanager.R.attr.strokeColor, com.incall.apps.softmanager.R.attr.strokeWidth};
        public static final int[] Slider = {android.R.attr.enabled, android.R.attr.value, android.R.attr.stepSize, android.R.attr.valueFrom, android.R.attr.valueTo, com.incall.apps.softmanager.R.attr.haloColor, com.incall.apps.softmanager.R.attr.haloRadius, com.incall.apps.softmanager.R.attr.labelBehavior, com.incall.apps.softmanager.R.attr.labelStyle, com.incall.apps.softmanager.R.attr.thumbColor, com.incall.apps.softmanager.R.attr.thumbElevation, com.incall.apps.softmanager.R.attr.thumbRadius, com.incall.apps.softmanager.R.attr.thumbStrokeColor, com.incall.apps.softmanager.R.attr.thumbStrokeWidth, com.incall.apps.softmanager.R.attr.tickColor, com.incall.apps.softmanager.R.attr.tickColorActive, com.incall.apps.softmanager.R.attr.tickColorInactive, com.incall.apps.softmanager.R.attr.tickVisible, com.incall.apps.softmanager.R.attr.trackColor, com.incall.apps.softmanager.R.attr.trackColorActive, com.incall.apps.softmanager.R.attr.trackColorInactive, com.incall.apps.softmanager.R.attr.trackHeight};
        public static final int[] Snackbar = {com.incall.apps.softmanager.R.attr.snackbarButtonStyle, com.incall.apps.softmanager.R.attr.snackbarStyle, com.incall.apps.softmanager.R.attr.snackbarTextViewStyle};
        public static final int[] SnackbarLayout = {android.R.attr.maxWidth, com.incall.apps.softmanager.R.attr.actionTextColorAlpha, com.incall.apps.softmanager.R.attr.animationMode, com.incall.apps.softmanager.R.attr.backgroundOverlayColorAlpha, com.incall.apps.softmanager.R.attr.backgroundTint, com.incall.apps.softmanager.R.attr.backgroundTintMode, com.incall.apps.softmanager.R.attr.elevation, com.incall.apps.softmanager.R.attr.maxActionInlineWidth};
        public static final int[] Spinner = {android.R.attr.entries, android.R.attr.popupBackground, android.R.attr.prompt, android.R.attr.dropDownWidth, com.incall.apps.softmanager.R.attr.popupTheme};
        public static final int[] StateListDrawable = {android.R.attr.dither, android.R.attr.visible, android.R.attr.variablePadding, android.R.attr.constantSize, android.R.attr.enterFadeDuration, android.R.attr.exitFadeDuration};
        public static final int[] StateListDrawableItem = {android.R.attr.drawable};
        public static final int[] SwitchCompat = {android.R.attr.textOn, android.R.attr.textOff, android.R.attr.thumb, com.incall.apps.softmanager.R.attr.showText, com.incall.apps.softmanager.R.attr.splitTrack, com.incall.apps.softmanager.R.attr.switchMinWidth, com.incall.apps.softmanager.R.attr.switchPadding, com.incall.apps.softmanager.R.attr.switchTextAppearance, com.incall.apps.softmanager.R.attr.thumbTextPadding, com.incall.apps.softmanager.R.attr.thumbTint, com.incall.apps.softmanager.R.attr.thumbTintMode, com.incall.apps.softmanager.R.attr.track, com.incall.apps.softmanager.R.attr.trackTint, com.incall.apps.softmanager.R.attr.trackTintMode};
        public static final int[] SwitchMaterial = {com.incall.apps.softmanager.R.attr.useMaterialThemeColors};
        public static final int[] TabItem = {android.R.attr.icon, android.R.attr.layout, android.R.attr.text};
        public static final int[] TabLayout = {com.incall.apps.softmanager.R.attr.tabBackground, com.incall.apps.softmanager.R.attr.tabContentStart, com.incall.apps.softmanager.R.attr.tabGravity, com.incall.apps.softmanager.R.attr.tabIconTint, com.incall.apps.softmanager.R.attr.tabIconTintMode, com.incall.apps.softmanager.R.attr.tabIndicator, com.incall.apps.softmanager.R.attr.tabIndicatorAnimationDuration, com.incall.apps.softmanager.R.attr.tabIndicatorAnimationMode, com.incall.apps.softmanager.R.attr.tabIndicatorColor, com.incall.apps.softmanager.R.attr.tabIndicatorFullWidth, com.incall.apps.softmanager.R.attr.tabIndicatorGravity, com.incall.apps.softmanager.R.attr.tabIndicatorHeight, com.incall.apps.softmanager.R.attr.tabInlineLabel, com.incall.apps.softmanager.R.attr.tabMaxWidth, com.incall.apps.softmanager.R.attr.tabMinWidth, com.incall.apps.softmanager.R.attr.tabMode, com.incall.apps.softmanager.R.attr.tabPadding, com.incall.apps.softmanager.R.attr.tabPaddingBottom, com.incall.apps.softmanager.R.attr.tabPaddingEnd, com.incall.apps.softmanager.R.attr.tabPaddingStart, com.incall.apps.softmanager.R.attr.tabPaddingTop, com.incall.apps.softmanager.R.attr.tabRippleColor, com.incall.apps.softmanager.R.attr.tabSelectedTextColor, com.incall.apps.softmanager.R.attr.tabTextAppearance, com.incall.apps.softmanager.R.attr.tabTextColor, com.incall.apps.softmanager.R.attr.tabUnboundedRipple};
        public static final int[] TextAppearance = {android.R.attr.textSize, android.R.attr.typeface, android.R.attr.textStyle, android.R.attr.textColor, android.R.attr.textColorHint, android.R.attr.textColorLink, android.R.attr.shadowColor, android.R.attr.shadowDx, android.R.attr.shadowDy, android.R.attr.shadowRadius, android.R.attr.fontFamily, android.R.attr.textFontWeight, com.incall.apps.softmanager.R.attr.fontFamily, com.incall.apps.softmanager.R.attr.fontVariationSettings, com.incall.apps.softmanager.R.attr.textAllCaps, com.incall.apps.softmanager.R.attr.textLocale};
        public static final int[] TextInputEditText = {com.incall.apps.softmanager.R.attr.textInputLayoutFocusedRectEnabled};
        public static final int[] TextInputLayout = {android.R.attr.enabled, android.R.attr.textColorHint, android.R.attr.maxWidth, android.R.attr.minWidth, android.R.attr.hint, android.R.attr.maxEms, android.R.attr.minEms, com.incall.apps.softmanager.R.attr.boxBackgroundColor, com.incall.apps.softmanager.R.attr.boxBackgroundMode, com.incall.apps.softmanager.R.attr.boxCollapsedPaddingTop, com.incall.apps.softmanager.R.attr.boxCornerRadiusBottomEnd, com.incall.apps.softmanager.R.attr.boxCornerRadiusBottomStart, com.incall.apps.softmanager.R.attr.boxCornerRadiusTopEnd, com.incall.apps.softmanager.R.attr.boxCornerRadiusTopStart, com.incall.apps.softmanager.R.attr.boxStrokeColor, com.incall.apps.softmanager.R.attr.boxStrokeErrorColor, com.incall.apps.softmanager.R.attr.boxStrokeWidth, com.incall.apps.softmanager.R.attr.boxStrokeWidthFocused, com.incall.apps.softmanager.R.attr.counterEnabled, com.incall.apps.softmanager.R.attr.counterMaxLength, com.incall.apps.softmanager.R.attr.counterOverflowTextAppearance, com.incall.apps.softmanager.R.attr.counterOverflowTextColor, com.incall.apps.softmanager.R.attr.counterTextAppearance, com.incall.apps.softmanager.R.attr.counterTextColor, com.incall.apps.softmanager.R.attr.endIconCheckable, com.incall.apps.softmanager.R.attr.endIconContentDescription, com.incall.apps.softmanager.R.attr.endIconDrawable, com.incall.apps.softmanager.R.attr.endIconMode, com.incall.apps.softmanager.R.attr.endIconTint, com.incall.apps.softmanager.R.attr.endIconTintMode, com.incall.apps.softmanager.R.attr.errorContentDescription, com.incall.apps.softmanager.R.attr.errorEnabled, com.incall.apps.softmanager.R.attr.errorIconDrawable, com.incall.apps.softmanager.R.attr.errorIconTint, com.incall.apps.softmanager.R.attr.errorIconTintMode, com.incall.apps.softmanager.R.attr.errorTextAppearance, com.incall.apps.softmanager.R.attr.errorTextColor, com.incall.apps.softmanager.R.attr.expandedHintEnabled, com.incall.apps.softmanager.R.attr.helperText, com.incall.apps.softmanager.R.attr.helperTextEnabled, com.incall.apps.softmanager.R.attr.helperTextTextAppearance, com.incall.apps.softmanager.R.attr.helperTextTextColor, com.incall.apps.softmanager.R.attr.hintAnimationEnabled, com.incall.apps.softmanager.R.attr.hintEnabled, com.incall.apps.softmanager.R.attr.hintTextAppearance, com.incall.apps.softmanager.R.attr.hintTextColor, com.incall.apps.softmanager.R.attr.passwordToggleContentDescription, com.incall.apps.softmanager.R.attr.passwordToggleDrawable, com.incall.apps.softmanager.R.attr.passwordToggleEnabled, com.incall.apps.softmanager.R.attr.passwordToggleTint, com.incall.apps.softmanager.R.attr.passwordToggleTintMode, com.incall.apps.softmanager.R.attr.placeholderText, com.incall.apps.softmanager.R.attr.placeholderTextAppearance, com.incall.apps.softmanager.R.attr.placeholderTextColor, com.incall.apps.softmanager.R.attr.prefixText, com.incall.apps.softmanager.R.attr.prefixTextAppearance, com.incall.apps.softmanager.R.attr.prefixTextColor, com.incall.apps.softmanager.R.attr.shapeAppearance, com.incall.apps.softmanager.R.attr.shapeAppearanceOverlay, com.incall.apps.softmanager.R.attr.startIconCheckable, com.incall.apps.softmanager.R.attr.startIconContentDescription, com.incall.apps.softmanager.R.attr.startIconDrawable, com.incall.apps.softmanager.R.attr.startIconTint, com.incall.apps.softmanager.R.attr.startIconTintMode, com.incall.apps.softmanager.R.attr.suffixText, com.incall.apps.softmanager.R.attr.suffixTextAppearance, com.incall.apps.softmanager.R.attr.suffixTextColor};
        public static final int[] ThemeEnforcement = {android.R.attr.textAppearance, com.incall.apps.softmanager.R.attr.enforceMaterialTheme, com.incall.apps.softmanager.R.attr.enforceTextAppearance};
        public static final int[] Toolbar = {android.R.attr.gravity, android.R.attr.minHeight, com.incall.apps.softmanager.R.attr.buttonGravity, com.incall.apps.softmanager.R.attr.collapseContentDescription, com.incall.apps.softmanager.R.attr.collapseIcon, com.incall.apps.softmanager.R.attr.contentInsetEnd, com.incall.apps.softmanager.R.attr.contentInsetEndWithActions, com.incall.apps.softmanager.R.attr.contentInsetLeft, com.incall.apps.softmanager.R.attr.contentInsetRight, com.incall.apps.softmanager.R.attr.contentInsetStart, com.incall.apps.softmanager.R.attr.contentInsetStartWithNavigation, com.incall.apps.softmanager.R.attr.logo, com.incall.apps.softmanager.R.attr.logoDescription, com.incall.apps.softmanager.R.attr.maxButtonHeight, com.incall.apps.softmanager.R.attr.menu, com.incall.apps.softmanager.R.attr.navigationContentDescription, com.incall.apps.softmanager.R.attr.navigationIcon, com.incall.apps.softmanager.R.attr.popupTheme, com.incall.apps.softmanager.R.attr.subtitle, com.incall.apps.softmanager.R.attr.subtitleTextAppearance, com.incall.apps.softmanager.R.attr.subtitleTextColor, com.incall.apps.softmanager.R.attr.title, com.incall.apps.softmanager.R.attr.titleMargin, com.incall.apps.softmanager.R.attr.titleMarginBottom, com.incall.apps.softmanager.R.attr.titleMarginEnd, com.incall.apps.softmanager.R.attr.titleMarginStart, com.incall.apps.softmanager.R.attr.titleMarginTop, com.incall.apps.softmanager.R.attr.titleMargins, com.incall.apps.softmanager.R.attr.titleTextAppearance, com.incall.apps.softmanager.R.attr.titleTextColor};
        public static final int[] Tooltip = {android.R.attr.textAppearance, android.R.attr.textColor, android.R.attr.padding, android.R.attr.layout_margin, android.R.attr.minWidth, android.R.attr.minHeight, android.R.attr.text, com.incall.apps.softmanager.R.attr.backgroundTint};
        public static final int[] View = {android.R.attr.theme, android.R.attr.focusable, com.incall.apps.softmanager.R.attr.paddingEnd, com.incall.apps.softmanager.R.attr.paddingStart, com.incall.apps.softmanager.R.attr.theme};
        public static final int[] ViewBackgroundHelper = {android.R.attr.background, com.incall.apps.softmanager.R.attr.backgroundTint, com.incall.apps.softmanager.R.attr.backgroundTintMode};
        public static final int[] ViewPager2 = {android.R.attr.orientation};
        public static final int[] ViewStubCompat = {android.R.attr.id, android.R.attr.layout, android.R.attr.inflatedId};

        private styleable() {
        }
    }

    public static final class xml {
        public static final int standalone_badge = 0x7f140003;
        public static final int standalone_badge_gravity_bottom_end = 0x7f140004;
        public static final int standalone_badge_gravity_bottom_start = 0x7f140005;
        public static final int standalone_badge_gravity_top_start = 0x7f140006;
        public static final int standalone_badge_offset = 0x7f140007;

        private xml() {
        }
    }

    private R() {
    }
}
