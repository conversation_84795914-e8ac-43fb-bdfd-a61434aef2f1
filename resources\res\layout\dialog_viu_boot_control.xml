<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/caui_config_content_bg_color_primary"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ImageView
        android:layout_width="432dp"
        android:layout_height="252dp"
        android:layout_marginTop="466dp"
        android:src="@drawable/fota_net_error"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="56sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:layout_width="wrap_content"
        android:layout_height="78dp"
        android:layout_marginTop="726dp"
        android:text="@string/dialog_viu_control_tip"
        android:alpha="0.92"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="40sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="836dp"
        android:text="@string/dialog_viu_control_number"
        android:alpha="0.7"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/btn_update_again"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="1028dp"
        android:text="@string/dialog_viu_control_update"
        android:contentDescription="重新升级"
        android:layout_marginStart="904dp"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/btn_power_down"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="1028dp"
        android:text="@string/dialog_viu_control_power_down"
        android:contentDescription="车辆下电"
        android:layout_marginStart="1312dp"
        app:caui_round_btn_type="negative"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
