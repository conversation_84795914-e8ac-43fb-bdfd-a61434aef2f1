<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:tag="layout/layout_main_0"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">
    <ImageView
        android:id="@+id/img_car"
        android:layout_width="1240dp"
        android:layout_height="1240dp"
        android:layout_marginBottom="67dp"
        android:src="@drawable/fota_normal_model"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <View
        android:tag="binding_1"
        android:layout_width="1044dp"
        android:layout_height="820dp"
        android:layout_marginTop="185dp"
        android:layout_marginBottom="204dp"
        android:layout_marginStart="1412dp"
        android:layout_marginEnd="104dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="56sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/tv_current_big_version"
        android:tag="binding_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="104dp"
        android:layout_marginStart="104dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="40sp"
        android:textColor="@color/caui_config_text_color_secondary"
        android:id="@+id/latest_version_tx"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/latest_version_tip"
        android:layout_marginStart="104dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_current_big_version"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/detect_new_version"
        android:tag="binding_3"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="136dp"
        android:contentDescription="检测新版本"
        android:layout_marginStart="104dp"
        app:caui_round_btn_type="main"
        app:caui_round_radius="@dimen/caui_config_corner_radius_x_small"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/latest_version_tx"/>
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="1260dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="80dp"
        android:layout_marginStart="104dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/detect_new_version">
        <com.incall.apps.softmanager.base.view.AutoDownloadSwitchBar
            android:id="@+id/auto_download_sw_main_page"
            android:tag="binding_4"
            android:layout_width="1260dp"
            android:layout_height="wrap_content"/>
        <com.incall.apps.softmanager.base.view.AutoUpdateSwitchBar
            android:id="@+id/auto_sw_main_page"
            android:tag="binding_5"
            android:layout_width="1260dp"
            android:layout_height="wrap_content"/>
        <View
            android:background="@color/caui_config_divider_color_primary"
            android:layout_width="1260dp"
            android:layout_height="2dp"
            android:layout_marginTop="16dp"/>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
