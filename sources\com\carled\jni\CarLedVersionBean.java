package com.carled.jni;

/* loaded from: classes.dex */
public class CarLedVersionBean {
    private String bootVersion;
    private String calVersion;
    private String hardwareNumber;
    private String hardwareVersion;
    private String objName;
    private int response;
    private String softwareNumber;
    private String softwareVersion;

    public int getResponse() {
        return this.response;
    }

    public void setResponse(int i) {
        this.response = i;
    }

    public String getObjName() {
        return this.objName;
    }

    public void setObjName(String str) {
        this.objName = str;
    }

    public String getHardwareNumber() {
        return this.hardwareNumber;
    }

    public void setHardwareNumber(String str) {
        this.hardwareNumber = str;
    }

    public String getHardwareVersion() {
        return this.hardwareVersion;
    }

    public void setHardwareVersion(String str) {
        this.hardwareVersion = str;
    }

    public String getSoftwareNumber() {
        return this.softwareNumber;
    }

    public void setSoftwareNumber(String str) {
        this.softwareNumber = str;
    }

    public String getSoftwareVersion() {
        return this.softwareVersion;
    }

    public void setSoftwareVersion(String str) {
        this.softwareVersion = str;
    }

    public String getBootVersion() {
        return this.bootVersion;
    }

    public void setBootVersion(String str) {
        this.bootVersion = str;
    }

    public String getCalVersion() {
        return this.calVersion;
    }

    public void setCalVersion(String str) {
        this.calVersion = str;
    }

    public String information() {
        StringBuilder sb = new StringBuilder();
        sb.append("response: ").append(this.response).append("\n").append("objName: ").append(this.objName).append("\n").append("hardwareNumber: ").append(this.hardwareNumber).append("\n").append("hardwareVersion: ").append(this.hardwareVersion).append("\n").append("softwareNumber: ").append(this.softwareNumber).append("\n").append("softwareVersion: ").append(this.softwareVersion).append("\n").append("bootVersion: ").append(this.bootVersion).append("\n").append("calVersion: ").append(this.calVersion).append("\n");
        return sb.toString();
    }
}
