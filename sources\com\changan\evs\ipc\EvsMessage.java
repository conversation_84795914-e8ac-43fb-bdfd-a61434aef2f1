package com.changan.evs.ipc;

import android.os.Parcel;
import android.os.Parcelable;
import android.view.Surface;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.ToDoubleFunction;
import java.util.function.ToIntFunction;
import java.util.function.ToLongFunction;
import java.util.stream.Collectors;

/* loaded from: classes.dex */
public class EvsMessage implements Parcelable {
    public static final Parcelable.Creator<EvsMessage> CREATOR = new Parcelable.Creator<EvsMessage>() { // from class: com.changan.evs.ipc.EvsMessage.1
        /* JADX WARN: Can't rename method to resolve collision */
        @Override // android.os.Parcelable.Creator
        public EvsMessage createFromParcel(Parcel parcel) {
            return new EvsMessage(parcel);
        }

        /* JADX WARN: Can't rename method to resolve collision */
        @Override // android.os.Parcelable.Creator
        public EvsMessage[] newArray(int i) {
            return new EvsMessage[i];
        }
    };
    private List<Byte> byteList;
    private int code;
    private List<Double> doubleList;
    private List<Integer> intList;
    private List<Long> longList;
    private int messageId;
    private int requestId;
    private List<String> stringList;
    private List<Surface> surfaceList;
    private long timeStamp;

    @Override // android.os.Parcelable
    public int describeContents() {
        return 0;
    }

    public EvsMessage() {
        this.surfaceList = new ArrayList();
        this.intList = new ArrayList();
        this.longList = new ArrayList();
        this.byteList = new ArrayList();
        this.doubleList = new ArrayList();
        this.stringList = new ArrayList();
    }

    public EvsMessage(int i) {
        this.surfaceList = new ArrayList();
        this.intList = new ArrayList();
        this.longList = new ArrayList();
        this.byteList = new ArrayList();
        this.doubleList = new ArrayList();
        this.stringList = new ArrayList();
        this.messageId = i;
    }

    public int getCode() {
        return this.code;
    }

    public void setCode(int i) {
        this.code = i;
    }

    public int getMessageId() {
        return this.messageId;
    }

    public void setMessageId(int i) {
        this.messageId = i;
    }

    public long getTimeStamp() {
        return this.timeStamp;
    }

    public void setTimeStamp(Long l) {
        this.timeStamp = l.longValue();
    }

    public int getRequestId() {
        return this.requestId;
    }

    public void setRequestId(int i) {
        this.requestId = i;
    }

    public List<Surface> getSurfaceList() {
        return this.surfaceList;
    }

    public void setSurfaceList(List<Surface> list) {
        this.surfaceList = list;
    }

    public List<Integer> getIntList() {
        return new ArrayList(this.intList);
    }

    public List<Long> getLongList() {
        return new ArrayList(this.longList);
    }

    public List<Byte> getByteList() {
        return new ArrayList(this.byteList);
    }

    public List<Double> getDoubleList() {
        return new ArrayList(this.doubleList);
    }

    public List<String> getStringList() {
        return new ArrayList(this.stringList);
    }

    public EvsMessage addData(int i) {
        this.intList.add(Integer.valueOf(i));
        return this;
    }

    public EvsMessage addData(Long l) {
        this.longList.add(l);
        return this;
    }

    public EvsMessage addData(byte b) {
        this.byteList.add(Byte.valueOf(b));
        return this;
    }

    public EvsMessage addData(Double d) {
        this.doubleList.add(d);
        return this;
    }

    public EvsMessage addData(String str) {
        this.stringList.add(str);
        return this;
    }

    public EvsMessage addData(Boolean bool) {
        this.byteList.add(Byte.valueOf(bool.booleanValue() ? (byte) 1 : (byte) 0));
        return this;
    }

    public EvsMessage addData(Surface surface) {
        this.surfaceList.add(surface);
        return this;
    }

    public EvsMessage getEvsMessage() {
        EvsMessage evsMessage = new EvsMessage();
        evsMessage.setMessageId(this.messageId);
        evsMessage.setCode(this.code);
        evsMessage.setTimeStamp(Long.valueOf(this.timeStamp));
        evsMessage.setRequestId(this.requestId);
        return evsMessage;
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeInt(this.messageId);
        parcel.writeLong(this.timeStamp);
        parcel.writeInt(this.requestId);
        parcel.writeInt(this.code);
        parcel.writeTypedList(this.surfaceList);
        parcel.writeIntArray(this.intList.stream().mapToInt(new ToIntFunction() { // from class: com.changan.evs.ipc.-$$Lambda$EvsMessage$uBsCIBAbAW1p84hPJ6ULJah0SPE
            @Override // java.util.function.ToIntFunction
            public final int applyAsInt(Object obj) {
                int intValue;
                intValue = ((Integer) obj).intValue();
                return intValue;
            }
        }).toArray());
        parcel.writeLongArray(this.longList.stream().mapToLong(new ToLongFunction() { // from class: com.changan.evs.ipc.-$$Lambda$EvsMessage$uAjrY3NI4r9F1o9CaCMzj_QnUcY
            @Override // java.util.function.ToLongFunction
            public final long applyAsLong(Object obj) {
                long longValue;
                longValue = ((Long) obj).longValue();
                return longValue;
            }
        }).toArray());
        byte[] bArr = new byte[this.byteList.size()];
        for (int i2 = 0; i2 < this.byteList.size(); i2++) {
            bArr[i2] = this.byteList.get(i2).byteValue();
        }
        parcel.writeByteArray(bArr);
        parcel.writeDoubleArray(this.doubleList.stream().mapToDouble(new ToDoubleFunction() { // from class: com.changan.evs.ipc.-$$Lambda$EvsMessage$bnGGj1hJokzrKZFVqTRt6LP7zqw
            @Override // java.util.function.ToDoubleFunction
            public final double applyAsDouble(Object obj) {
                double doubleValue;
                doubleValue = ((Double) obj).doubleValue();
                return doubleValue;
            }
        }).toArray());
        parcel.writeStringList(this.stringList);
    }

    public void readFromParcel(Parcel parcel) {
        this.messageId = parcel.readInt();
        this.timeStamp = parcel.readLong();
        this.requestId = parcel.readInt();
        this.code = parcel.readInt();
        this.surfaceList = parcel.createTypedArrayList(Surface.CREATOR);
        this.intList = (List) Arrays.stream(parcel.createIntArray()).boxed().collect(Collectors.toList());
        this.longList = (List) Arrays.stream(parcel.createLongArray()).boxed().collect(Collectors.toList());
        this.byteList.clear();
        for (byte b : parcel.createByteArray()) {
            this.byteList.add(Byte.valueOf(b));
        }
        this.doubleList = (List) Arrays.stream(parcel.createDoubleArray()).boxed().collect(Collectors.toList());
        this.stringList = parcel.createStringArrayList();
    }

    protected EvsMessage(Parcel parcel) {
        this.surfaceList = new ArrayList();
        this.intList = new ArrayList();
        this.longList = new ArrayList();
        this.byteList = new ArrayList();
        this.doubleList = new ArrayList();
        this.stringList = new ArrayList();
        this.messageId = parcel.readInt();
        this.timeStamp = parcel.readLong();
        this.requestId = parcel.readInt();
        this.code = parcel.readInt();
        this.surfaceList = parcel.createTypedArrayList(Surface.CREATOR);
        this.intList = (List) Arrays.stream(parcel.createIntArray()).boxed().collect(Collectors.toList());
        this.longList = (List) Arrays.stream(parcel.createLongArray()).boxed().collect(Collectors.toList());
        this.byteList.clear();
        for (byte b : parcel.createByteArray()) {
            this.byteList.add(Byte.valueOf(b));
        }
        this.doubleList = (List) Arrays.stream(parcel.createDoubleArray()).boxed().collect(Collectors.toList());
        this.stringList = parcel.createStringArrayList();
    }
}
