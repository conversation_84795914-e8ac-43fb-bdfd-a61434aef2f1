package com.changan.evs.ipc;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface NativeMessageType extends IInterface {
    public static final int BINDER_DIED = -1;
    public static final int BOOL_FALSE = 0;
    public static final int BOOL_TRUE = 1;
    public static final int CODE_COMMON_CON_NO = 101;
    public static final int CODE_COMMON_CON_OK = 100;
    public static final int CODE_COMMON_MSG_NO = 201;
    public static final int CODE_COMMON_MSG_OK = 200;
    public static final int CODE_COMMON_MSG_TIMEOUT = 203;
    public static final int CODE_COMMON_MSG_UNHANDLED = 202;
    public static final int CODE_DVR_DISK_SPACE_LOW = 300;
    public static final int CODE_SENTRY_DISK_SPACE_LOW = 401;
    public static final int EVS_EXECUTE_CMD_FAILED = -2;
    public static final int NATIVE_APA_GET_SWITCH_STATE = 1802;
    public static final int NATIVE_APA_NOTICE_OPEN_PARK_STATE = 21803;
    public static final int NATIVE_APA_OPEN_PARK = 1805;
    public static final int NATIVE_APA_OPEN_SMALL_AVM_SWITCH_STATE = 1807;
    public static final int NATIVE_APA_PARK_SWITCH = 1801;
    public static final int NATIVE_APA_PLAY_TTS_UP = 21801;
    public static final int NATIVE_APA_SPORG_TRAIN_START_UP = 21802;
    public static final int NATIVE_APA_SPROG_TRAIN_BREAK = 1803;
    public static final int NATIVE_APA_SPROG_TRAIN_END = 1804;
    public static final int NATIVE_APA_UPDATE_SWITCH_STATE = 1806;
    public static final int NATIVE_AVM_CLOSE_APP = 1402;
    public static final int NATIVE_AVM_START_APP = 1401;
    public static final int NATIVE_AVM_STEEP_SECURITY_WINDOW = 1405;
    public static final int NATIVE_AVM_SURFACE_DESTROY = 1404;
    public static final int NATIVE_AVM_SWITCH_ACTIVITY_UP = 21401;
    public static final int NATIVE_AVM_SWITCH_MASK_WINDOW_UP = 21402;
    public static final int NATIVE_AVM_SWITCH_STATE_GET = 1403;
    public static final int NATIVE_AVM_VIEW_SMALL_TO_BIG = 1406;
    public static final int NATIVE_COMMON_SHUTDOWN = 1;
    public static final int NATIVE_DVR_CAMERA_SIGNAL_STATE_UPDATE_UP = 21008;
    public static final int NATIVE_DVR_CAMERA_STATUS_UPDATE_UP = 21007;
    public static final int NATIVE_DVR_CHANGE_VIEW = 1006;
    public static final int NATIVE_DVR_CLOSE_URGENCY = 1005;
    public static final int NATIVE_DVR_CLOSE_VOICE_RECORE = 1013;
    public static final int NATIVE_DVR_CLOSE_WATER_SHOW = 1018;
    public static final int NATIVE_DVR_GET_RECORD_DURATION = 1011;
    public static final int NATIVE_DVR_GET_RECORD_STATUS = 1009;
    public static final int NATIVE_DVR_GET_RESOLUTION = 1016;
    public static final int NATIVE_DVR_GET_VOICE_RECORD_STATUS = 1014;
    public static final int NATIVE_DVR_GET_WATER_SHOW_STATUS = 1019;
    public static final int NATIVE_DVR_OPEN_VOICE_RECORE = 1012;
    public static final int NATIVE_DVR_OPEN_WATER_SHOW = 1017;
    public static final int NATIVE_DVR_RECORD_CYCLE_DELETE_UP = 21005;
    public static final int NATIVE_DVR_RECORD_DISK_ERROR_UP = 21004;
    public static final int NATIVE_DVR_RECORD_FINISH_UP = 21002;
    public static final int NATIVE_DVR_RECORD_UNKNOWN_ERROR_UP = 21003;
    public static final int NATIVE_DVR_SET_LANGUAGE = 1020;
    public static final int NATIVE_DVR_SET_RECORD_DURATION = 1010;
    public static final int NATIVE_DVR_SET_RESOLUTION = 1015;
    public static final int NATIVE_DVR_SPACE_LOW_UP = 21006;
    public static final int NATIVE_DVR_START_PREVIEW = 1007;
    public static final int NATIVE_DVR_START_RECORD = 1001;
    public static final int NATIVE_DVR_START_URGENCY = 1004;
    public static final int NATIVE_DVR_STOP_PREVIEW = 1008;
    public static final int NATIVE_DVR_STOP_RECORD = 1002;
    public static final int NATIVE_DVR_STREAM_STATUS_UPDATE_UP = 21001;
    public static final int NATIVE_DVR_TAKE_PICTURE = 1003;
    public static final int NATIVE_DVR_UPDATE_LOCATION = 1021;
    public static final int NATIVE_DVR_UPDATE_SDCARD_STATUS = 1022;
    public static final int NATIVE_PARKNUM_UPLOAD_UP = 21601;
    public static final int NATIVE_PARKNUM_USER_LOGIN_UPDATE = 1601;
    public static final int NATIVE_RMA_PHONE_TOUCH_POINT = 2001;
    public static final int NATIVE_RMA_SWITCH_STATE = 2002;
    public static final int NATIVE_SENTRY_CYCLE_DELETE_UP = 21204;
    public static final int NATIVE_SENTRY_FUNCTION_EXIT = 1203;
    public static final int NATIVE_SENTRY_FUNCTION_START_TO_DVR = 1206;
    public static final int NATIVE_SENTRY_FUNCTION_START_TO_SENTRY = 1202;
    public static final int NATIVE_SENTRY_GET_RECORD_STATE = 1204;
    public static final int NATIVE_SENTRY_RECORD_DISK_ERROR_UP = 21203;
    public static final int NATIVE_SENTRY_RECORD_FINISH_UP = 21201;
    public static final int NATIVE_SENTRY_RECORD_UNKNOWN_ERROR_UP = 21202;
    public static final int NATIVE_SENTRY_START_RECORD = 1201;
    public static final int NATIVE_SENTRY_TRIG_EVENT_UP = 21205;
    public static final int NATIVE_SENTRY_UPDATE_SDCARD_STATUS = 1205;
    public static final int PARAMETER_APA_PARK_APP_LIST = 3;
    public static final int PARAMETER_APA_PARK_ICON = 0;
    public static final int PARAMETER_APA_PARK_OFF = 0;
    public static final int PARAMETER_APA_PARK_ON = 1;
    public static final int PARAMETER_APA_PARK_STEER = 2;
    public static final int PARAMETER_APA_PARK_VOICE = 1;
    public static final int PARAMETER_AVM_START_SOFTKEY = 1;
    public static final int PARAMETER_AVM_START_VOICE = 2;
    public static final int PARAMETER_CAMERA_FRONT = 1;
    public static final int PARAMETER_CAMERA_LEFT = 4;
    public static final int PARAMETER_CAMERA_PERISCOPIC_TYPE_FROUNT = 5;
    public static final int PARAMETER_CAMERA_REAR = 2;
    public static final int PARAMETER_CAMERA_RIGHT = 8;
    public static final int PARAMETER_CAMERA_TYPE_FRONT = 0;
    public static final int PARAMETER_CAMERA_TYPE_GROUP = 4;
    public static final int PARAMETER_CAMERA_TYPE_LEFT = 2;
    public static final int PARAMETER_CAMERA_TYPE_REAR = 1;
    public static final int PARAMETER_CAMERA_TYPE_RIGHT = 3;
    public static final int PARAMETER_CAMERA_VISION_FRONT = 1;
    public static final int PARAMETER_CAMERA_VISION_GROUP = 16;
    public static final int PARAMETER_CAMERA_VISION_LEFT = 4;
    public static final int PARAMETER_CAMERA_VISION_PERISCOPIC = 32;
    public static final int PARAMETER_CAMERA_VISION_REAR = 2;
    public static final int PARAMETER_CAMERA_VISION_RIGHT = 8;
    public static final int PARAMETER_COMMON_0 = 0;
    public static final int PARAMETER_COMMON_1 = 1;
    public static final int PARAMETER_DISK_DAMAGE = 2;
    public static final int PARAMETER_DISK_WRITE_READ_LOW_SPEED = 1;
    public static final int PARAMETER_LANGUAGE_CN = 2;
    public static final int PARAMETER_LANGUAGE_EN = 1;
    public static final int PARAMETER_MEDIA_TYPE_ALL = 0;
    public static final int PARAMETER_MEDIA_TYPE_ASSIST = 3;
    public static final int PARAMETER_MEDIA_TYPE_CYCLE = 1;
    public static final int PARAMETER_MEDIA_TYPE_PHOTO = 4;
    public static final int PARAMETER_MEDIA_TYPE_URGENCY = 2;
    public static final int PARAMETER_RESOLUTION_HEIGHT_1080 = 1080;
    public static final int PARAMETER_RESOLUTION_HEIGHT_720 = 720;
    public static final int PARAMETER_RESOLUTION_WIDTH_1080 = 1080;
    public static final int PARAMETER_RESOLUTION_WIDTH_1280 = 1280;
    public static final int PARAMETER_RESOLUTION_WIDTH_1920 = 1920;
    public static final int PARAMETER_SENTRY_ALARM_NONE = 1;
    public static final int PARAMETER_SENTRY_ALARM_PEOPLE_CAR = 3;
    public static final int PARAMETER_SENTRY_ALARM_SHARK = 2;
    public static final int PARAMETER_TRANSIENT_TYPE_U = 1;
    public static final int PARAMETER_TRANSIENT_TYPE_URGENCY = 0;
    public static final int PARAMETER_UPDATE_TYPE_ADD = 0;
    public static final int PARAMETER_UPDATE_TYPE_CLEAR = 2;
    public static final int PARAMETER_UPDATE_TYPE_DEL = 1;
    public static final int PARAMETER_WATER_PREVIEW = 1;
    public static final int PARAMETER_WATER_RECORD = 2;
    public static final int PARAMETER_WATER_TAKE_PICTURE = 4;

    public static class Default implements NativeMessageType {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }
    }

    public static abstract class Stub extends Binder implements NativeMessageType {
        private static final String DESCRIPTOR = "com.changan.evs.ipc.NativeMessageType";

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static NativeMessageType asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof NativeMessageType)) {
                return (NativeMessageType) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i == 1598968902) {
                parcel2.writeString(DESCRIPTOR);
                return true;
            }
            return super.onTransact(i, parcel, parcel2, i2);
        }

        private static class Proxy implements NativeMessageType {
            public static NativeMessageType sDefaultImpl;
            private IBinder mRemote;

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }
        }

        public static boolean setDefaultImpl(NativeMessageType nativeMessageType) {
            if (Proxy.sDefaultImpl != null) {
                throw new IllegalStateException("setDefaultImpl() called twice");
            }
            if (nativeMessageType == null) {
                return false;
            }
            Proxy.sDefaultImpl = nativeMessageType;
            return true;
        }

        public static NativeMessageType getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }
    }
}
