package com.ca.car.proxy.hardware.defs;

import java.util.ArrayList;

/* loaded from: classes.dex */
public final class VehicleProperty {
    public static final int ABS_ACTIVE = 287310858;
    public static final int ADAS_ACC_AUTOBRAKE_ON = 557847328;
    public static final int ADAS_ACC_AUTOBRAKE_PEOPLE_ON = 557847327;
    public static final int ADAS_ACC_CRUISE_MODE = 557847329;
    public static final int ADAS_ACC_CTA_ON = 557847343;
    public static final int ADAS_ACC_FCW_OVERSPEED_ON = 557847337;
    public static final int ADAS_ACC_FCW_OVERSPEED_SOUND_ON = 557847338;
    public static final int ADAS_ACC_FCW_REAR_COLLISION_ON = 557847339;
    public static final int ADAS_ACC_FCW_WARN_ON = 826286179;
    public static final int ADAS_ACC_FCW_WARN_STATE = 557847330;
    public static final int ADAS_ACC_ISA_ON = 557847331;
    public static final int ADAS_ACC_LDW_LAS_MODE = 557847340;
    public static final int ADAS_ACC_OBJENABLE_ON = 557847332;
    public static final int ADAS_ACC_PARALLEL_LINE_ASSIST_MODE = 557847333;
    public static final int ADAS_ACC_PCW_FRONT_LOWSPEED_PEDESTRIAN_ON = 557847341;
    public static final int ADAS_ACC_SENSITIVITY = 826286178;
    public static final int ADAS_ACC_SPEED_VALUE = 557847334;
    public static final int ADAS_ACC_ULS_FKP_ON = 557847342;
    public static final int ADAS_ACC_WORK_MODE = 557847335;
    public static final int ADAS_APA_ABORTFEEDBACK = 826286149;
    public static final int ADAS_APA_ACCPEDSHIELDREQ = 826286132;
    public static final int ADAS_APA_ACTIVATION_STATUS = 826286125;
    public static final int ADAS_APA_ACTIVE_ON = 557847232;
    public static final int ADAS_APA_ASPAVAILABLESTATUS = 826286105;
    public static final int ADAS_APA_ASPSTATUS = 826286107;
    public static final int ADAS_APA_ASP_MODE_SELECT = 826286084;
    public static final int ADAS_APA_ASP_REMIND_ON = 557847233;
    public static final int ADAS_APA_ASP_SWITCH = 826286081;
    public static final int ADAS_APA_BCMHORNCOMMAND = 826286108;
    public static final int ADAS_APA_BLECONNECTIONREMIND = 826286102;
    public static final int ADAS_APA_BSD_LCA_ON = 557847234;
    public static final int ADAS_APA_BUTTONPRESS = 826286144;
    public static final int ADAS_APA_CLOSEWINFEEDBACK = 826286161;
    public static final int ADAS_APA_CONTROLFEEDBACK = 826286150;
    public static final int ADAS_APA_COORDINATEANGLE = 826351687;
    public static final int ADAS_APA_CROSSMODESELECTREQ = 826286106;
    public static final int ADAS_APA_CROSS_PARKING_MODE = 826286085;
    public static final int ADAS_APA_DYNAMIC_SLOT_NOTICE = 557847235;
    public static final int ADAS_APA_EMERGENCEBRAKE = 826286141;
    public static final int ADAS_APA_ENGINEOFFFEEDBACK = 826286162;
    public static final int ADAS_APA_ENGINETRQREQENABLE = 826286123;
    public static final int ADAS_APA_ENGTORQREQ = 826286124;
    public static final int ADAS_APA_EPASFAILED = 826286148;
    public static final int ADAS_APA_EPBREQUEST = 826286117;
    public static final int ADAS_APA_EPBREQUESTVALID = 826286118;
    public static final int ADAS_APA_ERRORSTATUS = 826286139;
    public static final int ADAS_APA_ESPDECOMPRESSIONMODEL = 826286133;
    public static final int ADAS_APA_FUNCTION_ON_OFF_STATE = 557847236;
    public static final int ADAS_APA_HSAHDFORBIDDEN = 826286128;
    public static final int ADAS_APA_INCREASEPRESSUREREQ = 826286145;
    public static final int ADAS_APA_INDICATION = 826286140;
    public static final int ADAS_APA_LAEBNOTICE = 826286103;
    public static final int ADAS_APA_LAEB_ENABLE_STATUS = 826286083;
    public static final int ADAS_APA_LAEB_STATUS = 826286101;
    public static final int ADAS_APA_LOCKFEEDBACK = 826286163;
    public static final int ADAS_APA_LSCACTION = 826286127;
    public static final int ADAS_APA_MODE = 557847237;
    public static final int ADAS_APA_MODE_SELECT = 826286167;
    public static final int ADAS_APA_NUMBEROFPARKINGSLOT = 826286112;
    public static final int ADAS_APA_OBSTACLE_SALF_DISTANCE = 557847238;
    public static final int ADAS_APA_ON_TOUCH = 826351704;
    public static final int ADAS_APA_PARKINGMODEAVAILABLESTS = 826286113;
    public static final int ADAS_APA_PARKING_CONFIRM_STATE = 557847240;
    public static final int ADAS_APA_PARKING_PERCENTAGE_INFO = 557847241;
    public static final int ADAS_APA_PARKING_RESUME_STATE = 557847242;
    public static final int ADAS_APA_PARKNOTICE = 826286146;
    public static final int ADAS_APA_PARKNOTICE_4 = 826286130;
    public static final int ADAS_APA_PARK_NOTICE_5 = 826286100;
    public static final int ADAS_APA_PARK_NOTICE_INFO = 557847239;
    public static final int ADAS_APA_PEPS_ENGINEOFF_LOCKREQUEST = 826286110;
    public static final int ADAS_APA_PREFILLREQ = 826286131;
    public static final int ADAS_APA_REBUILD_TARGET_DIST_INFO = 557912779;
    public static final int ADAS_APA_REMOTEAPADONEKEYOFFREQ = 826286165;
    public static final int ADAS_APA_REMOTEONOFF = 826286143;
    public static final int ADAS_APA_REMOTEPARKINGOFF = 826286166;
    public static final int ADAS_APA_REMOTEPARKINGON = 826286164;
    public static final int ADAS_APA_REMOTEPARKINGUSINGREMIND = 826286104;
    public static final int ADAS_APA_REMOTE_DISTANCE = 557847244;
    public static final int ADAS_APA_RFBT_APA_MANEUVER_CANCEL = 826286088;
    public static final int ADAS_APA_RFBT_APA_MANEUVER_START = 826286086;
    public static final int ADAS_APA_RFBT_APA_MANEUVER_SUSPEND = 826286087;
    public static final int ADAS_APA_RFBT_APA_POMODE_SELECT = 826286089;
    public static final int ADAS_APA_RFBT_ASP_SWITCH = 826286090;
    public static final int ADAS_APA_RFBT_CELLPHONE_APA_MODULE_ENABLE = 826286092;
    public static final int ADAS_APA_RFBT_RPA_SWITCH = 826286093;
    public static final int ADAS_APA_RFBT_SLOT_USR_SELECTED = 826286091;
    public static final int ADAS_APA_RFBT_VERTICAL_BAKWARD = 826286095;
    public static final int ADAS_APA_RFBT_VERTICAL_FORWARD = 826286094;
    public static final int ADAS_APA_SLOTVOICESELECT = 826286158;
    public static final int ADAS_APA_SLOT_DISTANCE = 557847245;
    public static final int ADAS_APA_SLOT_DISTANCE_ALL = 826351695;
    public static final int ADAS_APA_SLOT_NOTICE_INFO = 557847246;
    public static final int ADAS_APA_SLOT_TYPE = 557847247;
    public static final int ADAS_APA_SLOT_TYPE_ALL = 826351696;
    public static final int ADAS_APA_SLOT_USER_STATE = 557847248;
    public static final int ADAS_APA_STEERINGANGLEREQ = 826286142;
    public static final int ADAS_APA_STEERINGANGLEREQPROTECTION = 826286138;
    public static final int ADAS_APA_SYSTEM_FAILURE_FLAG = 826286082;
    public static final int ADAS_APA_TARGETACCELERATION = 826286119;
    public static final int ADAS_APA_TARGETACCELERATIONVALID = 826286120;
    public static final int ADAS_APA_TCUCLUTCHCOMBINATIONREQ = 826286111;
    public static final int ADAS_APA_TRANSPRNDSHIFTENABLE = 826286126;
    public static final int ADAS_APA_TRANSPRNDSHIFTREQUEST = 826286121;
    public static final int ADAS_APA_TRANSPRNDSHIFTREQVALID = 826286122;
    public static final int ADAS_APA_TURNLIGHTSCOMMAND = 826286147;
    public static final int ADAS_APA_TYPE = 826286157;
    public static final int ADAS_APA_ULS_FKP_WARNINGSOUNDSTATUS = 826286134;
    public static final int ADAS_APA_VEHICLEFRONTDETECT = 826286109;
    public static final int ADAS_BLIND_AREA_DETECTION_ON = 557848029;
    public static final int ADAS_CTA_LEFT_ALERT = 557848012;
    public static final int ADAS_CTA_RIGHT_ALERT = 557848013;
    public static final int ADAS_HDC_CONTROL_ON = 557847280;
    public static final int ADAS_HDC_STATE = 555750129;
    public static final int ADAS_IACC_LAS_ON = 557848031;
    public static final int ADAS_KEY_DETECTED_STATE = 557848030;
    public static final int ADAS_KEY_VEH_NUMBER_INFO = 557848028;
    public static final int ADAS_KEY_VEH_RELEVANCE_STATE = 557848027;
    public static final int ADAS_LAS_CALIBRATION_STATUS = 557848017;
    public static final int ADAS_LAS_HOST_LANE_LEFT_STATUS = 557848020;
    public static final int ADAS_LAS_HOST_LANE_RIGHT_STATUS = 557848021;
    public static final int ADAS_LAS_LDW_SENSITIVITY = 557848016;
    public static final int ADAS_LAS_LDW_SHAKELEV_STATE = 557847424;
    public static final int ADAS_LAS_LDW_STATUS = 557848015;
    public static final int ADAS_LAS_LEFT_WARNING_STATUS = 557848018;
    public static final int ADAS_LAS_LLANE_MARKER_TYPE = 557848022;
    public static final int ADAS_LAS_MODE_SELECTION = 557848014;
    public static final int ADAS_LAS_RIGHT_WARNING_STATUS = 557848019;
    public static final int ADAS_LAS_RLANE_MARKER_TYPE = 557848023;
    public static final int ADAS_LAS_WARNINGMODE_STATE = 557847425;
    public static final int AIR_CLEANER_COMM_FID_AUTO = 826282509;
    public static final int AIR_CLEANER_COMM_FID_FRAGRANCE = 826282511;
    public static final int AIR_CLEANER_COMM_FID_OFF = 826282510;
    public static final int AIR_CLEANER_COMM_FID_WIN = 826282508;
    public static final int AIR_CLEANER_STATUS_FID_AUTOSTATUS = 826282499;
    public static final int AIR_CLEANER_STATUS_FID_DISPLAYACTIVE = 826282506;
    public static final int AIR_CLEANER_STATUS_FID_FILTERLIFEEND = 826282497;
    public static final int AIR_CLEANER_STATUS_FID_FRAGRANCESW = 826282504;
    public static final int AIR_CLEANER_STATUS_FID_INCAR_PM25_DATA = 826282502;
    public static final int AIR_CLEANER_STATUS_FID_INCAR_PM25_LVL = 826282500;
    public static final int AIR_CLEANER_STATUS_FID_OUTCAR_PM25_DATA = 826282503;
    public static final int AIR_CLEANER_STATUS_FID_OUTCAR_PM25_LVL = 826282501;
    public static final int AIR_CLEANER_STATUS_FID_PROMPTREQ = 826282505;
    public static final int AIR_CLEANER_STATUS_FID_WINDSET = 826282507;
    public static final int AIR_CLEANER_STATUS_FID_WORK_STATUS = 826282498;
    public static final int APU_STATUS = 826281991;
    public static final int AP_DISPLAY_LEAVE_CAR_ANIMATION_STATE = 555745473;
    public static final int AP_DRIVE_MODE_SET_STATUS = 557842624;
    public static final int AP_MEDIA_ALBUM_INFO = 554696800;
    public static final int AP_MEDIA_PLAYING_CURRENT_TIME = 557842529;
    public static final int AP_MEDIA_PLAYING_SOURCE_INFO = 554696803;
    public static final int AP_MEDIA_PLAYING_SOURCE_MESSAGE = 554696804;
    public static final int AP_MEDIA_PLAYING_STATE = 557842533;
    public static final int AP_MEDIA_PLAYING_TOTAL_TIME = 557842534;
    public static final int AP_MEDIA_SINGER_NAME = 554696807;
    public static final int AP_NAVI_BYROAD_SIGNAL = 557842576;
    public static final int AP_NAVI_COUNTRY_CODE_VALUE = 554696849;
    public static final int AP_NAVI_CURRENT_ROAD_TYPE = 557842578;
    public static final int AP_NAVI_CURVE_DISTANCE = 557842579;
    public static final int AP_NAVI_ENTER_CURVE_INFO = 557842580;
    public static final int AP_NAVI_ENTER_RAMP_INFO = 557842581;
    public static final int AP_NAVI_ENTER_TUNNEL_INFO = 557842582;
    public static final int AP_NAVI_GUIGANCE_ON = 557842583;
    public static final int AP_NAVI_NEXT_ROADCROSS_DISTANCE = 826286209;
    public static final int AP_NAVI_NUMBER = 557842585;
    public static final int AP_NAVI_NUMBER_V2 = 557842586;
    public static final int AP_NAVI_RAMP2_DISTANCE = 557842589;
    public static final int AP_NAVI_RAMP_INFO = 557842626;
    public static final int AP_NAVI_RAMP_SLOPE_FAR_VALUE = 557842587;
    public static final int AP_NAVI_RAMP_SLOPE_INFO = 557842588;
    public static final int AP_NAVI_ROAD_CURVATURE = 557842590;
    public static final int AP_NAVI_ROAD_CURVATURE_FAR_VALUE = 557842591;
    public static final int AP_NAVI_SPEEDLIMIT_SIGN_ON = 557842596;
    public static final int AP_NAVI_SPEED_LIMIT_DISTANCE = 557842592;
    public static final int AP_NAVI_SPEED_LIMIT_TYPE = 557842593;
    public static final int AP_NAVI_SPEED_LIMIT_UNITS = 557842594;
    public static final int AP_NAVI_SPEED_LIMIT_VALUE = 557842595;
    public static final int AP_NAVI_STATE = 557842597;
    public static final int AP_NAVI_STATE_INFO_ICON_NUMBER = 826351747;
    public static final int AP_NAVI_STATE_INFO_LOCATION = 827400322;
    public static final int AP_NAVI_TUNNEL_DISTANCE = 557842598;
    public static final int AP_NAVI_TUNNEL_LENGTH = 557842599;
    public static final int AP_PHONE_CONTACT_INFO = 554696752;
    public static final int AP_PHONE_NUMBER = 554696753;
    public static final int AP_PHONE_STATE = 557842482;
    public static final int AP_POWER_BOOTUP_REASON = 289409538;
    public static final int AP_POWER_STATE_REPORT = 289475073;
    public static final int AP_POWER_STATE_REQ = 289475072;
    public static final int AP_SCENE_MODE_ACT = 557848831;
    public static final int AP_SCENE_MODE_CTL_ON = 557848830;
    public static final int AP_USER_STATE = 557848829;
    public static final int AUDIO_MUTE = 826282004;
    public static final int AUDIO_SAMPLE_DATA = 829427730;
    public static final int AUDIO_SAMPLE_STATE = 826282001;
    public static final int AVM_360_AVM_DET_REQ = 826286170;
    public static final int AVM_3D_ENABLE = 557903547;
    public static final int AVM_3D_HOR_ANGLE = 557903548;
    public static final int AVM_BODY_COLOR_STATUS = 557903558;
    public static final int AVM_BOOT_ANIMATION = 557903564;
    public static final int AVM_CALIBRATION = 557903550;
    public static final int AVM_CALIBRATION_CONFIRM = 557903551;
    public static final int AVM_COMM_FID_ACTIVE = 826282538;
    public static final int AVM_COMM_FID_ANGLE = 826282540;
    public static final int AVM_COMM_FID_ASSIST_LINE = 826282539;
    public static final int AVM_COMM_FID_CALI_INFO_REQ = 826282544;
    public static final int AVM_COMM_FID_CALI_START = 826282550;
    public static final int AVM_COMM_FID_CALI_START_SN = 829428271;
    public static final int AVM_COMM_FID_CAM_DISP_REQ = 826282542;
    public static final int AVM_COMM_FID_LANGUAGE = 826282546;
    public static final int AVM_COMM_FID_ORDINATE_OPERATION = 826348085;
    public static final int AVM_COMM_FID_PURPOSE_KEY = 826282548;
    public static final int AVM_COMM_FID_RADAR_TRIGGER = 826282541;
    public static final int AVM_COMM_FID_SCREEN_STATE = 826282547;
    public static final int AVM_COMM_FID_TIME_INFO = 826282545;
    public static final int AVM_COORDINATE_TRANSFER = 557969080;
    public static final int AVM_DEF_OPEN = 557903565;
    public static final int AVM_DISPLAY_FORM = 557903545;
    public static final int AVM_DISPLAY_SWITCH = 557903540;
    public static final int AVM_FORMAT_FAT32_REQ = 557903529;
    public static final int AVM_LICENSE_NUMBER_ACK = 557969098;
    public static final int AVM_LICENSE_NUMBER_SET = 557969097;
    public static final int AVM_LINE_SWITCH = 557903543;
    public static final int AVM_LIVE_INFO = 557903942;
    public static final int AVM_MOD_ENABLE = 557903549;
    public static final int AVM_OBJECT_DETECT = 557903568;
    public static final int AVM_OBJECT_DETECT_ALERT = 557903569;
    public static final int AVM_OVERSPEED_WARNING = 557903552;
    public static final int AVM_RADAR_TRIGGER_SWITCH = 557903542;
    public static final int AVM_RECORD_STATUS = 557903566;
    public static final int AVM_REMOTE_DETECT_UPLOAD_STATUS = 557903557;
    public static final int AVM_SCREEN_OPERATE_STATUS = 557903560;
    public static final int AVM_STATUS_FID_CALIBRATION_STATE = 826282532;
    public static final int AVM_STATUS_FID_CALI_TIME_REQ = 826282533;
    public static final int AVM_STATUS_FID_CTRL_REQ = 826282529;
    public static final int AVM_STATUS_FID_DISP_VERSION = 823136803;
    public static final int AVM_STATUS_FID_FRONT_CAM_CALI_FAULT = 826282534;
    public static final int AVM_STATUS_FID_LEFT_CAM_CALI_FAULT = 826282536;
    public static final int AVM_STATUS_FID_MAIN_VERSION = 823136802;
    public static final int AVM_STATUS_FID_REAR_CAM_CALI_FAULT = 826282535;
    public static final int AVM_STATUS_FID_RIGHT_CAM_CALI_FAULT = 826282537;
    public static final int AVM_TOP_LOOK_DOWN_SWITCH = 826286169;
    public static final int AVM_TRANSPARENT_CAR_SWITCH = 557903553;
    public static final int AVM_TURNLIGHT_TRIGGER_SWITCH = 557903541;
    public static final int AVM_VISION_ANGLE_REQUES = 557903546;
    public static final int AVM_WHEEL_HUB_STYLE = 557903559;
    public static final int BODY_AUTO_HEAD_LIGHT = 557845051;
    public static final int BODY_BATTERY_BCN_LEVEL = 557845120;
    public static final int BODY_BATTERY_REMAINING_INFO = 557845121;
    public static final int BODY_BATTERY_U_BATT_INFO = 557845122;
    public static final int BODY_BUZZER_WARNING_MODE = 826286173;
    public static final int BODY_CONTROL_PANEL_DIS_TYPE = 826286199;
    public static final int BODY_CONTROL_PANEL_MEADIA_MUTE = 826286201;
    public static final int BODY_CONTROL_PANEL_MEADIA_VOL = 826286197;
    public static final int BODY_CONTROL_PANEL_NAVI_VOL = 826286196;
    public static final int BODY_CONTROL_PANEL_PHONE_MUTE = 826286203;
    public static final int BODY_CONTROL_PANEL_TBOX_PHONE_VOL = 826286202;
    public static final int BODY_CONTROL_PANEL_TTS_VOL = 826286200;
    public static final int BODY_CONTROL_PANEL_VOLUMEL_ADJUST_LEVEL = 557845217;
    public static final int BODY_CONTROL_PANEL_VOLUME_ADJUST_DIRECTION = 557845216;
    public static final int BODY_CONTROL_PANEL_VOL_AJ_SENSSTIVE = 826286194;
    public static final int BODY_CONTROL_PANEL_VOL_AJ_TYPE = 826286198;
    public static final int BODY_CONTROL_PANEL_VOL_MUTE = 826286195;
    public static final int BODY_DOME_LIGHT = 624953914;
    public static final int BODY_DOOR_AUDIOWARNING_ON = 557844832;
    public static final int BODY_DOOR_AUTO_IGNOFF_UNLOCK_ON = 557844833;
    public static final int BODY_DOOR_AUTO_SPEEKLOCK_ON = 557844834;
    public static final int BODY_DOOR_FCW_OPEN_ON = 557844844;
    public static final int BODY_DOOR_LEAVE_AUTO_LOCK_STATE = 557844835;
    public static final int BODY_DOOR_LOCK_STATE = 557844836;
    public static final int BODY_DOOR_OPEN_LOCK_PROTECT_ON = 557844837;
    public static final int BODY_DOOR_REMOTE_LOCK_REQ = 557844838;
    public static final int BODY_DOOR_RKE_UNLOCKDOOR_MODE = 557844839;
    public static final int BODY_DOOR_SMART_TRUNKULOCK_ON = 557844840;
    public static final int BODY_DOOR_TRUNK_DOOR_POS = 557903928;
    public static final int BODY_DOOR_TRUNK_DOOR_STATE = 557844845;
    public static final int BODY_DOOR_TWICE_LOCK_ON = 557844841;
    public static final int BODY_DOOR_UNLOCK_STATE = 557844842;
    public static final int BODY_DOOR_WELCOME_UNLOCK_ON = 557844843;
    public static final int BODY_EMERGENCY_LIGHT = 557845049;
    public static final int BODY_ENGINE_OIL_INFO = 557845123;
    public static final int BODY_LIGHT_ATMO_ASSOCIATE_DRIVING_STATE = 557845024;
    public static final int BODY_LIGHT_ATMO_AUTO_ADJUST_ON = 557845025;
    public static final int BODY_LIGHT_ATMO_BRIGHT_LEVEL = 557845026;
    public static final int BODY_LIGHT_ATMO_COLOR_STATE = 557845027;
    public static final int BODY_LIGHT_ATMO_ON = 557845029;
    public static final int BODY_LIGHT_DAY_NIGHT_ON = 557845028;
    public static final int BODY_LIGHT_DIMMER_LEVEL = 557845030;
    public static final int BODY_LIGHT_DOME_ON = 557845031;
    public static final int BODY_LIGHT_DOOR_ON = 557845032;
    public static final int BODY_LIGHT_FOLLOWME_HOME_STATE = 557845033;
    public static final int BODY_LIGHT_HEAD_AUTO_ON = 557845034;
    public static final int BODY_LIGHT_HEAD_HEIGHT_STATE = 557845035;
    public static final int BODY_LIGHT_HMAENABLE_ON = 557845036;
    public static final int BODY_LIGHT_LANE_CHANGE_TURN_STATE = 557845037;
    public static final int BODY_LIGHT_NODE_CTL = 557845038;
    public static final int BODY_LIGHT_REALTIME_ON = 557845039;
    public static final int BODY_LIGHT_SMART_UNLOCK_ON = 557845040;
    public static final int BODY_LIGHT_SMART_WELCOMELIGHT_ON = 557845041;
    public static final int BODY_LIGHT_WELCOME_MODE = 557845042;
    public static final int BODY_LIGHT_WIDTHLAMP_ON = 557845043;
    public static final int BODY_LIGHT_WIDTHLAMP_SW = 557845044;
    public static final int BODY_LOCK_AUTO_CLS_WIN_SET = 557903888;
    public static final int BODY_MIRROR_AUTOFOLD_ON = 557844928;
    public static final int BODY_MIRROR_REAR_BACK_ASSISTANCE_ON = 557844929;
    public static final int BODY_OUTLIGHT_FLASH_CTL = 829431920;
    public static final int BODY_REMOTE_CTRL_SUNROOF_SET = 557903889;
    public static final int BODY_REMOTE_CTRL_SW_SET = 557903891;
    public static final int BODY_SEAT_COMTIY_ON = 557903905;
    public static final int BODY_SEAT_DSM_MEMORY_REQ = 557844976;
    public static final int BODY_SEAT_EASY_ENTRY_ON = 557844977;
    public static final int BODY_SEAT_REAR_BELT_WARNING_ON = 557844978;
    public static final int BODY_SIRENS_SOUND_ALARM_MODE = 557903892;
    public static final int BODY_STEERING_WHEEL_CUSTOME_STATE = 557845218;
    public static final int BODY_STEERING_WHEEL_STEERING_ANGLE = 557845221;
    public static final int BODY_SUNROOF_SUNVISOR_ON_OFF = 591523858;
    public static final int BODY_TIRE_PRESSURE_STATE = 557910608;
    public static final int BODY_TIRE_TEMP_STATE = 557910609;
    public static final int BODY_TURN_LEFT_SIGNAL_STATE = 557845045;
    public static final int BODY_TURN_LEFT_SIGNAL_SWITCH = 557845046;
    public static final int BODY_TURN_RIGHT_SIGNAL_STATE = 557845047;
    public static final int BODY_TURN_RIGHT_SIGNAL_SWITCH = 557845048;
    public static final int BODY_WINDOW_LOCK_AUTO_UP_ON = 557844880;
    public static final int BODY_WINDOW_REAR_WIPER_ON = 557844881;
    public static final int BODY_WINDOW_SUNROOFRAIN_DETECTClOSE_ON = 557844883;
    public static final int BODY_WINDOW_SUNROOF_CONTROL_POS = 557903926;
    public static final int BODY_WINDOW_SUNROOF_CONTROL_STATE = 557844882;
    public static final int BODY_WINDOW_SUNROOF_MOTOR_STATE = 557844892;
    public static final int BODY_WINDOW_SUNROOF_STATE = 557844893;
    public static final int BODY_WINDOW_SUNROOT_POS_DIRECTION = 557903972;
    public static final int BODY_WINDOW_SUNSHADE_POS = 557903927;
    public static final int BODY_WINDOW_SUNSHADE_STATE = 557844884;
    public static final int BODY_WINDOW_WASH = 557844888;
    public static final int BODY_WINDOW_WIPER = 591399319;
    public static final int BODY_WINDOW_WIPER_INTERVAL = 557844889;
    public static final int BODY_WINDOW_WIPER_SENSOR = 557844891;
    public static final int BODY_WIRELESS_CHARGING_ON = 557845220;
    public static final int CAN_DEV_AC_IS_CONNECTED = 826286176;
    public static final int CAN_DEV_APA_IS_CONNECTED = 826286177;
    public static final int CAN_DEV_AVM_IS_CONNECTED = 826286175;
    public static final int CAN_DEV_DVR_IS_CONNECTED = 826286174;
    public static final int CAR_VEHICLE_REQ = 557969497;
    public static final int CAR_VOLTAGE_STATUS = 557903971;
    public static final int CURRENT_GEAR = 289408001;
    public static final int DISPLAY_BRIGHTNESS = 289409539;
    public static final int DISP_MODE = 826281990;
    public static final int DISP_SETTING_FID_SCENE_MODE = 826349824;
    public static final int DOOR_LOCK = 371198722;
    public static final int DOOR_MOVE = 373295873;
    public static final int DOOR_POS = 373295872;
    public static final int DRIVE_ACC_FCW_OVERSPEED_INFO = 557847336;
    public static final int DRIVE_ENGINE_POWER_MODE = 557846432;
    public static final int DRIVE_ENGINE_START_ON = 557846433;
    public static final int DRIVE_ENGINE_STATUS = 557846435;
    public static final int DRIVE_ENGINE_STOP_START_ON = 557846434;
    public static final int DRIVE_ESC_ON = 557846528;
    public static final int DRIVE_GEAR_SHIFT_POSTION_VALID_STATE = 557846529;
    public static final int DRIVE_LAEB_ON = 557848025;
    public static final int DRIVE_LAS_FATIGUE_MONITOR_ON = 557848024;
    public static final int DRIVE_MODE_MEMORY_ON = 557846530;
    public static final int DRIVE_RADAR_CRASH_OUTPUT_STATE = 555749328;
    public static final int DRIVE_RADAR_FRONT_ACTIVE_ON = 557846531;
    public static final int DRIVE_RADAR_OBSTACLE_DISTANCE_INFO = 557912017;
    public static final int DRIVE_RADAR_RFC_OBSTACLE_DISTANCE = 557846483;
    public static final int DRIVE_RADAR_SIDE_ZONE_STATE = 557912018;
    public static final int DRIVE_SWC_EPS_MODE = 557846532;
    public static final int DSM_BR_BW = 826283019;
    public static final int DSM_BR_FW = 826283018;
    public static final int DSM_COPILOTKNEADSTS = 826283025;
    public static final int DSM_DRVKNEADSTS = 826283024;
    public static final int DSM_FH_DN = 826283013;
    public static final int DSM_FH_UP = 826283012;
    public static final int DSM_INITSTATUS = 826283010;
    public static final int DSM_LEFTBATVOLTSTS = 826283020;
    public static final int DSM_LEFTHEATABNORMAL = 826283022;
    public static final int DSM_POWERSTATUS = 826283009;
    public static final int DSM_RECALLSTATUS = 826283011;
    public static final int DSM_RH_DN = 826283015;
    public static final int DSM_RH_UP = 826283014;
    public static final int DSM_RIGHTBATVOLTSTS = 826283021;
    public static final int DSM_RIGHTHEATABNORMAL = 826283023;
    public static final int DSM_SLIDE_BW = 826283017;
    public static final int DSM_SLIDE_FW = 826283016;
    public static final int DVR3RD_ALLPARMSSTATE = 826349591;
    public static final int DVR3RD_CURRECORDTIME = 826349596;
    public static final int DVR3RD_DATEFORMAT = 826284048;
    public static final int DVR3RD_DATETIME = 826349590;
    public static final int DVR3RD_DVR_CURPLAYTIME = 826349597;
    public static final int DVR3RD_FILELISTINFO = 826284057;
    public static final int DVR3RD_FILE_NAME = 826349592;
    public static final int DVR3RD_FORMATINFO = 826284052;
    public static final int DVR3RD_GRAVITYSENSOR = 826284046;
    public static final int DVR3RD_HARDKEYCMD = 826284040;
    public static final int DVR3RD_LANGUAGE = 826284045;
    public static final int DVR3RD_LIGHTFREQUENCY = 826284047;
    public static final int DVR3RD_MODESTATUSREQ = 826284039;
    public static final int DVR3RD_PARMSYSRESET = 826284053;
    public static final int DVR3RD_PLAYBYNAME = 826349594;
    public static final int DVR3RD_PLAYSTATE = 826284034;
    public static final int DVR3RD_RECIRCLERECORDTIME = 826284043;
    public static final int DVR3RD_RECORDLOCK = 826284032;
    public static final int DVR3RD_RECORDVOICE = 826284044;
    public static final int DVR3RD_RESOLUTION = 826284041;
    public static final int DVR3RD_SDCARDSTATUS = 826284035;
    public static final int DVR3RD_SHUTDOWN = 826284059;
    public static final int DVR3RD_SYSTEMDATE = 826349585;
    public static final int DVR3RD_SYSTEMTIME = 826349586;
    public static final int DVR3RD_TIMEDISFORMAT = 826284042;
    public static final int DVR3RD_TIMEDISMODE = 826284036;
    public static final int DVR3RD_TIMEINFO = 826349573;
    public static final int DVR3RD_TOTALTIME = 826349598;
    public static final int DVR3RD_UPDATESTATUS = 826284033;
    public static final int DVR3RD_VERSIONINFO = 826349587;
    public static final int DVR3RD_VIDEOPLAYSTATUS = 826284038;
    public static final int DVR_360_REPLAY_MODE = 557903528;
    public static final int DVR_BROWSE_CTRL = 557903532;
    public static final int DVR_COMM_FID_DATEFORMAT = 826282622;
    public static final int DVR_COMM_FID_DISPLAYVISION = 826282612;
    public static final int DVR_COMM_FID_DVRDISTORTCORRECTSWITCH = 826282599;
    public static final int DVR_COMM_FID_EDITSELECTED = 826282621;
    public static final int DVR_COMM_FID_EMERGENCYRECORD = 826282600;
    public static final int DVR_COMM_FID_ENABLESET = 826282607;
    public static final int DVR_COMM_FID_ERRORRECORD = 826282619;
    public static final int DVR_COMM_FID_FORMATREQ = 826282602;
    public static final int DVR_COMM_FID_GSENSOR = 826282623;
    public static final int DVR_COMM_FID_LANGUAGESET = 826282604;
    public static final int DVR_COMM_FID_LOCK = 826282624;
    public static final int DVR_COMM_FID_MODE = 826282614;
    public static final int DVR_COMM_FID_NORMALTOEMERGENCYSWITCH = 826282601;
    public static final int DVR_COMM_FID_PRINTSCREEN = 826282613;
    public static final int DVR_COMM_FID_REALTIMEREQ = 826282618;
    public static final int DVR_COMM_FID_REBROADCASTREQ = 826282617;
    public static final int DVR_COMM_FID_RECORDINGCYCLESETTING = 826282606;
    public static final int DVR_COMM_FID_RECOVERYREQ = 826282626;
    public static final int DVR_COMM_FID_REPLAYDELREQ = 826282625;
    public static final int DVR_COMM_FID_REPLAYMODE = 826282610;
    public static final int DVR_COMM_FID_REPLAYSPEED = 826282611;
    public static final int DVR_COMM_FID_RESOLUTIONSET = 826282605;
    public static final int DVR_COMM_FID_SDCAPACITY = 826282615;
    public static final int DVR_COMM_FID_SHOOTCONTROL = 826282609;
    public static final int DVR_COMM_FID_SYSTEMIMPRINT = 826282616;
    public static final int DVR_COMM_FID_TAPESET = 826282608;
    public static final int DVR_COMM_FID_VEDIOSEEK = 826282603;
    public static final int DVR_COMM_FID_VIDEOPLAYPERCENTAGE = 826282620;
    public static final int DVR_CURRENT_MEDIA_COUNT = 557903486;
    public static final int DVR_CURRENT_VIDEO_COUNTS = 557903495;
    public static final int DVR_CUR_LOCATION = 560066208;
    public static final int DVR_DATE_SET_1 = 557903521;
    public static final int DVR_DATE_SET_2 = 557903522;
    public static final int DVR_DELETE_COMMAND = 557903490;
    public static final int DVR_DISPLAY_VISION = 557903502;
    public static final int DVR_DISTORT_CORRECT_SWITCH = 557903524;
    public static final int DVR_EDIT_MODE_COMMAND = 557903492;
    public static final int DVR_EDIT_SELECTED = 557969032;
    public static final int DVR_EMERGENCY_COMMAND = 557903491;
    public static final int DVR_EMERGENCY_RECORD = 557903514;
    public static final int DVR_ERROR = 557903516;
    public static final int DVR_FORMAT = 557903512;
    public static final int DVR_LANGUAGE_SET = 557903511;
    public static final int DVR_MEDIA_SELECT = 557903498;
    public static final int DVR_MODE = 557903494;
    public static final int DVR_NORMAL_TO_EMERGENCY_SWITCH = 557903525;
    public static final int DVR_PRINT_SCREEN = 557903501;
    public static final int DVR_REALTIME_DISPLAY = 557903526;
    public static final int DVR_REBROADCAST = 557903527;
    public static final int DVR_RECORDING_CYCLE_SETTING = 557903515;
    public static final int DVR_RECOVERY = 557903513;
    public static final int DVR_REC_SWITCH = 557903487;
    public static final int DVR_REPLAY_COMMAND = 557903500;
    public static final int DVR_REPLAY_MODE = 557903493;
    public static final int DVR_REPLAY_SPEED = 557903499;
    public static final int DVR_RESOLUTION = 557903506;
    public static final int DVR_SDCARDERROR_STATUS = 557903518;
    public static final int DVR_SDCARDFULL_STATUS = 557903519;
    public static final int DVR_SD_CAPACITY = 557903509;
    public static final int DVR_SD_CAPACITY_PERCENT = 557903531;
    public static final int DVR_SLIDE_PAGING = 557903489;
    public static final int DVR_SNAP_SHOOT = 557903488;
    public static final int DVR_STATUS_FID_ACTIVESTATUS = 826282594;
    public static final int DVR_STATUS_FID_CURRENTVIDEO_COUNTS = 826282597;
    public static final int DVR_STATUS_FID_ERROR = 826282592;
    public static final int DVR_STATUS_FID_FILELISTSTATUS = 826282598;
    public static final int DVR_STATUS_FID_SDCARDSTATUS = 826282593;
    public static final int DVR_STATUS_FID_STORAGEPERCENT = 826282595;
    public static final int DVR_STATUS_FID_STORAGESTATUS = 826282596;
    public static final int DVR_STORAGE_STATUS = 557903517;
    public static final int DVR_STORGE_PERCENT = 557903497;
    public static final int DVR_SYSTEM_UPDATE = 557903508;
    public static final int DVR_SYSTEM_VERSION = 561049235;
    public static final int DVR_TAPE = 557903510;
    public static final int DVR_TIME_SET = 557903523;
    public static final int DVR_VIDEO_CURRENT_TIME = 557903530;
    public static final int DVR_VIDEO_CUR_TIME = 557903504;
    public static final int DVR_VIDEO_PLAY_USER_TIME = 557903503;
    public static final int DVR_VIDEO_TOTAL_TIME = 557903505;
    public static final int ENGINE_COOLANT_TEMP = 291504897;
    public static final int ENGINE_OIL_LEVEL = 289407747;
    public static final int ENGINE_OIL_TEMP = 291504900;
    public static final int ENGINE_RPM = 291504901;
    public static final int ENV_OUTSIDE_TEMPERATURE = 291505923;
    public static final int EV_BATTERY_INSTANTANEOUS_CHARGE_RATE = 291504908;
    public static final int EV_BATTERY_LEVEL = 291504905;
    public static final int EV_CHARGE_PORT_CONNECTED = 287310603;
    public static final int EV_CHARGE_PORT_OPEN = 287310602;
    public static final int FACTORY_RECOVERY = 826281993;
    public static final int FAULT_BCM_CODE = 557844033;
    public static final int FAULT_HVAC_CODE = 557844034;
    public static final int FAULT_SYSTEM_CODE_INFO = 557909568;
    public static final int FOG_LIGHTS_STATE = 322964994;
    public static final int FOG_LIGHTS_SWITCH = 289410578;
    public static final int FTYPE_AC_ALL = 826351743;
    public static final int FTYPE_BODY_ALL = 826351742;
    public static final int FTYPE_FRONT_ASSIST_ALL = 826351744;
    public static final int FUEL_DOOR_OPEN = 287310840;
    public static final int FUEL_LEVEL = 291504903;
    public static final int FUEL_LEVEL_LOW = 287310853;
    public static final int FUEL_LEVEL_STATE = 557903883;
    public static final int GEAR_SELECTION = 289408000;
    public static final int HAZARD_LIGHTS_STATE = 289410563;
    public static final int HAZARD_LIGHTS_SWITCH = 289410579;
    public static final int HEADLIGHTS_STATE = 289410560;
    public static final int HEADLIGHTS_SWITCH = 289410576;
    public static final int HIGH_BEAM_LIGHTS_STATE = 289410561;
    public static final int HIGH_BEAM_LIGHTS_SWITCH = 289410577;
    public static final int HVAC_ACTIVE_VENTILATION_ON = 557848832;
    public static final int HVAC_ACTUAL_FAN_SPEED_RPM = 356517135;
    public static final int HVAC_AC_ON = 356517125;
    public static final int HVAC_AIR_DRYIONG_ON = 557848833;
    public static final int HVAC_AIR_QUALITY_IN_OUT_CAR = 675289346;
    public static final int HVAC_AIR_QUALITY_RATING_INNER = 557848836;
    public static final int HVAC_AQS_MODE_ON = 557848837;
    public static final int HVAC_AUTO_ON = 356517130;
    public static final int HVAC_AUTO_RECIRC_ON = 354419986;
    public static final int HVAC_COMFORT_MODE = 557848838;
    public static final int HVAC_DEFROSTER = 322962692;
    public static final int HVAC_DISPLAY_REQ = 557848839;
    public static final int HVAC_DUAL_ON = 356517129;
    public static final int HVAC_DUAL_STATE = 557848851;
    public static final int HVAC_FAN_DIRECTION = 356517121;
    public static final int HVAC_FAN_DIRECTION_AVAILABLE = 356582673;
    public static final int HVAC_FAN_SPEED = 356517120;
    public static final int HVAC_FAN_SPEED_ACK = 356517140;
    public static final int HVAC_FAN_SPEED_ADJUST = 624957704;
    public static final int HVAC_ION_GENERATOR_ON = 557848841;
    public static final int HVAC_KEY_LOCK_WINDOW_VENTILATION_ON = 557903906;
    public static final int HVAC_LOCK_VENTILATION_ON = 557848852;
    public static final int HVAC_MAX_AC_ON = 356517126;
    public static final int HVAC_MAX_DEFROST_ON = 354419975;
    public static final int HVAC_POWER_ON = 356517136;
    public static final int HVAC_RECIRC_ON = 356517128;
    public static final int HVAC_REMOTE_WINDOW_VENTILATION_ON = 557903893;
    public static final int HVAC_SEAT_TEMPERATURE = 356517131;
    public static final int HVAC_SEAT_VENTILATION = 356517139;
    public static final int HVAC_SETTING_LOCK = 624957706;
    public static final int HVAC_SIDE_MIRROR_HEAT = 339739916;
    public static final int HVAC_STEERING_WHEEL_HEAT = 289408269;
    public static final int HVAC_TEMPERATURE_ADJUST = 624957714;
    public static final int HVAC_TEMPERATURE_CURRENT = 358614274;
    public static final int HVAC_TEMPERATURE_DISPLAY_UNITS = 289408270;
    public static final int HVAC_TEMPERATURE_LV_SET = 624957707;
    public static final int HVAC_TEMPERATURE_SET = 358614275;
    public static final int HVAC_TIMING_VENTILATION_MODE = 557848844;
    public static final int HVAC_VENTILATION_USER_DEFINED_SETTING_AFTERNOON = 557848845;
    public static final int HVAC_VENTILATION_USER_DEFINED_SETTING_EVENING = 557848846;
    public static final int HVAC_VENTILATION_USER_DEFINED_SETTING_MORNING = 557848847;
    public static final int HVAC_VIBRATION_FEEDBACK = 557906432;
    public static final int HVAC_WINDOW_VENTILATION_ON = 557848849;
    public static final int HW_KEY_INPUT = 289475088;
    public static final int IGNITION_STATE = 289408009;
    public static final int INFO_DRIVER_SEAT = 356516106;
    public static final int INFO_EV_BATTERY_CAPACITY = 291504390;
    public static final int INFO_EV_CONNECTOR_TYPE = 289472775;
    public static final int INFO_EV_PORT_LOCATION = 289407241;
    public static final int INFO_FUEL_CAPACITY = 291504388;
    public static final int INFO_FUEL_DOOR_LOCATION = 289407240;
    public static final int INFO_FUEL_TYPE = 289472773;
    public static final int INFO_MAKE = 286261505;
    public static final int INFO_MCU_VER = 823136264;
    public static final int INFO_MODEL = 286261506;
    public static final int INFO_MODEL_YEAR = 289407235;
    public static final int INFO_VIN = 286261504;
    public static final int INVALID = 0;
    public static final int IP_CURRENT_IMAGE = 826283778;
    public static final int IP_CURRENT_SRC = 826349312;
    public static final int IP_DISPLAY_MODE_SET = 557845633;
    public static final int IP_DISPLAY_REQ = 557845632;
    public static final int IP_DISPLAY_THEME_STATE = 557845634;
    public static final int IP_DISP_SYNC = 826283785;
    public static final int IP_DISP_THEME_LINK = 826283784;
    public static final int IP_KEY_STATUS = 826283783;
    public static final int IP_NAVI_DISTANCE = 557842584;
    public static final int IP_NAVI_STATE = 557845635;
    public static final int IP_REQ_SCREENSHOTS_REQ = 557845636;
    public static final int IP_REQ_SCREENSHOTS_RESULT = 555748485;
    public static final int IP_REQ_SHOW_VERSION = 555748486;
    public static final int IP_THEME = 826283777;
    public static final int IP_UPDATE_CHECK = 826283782;
    public static final int IP_UPDATE_COPY = 826283781;
    public static final int IP_UPDATE_REQ = 826283779;
    public static final int IP_USB_LOAD = 826283780;
    public static final int IP_VERSION_INFO = 554699911;
    public static final int IP_VOLUME_LEVEL = 557845640;
    public static final int IP_VOLUME_TYPE = 557845641;
    public static final int KEY_QUERY_INFO = 557969477;
    public static final int MCU_SYSTEM_SET_DEVELOPER_MODE = 826281998;
    public static final int MCU_UPDATE = 826281987;
    public static final int MIRROR_FOLD = 289409861;
    public static final int MIRROR_LOCK = 287312708;
    public static final int MIRROR_Y_MOVE = 339741507;
    public static final int MIRROR_Y_POS = 339741506;
    public static final int MIRROR_Z_MOVE = 339741505;
    public static final int MIRROR_Z_POS = 339741504;
    public static final int NAVI_STATE_INFO = 826347535;
    public static final int NAVI_TEXT_INFO = 829427728;
    public static final int NIGHT_MODE = 287310855;
    public static final int OBD2_FREEZE_FRAME = 299896065;
    public static final int OBD2_FREEZE_FRAME_CLEAR = 299896067;
    public static final int OBD2_FREEZE_FRAME_INFO = 299896066;
    public static final int OBD2_LIVE_FRAME = 299896064;
    public static final int PARAM_UNIT_SETTING = 826281988;
    public static final int PARKING_BRAKE_AUTO_APPLY = 287310851;
    public static final int PARKING_BRAKE_ON = 287310850;
    public static final int PASSTHROUGH_DATA = 829427712;
    public static final int PDEV_VEHICLE_INFO_FID_EOL = 829427722;
    public static final int PDEV_VEHICLE_INFO_FID_TUID = 823136268;
    public static final int PDEV_VEHICLE_INFO_FID_VIN = 823136267;
    public static final int PDEV_VEHICLE_INFO_FID_VSOL = 829427725;
    public static final int PDEV_VEHICLE_INFO_LOGIN_TSP = 826282003;
    public static final int PERF_ODOMETER = 291504644;
    public static final int PERF_VEHICLE_SPEED = 291504647;
    public static final int POWER_WARNNING_STATE = 826286204;
    public static final int RADAR_COMMAND_RR_BEEP_FRE = 826283285;
    public static final int RADAR_COMMAND_RR_SWITCH_STATE = 826283284;
    public static final int RADAR_DATA_FL = 826283272;
    public static final int RADAR_DATA_FLM = 826283273;
    public static final int RADAR_DATA_FR = 826283274;
    public static final int RADAR_DATA_FRM = 826283275;
    public static final int RADAR_DATA_RL = 826283276;
    public static final int RADAR_DATA_RLM = 826283277;
    public static final int RADAR_DATA_RR = 826283278;
    public static final int RADAR_DATA_RRM = 826283279;
    public static final int RADAR_PDC_BUZZERALARMPATTERN = 826283280;
    public static final int RADAR_PDC_ECUFAULT = 826283283;
    public static final int RADAR_PDC_LED = 826283282;
    public static final int RADAR_PDC_MODESTATUS = 826283281;
    public static final int RADAR_STATE_FL = 826283264;
    public static final int RADAR_STATE_FLM = 826283265;
    public static final int RADAR_STATE_FR = 826283266;
    public static final int RADAR_STATE_FRM = 826283267;
    public static final int RADAR_STATE_RL = 826283268;
    public static final int RADAR_STATE_RLM = 826283269;
    public static final int RADAR_STATE_RR = 826283270;
    public static final int RADAR_STATE_RRM = 826283271;
    public static final int RADAR_TRIGGER_BLIND_ON = 826286172;
    public static final int RANGE_REMAINING = 291504904;
    public static final int REVERSE_CAMERA = 557903962;
    public static final int REVERSE_SIGNAL = 557903957;
    public static final int SCREEN_BACKLIGHT_TOUCH_SWITCH = 557903970;
    public static final int SCREEN_BRIGHTNESS = 826281986;
    public static final int SCREEN_INFO = 557969103;
    public static final int SCREEN_LIGHT_AUTO = 557903974;
    public static final int SCREEN_POWER_ON = 557903973;
    public static final int SEAT_BACKREST_ANGLE_1_MOVE = 356518792;
    public static final int SEAT_BACKREST_ANGLE_1_POS = 356518791;
    public static final int SEAT_BACKREST_ANGLE_2_MOVE = 356518794;
    public static final int SEAT_BACKREST_ANGLE_2_POS = 356518793;
    public static final int SEAT_BELT_BUCKLED = 354421634;
    public static final int SEAT_BELT_HEIGHT_MOVE = 356518788;
    public static final int SEAT_BELT_HEIGHT_POS = 356518787;
    public static final int SEAT_COMMAND_FID_DRIVE_HEAT = 826283026;
    public static final int SEAT_COMMAND_FID_DRIVE_SET1 = 826283030;
    public static final int SEAT_COMMAND_FID_DRIVE_SET2 = 826283031;
    public static final int SEAT_COMMAND_FID_DRIVE_VENT = 826283028;
    public static final int SEAT_COMMAND_FID_PASS_HEAT = 826283027;
    public static final int SEAT_COMMAND_FID_PASS_SET1 = 826283032;
    public static final int SEAT_COMMAND_FID_PASS_SET2 = 826283033;
    public static final int SEAT_COMMAND_FID_PASS_VENT = 826283029;
    public static final int SEAT_DEPTH_MOVE = 356518798;
    public static final int SEAT_DEPTH_POS = 356518797;
    public static final int SEAT_FORE_AFT_MOVE = 356518790;
    public static final int SEAT_FORE_AFT_POS = 356518789;
    public static final int SEAT_HEADREST_ANGLE_MOVE = 356518808;
    public static final int SEAT_HEADREST_ANGLE_POS = 356518807;
    public static final int SEAT_HEADREST_FORE_AFT_MOVE = 356518810;
    public static final int SEAT_HEADREST_FORE_AFT_POS = 356518809;
    public static final int SEAT_HEADREST_HEIGHT_MOVE = 356518806;
    public static final int SEAT_HEADREST_HEIGHT_POS = 289409941;
    public static final int SEAT_HEIGHT_MOVE = 356518796;
    public static final int SEAT_HEIGHT_POS = 356518795;
    public static final int SEAT_INFO = 826283008;
    public static final int SEAT_LUMBAR_FORE_AFT_MOVE = 356518802;
    public static final int SEAT_LUMBAR_FORE_AFT_POS = 356518801;
    public static final int SEAT_LUMBAR_SIDE_SUPPORT_MOVE = 356518804;
    public static final int SEAT_LUMBAR_SIDE_SUPPORT_POS = 356518803;
    public static final int SEAT_MEMORY_SELECT = 356518784;
    public static final int SEAT_MEMORY_SET = 356518785;
    public static final int SEAT_TILT_MOVE = 356518800;
    public static final int SEAT_TILT_POS = 356518799;
    public static final int SET_TUID_INFO = 561049622;
    public static final int SHUTDOWN_WARING_INFO = 557903876;
    public static final int SOC_POWER_OFF_SIGN = 557903968;
    public static final int SOC_SLEEP_SIGN = 557903969;
    public static final int SYSTEM_STATUS = 826281989;
    public static final int TBOX_4G_AUDIO_REQ_ID = 826286184;
    public static final int TBOX_4G_BCALL_STATUS_ID = 826286188;
    public static final int TBOX_4G_BOX_TYPE_ID = 826286186;
    public static final int TBOX_4G_CALL_STATUS_ID = 826286187;
    public static final int TBOX_4G_CHANGE_PART_STATUS_ID = 826286185;
    public static final int TBOX_4G_ICCID_ID = 829431908;
    public static final int TBOX_4G_PARKMODE_ID = 826286181;
    public static final int TBOX_4G_SURROND_REQ_ID = 826286189;
    public static final int TBOX_CTR_4G_UPGRADE_ID = 826286191;
    public static final int TBOX_OS_F1F1_CONFIG_ID = 826286183;
    public static final int TBOX_RESET_4G_ID = 826286190;
    public static final int TBOX_WORK_STATE_ID = 826286182;
    public static final int TIME_SETTING = 826281985;
    public static final int TIRE_PRESSURE = 291570441;
    public static final int TIRE_PRESSURE_SIGNAL = 557903945;
    public static final int TRACTION_CONTROL_ACTIVE = 287310859;
    public static final int TUID_INFO = 561049612;
    public static final int TUID_REQ = 557903887;
    public static final int TURN_SIGNAL_STATE = 289408008;
    public static final int TURN_TRIGGER_BLIND_ON = 826286171;
    public static final int VEHICLESET_BS_AUTOIGNOFFUNLOCKSET = 826282794;
    public static final int VEHICLESET_BS_AUTOSPEEDLOCKSET = 826282793;
    public static final int VEHICLESET_BS_DRIVEMODE = 826282819;
    public static final int VEHICLESET_BS_DRIVINGMODEMEMORYENABLE = 826282808;
    public static final int VEHICLESET_BS_EASYENTRYSET = 826282801;
    public static final int VEHICLESET_BS_GETOFFAUTOLOCK = 826282797;
    public static final int VEHICLESET_BS_LOCKAUTOCLOSEWINDOW = 826282809;
    public static final int VEHICLESET_BS_LOWSPEEDPEDESTRIANWARN = 826282806;
    public static final int VEHICLESET_BS_MIRRORAUTOFOLDSET = 826282802;
    public static final int VEHICLESET_BS_MIRRORHEAT = 826282817;
    public static final int VEHICLESET_BS_MIRRORSETSTATUS = 826282803;
    public static final int VEHICLESET_BS_REARSEATBELTWARNINGENABLE = 826282805;
    public static final int VEHICLESET_BS_REARWIPERSET = 826282804;
    public static final int VEHICLESET_BS_RKESUNROOFCTRLTYPE = 826282814;
    public static final int VEHICLESET_BS_RKEUNLOCKDOORTYPESET = 826282792;
    public static final int VEHICLESET_BS_SMARTTRUNKULOCKSTAUS = 826282800;
    public static final int VEHICLESET_BS_SMARTWELCOM = 826282798;
    public static final int VEHICLESET_BS_SUNROOFOPENSET = 826282815;
    public static final int VEHICLESET_BS_SUNROOFRAINSETSTATU = 826282795;
    public static final int VEHICLESET_BS_SUNROOFSET = 826282811;
    public static final int VEHICLESET_BS_SUNSHADEOPENSET = 826282816;
    public static final int VEHICLESET_BS_SUNSHADESET = 826282813;
    public static final int VEHICLESET_BS_TAILGATESET = 826282812;
    public static final int VEHICLESET_BS_TPMSRESETSTATUS = 826282807;
    public static final int VEHICLESET_BS_TWICELOCKDOORSETSTATUS = 826282796;
    public static final int VEHICLESET_BS_WD = 826282818;
    public static final int VEHICLESET_BS_WELCOMLOCK = 826282799;
    public static final int VEHICLESET_BS_WIPERSET = 826282810;
    public static final int VEHICLESET_BTC_AUTOBRAKEENABLE = 826282759;
    public static final int VEHICLESET_BTC_DISTANCEALERTENABLE = 826282756;
    public static final int VEHICLESET_BTC_FCWBEEP = 826282760;
    public static final int VEHICLESET_BTC_FCWENABLE = 826282757;
    public static final int VEHICLESET_BTC_FCWSENSITIVITY = 826282758;
    public static final int VEHICLESET_CRUISE_ACCOBJENABLE = 826282755;
    public static final int VEHICLESET_CRUISE_CONTROLSET = 826282752;
    public static final int VEHICLESET_CRUISE_INTEGRATED = 826282753;
    public static final int VEHICLESET_CRUISE_MODE = 826282754;
    public static final int VEHICLESET_HUD_DISPLAY = 826282847;
    public static final int VEHICLESET_HUD_ENABLE = 826282843;
    public static final int VEHICLESET_HUD_HEIGHT = 826282848;
    public static final int VEHICLESET_HUD_LANGUAGE = 826282846;
    public static final int VEHICLESET_HUD_LIGHTSET = 826282845;
    public static final int VEHICLESET_HUD_POSITIONSET = 826282844;
    public static final int VEHICLESET_IP_ALARMSOUNDSET = 826282851;
    public static final int VEHICLESET_IP_MESSAGEALERTSOUNDSET = 826282850;
    public static final int VEHICLESET_IP_WARNINGVOLUMESETTING = 826282852;
    public static final int VEHICLESET_IP_WELCOMESOUNDSET = 826282849;
    public static final int VEHICLESET_LA_LASWARNINGBEEP = 826282775;
    public static final int VEHICLESET_LA_LASWARNINGMODESELECTSTAS = 826282771;
    public static final int VEHICLESET_LA_LAS_FATIGUEDECTIONENABLESTATUS = 826282774;
    public static final int VEHICLESET_LA_LAS_FUNCSEL = 826282770;
    public static final int VEHICLESET_LA_LAS_LASMODESELECTIONSTATUS = 826282769;
    public static final int VEHICLESET_LA_LAS_LDWSENSITIVITYSTATUS = 826282773;
    public static final int VEHICLESET_LA_LAS_LDWSHAKELEVSTATUS = 826282772;
    public static final int VEHICLESET_LS_AMBIENTLIGHTCOLORFEED = 826282832;
    public static final int VEHICLESET_LS_ATMLIGHTBRIGHTNESSCFG = 826282853;
    public static final int VEHICLESET_LS_ATMLIGHTBRIGHTSET = 826282820;
    public static final int VEHICLESET_LS_ATMLIGHTCOLORCFG = 826282854;
    public static final int VEHICLESET_LS_ATMLIGHTCOLORSET = 826282823;
    public static final int VEHICLESET_LS_ATMLIGHTINSTRUMENTDESKCOLOR = 826282855;
    public static final int VEHICLESET_LS_ATMLIGHTMUSICMODE = 826282857;
    public static final int VEHICLESET_LS_ATMLIGHTOTHERCOLOR = 826282856;
    public static final int VEHICLESET_LS_ATMOLIGHTAAENABLE = 826282821;
    public static final int VEHICLESET_LS_ATMOLIGHTAREA = 826282840;
    public static final int VEHICLESET_LS_ATMOLIGHTAUTOADJUST = 826282824;
    public static final int VEHICLESET_LS_ATMOLIGHTAUTOADJUSTSTS = 826282822;
    public static final int VEHICLESET_LS_ATMOLIGHTFUNCSW = 826282842;
    public static final int VEHICLESET_LS_ATMOLIGHTLINKDOORENABLE = 826282829;
    public static final int VEHICLESET_LS_ATMOLIGHTLINKOVERSPEEDENABLE = 826282826;
    public static final int VEHICLESET_LS_ATMOLIGHTLINKTOPLIGHTENABLE = 826282828;
    public static final int VEHICLESET_LS_ATMOLIGHTMODE = 826282839;
    public static final int VEHICLESET_LS_ATMOLIGHTSPEEDENABLE = 826282827;
    public static final int VEHICLESET_LS_ATMOLIGHTVEHICLEENABLE = 826282831;
    public static final int VEHICLESET_LS_COPILOTDISPMODE = 826282838;
    public static final int VEHICLESET_LS_DRLSET = 826282834;
    public static final int VEHICLESET_LS_FOLLOWMEHOMELIGHTSETSTATUS = 826282836;
    public static final int VEHICLESET_LS_HMAENABLE = 826282835;
    public static final int VEHICLESET_LS_LANECHANGETURNLIGHTSETSTATUS = 826282837;
    public static final int VEHICLESET_LS_SEWENABLE = 826282830;
    public static final int VEHICLESET_LS_WELCOMEATMOLIGHTENABLE = 826282825;
    public static final int VEHICLESET_LS_WELCOMELIGHTSTAUS = 826282833;
    public static final int VEHICLESET_LS_WELCOMELIGHTTIMESET = 826282841;
    public static final int VEHICLESET_PA_360AVMDETECTIONREQUEST = 826282785;
    public static final int VEHICLESET_PA_ASS = 826282789;
    public static final int VEHICLESET_PA_AVMCALIBRATIONSWITCH = 826282786;
    public static final int VEHICLESET_PA_ESP = 826282790;
    public static final int VEHICLESET_PA_ESPMODE = 826282787;
    public static final int VEHICLESET_PA_FRONTRADAR = 826282788;
    public static final int VEHICLESET_PA_HDC = 826282791;
    public static final int VEHICLESET_PA_OBSTACLESAFEDISTANCESET = 826282784;
    public static final int VEHICLESET_PA_RADARAVMSWITCH = 826282782;
    public static final int VEHICLESET_PA_REMOTEDISTANCESET = 826282783;
    public static final int VEHICLESET_PA_TURNLIGHTAVMSWITCH = 826282781;
    public static final int VEHICLESET_PA_ULS_FKP_ACTIVATION = 826282780;
    public static final int VEHICLESET_RW_LCDAL_AUDIOWARNINGENABLESTATUS = 826282767;
    public static final int VEHICLESET_RW_LCDAL_BSDLCAENABLESTATUS = 826282761;
    public static final int VEHICLESET_RW_LCDAL_CTAENABLESTATUS = 826282762;
    public static final int VEHICLESET_RW_LCDAL_LOCKCTRLENABLESTATUS = 826282768;
    public static final int VEHICLESET_RW_LCDAL_RCWENABLESTATU = 826282763;
    public static final int VEHICLESET_RW_LCDAL_SEAENABLESTATUS = 826282765;
    public static final int VEHICLESET_RW_RCWSOUNDENABLE = 826282764;
    public static final int VEHICLESET_RW_SEWENABLE = 826282766;
    public static final int VEHICLESET_SLA_LAS_OVERSPEEDSOUNDWARNENASTS = 826282778;
    public static final int VEHICLESET_SLA_LAS_OVERSPEEDWARNINGENABLESTATUS = 826282777;
    public static final int VEHICLESET_SLA_LAS_OVERSPEEDWARNINGOFFSET = 826282779;
    public static final int VEHICLESET_SLA_LAS_SLASWITCHSTATUS = 826282776;
    public static final int VEHICLE_DOOR_FID_BACK_DOOR = 826283525;
    public static final int VEHICLE_DOOR_FID_ENGINE_DOOR = 826283524;
    public static final int VEHICLE_DOOR_FID_LF_DOOR = 826283520;
    public static final int VEHICLE_DOOR_FID_LR_DOOR = 826283522;
    public static final int VEHICLE_DOOR_FID_RF_DOOR = 826283521;
    public static final int VEHICLE_DOOR_FID_RR_DOOR = 826283523;
    public static final int VEHICLE_FID_STEE_ANGLE = 826283533;
    public static final int VEHICLE_GEAR_FID_POSITION = 826283534;
    public static final int VEHICLE_LIGH_FID_FRONT_FOG_LAMP = 826283545;
    public static final int VEHICLE_LIGH_FID_HIGH_BEAM = 826283544;
    public static final int VEHICLE_LIGH_FID_LOW_BEAM = 826283543;
    public static final int VEHICLE_LIGH_FID_REAR_FOG_LAMP = 826283546;
    public static final int VEHICLE_LIGH_FID_TURN_INDICATOR_LEFT = 826283547;
    public static final int VEHICLE_LIGH_FID_TURN_INDICATOR_RIGHT = 826283548;
    public static final int VEHICLE_MAP_SERVICE = 299895808;
    public static final int VEHICLE_POWER_FID_STATUS = 826283532;
    public static final int VEHICLE_SPEED_FID_INFO = 826283531;
    public static final int VEHICLE_WINDOW_FID_ALL_WINDOW = 826283542;
    public static final int VEHICLE_WINDOW_FID_LF_WINDOW = 826283526;
    public static final int VEHICLE_WINDOW_FID_LR_WINDOW = 826283528;
    public static final int VEHICLE_WINDOW_FID_RF_WINDOW = 826283527;
    public static final int VEHICLE_WINDOW_FID_RR_WINDOW = 826283529;
    public static final int VEHICLE_WINDOW_FID_SHADE = 826283549;
    public static final int VEHICLE_WINDOW_FID_SUNROOF = 826283530;
    public static final int VEHICLE_WIRELESS_CHARGE_CHARGINGSTATUS = 826283536;
    public static final int VEHICLE_WIRELESS_CHARGE_CTRL = 826283535;
    public static final int VEHICLE_WIRELESS_CHARGE_FODWARMING = 826283540;
    public static final int VEHICLE_WIRELESS_CHARGE_PHONEREMINDER = 826283537;
    public static final int VEHICLE_WIRELESS_CHARGE_SHUTDOWNFEEDBACK = 826283538;
    public static final int VEHICLE_WIRELESS_CHARGE_SWMEMORYSTS = 826283539;
    public static final int VEHICLE_WIRELESS_CHARGINGERR = 826283541;
    public static final int VENDOR_AMPLIFIER_SWITCH_STATUS = 557903879;
    public static final int VENDOR_APU_ILL_ON = 826286193;
    public static final int VENDOR_AUDIO_MODE_REQ = 557848032;
    public static final int VENDOR_BATTERY_CHARGE_SW = 557903897;
    public static final int VENDOR_BCALL_REQ = 557848033;
    public static final int VENDOR_BCALL_STATE = 557848034;
    public static final int VENDOR_BLE_USER_ID_INFO = 557848035;
    public static final int VENDOR_CAR_FACTORY_RESET = 557848038;
    public static final int VENDOR_CERTIFICATE_ACK = 561049665;
    public static final int VENDOR_CERTIFICATE_ID = 561049671;
    public static final int VENDOR_DISPLAY_PCPD_SCREEN_MODE = 557848041;
    public static final int VENDOR_DRIVER_ID_INFO = 557848036;
    public static final int VENDOR_DRIVER_MODE_SET = 557903920;
    public static final int VENDOR_ECALL_STATE = 557848037;
    public static final int VENDOR_ECU_MCU_VERSION = 554758230;
    public static final int VENDOR_FACTORY_RESET = 557903975;
    public static final int VENDOR_HW_VERSION_ACK = 554758193;
    public static final int VENDOR_HW_VERSION_REQ = 557903922;
    public static final int VENDOR_ICCID_ACK = 554758157;
    public static final int VENDOR_ICCID_REQ = 557903886;
    public static final int VENDOR_IC_UPDATE_ACK = 557903895;
    public static final int VENDOR_IC_UPDATE_REQ = 557903896;
    public static final int VENDOR_IMEI_ACK = 554758195;
    public static final int VENDOR_IMEI_REQ = 557903924;
    public static final int VENDOR_LIGHT_MUSIC_MODE_SELECT_STATE = 557848040;
    public static final int VENDOR_LIGHT_MUSIC_ON = 557848039;
    public static final int VENDOR_LIGHT_NIGHT_MODE_STATE = 557903964;
    public static final int VENDOR_MCU_INIT_INFO = 561049601;
    public static final int VENDOR_MCU_POWER_MODE = 557903874;
    public static final int VENDOR_MCU_RECV_DATA_INF0 = 560993772;
    public static final int VENDOR_MCU_RESTART_REQ = 557903880;
    public static final int VENDOR_MCU_SEND_DATA_INFO = 560993773;
    public static final int VENDOR_MCU_TIME = 557969413;
    public static final int VENDOR_MCU_TIME_REQ = 557903878;
    public static final int VENDOR_MCU_VERSION = 554702319;
    public static final int VENDOR_MCU_VERSION_REQ = 557848046;
    public static final int VENDOR_MFD_RECV_DATA_INFO = 560993771;
    public static final int VENDOR_MFD_SEND_DATA_INFO = 560993776;
    public static final int VENDOR_MFD_VERSION = 554702314;
    public static final int VENDOR_MFD_VERSION_REQ = 554702321;
    public static final int VENDOR_OFF_LINE_REQ = 557903946;
    public static final int VENDOR_OFF_LINE_SET = 561049668;
    public static final int VENDOR_OFF_LINE_STATUS = 561049603;
    public static final int VENDOR_PART_NUMBER = 554758231;
    public static final int VENDOR_PHOTO_REQ = 625012816;
    public static final int VENDOR_POWER_OFF_ON = 826286205;
    public static final int VENDOR_POWER_ON_NOTICE = 557903881;
    public static final int VENDOR_SECRET_ACK = 561049666;
    public static final int VENDOR_SECRET_REQ = 557903936;
    public static final int VENDOR_SEND_TO_CAR_REQ = 557903925;
    public static final int VENDOR_SET_VIN_INFO = 554758211;
    public static final int VENDOR_SW_PART_NUMBER = 554758232;
    public static final int VENDOR_TBOX_FW_VERSION = 554758228;
    public static final int VENDOR_TBOX_FW_VERSION_REQ = 557903955;
    public static final int VENDOR_TBOX_VERSION = 554758226;
    public static final int VENDOR_TBOX_VERSION_REQ = 557903953;
    public static final int VENDOR_TIRE_TEMPERATURE = 560066632;
    public static final int VENDOR_UPDATE_BCM_CANCEL_SET = 557848050;
    public static final int VENDOR_UPDATE_BCM_REQ = 554702323;
    public static final int VENDOR_UPDATE_BCM_STATE = 557913588;
    public static final int VENDOR_UPDATE_BCM_VERSION = 554702326;
    public static final int VENDOR_UPDATE_BCM_VERSION_REQ = 554702325;
    public static final int VENDOR_UPDATE_HVAC_CANCEL_SET = 557848055;
    public static final int VENDOR_UPDATE_HVAC_REQ = 554702328;
    public static final int VENDOR_UPDATE_HVAC_STATE = 557913593;
    public static final int VENDOR_UPDATE_HVAC_VERSION = 554702331;
    public static final int VENDOR_UPDATE_HVAC_VERSION_REQ = 554702330;
    public static final int VENDOR_UPDATE_MCU_CANCEL_SET = 557848060;
    public static final int VENDOR_UPDATE_MCU_REQ = 554702333;
    public static final int VENDOR_UPDATE_MCU_STATE = 557913598;
    public static final int VENDOR_UPDATE_MFD_CANCEL_SET = 557848063;
    public static final int VENDOR_UPDATE_MFD_REQ = 554702336;
    public static final int VENDOR_UPDATE_MFD_STATE = 557913601;
    public static final int VENDOR_UPDATE_STATE = 557906433;
    public static final int VENDOR_WINDOW_STATE = 591399317;
    public static final int VENDPR_LIGHT_MUSIC_PLAY_SETBACK_STATE = 557848066;
    public static final int WARNING_VEH_INDICATE_INFO = 557908768;
    public static final int WHEEL_TICK = 290521862;
    public static final int WINDOW_LOCK = 320867268;
    public static final int WINDOW_MOVE = 322964417;
    public static final int WINDOW_POS = 322964416;

    public static final String toString(int i) {
        return i == 0 ? "INVALID" : i == 286261504 ? "INFO_VIN" : i == 286261505 ? "INFO_MAKE" : i == 286261506 ? "INFO_MODEL" : i == 289407235 ? "INFO_MODEL_YEAR" : i == 291504388 ? "INFO_FUEL_CAPACITY" : i == 289472773 ? "INFO_FUEL_TYPE" : i == 291504390 ? "INFO_EV_BATTERY_CAPACITY" : i == 289472775 ? "INFO_EV_CONNECTOR_TYPE" : i == 289407240 ? "INFO_FUEL_DOOR_LOCATION" : i == 289407241 ? "INFO_EV_PORT_LOCATION" : i == 356516106 ? "INFO_DRIVER_SEAT" : i == 291504644 ? "PERF_ODOMETER" : i == 291504647 ? "PERF_VEHICLE_SPEED" : i == 291504897 ? "ENGINE_COOLANT_TEMP" : i == 289407747 ? "ENGINE_OIL_LEVEL" : i == 291504900 ? "ENGINE_OIL_TEMP" : i == 291504901 ? "ENGINE_RPM" : i == 290521862 ? "WHEEL_TICK" : i == 291504903 ? "FUEL_LEVEL" : i == 287310840 ? "FUEL_DOOR_OPEN" : i == 291504905 ? "EV_BATTERY_LEVEL" : i == 287310602 ? "EV_CHARGE_PORT_OPEN" : i == 287310603 ? "EV_CHARGE_PORT_CONNECTED" : i == 291504908 ? "EV_BATTERY_INSTANTANEOUS_CHARGE_RATE" : i == 291504904 ? "RANGE_REMAINING" : i == 291570441 ? "TIRE_PRESSURE" : i == 289408000 ? "GEAR_SELECTION" : i == 289408001 ? "CURRENT_GEAR" : i == 287310850 ? "PARKING_BRAKE_ON" : i == 287310851 ? "PARKING_BRAKE_AUTO_APPLY" : i == 287310853 ? "FUEL_LEVEL_LOW" : i == 287310855 ? "NIGHT_MODE" : i == 289408008 ? "TURN_SIGNAL_STATE" : i == 289408009 ? "IGNITION_STATE" : i == 287310858 ? "ABS_ACTIVE" : i == 287310859 ? "TRACTION_CONTROL_ACTIVE" : i == 356517120 ? "HVAC_FAN_SPEED" : i == 356517121 ? "HVAC_FAN_DIRECTION" : i == 358614274 ? "HVAC_TEMPERATURE_CURRENT" : i == 358614275 ? "HVAC_TEMPERATURE_SET" : i == 322962692 ? "HVAC_DEFROSTER" : i == 356517125 ? "HVAC_AC_ON" : i == 356517126 ? "HVAC_MAX_AC_ON" : i == 354419975 ? "HVAC_MAX_DEFROST_ON" : i == 356517128 ? "HVAC_RECIRC_ON" : i == 356517129 ? "HVAC_DUAL_ON" : i == 356517130 ? "HVAC_AUTO_ON" : i == 356517131 ? "HVAC_SEAT_TEMPERATURE" : i == 339739916 ? "HVAC_SIDE_MIRROR_HEAT" : i == 289408269 ? "HVAC_STEERING_WHEEL_HEAT" : i == 289408270 ? "HVAC_TEMPERATURE_DISPLAY_UNITS" : i == 356517135 ? "HVAC_ACTUAL_FAN_SPEED_RPM" : i == 356517136 ? "HVAC_POWER_ON" : i == 356582673 ? "HVAC_FAN_DIRECTION_AVAILABLE" : i == 354419986 ? "HVAC_AUTO_RECIRC_ON" : i == 356517139 ? "HVAC_SEAT_VENTILATION" : i == 356517140 ? "HVAC_FAN_SPEED_ACK" : i == 291505923 ? "ENV_OUTSIDE_TEMPERATURE" : i == 289475072 ? "AP_POWER_STATE_REQ" : i == 289475073 ? "AP_POWER_STATE_REPORT" : i == 289409538 ? "AP_POWER_BOOTUP_REASON" : i == 289409539 ? "DISPLAY_BRIGHTNESS" : i == 289475088 ? "HW_KEY_INPUT" : i == 373295872 ? "DOOR_POS" : i == 373295873 ? "DOOR_MOVE" : i == 371198722 ? "DOOR_LOCK" : i == 339741504 ? "MIRROR_Z_POS" : i == 339741505 ? "MIRROR_Z_MOVE" : i == 339741506 ? "MIRROR_Y_POS" : i == 339741507 ? "MIRROR_Y_MOVE" : i == 287312708 ? "MIRROR_LOCK" : i == 289409861 ? "MIRROR_FOLD" : i == 356518784 ? "SEAT_MEMORY_SELECT" : i == 356518785 ? "SEAT_MEMORY_SET" : i == 354421634 ? "SEAT_BELT_BUCKLED" : i == 356518787 ? "SEAT_BELT_HEIGHT_POS" : i == 356518788 ? "SEAT_BELT_HEIGHT_MOVE" : i == 356518789 ? "SEAT_FORE_AFT_POS" : i == 356518790 ? "SEAT_FORE_AFT_MOVE" : i == 356518791 ? "SEAT_BACKREST_ANGLE_1_POS" : i == 356518792 ? "SEAT_BACKREST_ANGLE_1_MOVE" : i == 356518793 ? "SEAT_BACKREST_ANGLE_2_POS" : i == 356518794 ? "SEAT_BACKREST_ANGLE_2_MOVE" : i == 356518795 ? "SEAT_HEIGHT_POS" : i == 356518796 ? "SEAT_HEIGHT_MOVE" : i == 356518797 ? "SEAT_DEPTH_POS" : i == 356518798 ? "SEAT_DEPTH_MOVE" : i == 356518799 ? "SEAT_TILT_POS" : i == 356518800 ? "SEAT_TILT_MOVE" : i == 356518801 ? "SEAT_LUMBAR_FORE_AFT_POS" : i == 356518802 ? "SEAT_LUMBAR_FORE_AFT_MOVE" : i == 356518803 ? "SEAT_LUMBAR_SIDE_SUPPORT_POS" : i == 356518804 ? "SEAT_LUMBAR_SIDE_SUPPORT_MOVE" : i == 289409941 ? "SEAT_HEADREST_HEIGHT_POS" : i == 356518806 ? "SEAT_HEADREST_HEIGHT_MOVE" : i == 356518807 ? "SEAT_HEADREST_ANGLE_POS" : i == 356518808 ? "SEAT_HEADREST_ANGLE_MOVE" : i == 356518809 ? "SEAT_HEADREST_FORE_AFT_POS" : i == 356518810 ? "SEAT_HEADREST_FORE_AFT_MOVE" : i == 322964416 ? "WINDOW_POS" : i == 322964417 ? "WINDOW_MOVE" : i == 320867268 ? "WINDOW_LOCK" : i == 299895808 ? "VEHICLE_MAP_SERVICE" : i == 299896064 ? "OBD2_LIVE_FRAME" : i == 299896065 ? "OBD2_FREEZE_FRAME" : i == 299896066 ? "OBD2_FREEZE_FRAME_INFO" : i == 299896067 ? "OBD2_FREEZE_FRAME_CLEAR" : i == 289410560 ? "HEADLIGHTS_STATE" : i == 289410561 ? "HIGH_BEAM_LIGHTS_STATE" : i == 322964994 ? "FOG_LIGHTS_STATE" : i == 289410563 ? "HAZARD_LIGHTS_STATE" : i == 289410576 ? "HEADLIGHTS_SWITCH" : i == 289410577 ? "HIGH_BEAM_LIGHTS_SWITCH" : i == 289410578 ? "FOG_LIGHTS_SWITCH" : i == 289410579 ? "HAZARD_LIGHTS_SWITCH" : i == 554696752 ? "AP_PHONE_CONTACT_INFO" : i == 554696753 ? "AP_PHONE_NUMBER" : i == 557842482 ? "AP_PHONE_STATE" : i == 554696800 ? "AP_MEDIA_ALBUM_INFO" : i == 557842529 ? "AP_MEDIA_PLAYING_CURRENT_TIME" : i == 554696803 ? "AP_MEDIA_PLAYING_SOURCE_INFO" : i == 554696804 ? "AP_MEDIA_PLAYING_SOURCE_MESSAGE" : i == 557842533 ? "AP_MEDIA_PLAYING_STATE" : i == 557842534 ? "AP_MEDIA_PLAYING_TOTAL_TIME" : i == 554696807 ? "AP_MEDIA_SINGER_NAME" : i == 557842576 ? "AP_NAVI_BYROAD_SIGNAL" : i == 554696849 ? "AP_NAVI_COUNTRY_CODE_VALUE" : i == 557842578 ? "AP_NAVI_CURRENT_ROAD_TYPE" : i == 557842579 ? "AP_NAVI_CURVE_DISTANCE" : i == 557842580 ? "AP_NAVI_ENTER_CURVE_INFO" : i == 557842581 ? "AP_NAVI_ENTER_RAMP_INFO" : i == 557842582 ? "AP_NAVI_ENTER_TUNNEL_INFO" : i == 557842583 ? "AP_NAVI_GUIGANCE_ON" : i == 557842584 ? "IP_NAVI_DISTANCE" : i == 557842585 ? "AP_NAVI_NUMBER" : i == 557842586 ? "AP_NAVI_NUMBER_V2" : i == 557842587 ? "AP_NAVI_RAMP_SLOPE_FAR_VALUE" : i == 557842588 ? "AP_NAVI_RAMP_SLOPE_INFO" : i == 557842589 ? "AP_NAVI_RAMP2_DISTANCE" : i == 557842590 ? "AP_NAVI_ROAD_CURVATURE" : i == 557842591 ? "AP_NAVI_ROAD_CURVATURE_FAR_VALUE" : i == 557842592 ? "AP_NAVI_SPEED_LIMIT_DISTANCE" : i == 557842593 ? "AP_NAVI_SPEED_LIMIT_TYPE" : i == 557842594 ? "AP_NAVI_SPEED_LIMIT_UNITS" : i == 557842595 ? "AP_NAVI_SPEED_LIMIT_VALUE" : i == 557842596 ? "AP_NAVI_SPEEDLIMIT_SIGN_ON" : i == 557842597 ? "AP_NAVI_STATE" : i == 557842598 ? "AP_NAVI_TUNNEL_DISTANCE" : i == 557842599 ? "AP_NAVI_TUNNEL_LENGTH" : i == 557842624 ? "AP_DRIVE_MODE_SET_STATUS" : i == 555745473 ? "AP_DISPLAY_LEAVE_CAR_ANIMATION_STATE" : i == 557842626 ? "AP_NAVI_RAMP_INFO" : i == 557908768 ? "WARNING_VEH_INDICATE_INFO" : i == 557909568 ? "FAULT_SYSTEM_CODE_INFO" : i == 557844033 ? "FAULT_BCM_CODE" : i == 557844034 ? "FAULT_HVAC_CODE" : i == 557844832 ? "BODY_DOOR_AUDIOWARNING_ON" : i == 557844833 ? "BODY_DOOR_AUTO_IGNOFF_UNLOCK_ON" : i == 557844834 ? "BODY_DOOR_AUTO_SPEEKLOCK_ON" : i == 557844835 ? "BODY_DOOR_LEAVE_AUTO_LOCK_STATE" : i == 557844836 ? "BODY_DOOR_LOCK_STATE" : i == 557844837 ? "BODY_DOOR_OPEN_LOCK_PROTECT_ON" : i == 557844838 ? "BODY_DOOR_REMOTE_LOCK_REQ" : i == 557844839 ? "BODY_DOOR_RKE_UNLOCKDOOR_MODE" : i == 557844840 ? "BODY_DOOR_SMART_TRUNKULOCK_ON" : i == 557844841 ? "BODY_DOOR_TWICE_LOCK_ON" : i == 557844842 ? "BODY_DOOR_UNLOCK_STATE" : i == 557844843 ? "BODY_DOOR_WELCOME_UNLOCK_ON" : i == 557844844 ? "BODY_DOOR_FCW_OPEN_ON" : i == 557844845 ? "BODY_DOOR_TRUNK_DOOR_STATE" : i == 557844880 ? "BODY_WINDOW_LOCK_AUTO_UP_ON" : i == 557844881 ? "BODY_WINDOW_REAR_WIPER_ON" : i == 557844882 ? "BODY_WINDOW_SUNROOF_CONTROL_STATE" : i == 557844883 ? "BODY_WINDOW_SUNROOFRAIN_DETECTClOSE_ON" : i == 557844884 ? "BODY_WINDOW_SUNSHADE_STATE" : i == 591399317 ? "VENDOR_WINDOW_STATE" : i == 591399319 ? "BODY_WINDOW_WIPER" : i == 557844888 ? "BODY_WINDOW_WASH" : i == 557844889 ? "BODY_WINDOW_WIPER_INTERVAL" : i == 557844891 ? "BODY_WINDOW_WIPER_SENSOR" : i == 557844892 ? "BODY_WINDOW_SUNROOF_MOTOR_STATE" : i == 557844893 ? "BODY_WINDOW_SUNROOF_STATE" : i == 557844928 ? "BODY_MIRROR_AUTOFOLD_ON" : i == 557844929 ? "BODY_MIRROR_REAR_BACK_ASSISTANCE_ON" : i == 557844976 ? "BODY_SEAT_DSM_MEMORY_REQ" : i == 557844977 ? "BODY_SEAT_EASY_ENTRY_ON" : i == 557844978 ? "BODY_SEAT_REAR_BELT_WARNING_ON" : i == 557845024 ? "BODY_LIGHT_ATMO_ASSOCIATE_DRIVING_STATE" : i == 557845025 ? "BODY_LIGHT_ATMO_AUTO_ADJUST_ON" : i == 557845026 ? "BODY_LIGHT_ATMO_BRIGHT_LEVEL" : i == 557845027 ? "BODY_LIGHT_ATMO_COLOR_STATE" : i == 557845028 ? "BODY_LIGHT_DAY_NIGHT_ON" : i == 557845029 ? "BODY_LIGHT_ATMO_ON" : i == 557845030 ? "BODY_LIGHT_DIMMER_LEVEL" : i == 557845031 ? "BODY_LIGHT_DOME_ON" : i == 557845032 ? "BODY_LIGHT_DOOR_ON" : i == 557845033 ? "BODY_LIGHT_FOLLOWME_HOME_STATE" : i == 557845034 ? "BODY_LIGHT_HEAD_AUTO_ON" : i == 557845035 ? "BODY_LIGHT_HEAD_HEIGHT_STATE" : i == 557845036 ? "BODY_LIGHT_HMAENABLE_ON" : i == 557845037 ? "BODY_LIGHT_LANE_CHANGE_TURN_STATE" : i == 557845038 ? "BODY_LIGHT_NODE_CTL" : i == 557845039 ? "BODY_LIGHT_REALTIME_ON" : i == 557845040 ? "BODY_LIGHT_SMART_UNLOCK_ON" : i == 557845041 ? "BODY_LIGHT_SMART_WELCOMELIGHT_ON" : i == 557845042 ? "BODY_LIGHT_WELCOME_MODE" : i == 557845043 ? "BODY_LIGHT_WIDTHLAMP_ON" : i == 557845044 ? "BODY_LIGHT_WIDTHLAMP_SW" : i == 557845045 ? "BODY_TURN_LEFT_SIGNAL_STATE" : i == 557845046 ? "BODY_TURN_LEFT_SIGNAL_SWITCH" : i == 557845047 ? "BODY_TURN_RIGHT_SIGNAL_STATE" : i == 557845048 ? "BODY_TURN_RIGHT_SIGNAL_SWITCH" : i == 557845049 ? "BODY_EMERGENCY_LIGHT" : i == 624953914 ? "BODY_DOME_LIGHT" : i == 557845051 ? "BODY_AUTO_HEAD_LIGHT" : i == 557910608 ? "BODY_TIRE_PRESSURE_STATE" : i == 557910609 ? "BODY_TIRE_TEMP_STATE" : i == 557845120 ? "BODY_BATTERY_BCN_LEVEL" : i == 557845121 ? "BODY_BATTERY_REMAINING_INFO" : i == 557845122 ? "BODY_BATTERY_U_BATT_INFO" : i == 557845123 ? "BODY_ENGINE_OIL_INFO" : i == 557845216 ? "BODY_CONTROL_PANEL_VOLUME_ADJUST_DIRECTION" : i == 557845217 ? "BODY_CONTROL_PANEL_VOLUMEL_ADJUST_LEVEL" : i == 557845218 ? "BODY_STEERING_WHEEL_CUSTOME_STATE" : i == 557845220 ? "BODY_WIRELESS_CHARGING_ON" : i == 557845221 ? "BODY_STEERING_WHEEL_STEERING_ANGLE" : i == 557845632 ? "IP_DISPLAY_REQ" : i == 557845633 ? "IP_DISPLAY_MODE_SET" : i == 557845634 ? "IP_DISPLAY_THEME_STATE" : i == 557845635 ? "IP_NAVI_STATE" : i == 557845636 ? "IP_REQ_SCREENSHOTS_REQ" : i == 555748485 ? "IP_REQ_SCREENSHOTS_RESULT" : i == 555748486 ? "IP_REQ_SHOW_VERSION" : i == 554699911 ? "IP_VERSION_INFO" : i == 557845640 ? "IP_VOLUME_LEVEL" : i == 557845641 ? "IP_VOLUME_TYPE" : i == 557846432 ? "DRIVE_ENGINE_POWER_MODE" : i == 557846433 ? "DRIVE_ENGINE_START_ON" : i == 557846434 ? "DRIVE_ENGINE_STOP_START_ON" : i == 557846435 ? "DRIVE_ENGINE_STATUS" : i == 555749328 ? "DRIVE_RADAR_CRASH_OUTPUT_STATE" : i == 557912017 ? "DRIVE_RADAR_OBSTACLE_DISTANCE_INFO" : i == 557912018 ? "DRIVE_RADAR_SIDE_ZONE_STATE" : i == 557846483 ? "DRIVE_RADAR_RFC_OBSTACLE_DISTANCE" : i == 557846528 ? "DRIVE_ESC_ON" : i == 557846529 ? "DRIVE_GEAR_SHIFT_POSTION_VALID_STATE" : i == 557846530 ? "DRIVE_MODE_MEMORY_ON" : i == 557846531 ? "DRIVE_RADAR_FRONT_ACTIVE_ON" : i == 557846532 ? "DRIVE_SWC_EPS_MODE" : i == 557847232 ? "ADAS_APA_ACTIVE_ON" : i == 557847233 ? "ADAS_APA_ASP_REMIND_ON" : i == 557847234 ? "ADAS_APA_BSD_LCA_ON" : i == 557847235 ? "ADAS_APA_DYNAMIC_SLOT_NOTICE" : i == 557847236 ? "ADAS_APA_FUNCTION_ON_OFF_STATE" : i == 557847237 ? "ADAS_APA_MODE" : i == 557847238 ? "ADAS_APA_OBSTACLE_SALF_DISTANCE" : i == 557847239 ? "ADAS_APA_PARK_NOTICE_INFO" : i == 557847240 ? "ADAS_APA_PARKING_CONFIRM_STATE" : i == 557847241 ? "ADAS_APA_PARKING_PERCENTAGE_INFO" : i == 557847242 ? "ADAS_APA_PARKING_RESUME_STATE" : i == 557912779 ? "ADAS_APA_REBUILD_TARGET_DIST_INFO" : i == 557847244 ? "ADAS_APA_REMOTE_DISTANCE" : i == 557847245 ? "ADAS_APA_SLOT_DISTANCE" : i == 557847246 ? "ADAS_APA_SLOT_NOTICE_INFO" : i == 557847247 ? "ADAS_APA_SLOT_TYPE" : i == 557847248 ? "ADAS_APA_SLOT_USER_STATE" : i == 557847280 ? "ADAS_HDC_CONTROL_ON" : i == 555750129 ? "ADAS_HDC_STATE" : i == 557847327 ? "ADAS_ACC_AUTOBRAKE_PEOPLE_ON" : i == 557847328 ? "ADAS_ACC_AUTOBRAKE_ON" : i == 557847329 ? "ADAS_ACC_CRUISE_MODE" : i == 557847330 ? "ADAS_ACC_FCW_WARN_STATE" : i == 557847331 ? "ADAS_ACC_ISA_ON" : i == 557847332 ? "ADAS_ACC_OBJENABLE_ON" : i == 557847333 ? "ADAS_ACC_PARALLEL_LINE_ASSIST_MODE" : i == 557847334 ? "ADAS_ACC_SPEED_VALUE" : i == 557847335 ? "ADAS_ACC_WORK_MODE" : i == 557847336 ? "DRIVE_ACC_FCW_OVERSPEED_INFO" : i == 557847337 ? "ADAS_ACC_FCW_OVERSPEED_ON" : i == 557847338 ? "ADAS_ACC_FCW_OVERSPEED_SOUND_ON" : i == 557847339 ? "ADAS_ACC_FCW_REAR_COLLISION_ON" : i == 557847340 ? "ADAS_ACC_LDW_LAS_MODE" : i == 557847341 ? "ADAS_ACC_PCW_FRONT_LOWSPEED_PEDESTRIAN_ON" : i == 557847342 ? "ADAS_ACC_ULS_FKP_ON" : i == 557847343 ? "ADAS_ACC_CTA_ON" : i == 557847424 ? "ADAS_LAS_LDW_SHAKELEV_STATE" : i == 557847425 ? "ADAS_LAS_WARNINGMODE_STATE" : i == 557848012 ? "ADAS_CTA_LEFT_ALERT" : i == 557848013 ? "ADAS_CTA_RIGHT_ALERT" : i == 557848014 ? "ADAS_LAS_MODE_SELECTION" : i == 557848015 ? "ADAS_LAS_LDW_STATUS" : i == 557848016 ? "ADAS_LAS_LDW_SENSITIVITY" : i == 557848017 ? "ADAS_LAS_CALIBRATION_STATUS" : i == 557848018 ? "ADAS_LAS_LEFT_WARNING_STATUS" : i == 557848019 ? "ADAS_LAS_RIGHT_WARNING_STATUS" : i == 557848020 ? "ADAS_LAS_HOST_LANE_LEFT_STATUS" : i == 557848021 ? "ADAS_LAS_HOST_LANE_RIGHT_STATUS" : i == 557848022 ? "ADAS_LAS_LLANE_MARKER_TYPE" : i == 557848023 ? "ADAS_LAS_RLANE_MARKER_TYPE" : i == 557848024 ? "DRIVE_LAS_FATIGUE_MONITOR_ON" : i == 557848025 ? "DRIVE_LAEB_ON" : i == 557848027 ? "ADAS_KEY_VEH_RELEVANCE_STATE" : i == 557848028 ? "ADAS_KEY_VEH_NUMBER_INFO" : i == 557848029 ? "ADAS_BLIND_AREA_DETECTION_ON" : i == 557848030 ? "ADAS_KEY_DETECTED_STATE" : i == 557848031 ? "ADAS_IACC_LAS_ON" : i == 557848032 ? "VENDOR_AUDIO_MODE_REQ" : i == 557848033 ? "VENDOR_BCALL_REQ" : i == 557848034 ? "VENDOR_BCALL_STATE" : i == 557848035 ? "VENDOR_BLE_USER_ID_INFO" : i == 557848036 ? "VENDOR_DRIVER_ID_INFO" : i == 557848037 ? "VENDOR_ECALL_STATE" : i == 557848038 ? "VENDOR_CAR_FACTORY_RESET" : i == 557848039 ? "VENDOR_LIGHT_MUSIC_ON" : i == 557848066 ? "VENDPR_LIGHT_MUSIC_PLAY_SETBACK_STATE" : i == 557848040 ? "VENDOR_LIGHT_MUSIC_MODE_SELECT_STATE" : i == 557848041 ? "VENDOR_DISPLAY_PCPD_SCREEN_MODE" : i == 554702314 ? "VENDOR_MFD_VERSION" : i == 560993771 ? "VENDOR_MFD_RECV_DATA_INFO" : i == 560993772 ? "VENDOR_MCU_RECV_DATA_INF0" : i == 560993773 ? "VENDOR_MCU_SEND_DATA_INFO" : i == 557848046 ? "VENDOR_MCU_VERSION_REQ" : i == 554702319 ? "VENDOR_MCU_VERSION" : i == 560993776 ? "VENDOR_MFD_SEND_DATA_INFO" : i == 554702321 ? "VENDOR_MFD_VERSION_REQ" : i == 557848050 ? "VENDOR_UPDATE_BCM_CANCEL_SET" : i == 554702323 ? "VENDOR_UPDATE_BCM_REQ" : i == 557913588 ? "VENDOR_UPDATE_BCM_STATE" : i == 554702325 ? "VENDOR_UPDATE_BCM_VERSION_REQ" : i == 554702326 ? "VENDOR_UPDATE_BCM_VERSION" : i == 557848055 ? "VENDOR_UPDATE_HVAC_CANCEL_SET" : i == 554702328 ? "VENDOR_UPDATE_HVAC_REQ" : i == 557913593 ? "VENDOR_UPDATE_HVAC_STATE" : i == 554702330 ? "VENDOR_UPDATE_HVAC_VERSION_REQ" : i == 554702331 ? "VENDOR_UPDATE_HVAC_VERSION" : i == 557848060 ? "VENDOR_UPDATE_MCU_CANCEL_SET" : i == 554702333 ? "VENDOR_UPDATE_MCU_REQ" : i == 557913598 ? "VENDOR_UPDATE_MCU_STATE" : i == 557848063 ? "VENDOR_UPDATE_MFD_CANCEL_SET" : i == 554702336 ? "VENDOR_UPDATE_MFD_REQ" : i == 557913601 ? "VENDOR_UPDATE_MFD_STATE" : i == 557848829 ? "AP_USER_STATE" : i == 557848830 ? "AP_SCENE_MODE_CTL_ON" : i == 557848831 ? "AP_SCENE_MODE_ACT" : i == 557848832 ? "HVAC_ACTIVE_VENTILATION_ON" : i == 557848833 ? "HVAC_AIR_DRYIONG_ON" : i == 675289346 ? "HVAC_AIR_QUALITY_IN_OUT_CAR" : i == 557848836 ? "HVAC_AIR_QUALITY_RATING_INNER" : i == 557848837 ? "HVAC_AQS_MODE_ON" : i == 557848838 ? "HVAC_COMFORT_MODE" : i == 557848839 ? "HVAC_DISPLAY_REQ" : i == 624957704 ? "HVAC_FAN_SPEED_ADJUST" : i == 557848841 ? "HVAC_ION_GENERATOR_ON" : i == 624957706 ? "HVAC_SETTING_LOCK" : i == 624957707 ? "HVAC_TEMPERATURE_LV_SET" : i == 557848844 ? "HVAC_TIMING_VENTILATION_MODE" : i == 557848845 ? "HVAC_VENTILATION_USER_DEFINED_SETTING_AFTERNOON" : i == 557848846 ? "HVAC_VENTILATION_USER_DEFINED_SETTING_EVENING" : i == 557848847 ? "HVAC_VENTILATION_USER_DEFINED_SETTING_MORNING" : i == 557848849 ? "HVAC_WINDOW_VENTILATION_ON" : i == 624957714 ? "HVAC_TEMPERATURE_ADJUST" : i == 557848851 ? "HVAC_DUAL_STATE" : i == 557848852 ? "HVAC_LOCK_VENTILATION_ON" : i == 557903486 ? "DVR_CURRENT_MEDIA_COUNT" : i == 557903487 ? "DVR_REC_SWITCH" : i == 557903488 ? "DVR_SNAP_SHOOT" : i == 557903489 ? "DVR_SLIDE_PAGING" : i == 557903490 ? "DVR_DELETE_COMMAND" : i == 557903491 ? "DVR_EMERGENCY_COMMAND" : i == 557903492 ? "DVR_EDIT_MODE_COMMAND" : i == 557903493 ? "DVR_REPLAY_MODE" : i == 557903494 ? "DVR_MODE" : i == 557903495 ? "DVR_CURRENT_VIDEO_COUNTS" : i == 557969032 ? "DVR_EDIT_SELECTED" : i == 557903497 ? "DVR_STORGE_PERCENT" : i == 557903498 ? "DVR_MEDIA_SELECT" : i == 557903499 ? "DVR_REPLAY_SPEED" : i == 557903500 ? "DVR_REPLAY_COMMAND" : i == 557903501 ? "DVR_PRINT_SCREEN" : i == 557903502 ? "DVR_DISPLAY_VISION" : i == 557903503 ? "DVR_VIDEO_PLAY_USER_TIME" : i == 557903504 ? "DVR_VIDEO_CUR_TIME" : i == 557903505 ? "DVR_VIDEO_TOTAL_TIME" : i == 557903506 ? "DVR_RESOLUTION" : i == 561049235 ? "DVR_SYSTEM_VERSION" : i == 557903508 ? "DVR_SYSTEM_UPDATE" : i == 557903509 ? "DVR_SD_CAPACITY" : i == 557903510 ? "DVR_TAPE" : i == 557903511 ? "DVR_LANGUAGE_SET" : i == 557903512 ? "DVR_FORMAT" : i == 557903513 ? "DVR_RECOVERY" : i == 557903514 ? "DVR_EMERGENCY_RECORD" : i == 557903515 ? "DVR_RECORDING_CYCLE_SETTING" : i == 557903516 ? "DVR_ERROR" : i == 557903517 ? "DVR_STORAGE_STATUS" : i == 557903518 ? "DVR_SDCARDERROR_STATUS" : i == 557903519 ? "DVR_SDCARDFULL_STATUS" : i == 560066208 ? "DVR_CUR_LOCATION" : i == 557903521 ? "DVR_DATE_SET_1" : i == 557903522 ? "DVR_DATE_SET_2" : i == 557903523 ? "DVR_TIME_SET" : i == 557903524 ? "DVR_DISTORT_CORRECT_SWITCH" : i == 557903525 ? "DVR_NORMAL_TO_EMERGENCY_SWITCH" : i == 557903526 ? "DVR_REALTIME_DISPLAY" : i == 557903527 ? "DVR_REBROADCAST" : i == 557903528 ? "DVR_360_REPLAY_MODE" : i == 557903529 ? "AVM_FORMAT_FAT32_REQ" : i == 557903530 ? "DVR_VIDEO_CURRENT_TIME" : i == 557903531 ? "DVR_SD_CAPACITY_PERCENT" : i == 557903532 ? "DVR_BROWSE_CTRL" : i == 557903540 ? "AVM_DISPLAY_SWITCH" : i == 557903541 ? "AVM_TURNLIGHT_TRIGGER_SWITCH" : i == 557903542 ? "AVM_RADAR_TRIGGER_SWITCH" : i == 557903543 ? "AVM_LINE_SWITCH" : i == 557969080 ? "AVM_COORDINATE_TRANSFER" : i == 557903545 ? "AVM_DISPLAY_FORM" : i == 557903546 ? "AVM_VISION_ANGLE_REQUES" : i == 557903547 ? "AVM_3D_ENABLE" : i == 557903548 ? "AVM_3D_HOR_ANGLE" : i == 557903549 ? "AVM_MOD_ENABLE" : i == 557903550 ? "AVM_CALIBRATION" : i == 557903551 ? "AVM_CALIBRATION_CONFIRM" : i == 557903552 ? "AVM_OVERSPEED_WARNING" : i == 557903553 ? "AVM_TRANSPARENT_CAR_SWITCH" : i == 557903557 ? "AVM_REMOTE_DETECT_UPLOAD_STATUS" : i == 557903558 ? "AVM_BODY_COLOR_STATUS" : i == 557903559 ? "AVM_WHEEL_HUB_STYLE" : i == 557903560 ? "AVM_SCREEN_OPERATE_STATUS" : i == 557969097 ? "AVM_LICENSE_NUMBER_SET" : i == 557969098 ? "AVM_LICENSE_NUMBER_ACK" : i == 557903564 ? "AVM_BOOT_ANIMATION" : i == 557903565 ? "AVM_DEF_OPEN" : i == 557903566 ? "AVM_RECORD_STATUS" : i == 557969103 ? "SCREEN_INFO" : i == 557903568 ? "AVM_OBJECT_DETECT" : i == 557903569 ? "AVM_OBJECT_DETECT_ALERT" : i == 561049601 ? "VENDOR_MCU_INIT_INFO" : i == 557903874 ? "VENDOR_MCU_POWER_MODE" : i == 561049603 ? "VENDOR_OFF_LINE_STATUS" : i == 557903876 ? "SHUTDOWN_WARING_INFO" : i == 557969413 ? "VENDOR_MCU_TIME" : i == 557903878 ? "VENDOR_MCU_TIME_REQ" : i == 557903879 ? "VENDOR_AMPLIFIER_SWITCH_STATUS" : i == 557903880 ? "VENDOR_MCU_RESTART_REQ" : i == 557903881 ? "VENDOR_POWER_ON_NOTICE" : i == 557903883 ? "FUEL_LEVEL_STATE" : i == 561049612 ? "TUID_INFO" : i == 554758157 ? "VENDOR_ICCID_ACK" : i == 557903886 ? "VENDOR_ICCID_REQ" : i == 557903887 ? "TUID_REQ" : i == 557903888 ? "BODY_LOCK_AUTO_CLS_WIN_SET" : i == 557903889 ? "BODY_REMOTE_CTRL_SUNROOF_SET" : i == 591523858 ? "BODY_SUNROOF_SUNVISOR_ON_OFF" : i == 557903891 ? "BODY_REMOTE_CTRL_SW_SET" : i == 557903892 ? "BODY_SIRENS_SOUND_ALARM_MODE" : i == 557903893 ? "HVAC_REMOTE_WINDOW_VENTILATION_ON" : i == 561049622 ? "SET_TUID_INFO" : i == 557903895 ? "VENDOR_IC_UPDATE_ACK" : i == 557903896 ? "VENDOR_IC_UPDATE_REQ" : i == 557903897 ? "VENDOR_BATTERY_CHARGE_SW" : i == 557903905 ? "BODY_SEAT_COMTIY_ON" : i == 557903906 ? "HVAC_KEY_LOCK_WINDOW_VENTILATION_ON" : i == 557903920 ? "VENDOR_DRIVER_MODE_SET" : i == 554758193 ? "VENDOR_HW_VERSION_ACK" : i == 557903922 ? "VENDOR_HW_VERSION_REQ" : i == 554758195 ? "VENDOR_IMEI_ACK" : i == 557903924 ? "VENDOR_IMEI_REQ" : i == 557903925 ? "VENDOR_SEND_TO_CAR_REQ" : i == 557903926 ? "BODY_WINDOW_SUNROOF_CONTROL_POS" : i == 557903927 ? "BODY_WINDOW_SUNSHADE_POS" : i == 557903928 ? "BODY_DOOR_TRUNK_DOOR_POS" : i == 557903936 ? "VENDOR_SECRET_REQ" : i == 561049665 ? "VENDOR_CERTIFICATE_ACK" : i == 561049666 ? "VENDOR_SECRET_ACK" : i == 554758211 ? "VENDOR_SET_VIN_INFO" : i == 561049668 ? "VENDOR_OFF_LINE_SET" : i == 557969477 ? "KEY_QUERY_INFO" : i == 557903942 ? "AVM_LIVE_INFO" : i == 561049671 ? "VENDOR_CERTIFICATE_ID" : i == 560066632 ? "VENDOR_TIRE_TEMPERATURE" : i == 557903945 ? "TIRE_PRESSURE_SIGNAL" : i == 557903946 ? "VENDOR_OFF_LINE_REQ" : i == 625012816 ? "VENDOR_PHOTO_REQ" : i == 557903953 ? "VENDOR_TBOX_VERSION_REQ" : i == 554758226 ? "VENDOR_TBOX_VERSION" : i == 557903955 ? "VENDOR_TBOX_FW_VERSION_REQ" : i == 554758228 ? "VENDOR_TBOX_FW_VERSION" : i == 557903957 ? "REVERSE_SIGNAL" : i == 554758230 ? "VENDOR_ECU_MCU_VERSION" : i == 554758231 ? "VENDOR_PART_NUMBER" : i == 554758232 ? "VENDOR_SW_PART_NUMBER" : i == 557969497 ? "CAR_VEHICLE_REQ" : i == 557903962 ? "REVERSE_CAMERA" : i == 557903964 ? "VENDOR_LIGHT_NIGHT_MODE_STATE" : i == 557903968 ? "SOC_POWER_OFF_SIGN" : i == 557903969 ? "SOC_SLEEP_SIGN" : i == 557903970 ? "SCREEN_BACKLIGHT_TOUCH_SWITCH" : i == 557903971 ? "CAR_VOLTAGE_STATUS" : i == 557903972 ? "BODY_WINDOW_SUNROOT_POS_DIRECTION" : i == 557903973 ? "SCREEN_POWER_ON" : i == 557903974 ? "SCREEN_LIGHT_AUTO" : i == 557903975 ? "VENDOR_FACTORY_RESET" : i == 557906432 ? "HVAC_VIBRATION_FEEDBACK" : i == 557906433 ? "VENDOR_UPDATE_STATE" : i == 826286081 ? "ADAS_APA_ASP_SWITCH" : i == 826286082 ? "ADAS_APA_SYSTEM_FAILURE_FLAG" : i == 826286083 ? "ADAS_APA_LAEB_ENABLE_STATUS" : i == 826286084 ? "ADAS_APA_ASP_MODE_SELECT" : i == 826286085 ? "ADAS_APA_CROSS_PARKING_MODE" : i == 826286086 ? "ADAS_APA_RFBT_APA_MANEUVER_START" : i == 826286087 ? "ADAS_APA_RFBT_APA_MANEUVER_SUSPEND" : i == 826286088 ? "ADAS_APA_RFBT_APA_MANEUVER_CANCEL" : i == 826286089 ? "ADAS_APA_RFBT_APA_POMODE_SELECT" : i == 826286090 ? "ADAS_APA_RFBT_ASP_SWITCH" : i == 826286091 ? "ADAS_APA_RFBT_SLOT_USR_SELECTED" : i == 826286092 ? "ADAS_APA_RFBT_CELLPHONE_APA_MODULE_ENABLE" : i == 826286093 ? "ADAS_APA_RFBT_RPA_SWITCH" : i == 826286094 ? "ADAS_APA_RFBT_VERTICAL_FORWARD" : i == 826286095 ? "ADAS_APA_RFBT_VERTICAL_BAKWARD" : i == 826286100 ? "ADAS_APA_PARK_NOTICE_5" : i == 826286101 ? "ADAS_APA_LAEB_STATUS" : i == 826286102 ? "ADAS_APA_BLECONNECTIONREMIND" : i == 826286103 ? "ADAS_APA_LAEBNOTICE" : i == 826286104 ? "ADAS_APA_REMOTEPARKINGUSINGREMIND" : i == 826286105 ? "ADAS_APA_ASPAVAILABLESTATUS" : i == 826286106 ? "ADAS_APA_CROSSMODESELECTREQ" : i == 826286107 ? "ADAS_APA_ASPSTATUS" : i == 826286108 ? "ADAS_APA_BCMHORNCOMMAND" : i == 826286109 ? "ADAS_APA_VEHICLEFRONTDETECT" : i == 826286110 ? "ADAS_APA_PEPS_ENGINEOFF_LOCKREQUEST" : i == 826286111 ? "ADAS_APA_TCUCLUTCHCOMBINATIONREQ" : i == 826286112 ? "ADAS_APA_NUMBEROFPARKINGSLOT" : i == 826286113 ? "ADAS_APA_PARKINGMODEAVAILABLESTS" : i == 826286117 ? "ADAS_APA_EPBREQUEST" : i == 826286118 ? "ADAS_APA_EPBREQUESTVALID" : i == 826286119 ? "ADAS_APA_TARGETACCELERATION" : i == 826286120 ? "ADAS_APA_TARGETACCELERATIONVALID" : i == 826286121 ? "ADAS_APA_TRANSPRNDSHIFTREQUEST" : i == 826286122 ? "ADAS_APA_TRANSPRNDSHIFTREQVALID" : i == 826286123 ? "ADAS_APA_ENGINETRQREQENABLE" : i == 826286124 ? "ADAS_APA_ENGTORQREQ" : i == 826286125 ? "ADAS_APA_ACTIVATION_STATUS" : i == 826286126 ? "ADAS_APA_TRANSPRNDSHIFTENABLE" : i == 826286127 ? "ADAS_APA_LSCACTION" : i == 826286128 ? "ADAS_APA_HSAHDFORBIDDEN" : i == 826286130 ? "ADAS_APA_PARKNOTICE_4" : i == 826286131 ? "ADAS_APA_PREFILLREQ" : i == 826286132 ? "ADAS_APA_ACCPEDSHIELDREQ" : i == 826286133 ? "ADAS_APA_ESPDECOMPRESSIONMODEL" : i == 826286134 ? "ADAS_APA_ULS_FKP_WARNINGSOUNDSTATUS" : i == 826286138 ? "ADAS_APA_STEERINGANGLEREQPROTECTION" : i == 826286139 ? "ADAS_APA_ERRORSTATUS" : i == 826286140 ? "ADAS_APA_INDICATION" : i == 826286141 ? "ADAS_APA_EMERGENCEBRAKE" : i == 826286142 ? "ADAS_APA_STEERINGANGLEREQ" : i == 826286143 ? "ADAS_APA_REMOTEONOFF" : i == 826286144 ? "ADAS_APA_BUTTONPRESS" : i == 826286145 ? "ADAS_APA_INCREASEPRESSUREREQ" : i == 826286146 ? "ADAS_APA_PARKNOTICE" : i == 826286147 ? "ADAS_APA_TURNLIGHTSCOMMAND" : i == 826286148 ? "ADAS_APA_EPASFAILED" : i == 826286149 ? "ADAS_APA_ABORTFEEDBACK" : i == 826286150 ? "ADAS_APA_CONTROLFEEDBACK" : i == 826351687 ? "ADAS_APA_COORDINATEANGLE" : i == 826286157 ? "ADAS_APA_TYPE" : i == 826286158 ? "ADAS_APA_SLOTVOICESELECT" : i == 826351695 ? "ADAS_APA_SLOT_DISTANCE_ALL" : i == 826351696 ? "ADAS_APA_SLOT_TYPE_ALL" : i == 826286161 ? "ADAS_APA_CLOSEWINFEEDBACK" : i == 826286162 ? "ADAS_APA_ENGINEOFFFEEDBACK" : i == 826286163 ? "ADAS_APA_LOCKFEEDBACK" : i == 826286164 ? "ADAS_APA_REMOTEPARKINGON" : i == 826286165 ? "ADAS_APA_REMOTEAPADONEKEYOFFREQ" : i == 826286166 ? "ADAS_APA_REMOTEPARKINGOFF" : i == 826286167 ? "ADAS_APA_MODE_SELECT" : i == 826351704 ? "ADAS_APA_ON_TOUCH" : i == 826286169 ? "AVM_TOP_LOOK_DOWN_SWITCH" : i == 826286170 ? "AVM_360_AVM_DET_REQ" : i == 826286171 ? "TURN_TRIGGER_BLIND_ON" : i == 826286172 ? "RADAR_TRIGGER_BLIND_ON" : i == 826286173 ? "BODY_BUZZER_WARNING_MODE" : i == 826286174 ? "CAN_DEV_DVR_IS_CONNECTED" : i == 826286175 ? "CAN_DEV_AVM_IS_CONNECTED" : i == 826286176 ? "CAN_DEV_AC_IS_CONNECTED" : i == 826286177 ? "CAN_DEV_APA_IS_CONNECTED" : i == 826286178 ? "ADAS_ACC_SENSITIVITY" : i == 826286179 ? "ADAS_ACC_FCW_WARN_ON" : i == 829431908 ? "TBOX_4G_ICCID_ID" : i == 826286181 ? "TBOX_4G_PARKMODE_ID" : i == 826286182 ? "TBOX_WORK_STATE_ID" : i == 826286183 ? "TBOX_OS_F1F1_CONFIG_ID" : i == 826286184 ? "TBOX_4G_AUDIO_REQ_ID" : i == 826286185 ? "TBOX_4G_CHANGE_PART_STATUS_ID" : i == 826286186 ? "TBOX_4G_BOX_TYPE_ID" : i == 826286187 ? "TBOX_4G_CALL_STATUS_ID" : i == 826286188 ? "TBOX_4G_BCALL_STATUS_ID" : i == 826286189 ? "TBOX_4G_SURROND_REQ_ID" : i == 826286190 ? "TBOX_RESET_4G_ID" : i == 826286191 ? "TBOX_CTR_4G_UPGRADE_ID" : i == 829431920 ? "BODY_OUTLIGHT_FLASH_CTL" : i == 826286193 ? "VENDOR_APU_ILL_ON" : i == 826286194 ? "BODY_CONTROL_PANEL_VOL_AJ_SENSSTIVE" : i == 826286195 ? "BODY_CONTROL_PANEL_VOL_MUTE" : i == 826286196 ? "BODY_CONTROL_PANEL_NAVI_VOL" : i == 826286197 ? "BODY_CONTROL_PANEL_MEADIA_VOL" : i == 826286198 ? "BODY_CONTROL_PANEL_VOL_AJ_TYPE" : i == 826286199 ? "BODY_CONTROL_PANEL_DIS_TYPE" : i == 826286200 ? "BODY_CONTROL_PANEL_TTS_VOL" : i == 826286201 ? "BODY_CONTROL_PANEL_MEADIA_MUTE" : i == 826286202 ? "BODY_CONTROL_PANEL_TBOX_PHONE_VOL" : i == 826286203 ? "BODY_CONTROL_PANEL_PHONE_MUTE" : i == 826286204 ? "POWER_WARNNING_STATE" : i == 826286205 ? "VENDOR_POWER_OFF_ON" : i == 826351742 ? "FTYPE_BODY_ALL" : i == 826351743 ? "FTYPE_AC_ALL" : i == 826351744 ? "FTYPE_FRONT_ASSIST_ALL" : i == 826286209 ? "AP_NAVI_NEXT_ROADCROSS_DISTANCE" : i == 827400322 ? "AP_NAVI_STATE_INFO_LOCATION" : i == 826351747 ? "AP_NAVI_STATE_INFO_ICON_NUMBER" : i == 829427712 ? "PASSTHROUGH_DATA" : i == 826281985 ? "TIME_SETTING" : i == 826281986 ? "SCREEN_BRIGHTNESS" : i == 826281987 ? "MCU_UPDATE" : i == 826281988 ? "PARAM_UNIT_SETTING" : i == 826281989 ? "SYSTEM_STATUS" : i == 826281990 ? "DISP_MODE" : i == 826281991 ? "APU_STATUS" : i == 823136264 ? "INFO_MCU_VER" : i == 826281993 ? "FACTORY_RECOVERY" : i == 829427722 ? "PDEV_VEHICLE_INFO_FID_EOL" : i == 823136267 ? "PDEV_VEHICLE_INFO_FID_VIN" : i == 823136268 ? "PDEV_VEHICLE_INFO_FID_TUID" : i == 829427725 ? "PDEV_VEHICLE_INFO_FID_VSOL" : i == 826281998 ? "MCU_SYSTEM_SET_DEVELOPER_MODE" : i == 826347535 ? "NAVI_STATE_INFO" : i == 829427728 ? "NAVI_TEXT_INFO" : i == 826282001 ? "AUDIO_SAMPLE_STATE" : i == 829427730 ? "AUDIO_SAMPLE_DATA" : i == 826282003 ? "PDEV_VEHICLE_INFO_LOGIN_TSP" : i == 826282497 ? "AIR_CLEANER_STATUS_FID_FILTERLIFEEND" : i == 826282498 ? "AIR_CLEANER_STATUS_FID_WORK_STATUS" : i == 826282499 ? "AIR_CLEANER_STATUS_FID_AUTOSTATUS" : i == 826282500 ? "AIR_CLEANER_STATUS_FID_INCAR_PM25_LVL" : i == 826282501 ? "AIR_CLEANER_STATUS_FID_OUTCAR_PM25_LVL" : i == 826282502 ? "AIR_CLEANER_STATUS_FID_INCAR_PM25_DATA" : i == 826282503 ? "AIR_CLEANER_STATUS_FID_OUTCAR_PM25_DATA" : i == 826282504 ? "AIR_CLEANER_STATUS_FID_FRAGRANCESW" : i == 826282505 ? "AIR_CLEANER_STATUS_FID_PROMPTREQ" : i == 826282506 ? "AIR_CLEANER_STATUS_FID_DISPLAYACTIVE" : i == 826282507 ? "AIR_CLEANER_STATUS_FID_WINDSET" : i == 826282508 ? "AIR_CLEANER_COMM_FID_WIN" : i == 826282509 ? "AIR_CLEANER_COMM_FID_AUTO" : i == 826282510 ? "AIR_CLEANER_COMM_FID_OFF" : i == 826282511 ? "AIR_CLEANER_COMM_FID_FRAGRANCE" : i == 826282529 ? "AVM_STATUS_FID_CTRL_REQ" : i == 823136802 ? "AVM_STATUS_FID_MAIN_VERSION" : i == 823136803 ? "AVM_STATUS_FID_DISP_VERSION" : i == 826282532 ? "AVM_STATUS_FID_CALIBRATION_STATE" : i == 826282533 ? "AVM_STATUS_FID_CALI_TIME_REQ" : i == 826282534 ? "AVM_STATUS_FID_FRONT_CAM_CALI_FAULT" : i == 826282535 ? "AVM_STATUS_FID_REAR_CAM_CALI_FAULT" : i == 826282536 ? "AVM_STATUS_FID_LEFT_CAM_CALI_FAULT" : i == 826282537 ? "AVM_STATUS_FID_RIGHT_CAM_CALI_FAULT" : i == 826282538 ? "AVM_COMM_FID_ACTIVE" : i == 826282539 ? "AVM_COMM_FID_ASSIST_LINE" : i == 826282540 ? "AVM_COMM_FID_ANGLE" : i == 826282541 ? "AVM_COMM_FID_RADAR_TRIGGER" : i == 826282542 ? "AVM_COMM_FID_CAM_DISP_REQ" : i == 829428271 ? "AVM_COMM_FID_CALI_START_SN" : i == 826282544 ? "AVM_COMM_FID_CALI_INFO_REQ" : i == 826282545 ? "AVM_COMM_FID_TIME_INFO" : i == 826282546 ? "AVM_COMM_FID_LANGUAGE" : i == 826282547 ? "AVM_COMM_FID_SCREEN_STATE" : i == 826282548 ? "AVM_COMM_FID_PURPOSE_KEY" : i == 826348085 ? "AVM_COMM_FID_ORDINATE_OPERATION" : i == 826282550 ? "AVM_COMM_FID_CALI_START" : i == 826282592 ? "DVR_STATUS_FID_ERROR" : i == 826282593 ? "DVR_STATUS_FID_SDCARDSTATUS" : i == 826282594 ? "DVR_STATUS_FID_ACTIVESTATUS" : i == 826282595 ? "DVR_STATUS_FID_STORAGEPERCENT" : i == 826282596 ? "DVR_STATUS_FID_STORAGESTATUS" : i == 826282597 ? "DVR_STATUS_FID_CURRENTVIDEO_COUNTS" : i == 826282598 ? "DVR_STATUS_FID_FILELISTSTATUS" : i == 826282599 ? "DVR_COMM_FID_DVRDISTORTCORRECTSWITCH" : i == 826282600 ? "DVR_COMM_FID_EMERGENCYRECORD" : i == 826282601 ? "DVR_COMM_FID_NORMALTOEMERGENCYSWITCH" : i == 826282602 ? "DVR_COMM_FID_FORMATREQ" : i == 826282603 ? "DVR_COMM_FID_VEDIOSEEK" : i == 826282604 ? "DVR_COMM_FID_LANGUAGESET" : i == 826282605 ? "DVR_COMM_FID_RESOLUTIONSET" : i == 826282606 ? "DVR_COMM_FID_RECORDINGCYCLESETTING" : i == 826282607 ? "DVR_COMM_FID_ENABLESET" : i == 826282608 ? "DVR_COMM_FID_TAPESET" : i == 826282609 ? "DVR_COMM_FID_SHOOTCONTROL" : i == 826282610 ? "DVR_COMM_FID_REPLAYMODE" : i == 826282611 ? "DVR_COMM_FID_REPLAYSPEED" : i == 826282612 ? "DVR_COMM_FID_DISPLAYVISION" : i == 826282613 ? "DVR_COMM_FID_PRINTSCREEN" : i == 826282614 ? "DVR_COMM_FID_MODE" : i == 826282615 ? "DVR_COMM_FID_SDCAPACITY" : i == 826282616 ? "DVR_COMM_FID_SYSTEMIMPRINT" : i == 826282617 ? "DVR_COMM_FID_REBROADCASTREQ" : i == 826282618 ? "DVR_COMM_FID_REALTIMEREQ" : i == 826282619 ? "DVR_COMM_FID_ERRORRECORD" : i == 826282620 ? "DVR_COMM_FID_VIDEOPLAYPERCENTAGE" : i == 826282621 ? "DVR_COMM_FID_EDITSELECTED" : i == 826282622 ? "DVR_COMM_FID_DATEFORMAT" : i == 826282623 ? "DVR_COMM_FID_GSENSOR" : i == 826282624 ? "DVR_COMM_FID_LOCK" : i == 826282625 ? "DVR_COMM_FID_REPLAYDELREQ" : i == 826282626 ? "DVR_COMM_FID_RECOVERYREQ" : i == 826282752 ? "VEHICLESET_CRUISE_CONTROLSET" : i == 826282753 ? "VEHICLESET_CRUISE_INTEGRATED" : i == 826282754 ? "VEHICLESET_CRUISE_MODE" : i == 826282755 ? "VEHICLESET_CRUISE_ACCOBJENABLE" : i == 826282756 ? "VEHICLESET_BTC_DISTANCEALERTENABLE" : i == 826282757 ? "VEHICLESET_BTC_FCWENABLE" : i == 826282758 ? "VEHICLESET_BTC_FCWSENSITIVITY" : i == 826282759 ? "VEHICLESET_BTC_AUTOBRAKEENABLE" : i == 826282760 ? "VEHICLESET_BTC_FCWBEEP" : i == 826282761 ? "VEHICLESET_RW_LCDAL_BSDLCAENABLESTATUS" : i == 826282762 ? "VEHICLESET_RW_LCDAL_CTAENABLESTATUS" : i == 826282763 ? "VEHICLESET_RW_LCDAL_RCWENABLESTATU" : i == 826282764 ? "VEHICLESET_RW_RCWSOUNDENABLE" : i == 826282765 ? "VEHICLESET_RW_LCDAL_SEAENABLESTATUS" : i == 826282766 ? "VEHICLESET_RW_SEWENABLE" : i == 826282767 ? "VEHICLESET_RW_LCDAL_AUDIOWARNINGENABLESTATUS" : i == 826282768 ? "VEHICLESET_RW_LCDAL_LOCKCTRLENABLESTATUS" : i == 826282769 ? "VEHICLESET_LA_LAS_LASMODESELECTIONSTATUS" : i == 826282770 ? "VEHICLESET_LA_LAS_FUNCSEL" : i == 826282771 ? "VEHICLESET_LA_LASWARNINGMODESELECTSTAS" : i == 826282772 ? "VEHICLESET_LA_LAS_LDWSHAKELEVSTATUS" : i == 826282773 ? "VEHICLESET_LA_LAS_LDWSENSITIVITYSTATUS" : i == 826282774 ? "VEHICLESET_LA_LAS_FATIGUEDECTIONENABLESTATUS" : i == 826282775 ? "VEHICLESET_LA_LASWARNINGBEEP" : i == 826282776 ? "VEHICLESET_SLA_LAS_SLASWITCHSTATUS" : i == 826282777 ? "VEHICLESET_SLA_LAS_OVERSPEEDWARNINGENABLESTATUS" : i == 826282778 ? "VEHICLESET_SLA_LAS_OVERSPEEDSOUNDWARNENASTS" : i == 826282779 ? "VEHICLESET_SLA_LAS_OVERSPEEDWARNINGOFFSET" : i == 826282780 ? "VEHICLESET_PA_ULS_FKP_ACTIVATION" : i == 826282781 ? "VEHICLESET_PA_TURNLIGHTAVMSWITCH" : i == 826282782 ? "VEHICLESET_PA_RADARAVMSWITCH" : i == 826282783 ? "VEHICLESET_PA_REMOTEDISTANCESET" : i == 826282784 ? "VEHICLESET_PA_OBSTACLESAFEDISTANCESET" : i == 826282785 ? "VEHICLESET_PA_360AVMDETECTIONREQUEST" : i == 826282786 ? "VEHICLESET_PA_AVMCALIBRATIONSWITCH" : i == 826282787 ? "VEHICLESET_PA_ESPMODE" : i == 826282788 ? "VEHICLESET_PA_FRONTRADAR" : i == 826282789 ? "VEHICLESET_PA_ASS" : i == 826282790 ? "VEHICLESET_PA_ESP" : i == 826282791 ? "VEHICLESET_PA_HDC" : i == 826282792 ? "VEHICLESET_BS_RKEUNLOCKDOORTYPESET" : i == 826282793 ? "VEHICLESET_BS_AUTOSPEEDLOCKSET" : i == 826282794 ? "VEHICLESET_BS_AUTOIGNOFFUNLOCKSET" : i == 826282795 ? "VEHICLESET_BS_SUNROOFRAINSETSTATU" : i == 826282796 ? "VEHICLESET_BS_TWICELOCKDOORSETSTATUS" : i == 826282797 ? "VEHICLESET_BS_GETOFFAUTOLOCK" : i == 826282798 ? "VEHICLESET_BS_SMARTWELCOM" : i == 826282799 ? "VEHICLESET_BS_WELCOMLOCK" : i == 826282800 ? "VEHICLESET_BS_SMARTTRUNKULOCKSTAUS" : i == 826282801 ? "VEHICLESET_BS_EASYENTRYSET" : i == 826282802 ? "VEHICLESET_BS_MIRRORAUTOFOLDSET" : i == 826282803 ? "VEHICLESET_BS_MIRRORSETSTATUS" : i == 826282804 ? "VEHICLESET_BS_REARWIPERSET" : i == 826282805 ? "VEHICLESET_BS_REARSEATBELTWARNINGENABLE" : i == 826282806 ? "VEHICLESET_BS_LOWSPEEDPEDESTRIANWARN" : i == 826282807 ? "VEHICLESET_BS_TPMSRESETSTATUS" : i == 826282808 ? "VEHICLESET_BS_DRIVINGMODEMEMORYENABLE" : i == 826282809 ? "VEHICLESET_BS_LOCKAUTOCLOSEWINDOW" : i == 826282810 ? "VEHICLESET_BS_WIPERSET" : i == 826282811 ? "VEHICLESET_BS_SUNROOFSET" : i == 826282812 ? "VEHICLESET_BS_TAILGATESET" : i == 826282813 ? "VEHICLESET_BS_SUNSHADESET" : i == 826282814 ? "VEHICLESET_BS_RKESUNROOFCTRLTYPE" : i == 826282815 ? "VEHICLESET_BS_SUNROOFOPENSET" : i == 826282816 ? "VEHICLESET_BS_SUNSHADEOPENSET" : i == 826282817 ? "VEHICLESET_BS_MIRRORHEAT" : i == 826282818 ? "VEHICLESET_BS_WD" : i == 826282819 ? "VEHICLESET_BS_DRIVEMODE" : i == 826282820 ? "VEHICLESET_LS_ATMLIGHTBRIGHTSET" : i == 826282821 ? "VEHICLESET_LS_ATMOLIGHTAAENABLE" : i == 826282822 ? "VEHICLESET_LS_ATMOLIGHTAUTOADJUSTSTS" : i == 826282823 ? "VEHICLESET_LS_ATMLIGHTCOLORSET" : i == 826282824 ? "VEHICLESET_LS_ATMOLIGHTAUTOADJUST" : i == 826282825 ? "VEHICLESET_LS_WELCOMEATMOLIGHTENABLE" : i == 826282826 ? "VEHICLESET_LS_ATMOLIGHTLINKOVERSPEEDENABLE" : i == 826282827 ? "VEHICLESET_LS_ATMOLIGHTSPEEDENABLE" : i == 826282828 ? "VEHICLESET_LS_ATMOLIGHTLINKTOPLIGHTENABLE" : i == 826282829 ? "VEHICLESET_LS_ATMOLIGHTLINKDOORENABLE" : i == 826282830 ? "VEHICLESET_LS_SEWENABLE" : i == 826282831 ? "VEHICLESET_LS_ATMOLIGHTVEHICLEENABLE" : i == 826282832 ? "VEHICLESET_LS_AMBIENTLIGHTCOLORFEED" : i == 826282833 ? "VEHICLESET_LS_WELCOMELIGHTSTAUS" : i == 826282834 ? "VEHICLESET_LS_DRLSET" : i == 826282835 ? "VEHICLESET_LS_HMAENABLE" : i == 826282836 ? "VEHICLESET_LS_FOLLOWMEHOMELIGHTSETSTATUS" : i == 826282837 ? "VEHICLESET_LS_LANECHANGETURNLIGHTSETSTATUS" : i == 826282838 ? "VEHICLESET_LS_COPILOTDISPMODE" : i == 826282839 ? "VEHICLESET_LS_ATMOLIGHTMODE" : i == 826282840 ? "VEHICLESET_LS_ATMOLIGHTAREA" : i == 826282841 ? "VEHICLESET_LS_WELCOMELIGHTTIMESET" : i == 826282842 ? "VEHICLESET_LS_ATMOLIGHTFUNCSW" : i == 826282843 ? "VEHICLESET_HUD_ENABLE" : i == 826282844 ? "VEHICLESET_HUD_POSITIONSET" : i == 826282845 ? "VEHICLESET_HUD_LIGHTSET" : i == 826282846 ? "VEHICLESET_HUD_LANGUAGE" : i == 826282847 ? "VEHICLESET_HUD_DISPLAY" : i == 826282848 ? "VEHICLESET_HUD_HEIGHT" : i == 826282849 ? "VEHICLESET_IP_WELCOMESOUNDSET" : i == 826282850 ? "VEHICLESET_IP_MESSAGEALERTSOUNDSET" : i == 826282851 ? "VEHICLESET_IP_ALARMSOUNDSET" : i == 826282852 ? "VEHICLESET_IP_WARNINGVOLUMESETTING" : i == 826282853 ? "VEHICLESET_LS_ATMLIGHTBRIGHTNESSCFG" : i == 826282854 ? "VEHICLESET_LS_ATMLIGHTCOLORCFG" : i == 826282855 ? "VEHICLESET_LS_ATMLIGHTINSTRUMENTDESKCOLOR" : i == 826282856 ? "VEHICLESET_LS_ATMLIGHTOTHERCOLOR" : i == 826282857 ? "VEHICLESET_LS_ATMLIGHTMUSICMODE" : i == 826283008 ? "SEAT_INFO" : i == 826283009 ? "DSM_POWERSTATUS" : i == 826283010 ? "DSM_INITSTATUS" : i == 826283011 ? "DSM_RECALLSTATUS" : i == 826283012 ? "DSM_FH_UP" : i == 826283013 ? "DSM_FH_DN" : i == 826283014 ? "DSM_RH_UP" : i == 826283015 ? "DSM_RH_DN" : i == 826283016 ? "DSM_SLIDE_FW" : i == 826283017 ? "DSM_SLIDE_BW" : i == 826283018 ? "DSM_BR_FW" : i == 826283019 ? "DSM_BR_BW" : i == 826283020 ? "DSM_LEFTBATVOLTSTS" : i == 826283021 ? "DSM_RIGHTBATVOLTSTS" : i == 826283022 ? "DSM_LEFTHEATABNORMAL" : i == 826283023 ? "DSM_RIGHTHEATABNORMAL" : i == 826283024 ? "DSM_DRVKNEADSTS" : i == 826283025 ? "DSM_COPILOTKNEADSTS" : i == 826283026 ? "SEAT_COMMAND_FID_DRIVE_HEAT" : i == 826283027 ? "SEAT_COMMAND_FID_PASS_HEAT" : i == 826283028 ? "SEAT_COMMAND_FID_DRIVE_VENT" : i == 826283029 ? "SEAT_COMMAND_FID_PASS_VENT" : i == 826283030 ? "SEAT_COMMAND_FID_DRIVE_SET1" : i == 826283031 ? "SEAT_COMMAND_FID_DRIVE_SET2" : i == 826283032 ? "SEAT_COMMAND_FID_PASS_SET1" : i == 826283033 ? "SEAT_COMMAND_FID_PASS_SET2" : i == 826283264 ? "RADAR_STATE_FL" : i == 826283265 ? "RADAR_STATE_FLM" : i == 826283266 ? "RADAR_STATE_FR" : i == 826283267 ? "RADAR_STATE_FRM" : i == 826283268 ? "RADAR_STATE_RL" : i == 826283269 ? "RADAR_STATE_RLM" : i == 826283270 ? "RADAR_STATE_RR" : i == 826283271 ? "RADAR_STATE_RRM" : i == 826283272 ? "RADAR_DATA_FL" : i == 826283273 ? "RADAR_DATA_FLM" : i == 826283274 ? "RADAR_DATA_FR" : i == 826283275 ? "RADAR_DATA_FRM" : i == 826283276 ? "RADAR_DATA_RL" : i == 826283277 ? "RADAR_DATA_RLM" : i == 826283278 ? "RADAR_DATA_RR" : i == 826283279 ? "RADAR_DATA_RRM" : i == 826283280 ? "RADAR_PDC_BUZZERALARMPATTERN" : i == 826283281 ? "RADAR_PDC_MODESTATUS" : i == 826283282 ? "RADAR_PDC_LED" : i == 826283283 ? "RADAR_PDC_ECUFAULT" : i == 826283284 ? "RADAR_COMMAND_RR_SWITCH_STATE" : i == 826283285 ? "RADAR_COMMAND_RR_BEEP_FRE" : i == 826283520 ? "VEHICLE_DOOR_FID_LF_DOOR" : i == 826283521 ? "VEHICLE_DOOR_FID_RF_DOOR" : i == 826283522 ? "VEHICLE_DOOR_FID_LR_DOOR" : i == 826283523 ? "VEHICLE_DOOR_FID_RR_DOOR" : i == 826283524 ? "VEHICLE_DOOR_FID_ENGINE_DOOR" : i == 826283525 ? "VEHICLE_DOOR_FID_BACK_DOOR" : i == 826283526 ? "VEHICLE_WINDOW_FID_LF_WINDOW" : i == 826283527 ? "VEHICLE_WINDOW_FID_RF_WINDOW" : i == 826283528 ? "VEHICLE_WINDOW_FID_LR_WINDOW" : i == 826283529 ? "VEHICLE_WINDOW_FID_RR_WINDOW" : i == 826283530 ? "VEHICLE_WINDOW_FID_SUNROOF" : i == 826283531 ? "VEHICLE_SPEED_FID_INFO" : i == 826283532 ? "VEHICLE_POWER_FID_STATUS" : i == 826283533 ? "VEHICLE_FID_STEE_ANGLE" : i == 826283534 ? "VEHICLE_GEAR_FID_POSITION" : i == 826283535 ? "VEHICLE_WIRELESS_CHARGE_CTRL" : i == 826283536 ? "VEHICLE_WIRELESS_CHARGE_CHARGINGSTATUS" : i == 826283537 ? "VEHICLE_WIRELESS_CHARGE_PHONEREMINDER" : i == 826283538 ? "VEHICLE_WIRELESS_CHARGE_SHUTDOWNFEEDBACK" : i == 826283539 ? "VEHICLE_WIRELESS_CHARGE_SWMEMORYSTS" : i == 826283540 ? "VEHICLE_WIRELESS_CHARGE_FODWARMING" : i == 826283541 ? "VEHICLE_WIRELESS_CHARGINGERR" : i == 826349312 ? "IP_CURRENT_SRC" : i == 826283777 ? "IP_THEME" : i == 826283778 ? "IP_CURRENT_IMAGE" : i == 826283779 ? "IP_UPDATE_REQ" : i == 826283780 ? "IP_USB_LOAD" : i == 826283781 ? "IP_UPDATE_COPY" : i == 826283782 ? "IP_UPDATE_CHECK" : i == 826283783 ? "IP_KEY_STATUS" : i == 826283784 ? "IP_DISP_THEME_LINK" : i == 826283785 ? "IP_DISP_SYNC" : i == 826284032 ? "DVR3RD_RECORDLOCK" : i == 826284033 ? "DVR3RD_UPDATESTATUS" : i == 826284034 ? "DVR3RD_PLAYSTATE" : i == 826284035 ? "DVR3RD_SDCARDSTATUS" : i == 826284036 ? "DVR3RD_TIMEDISMODE" : i == 826349573 ? "DVR3RD_TIMEINFO" : i == 826284038 ? "DVR3RD_VIDEOPLAYSTATUS" : i == 826284039 ? "DVR3RD_MODESTATUSREQ" : i == 826284040 ? "DVR3RD_HARDKEYCMD" : i == 826284041 ? "DVR3RD_RESOLUTION" : i == 826284042 ? "DVR3RD_TIMEDISFORMAT" : i == 826284043 ? "DVR3RD_RECIRCLERECORDTIME" : i == 826284044 ? "DVR3RD_RECORDVOICE" : i == 826284045 ? "DVR3RD_LANGUAGE" : i == 826284046 ? "DVR3RD_GRAVITYSENSOR" : i == 826284047 ? "DVR3RD_LIGHTFREQUENCY" : i == 826284048 ? "DVR3RD_DATEFORMAT" : i == 826349585 ? "DVR3RD_SYSTEMDATE" : i == 826349586 ? "DVR3RD_SYSTEMTIME" : i == 826349587 ? "DVR3RD_VERSIONINFO" : i == 826284052 ? "DVR3RD_FORMATINFO" : i == 826284053 ? "DVR3RD_PARMSYSRESET" : i == 826349590 ? "DVR3RD_DATETIME" : i == 826349591 ? "DVR3RD_ALLPARMSSTATE" : i == 826349592 ? "DVR3RD_FILE_NAME" : i == 826284057 ? "DVR3RD_FILELISTINFO" : i == 826349594 ? "DVR3RD_PLAYBYNAME" : i == 826284059 ? "DVR3RD_SHUTDOWN" : i == 826349596 ? "DVR3RD_CURRECORDTIME" : i == 826349597 ? "DVR3RD_DVR_CURPLAYTIME" : i == 826349598 ? "DVR3RD_TOTALTIME" : i == 826349824 ? "DISP_SETTING_FID_SCENE_MODE" : i == 826283542 ? "VEHICLE_WINDOW_FID_ALL_WINDOW" : i == 826283543 ? "VEHICLE_LIGH_FID_LOW_BEAM" : i == 826283544 ? "VEHICLE_LIGH_FID_HIGH_BEAM" : i == 826283545 ? "VEHICLE_LIGH_FID_FRONT_FOG_LAMP" : i == 826283546 ? "VEHICLE_LIGH_FID_REAR_FOG_LAMP" : i == 826283547 ? "VEHICLE_LIGH_FID_TURN_INDICATOR_LEFT" : i == 826283548 ? "VEHICLE_LIGH_FID_TURN_INDICATOR_RIGHT" : i == 826283549 ? "VEHICLE_WINDOW_FID_SHADE" : i == 826282004 ? "AUDIO_MUTE" : "0x" + Integer.toHexString(i);
    }

    public static final String dumpBitfield(int i) {
        ArrayList arrayList = new ArrayList();
        arrayList.add("INVALID");
        int i2 = 286261504;
        if ((i & 286261504) == 286261504) {
            arrayList.add("INFO_VIN");
        } else {
            i2 = 0;
        }
        if ((i & 286261505) == 286261505) {
            arrayList.add("INFO_MAKE");
            i2 |= 286261505;
        }
        if ((i & 286261506) == 286261506) {
            arrayList.add("INFO_MODEL");
            i2 |= 286261506;
        }
        if ((i & 289407235) == 289407235) {
            arrayList.add("INFO_MODEL_YEAR");
            i2 |= 289407235;
        }
        if ((i & 291504388) == 291504388) {
            arrayList.add("INFO_FUEL_CAPACITY");
            i2 |= 291504388;
        }
        if ((i & 289472773) == 289472773) {
            arrayList.add("INFO_FUEL_TYPE");
            i2 |= 289472773;
        }
        if ((i & 291504390) == 291504390) {
            arrayList.add("INFO_EV_BATTERY_CAPACITY");
            i2 |= 291504390;
        }
        if ((i & 289472775) == 289472775) {
            arrayList.add("INFO_EV_CONNECTOR_TYPE");
            i2 |= 289472775;
        }
        if ((i & 289407240) == 289407240) {
            arrayList.add("INFO_FUEL_DOOR_LOCATION");
            i2 |= 289407240;
        }
        if ((i & 289407241) == 289407241) {
            arrayList.add("INFO_EV_PORT_LOCATION");
            i2 |= 289407241;
        }
        if ((i & 356516106) == 356516106) {
            arrayList.add("INFO_DRIVER_SEAT");
            i2 |= 356516106;
        }
        if ((i & 291504644) == 291504644) {
            arrayList.add("PERF_ODOMETER");
            i2 |= 291504644;
        }
        if ((i & 291504647) == 291504647) {
            arrayList.add("PERF_VEHICLE_SPEED");
            i2 |= 291504647;
        }
        if ((i & 291504897) == 291504897) {
            arrayList.add("ENGINE_COOLANT_TEMP");
            i2 |= 291504897;
        }
        if ((i & 289407747) == 289407747) {
            arrayList.add("ENGINE_OIL_LEVEL");
            i2 |= 289407747;
        }
        if ((i & 291504900) == 291504900) {
            arrayList.add("ENGINE_OIL_TEMP");
            i2 |= 291504900;
        }
        if ((i & 291504901) == 291504901) {
            arrayList.add("ENGINE_RPM");
            i2 |= 291504901;
        }
        if ((i & 290521862) == 290521862) {
            arrayList.add("WHEEL_TICK");
            i2 |= 290521862;
        }
        if ((i & 291504903) == 291504903) {
            arrayList.add("FUEL_LEVEL");
            i2 |= 291504903;
        }
        if ((i & FUEL_DOOR_OPEN) == 287310840) {
            arrayList.add("FUEL_DOOR_OPEN");
            i2 |= FUEL_DOOR_OPEN;
        }
        if ((i & 291504905) == 291504905) {
            arrayList.add("EV_BATTERY_LEVEL");
            i2 |= 291504905;
        }
        if ((i & 287310602) == 287310602) {
            arrayList.add("EV_CHARGE_PORT_OPEN");
            i2 |= 287310602;
        }
        if ((287310603 & i) == 287310603) {
            arrayList.add("EV_CHARGE_PORT_CONNECTED");
            i2 |= 287310603;
        }
        if ((291504908 & i) == 291504908) {
            arrayList.add("EV_BATTERY_INSTANTANEOUS_CHARGE_RATE");
            i2 |= 291504908;
        }
        if ((291504904 & i) == 291504904) {
            arrayList.add("RANGE_REMAINING");
            i2 |= 291504904;
        }
        if ((291570441 & i) == 291570441) {
            arrayList.add("TIRE_PRESSURE");
            i2 |= TIRE_PRESSURE;
        }
        if ((289408000 & i) == 289408000) {
            arrayList.add("GEAR_SELECTION");
            i2 |= 289408000;
        }
        if ((289408001 & i) == 289408001) {
            arrayList.add("CURRENT_GEAR");
            i2 |= 289408001;
        }
        if ((287310850 & i) == 287310850) {
            arrayList.add("PARKING_BRAKE_ON");
            i2 |= 287310850;
        }
        if ((287310851 & i) == 287310851) {
            arrayList.add("PARKING_BRAKE_AUTO_APPLY");
            i2 |= 287310851;
        }
        if ((287310853 & i) == 287310853) {
            arrayList.add("FUEL_LEVEL_LOW");
            i2 |= 287310853;
        }
        if ((287310855 & i) == 287310855) {
            arrayList.add("NIGHT_MODE");
            i2 |= 287310855;
        }
        if ((289408008 & i) == 289408008) {
            arrayList.add("TURN_SIGNAL_STATE");
            i2 |= 289408008;
        }
        if ((289408009 & i) == 289408009) {
            arrayList.add("IGNITION_STATE");
            i2 |= 289408009;
        }
        if ((287310858 & i) == 287310858) {
            arrayList.add("ABS_ACTIVE");
            i2 |= 287310858;
        }
        if ((287310859 & i) == 287310859) {
            arrayList.add("TRACTION_CONTROL_ACTIVE");
            i2 |= 287310859;
        }
        if ((356517120 & i) == 356517120) {
            arrayList.add("HVAC_FAN_SPEED");
            i2 |= 356517120;
        }
        if ((356517121 & i) == 356517121) {
            arrayList.add("HVAC_FAN_DIRECTION");
            i2 |= 356517121;
        }
        if ((358614274 & i) == 358614274) {
            arrayList.add("HVAC_TEMPERATURE_CURRENT");
            i2 |= 358614274;
        }
        if ((358614275 & i) == 358614275) {
            arrayList.add("HVAC_TEMPERATURE_SET");
            i2 |= 358614275;
        }
        if ((322962692 & i) == 322962692) {
            arrayList.add("HVAC_DEFROSTER");
            i2 |= HVAC_DEFROSTER;
        }
        if ((356517125 & i) == 356517125) {
            arrayList.add("HVAC_AC_ON");
            i2 |= HVAC_AC_ON;
        }
        if ((356517126 & i) == 356517126) {
            arrayList.add("HVAC_MAX_AC_ON");
            i2 |= HVAC_MAX_AC_ON;
        }
        if ((354419975 & i) == 354419975) {
            arrayList.add("HVAC_MAX_DEFROST_ON");
            i2 |= 354419975;
        }
        if ((356517128 & i) == 356517128) {
            arrayList.add("HVAC_RECIRC_ON");
            i2 |= HVAC_RECIRC_ON;
        }
        if ((356517129 & i) == 356517129) {
            arrayList.add("HVAC_DUAL_ON");
            i2 |= HVAC_DUAL_ON;
        }
        if ((356517130 & i) == 356517130) {
            arrayList.add("HVAC_AUTO_ON");
            i2 |= HVAC_AUTO_ON;
        }
        if ((356517131 & i) == 356517131) {
            arrayList.add("HVAC_SEAT_TEMPERATURE");
            i2 |= 356517131;
        }
        if ((339739916 & i) == 339739916) {
            arrayList.add("HVAC_SIDE_MIRROR_HEAT");
            i2 |= 339739916;
        }
        if ((289408269 & i) == 289408269) {
            arrayList.add("HVAC_STEERING_WHEEL_HEAT");
            i2 |= 289408269;
        }
        if ((289408270 & i) == 289408270) {
            arrayList.add("HVAC_TEMPERATURE_DISPLAY_UNITS");
            i2 |= 289408270;
        }
        if ((356517135 & i) == 356517135) {
            arrayList.add("HVAC_ACTUAL_FAN_SPEED_RPM");
            i2 |= 356517135;
        }
        if ((356517136 & i) == 356517136) {
            arrayList.add("HVAC_POWER_ON");
            i2 |= HVAC_POWER_ON;
        }
        if ((356582673 & i) == 356582673) {
            arrayList.add("HVAC_FAN_DIRECTION_AVAILABLE");
            i2 |= 356582673;
        }
        if ((354419986 & i) == 354419986) {
            arrayList.add("HVAC_AUTO_RECIRC_ON");
            i2 |= 354419986;
        }
        if ((356517139 & i) == 356517139) {
            arrayList.add("HVAC_SEAT_VENTILATION");
            i2 |= 356517139;
        }
        if ((356517140 & i) == 356517140) {
            arrayList.add("HVAC_FAN_SPEED_ACK");
            i2 |= HVAC_FAN_SPEED_ACK;
        }
        if ((291505923 & i) == 291505923) {
            arrayList.add("ENV_OUTSIDE_TEMPERATURE");
            i2 |= 291505923;
        }
        if ((289475072 & i) == 289475072) {
            arrayList.add("AP_POWER_STATE_REQ");
            i2 |= 289475072;
        }
        if ((289475073 & i) == 289475073) {
            arrayList.add("AP_POWER_STATE_REPORT");
            i2 |= 289475073;
        }
        if ((289409538 & i) == 289409538) {
            arrayList.add("AP_POWER_BOOTUP_REASON");
            i2 |= 289409538;
        }
        if ((289409539 & i) == 289409539) {
            arrayList.add("DISPLAY_BRIGHTNESS");
            i2 |= 289409539;
        }
        if ((289475088 & i) == 289475088) {
            arrayList.add("HW_KEY_INPUT");
            i2 |= 289475088;
        }
        if ((373295872 & i) == 373295872) {
            arrayList.add("DOOR_POS");
            i2 |= 373295872;
        }
        if ((373295873 & i) == 373295873) {
            arrayList.add("DOOR_MOVE");
            i2 |= 373295873;
        }
        if ((371198722 & i) == 371198722) {
            arrayList.add("DOOR_LOCK");
            i2 |= 371198722;
        }
        if ((339741504 & i) == 339741504) {
            arrayList.add("MIRROR_Z_POS");
            i2 |= 339741504;
        }
        if ((339741505 & i) == 339741505) {
            arrayList.add("MIRROR_Z_MOVE");
            i2 |= 339741505;
        }
        if ((339741506 & i) == 339741506) {
            arrayList.add("MIRROR_Y_POS");
            i2 |= 339741506;
        }
        if ((339741507 & i) == 339741507) {
            arrayList.add("MIRROR_Y_MOVE");
            i2 |= 339741507;
        }
        if ((287312708 & i) == 287312708) {
            arrayList.add("MIRROR_LOCK");
            i2 |= 287312708;
        }
        if ((289409861 & i) == 289409861) {
            arrayList.add("MIRROR_FOLD");
            i2 |= MIRROR_FOLD;
        }
        if ((356518784 & i) == 356518784) {
            arrayList.add("SEAT_MEMORY_SELECT");
            i2 |= 356518784;
        }
        if ((356518785 & i) == 356518785) {
            arrayList.add("SEAT_MEMORY_SET");
            i2 |= 356518785;
        }
        if ((354421634 & i) == 354421634) {
            arrayList.add("SEAT_BELT_BUCKLED");
            i2 |= 354421634;
        }
        if ((356518787 & i) == 356518787) {
            arrayList.add("SEAT_BELT_HEIGHT_POS");
            i2 |= 356518787;
        }
        if ((356518788 & i) == 356518788) {
            arrayList.add("SEAT_BELT_HEIGHT_MOVE");
            i2 |= 356518788;
        }
        if ((356518789 & i) == 356518789) {
            arrayList.add("SEAT_FORE_AFT_POS");
            i2 |= 356518789;
        }
        if ((356518790 & i) == 356518790) {
            arrayList.add("SEAT_FORE_AFT_MOVE");
            i2 |= 356518790;
        }
        if ((356518791 & i) == 356518791) {
            arrayList.add("SEAT_BACKREST_ANGLE_1_POS");
            i2 |= 356518791;
        }
        if ((356518792 & i) == 356518792) {
            arrayList.add("SEAT_BACKREST_ANGLE_1_MOVE");
            i2 |= 356518792;
        }
        if ((356518793 & i) == 356518793) {
            arrayList.add("SEAT_BACKREST_ANGLE_2_POS");
            i2 |= 356518793;
        }
        if ((356518794 & i) == 356518794) {
            arrayList.add("SEAT_BACKREST_ANGLE_2_MOVE");
            i2 |= 356518794;
        }
        if ((356518795 & i) == 356518795) {
            arrayList.add("SEAT_HEIGHT_POS");
            i2 |= 356518795;
        }
        if ((356518796 & i) == 356518796) {
            arrayList.add("SEAT_HEIGHT_MOVE");
            i2 |= 356518796;
        }
        if ((356518797 & i) == 356518797) {
            arrayList.add("SEAT_DEPTH_POS");
            i2 |= 356518797;
        }
        if ((356518798 & i) == 356518798) {
            arrayList.add("SEAT_DEPTH_MOVE");
            i2 |= 356518798;
        }
        if ((356518799 & i) == 356518799) {
            arrayList.add("SEAT_TILT_POS");
            i2 |= 356518799;
        }
        if ((356518800 & i) == 356518800) {
            arrayList.add("SEAT_TILT_MOVE");
            i2 |= 356518800;
        }
        if ((356518801 & i) == 356518801) {
            arrayList.add("SEAT_LUMBAR_FORE_AFT_POS");
            i2 |= 356518801;
        }
        if ((356518802 & i) == 356518802) {
            arrayList.add("SEAT_LUMBAR_FORE_AFT_MOVE");
            i2 |= 356518802;
        }
        if ((356518803 & i) == 356518803) {
            arrayList.add("SEAT_LUMBAR_SIDE_SUPPORT_POS");
            i2 |= 356518803;
        }
        if ((356518804 & i) == 356518804) {
            arrayList.add("SEAT_LUMBAR_SIDE_SUPPORT_MOVE");
            i2 |= 356518804;
        }
        if ((289409941 & i) == 289409941) {
            arrayList.add("SEAT_HEADREST_HEIGHT_POS");
            i2 |= 289409941;
        }
        if ((356518806 & i) == 356518806) {
            arrayList.add("SEAT_HEADREST_HEIGHT_MOVE");
            i2 |= 356518806;
        }
        if ((356518807 & i) == 356518807) {
            arrayList.add("SEAT_HEADREST_ANGLE_POS");
            i2 |= 356518807;
        }
        if ((356518808 & i) == 356518808) {
            arrayList.add("SEAT_HEADREST_ANGLE_MOVE");
            i2 |= 356518808;
        }
        if ((356518809 & i) == 356518809) {
            arrayList.add("SEAT_HEADREST_FORE_AFT_POS");
            i2 |= 356518809;
        }
        if ((356518810 & i) == 356518810) {
            arrayList.add("SEAT_HEADREST_FORE_AFT_MOVE");
            i2 |= 356518810;
        }
        if ((322964416 & i) == 322964416) {
            arrayList.add("WINDOW_POS");
            i2 |= 322964416;
        }
        if ((322964417 & i) == 322964417) {
            arrayList.add("WINDOW_MOVE");
            i2 |= 322964417;
        }
        if ((320867268 & i) == 320867268) {
            arrayList.add("WINDOW_LOCK");
            i2 |= 320867268;
        }
        if ((299895808 & i) == 299895808) {
            arrayList.add("VEHICLE_MAP_SERVICE");
            i2 |= 299895808;
        }
        if ((299896064 & i) == 299896064) {
            arrayList.add("OBD2_LIVE_FRAME");
            i2 |= 299896064;
        }
        if ((299896065 & i) == 299896065) {
            arrayList.add("OBD2_FREEZE_FRAME");
            i2 |= 299896065;
        }
        if ((299896066 & i) == 299896066) {
            arrayList.add("OBD2_FREEZE_FRAME_INFO");
            i2 |= 299896066;
        }
        if ((299896067 & i) == 299896067) {
            arrayList.add("OBD2_FREEZE_FRAME_CLEAR");
            i2 |= 299896067;
        }
        if ((289410560 & i) == 289410560) {
            arrayList.add("HEADLIGHTS_STATE");
            i2 |= 289410560;
        }
        if ((289410561 & i) == 289410561) {
            arrayList.add("HIGH_BEAM_LIGHTS_STATE");
            i2 |= 289410561;
        }
        if ((322964994 & i) == 322964994) {
            arrayList.add("FOG_LIGHTS_STATE");
            i2 |= FOG_LIGHTS_STATE;
        }
        if ((289410563 & i) == 289410563) {
            arrayList.add("HAZARD_LIGHTS_STATE");
            i2 |= 289410563;
        }
        if ((289410576 & i) == 289410576) {
            arrayList.add("HEADLIGHTS_SWITCH");
            i2 |= 289410576;
        }
        if ((289410577 & i) == 289410577) {
            arrayList.add("HIGH_BEAM_LIGHTS_SWITCH");
            i2 |= 289410577;
        }
        if ((289410578 & i) == 289410578) {
            arrayList.add("FOG_LIGHTS_SWITCH");
            i2 |= 289410578;
        }
        if ((289410579 & i) == 289410579) {
            arrayList.add("HAZARD_LIGHTS_SWITCH");
            i2 |= 289410579;
        }
        if ((554696752 & i) == 554696752) {
            arrayList.add("AP_PHONE_CONTACT_INFO");
            i2 |= AP_PHONE_CONTACT_INFO;
        }
        if ((554696753 & i) == 554696753) {
            arrayList.add("AP_PHONE_NUMBER");
            i2 |= AP_PHONE_NUMBER;
        }
        if ((557842482 & i) == 557842482) {
            arrayList.add("AP_PHONE_STATE");
            i2 |= AP_PHONE_STATE;
        }
        if ((554696800 & i) == 554696800) {
            arrayList.add("AP_MEDIA_ALBUM_INFO");
            i2 |= AP_MEDIA_ALBUM_INFO;
        }
        if ((557842529 & i) == 557842529) {
            arrayList.add("AP_MEDIA_PLAYING_CURRENT_TIME");
            i2 |= AP_MEDIA_PLAYING_CURRENT_TIME;
        }
        if ((554696803 & i) == 554696803) {
            arrayList.add("AP_MEDIA_PLAYING_SOURCE_INFO");
            i2 |= AP_MEDIA_PLAYING_SOURCE_INFO;
        }
        if ((554696804 & i) == 554696804) {
            arrayList.add("AP_MEDIA_PLAYING_SOURCE_MESSAGE");
            i2 |= AP_MEDIA_PLAYING_SOURCE_MESSAGE;
        }
        if ((557842533 & i) == 557842533) {
            arrayList.add("AP_MEDIA_PLAYING_STATE");
            i2 |= AP_MEDIA_PLAYING_STATE;
        }
        if ((557842534 & i) == 557842534) {
            arrayList.add("AP_MEDIA_PLAYING_TOTAL_TIME");
            i2 |= AP_MEDIA_PLAYING_TOTAL_TIME;
        }
        if ((554696807 & i) == 554696807) {
            arrayList.add("AP_MEDIA_SINGER_NAME");
            i2 |= AP_MEDIA_SINGER_NAME;
        }
        if ((557842576 & i) == 557842576) {
            arrayList.add("AP_NAVI_BYROAD_SIGNAL");
            i2 |= AP_NAVI_BYROAD_SIGNAL;
        }
        if ((554696849 & i) == 554696849) {
            arrayList.add("AP_NAVI_COUNTRY_CODE_VALUE");
            i2 |= AP_NAVI_COUNTRY_CODE_VALUE;
        }
        if ((557842578 & i) == 557842578) {
            arrayList.add("AP_NAVI_CURRENT_ROAD_TYPE");
            i2 |= AP_NAVI_CURRENT_ROAD_TYPE;
        }
        if ((557842579 & i) == 557842579) {
            arrayList.add("AP_NAVI_CURVE_DISTANCE");
            i2 |= AP_NAVI_CURVE_DISTANCE;
        }
        if ((557842580 & i) == 557842580) {
            arrayList.add("AP_NAVI_ENTER_CURVE_INFO");
            i2 |= AP_NAVI_ENTER_CURVE_INFO;
        }
        if ((557842581 & i) == 557842581) {
            arrayList.add("AP_NAVI_ENTER_RAMP_INFO");
            i2 |= AP_NAVI_ENTER_RAMP_INFO;
        }
        if ((557842582 & i) == 557842582) {
            arrayList.add("AP_NAVI_ENTER_TUNNEL_INFO");
            i2 |= AP_NAVI_ENTER_TUNNEL_INFO;
        }
        if ((557842583 & i) == 557842583) {
            arrayList.add("AP_NAVI_GUIGANCE_ON");
            i2 |= AP_NAVI_GUIGANCE_ON;
        }
        if ((557842584 & i) == 557842584) {
            arrayList.add("IP_NAVI_DISTANCE");
            i2 |= IP_NAVI_DISTANCE;
        }
        if ((557842585 & i) == 557842585) {
            arrayList.add("AP_NAVI_NUMBER");
            i2 |= AP_NAVI_NUMBER;
        }
        if ((557842586 & i) == 557842586) {
            arrayList.add("AP_NAVI_NUMBER_V2");
            i2 |= AP_NAVI_NUMBER_V2;
        }
        if ((557842587 & i) == 557842587) {
            arrayList.add("AP_NAVI_RAMP_SLOPE_FAR_VALUE");
            i2 |= AP_NAVI_RAMP_SLOPE_FAR_VALUE;
        }
        if ((557842588 & i) == 557842588) {
            arrayList.add("AP_NAVI_RAMP_SLOPE_INFO");
            i2 |= AP_NAVI_RAMP_SLOPE_INFO;
        }
        if ((557842589 & i) == 557842589) {
            arrayList.add("AP_NAVI_RAMP2_DISTANCE");
            i2 |= AP_NAVI_RAMP2_DISTANCE;
        }
        if ((557842590 & i) == 557842590) {
            arrayList.add("AP_NAVI_ROAD_CURVATURE");
            i2 |= AP_NAVI_ROAD_CURVATURE;
        }
        if ((557842591 & i) == 557842591) {
            arrayList.add("AP_NAVI_ROAD_CURVATURE_FAR_VALUE");
            i2 |= AP_NAVI_ROAD_CURVATURE_FAR_VALUE;
        }
        if ((557842592 & i) == 557842592) {
            arrayList.add("AP_NAVI_SPEED_LIMIT_DISTANCE");
            i2 |= AP_NAVI_SPEED_LIMIT_DISTANCE;
        }
        if ((557842593 & i) == 557842593) {
            arrayList.add("AP_NAVI_SPEED_LIMIT_TYPE");
            i2 |= AP_NAVI_SPEED_LIMIT_TYPE;
        }
        if ((557842594 & i) == 557842594) {
            arrayList.add("AP_NAVI_SPEED_LIMIT_UNITS");
            i2 |= AP_NAVI_SPEED_LIMIT_UNITS;
        }
        if ((557842595 & i) == 557842595) {
            arrayList.add("AP_NAVI_SPEED_LIMIT_VALUE");
            i2 |= AP_NAVI_SPEED_LIMIT_VALUE;
        }
        if ((557842596 & i) == 557842596) {
            arrayList.add("AP_NAVI_SPEEDLIMIT_SIGN_ON");
            i2 |= AP_NAVI_SPEEDLIMIT_SIGN_ON;
        }
        if ((557842597 & i) == 557842597) {
            arrayList.add("AP_NAVI_STATE");
            i2 |= AP_NAVI_STATE;
        }
        if ((557842598 & i) == 557842598) {
            arrayList.add("AP_NAVI_TUNNEL_DISTANCE");
            i2 |= AP_NAVI_TUNNEL_DISTANCE;
        }
        if ((557842599 & i) == 557842599) {
            arrayList.add("AP_NAVI_TUNNEL_LENGTH");
            i2 |= AP_NAVI_TUNNEL_LENGTH;
        }
        if ((557842624 & i) == 557842624) {
            arrayList.add("AP_DRIVE_MODE_SET_STATUS");
            i2 |= AP_DRIVE_MODE_SET_STATUS;
        }
        if ((555745473 & i) == 555745473) {
            arrayList.add("AP_DISPLAY_LEAVE_CAR_ANIMATION_STATE");
            i2 |= AP_DISPLAY_LEAVE_CAR_ANIMATION_STATE;
        }
        if ((557842626 & i) == 557842626) {
            arrayList.add("AP_NAVI_RAMP_INFO");
            i2 |= AP_NAVI_RAMP_INFO;
        }
        if ((557908768 & i) == 557908768) {
            arrayList.add("WARNING_VEH_INDICATE_INFO");
            i2 |= WARNING_VEH_INDICATE_INFO;
        }
        if ((557909568 & i) == 557909568) {
            arrayList.add("FAULT_SYSTEM_CODE_INFO");
            i2 |= FAULT_SYSTEM_CODE_INFO;
        }
        if ((557844033 & i) == 557844033) {
            arrayList.add("FAULT_BCM_CODE");
            i2 |= FAULT_BCM_CODE;
        }
        if ((557844034 & i) == 557844034) {
            arrayList.add("FAULT_HVAC_CODE");
            i2 |= FAULT_HVAC_CODE;
        }
        if ((557844832 & i) == 557844832) {
            arrayList.add("BODY_DOOR_AUDIOWARNING_ON");
            i2 |= BODY_DOOR_AUDIOWARNING_ON;
        }
        if ((557844833 & i) == 557844833) {
            arrayList.add("BODY_DOOR_AUTO_IGNOFF_UNLOCK_ON");
            i2 |= BODY_DOOR_AUTO_IGNOFF_UNLOCK_ON;
        }
        if ((557844834 & i) == 557844834) {
            arrayList.add("BODY_DOOR_AUTO_SPEEKLOCK_ON");
            i2 |= BODY_DOOR_AUTO_SPEEKLOCK_ON;
        }
        if ((557844835 & i) == 557844835) {
            arrayList.add("BODY_DOOR_LEAVE_AUTO_LOCK_STATE");
            i2 |= BODY_DOOR_LEAVE_AUTO_LOCK_STATE;
        }
        if ((557844836 & i) == 557844836) {
            arrayList.add("BODY_DOOR_LOCK_STATE");
            i2 |= BODY_DOOR_LOCK_STATE;
        }
        if ((557844837 & i) == 557844837) {
            arrayList.add("BODY_DOOR_OPEN_LOCK_PROTECT_ON");
            i2 |= BODY_DOOR_OPEN_LOCK_PROTECT_ON;
        }
        if ((557844838 & i) == 557844838) {
            arrayList.add("BODY_DOOR_REMOTE_LOCK_REQ");
            i2 |= BODY_DOOR_REMOTE_LOCK_REQ;
        }
        if ((557844839 & i) == 557844839) {
            arrayList.add("BODY_DOOR_RKE_UNLOCKDOOR_MODE");
            i2 |= BODY_DOOR_RKE_UNLOCKDOOR_MODE;
        }
        if ((557844840 & i) == 557844840) {
            arrayList.add("BODY_DOOR_SMART_TRUNKULOCK_ON");
            i2 |= BODY_DOOR_SMART_TRUNKULOCK_ON;
        }
        if ((557844841 & i) == 557844841) {
            arrayList.add("BODY_DOOR_TWICE_LOCK_ON");
            i2 |= BODY_DOOR_TWICE_LOCK_ON;
        }
        if ((557844842 & i) == 557844842) {
            arrayList.add("BODY_DOOR_UNLOCK_STATE");
            i2 |= BODY_DOOR_UNLOCK_STATE;
        }
        if ((557844843 & i) == 557844843) {
            arrayList.add("BODY_DOOR_WELCOME_UNLOCK_ON");
            i2 |= BODY_DOOR_WELCOME_UNLOCK_ON;
        }
        if ((557844844 & i) == 557844844) {
            arrayList.add("BODY_DOOR_FCW_OPEN_ON");
            i2 |= BODY_DOOR_FCW_OPEN_ON;
        }
        if ((557844845 & i) == 557844845) {
            arrayList.add("BODY_DOOR_TRUNK_DOOR_STATE");
            i2 |= BODY_DOOR_TRUNK_DOOR_STATE;
        }
        if ((557844880 & i) == 557844880) {
            arrayList.add("BODY_WINDOW_LOCK_AUTO_UP_ON");
            i2 |= BODY_WINDOW_LOCK_AUTO_UP_ON;
        }
        if ((557844881 & i) == 557844881) {
            arrayList.add("BODY_WINDOW_REAR_WIPER_ON");
            i2 |= BODY_WINDOW_REAR_WIPER_ON;
        }
        if ((557844882 & i) == 557844882) {
            arrayList.add("BODY_WINDOW_SUNROOF_CONTROL_STATE");
            i2 |= BODY_WINDOW_SUNROOF_CONTROL_STATE;
        }
        if ((557844883 & i) == 557844883) {
            arrayList.add("BODY_WINDOW_SUNROOFRAIN_DETECTClOSE_ON");
            i2 |= BODY_WINDOW_SUNROOFRAIN_DETECTClOSE_ON;
        }
        if ((557844884 & i) == 557844884) {
            arrayList.add("BODY_WINDOW_SUNSHADE_STATE");
            i2 |= BODY_WINDOW_SUNSHADE_STATE;
        }
        if ((591399317 & i) == 591399317) {
            arrayList.add("VENDOR_WINDOW_STATE");
            i2 |= VENDOR_WINDOW_STATE;
        }
        if ((591399319 & i) == 591399319) {
            arrayList.add("BODY_WINDOW_WIPER");
            i2 |= BODY_WINDOW_WIPER;
        }
        if ((557844888 & i) == 557844888) {
            arrayList.add("BODY_WINDOW_WASH");
            i2 |= BODY_WINDOW_WASH;
        }
        if ((557844889 & i) == 557844889) {
            arrayList.add("BODY_WINDOW_WIPER_INTERVAL");
            i2 |= BODY_WINDOW_WIPER_INTERVAL;
        }
        if ((557844891 & i) == 557844891) {
            arrayList.add("BODY_WINDOW_WIPER_SENSOR");
            i2 |= BODY_WINDOW_WIPER_SENSOR;
        }
        if ((557844892 & i) == 557844892) {
            arrayList.add("BODY_WINDOW_SUNROOF_MOTOR_STATE");
            i2 |= BODY_WINDOW_SUNROOF_MOTOR_STATE;
        }
        if ((557844893 & i) == 557844893) {
            arrayList.add("BODY_WINDOW_SUNROOF_STATE");
            i2 |= BODY_WINDOW_SUNROOF_STATE;
        }
        if ((557844928 & i) == 557844928) {
            arrayList.add("BODY_MIRROR_AUTOFOLD_ON");
            i2 |= BODY_MIRROR_AUTOFOLD_ON;
        }
        if ((557844929 & i) == 557844929) {
            arrayList.add("BODY_MIRROR_REAR_BACK_ASSISTANCE_ON");
            i2 |= BODY_MIRROR_REAR_BACK_ASSISTANCE_ON;
        }
        if ((557844976 & i) == 557844976) {
            arrayList.add("BODY_SEAT_DSM_MEMORY_REQ");
            i2 |= BODY_SEAT_DSM_MEMORY_REQ;
        }
        if ((557844977 & i) == 557844977) {
            arrayList.add("BODY_SEAT_EASY_ENTRY_ON");
            i2 |= BODY_SEAT_EASY_ENTRY_ON;
        }
        if ((557844978 & i) == 557844978) {
            arrayList.add("BODY_SEAT_REAR_BELT_WARNING_ON");
            i2 |= BODY_SEAT_REAR_BELT_WARNING_ON;
        }
        if ((557845024 & i) == 557845024) {
            arrayList.add("BODY_LIGHT_ATMO_ASSOCIATE_DRIVING_STATE");
            i2 |= BODY_LIGHT_ATMO_ASSOCIATE_DRIVING_STATE;
        }
        if ((557845025 & i) == 557845025) {
            arrayList.add("BODY_LIGHT_ATMO_AUTO_ADJUST_ON");
            i2 |= BODY_LIGHT_ATMO_AUTO_ADJUST_ON;
        }
        if ((557845026 & i) == 557845026) {
            arrayList.add("BODY_LIGHT_ATMO_BRIGHT_LEVEL");
            i2 |= BODY_LIGHT_ATMO_BRIGHT_LEVEL;
        }
        if ((557845027 & i) == 557845027) {
            arrayList.add("BODY_LIGHT_ATMO_COLOR_STATE");
            i2 |= BODY_LIGHT_ATMO_COLOR_STATE;
        }
        if ((557845028 & i) == 557845028) {
            arrayList.add("BODY_LIGHT_DAY_NIGHT_ON");
            i2 |= BODY_LIGHT_DAY_NIGHT_ON;
        }
        if ((557845029 & i) == 557845029) {
            arrayList.add("BODY_LIGHT_ATMO_ON");
            i2 |= BODY_LIGHT_ATMO_ON;
        }
        if ((557845030 & i) == 557845030) {
            arrayList.add("BODY_LIGHT_DIMMER_LEVEL");
            i2 |= BODY_LIGHT_DIMMER_LEVEL;
        }
        if ((557845031 & i) == 557845031) {
            arrayList.add("BODY_LIGHT_DOME_ON");
            i2 |= BODY_LIGHT_DOME_ON;
        }
        if ((557845032 & i) == 557845032) {
            arrayList.add("BODY_LIGHT_DOOR_ON");
            i2 |= BODY_LIGHT_DOOR_ON;
        }
        if ((557845033 & i) == 557845033) {
            arrayList.add("BODY_LIGHT_FOLLOWME_HOME_STATE");
            i2 |= BODY_LIGHT_FOLLOWME_HOME_STATE;
        }
        if ((557845034 & i) == 557845034) {
            arrayList.add("BODY_LIGHT_HEAD_AUTO_ON");
            i2 |= BODY_LIGHT_HEAD_AUTO_ON;
        }
        if ((557845035 & i) == 557845035) {
            arrayList.add("BODY_LIGHT_HEAD_HEIGHT_STATE");
            i2 |= BODY_LIGHT_HEAD_HEIGHT_STATE;
        }
        if ((557845036 & i) == 557845036) {
            arrayList.add("BODY_LIGHT_HMAENABLE_ON");
            i2 |= BODY_LIGHT_HMAENABLE_ON;
        }
        if ((557845037 & i) == 557845037) {
            arrayList.add("BODY_LIGHT_LANE_CHANGE_TURN_STATE");
            i2 |= BODY_LIGHT_LANE_CHANGE_TURN_STATE;
        }
        if ((557845038 & i) == 557845038) {
            arrayList.add("BODY_LIGHT_NODE_CTL");
            i2 |= BODY_LIGHT_NODE_CTL;
        }
        if ((557845039 & i) == 557845039) {
            arrayList.add("BODY_LIGHT_REALTIME_ON");
            i2 |= BODY_LIGHT_REALTIME_ON;
        }
        if ((557845040 & i) == 557845040) {
            arrayList.add("BODY_LIGHT_SMART_UNLOCK_ON");
            i2 |= BODY_LIGHT_SMART_UNLOCK_ON;
        }
        if ((557845041 & i) == 557845041) {
            arrayList.add("BODY_LIGHT_SMART_WELCOMELIGHT_ON");
            i2 |= BODY_LIGHT_SMART_WELCOMELIGHT_ON;
        }
        if ((557845042 & i) == 557845042) {
            arrayList.add("BODY_LIGHT_WELCOME_MODE");
            i2 |= BODY_LIGHT_WELCOME_MODE;
        }
        if ((557845043 & i) == 557845043) {
            arrayList.add("BODY_LIGHT_WIDTHLAMP_ON");
            i2 |= BODY_LIGHT_WIDTHLAMP_ON;
        }
        if ((557845044 & i) == 557845044) {
            arrayList.add("BODY_LIGHT_WIDTHLAMP_SW");
            i2 |= BODY_LIGHT_WIDTHLAMP_SW;
        }
        if ((557845045 & i) == 557845045) {
            arrayList.add("BODY_TURN_LEFT_SIGNAL_STATE");
            i2 |= BODY_TURN_LEFT_SIGNAL_STATE;
        }
        if ((557845046 & i) == 557845046) {
            arrayList.add("BODY_TURN_LEFT_SIGNAL_SWITCH");
            i2 |= BODY_TURN_LEFT_SIGNAL_SWITCH;
        }
        if ((557845047 & i) == 557845047) {
            arrayList.add("BODY_TURN_RIGHT_SIGNAL_STATE");
            i2 |= BODY_TURN_RIGHT_SIGNAL_STATE;
        }
        if ((557845048 & i) == 557845048) {
            arrayList.add("BODY_TURN_RIGHT_SIGNAL_SWITCH");
            i2 |= BODY_TURN_RIGHT_SIGNAL_SWITCH;
        }
        if ((557845049 & i) == 557845049) {
            arrayList.add("BODY_EMERGENCY_LIGHT");
            i2 |= BODY_EMERGENCY_LIGHT;
        }
        if ((624953914 & i) == 624953914) {
            arrayList.add("BODY_DOME_LIGHT");
            i2 |= BODY_DOME_LIGHT;
        }
        if ((557845051 & i) == 557845051) {
            arrayList.add("BODY_AUTO_HEAD_LIGHT");
            i2 |= BODY_AUTO_HEAD_LIGHT;
        }
        if ((557910608 & i) == 557910608) {
            arrayList.add("BODY_TIRE_PRESSURE_STATE");
            i2 |= BODY_TIRE_PRESSURE_STATE;
        }
        if ((557910609 & i) == 557910609) {
            arrayList.add("BODY_TIRE_TEMP_STATE");
            i2 |= BODY_TIRE_TEMP_STATE;
        }
        if ((557845120 & i) == 557845120) {
            arrayList.add("BODY_BATTERY_BCN_LEVEL");
            i2 |= BODY_BATTERY_BCN_LEVEL;
        }
        if ((557845121 & i) == 557845121) {
            arrayList.add("BODY_BATTERY_REMAINING_INFO");
            i2 |= BODY_BATTERY_REMAINING_INFO;
        }
        if ((557845122 & i) == 557845122) {
            arrayList.add("BODY_BATTERY_U_BATT_INFO");
            i2 |= BODY_BATTERY_U_BATT_INFO;
        }
        if ((557845123 & i) == 557845123) {
            arrayList.add("BODY_ENGINE_OIL_INFO");
            i2 |= BODY_ENGINE_OIL_INFO;
        }
        if ((557845216 & i) == 557845216) {
            arrayList.add("BODY_CONTROL_PANEL_VOLUME_ADJUST_DIRECTION");
            i2 |= BODY_CONTROL_PANEL_VOLUME_ADJUST_DIRECTION;
        }
        if ((557845217 & i) == 557845217) {
            arrayList.add("BODY_CONTROL_PANEL_VOLUMEL_ADJUST_LEVEL");
            i2 |= BODY_CONTROL_PANEL_VOLUMEL_ADJUST_LEVEL;
        }
        if ((557845218 & i) == 557845218) {
            arrayList.add("BODY_STEERING_WHEEL_CUSTOME_STATE");
            i2 |= BODY_STEERING_WHEEL_CUSTOME_STATE;
        }
        if ((557845220 & i) == 557845220) {
            arrayList.add("BODY_WIRELESS_CHARGING_ON");
            i2 |= BODY_WIRELESS_CHARGING_ON;
        }
        if ((557845221 & i) == 557845221) {
            arrayList.add("BODY_STEERING_WHEEL_STEERING_ANGLE");
            i2 |= BODY_STEERING_WHEEL_STEERING_ANGLE;
        }
        if ((557845632 & i) == 557845632) {
            arrayList.add("IP_DISPLAY_REQ");
            i2 |= IP_DISPLAY_REQ;
        }
        if ((557845633 & i) == 557845633) {
            arrayList.add("IP_DISPLAY_MODE_SET");
            i2 |= IP_DISPLAY_MODE_SET;
        }
        if ((557845634 & i) == 557845634) {
            arrayList.add("IP_DISPLAY_THEME_STATE");
            i2 |= IP_DISPLAY_THEME_STATE;
        }
        if ((557845635 & i) == 557845635) {
            arrayList.add("IP_NAVI_STATE");
            i2 |= IP_NAVI_STATE;
        }
        if ((557845636 & i) == 557845636) {
            arrayList.add("IP_REQ_SCREENSHOTS_REQ");
            i2 |= IP_REQ_SCREENSHOTS_REQ;
        }
        if ((555748485 & i) == 555748485) {
            arrayList.add("IP_REQ_SCREENSHOTS_RESULT");
            i2 |= IP_REQ_SCREENSHOTS_RESULT;
        }
        if ((555748486 & i) == 555748486) {
            arrayList.add("IP_REQ_SHOW_VERSION");
            i2 |= IP_REQ_SHOW_VERSION;
        }
        if ((554699911 & i) == 554699911) {
            arrayList.add("IP_VERSION_INFO");
            i2 |= IP_VERSION_INFO;
        }
        if ((557845640 & i) == 557845640) {
            arrayList.add("IP_VOLUME_LEVEL");
            i2 |= IP_VOLUME_LEVEL;
        }
        if ((557845641 & i) == 557845641) {
            arrayList.add("IP_VOLUME_TYPE");
            i2 |= IP_VOLUME_TYPE;
        }
        if ((557846432 & i) == 557846432) {
            arrayList.add("DRIVE_ENGINE_POWER_MODE");
            i2 |= DRIVE_ENGINE_POWER_MODE;
        }
        if ((557846433 & i) == 557846433) {
            arrayList.add("DRIVE_ENGINE_START_ON");
            i2 |= DRIVE_ENGINE_START_ON;
        }
        if ((557846434 & i) == 557846434) {
            arrayList.add("DRIVE_ENGINE_STOP_START_ON");
            i2 |= DRIVE_ENGINE_STOP_START_ON;
        }
        if ((557846435 & i) == 557846435) {
            arrayList.add("DRIVE_ENGINE_STATUS");
            i2 |= DRIVE_ENGINE_STATUS;
        }
        if ((555749328 & i) == 555749328) {
            arrayList.add("DRIVE_RADAR_CRASH_OUTPUT_STATE");
            i2 |= DRIVE_RADAR_CRASH_OUTPUT_STATE;
        }
        if ((557912017 & i) == 557912017) {
            arrayList.add("DRIVE_RADAR_OBSTACLE_DISTANCE_INFO");
            i2 |= DRIVE_RADAR_OBSTACLE_DISTANCE_INFO;
        }
        if ((557912018 & i) == 557912018) {
            arrayList.add("DRIVE_RADAR_SIDE_ZONE_STATE");
            i2 |= DRIVE_RADAR_SIDE_ZONE_STATE;
        }
        if ((557846483 & i) == 557846483) {
            arrayList.add("DRIVE_RADAR_RFC_OBSTACLE_DISTANCE");
            i2 |= DRIVE_RADAR_RFC_OBSTACLE_DISTANCE;
        }
        if ((557846528 & i) == 557846528) {
            arrayList.add("DRIVE_ESC_ON");
            i2 |= DRIVE_ESC_ON;
        }
        if ((557846529 & i) == 557846529) {
            arrayList.add("DRIVE_GEAR_SHIFT_POSTION_VALID_STATE");
            i2 |= DRIVE_GEAR_SHIFT_POSTION_VALID_STATE;
        }
        if ((557846530 & i) == 557846530) {
            arrayList.add("DRIVE_MODE_MEMORY_ON");
            i2 |= DRIVE_MODE_MEMORY_ON;
        }
        if ((557846531 & i) == 557846531) {
            arrayList.add("DRIVE_RADAR_FRONT_ACTIVE_ON");
            i2 |= DRIVE_RADAR_FRONT_ACTIVE_ON;
        }
        if ((557846532 & i) == 557846532) {
            arrayList.add("DRIVE_SWC_EPS_MODE");
            i2 |= DRIVE_SWC_EPS_MODE;
        }
        if ((557847232 & i) == 557847232) {
            arrayList.add("ADAS_APA_ACTIVE_ON");
            i2 |= ADAS_APA_ACTIVE_ON;
        }
        if ((557847233 & i) == 557847233) {
            arrayList.add("ADAS_APA_ASP_REMIND_ON");
            i2 |= ADAS_APA_ASP_REMIND_ON;
        }
        if ((557847234 & i) == 557847234) {
            arrayList.add("ADAS_APA_BSD_LCA_ON");
            i2 |= ADAS_APA_BSD_LCA_ON;
        }
        if ((557847235 & i) == 557847235) {
            arrayList.add("ADAS_APA_DYNAMIC_SLOT_NOTICE");
            i2 |= ADAS_APA_DYNAMIC_SLOT_NOTICE;
        }
        if ((557847236 & i) == 557847236) {
            arrayList.add("ADAS_APA_FUNCTION_ON_OFF_STATE");
            i2 |= ADAS_APA_FUNCTION_ON_OFF_STATE;
        }
        if ((557847237 & i) == 557847237) {
            arrayList.add("ADAS_APA_MODE");
            i2 |= ADAS_APA_MODE;
        }
        if ((557847238 & i) == 557847238) {
            arrayList.add("ADAS_APA_OBSTACLE_SALF_DISTANCE");
            i2 |= ADAS_APA_OBSTACLE_SALF_DISTANCE;
        }
        if ((557847239 & i) == 557847239) {
            arrayList.add("ADAS_APA_PARK_NOTICE_INFO");
            i2 |= ADAS_APA_PARK_NOTICE_INFO;
        }
        if ((557847240 & i) == 557847240) {
            arrayList.add("ADAS_APA_PARKING_CONFIRM_STATE");
            i2 |= ADAS_APA_PARKING_CONFIRM_STATE;
        }
        if ((557847241 & i) == 557847241) {
            arrayList.add("ADAS_APA_PARKING_PERCENTAGE_INFO");
            i2 |= ADAS_APA_PARKING_PERCENTAGE_INFO;
        }
        if ((557847242 & i) == 557847242) {
            arrayList.add("ADAS_APA_PARKING_RESUME_STATE");
            i2 |= ADAS_APA_PARKING_RESUME_STATE;
        }
        if ((557912779 & i) == 557912779) {
            arrayList.add("ADAS_APA_REBUILD_TARGET_DIST_INFO");
            i2 |= ADAS_APA_REBUILD_TARGET_DIST_INFO;
        }
        if ((557847244 & i) == 557847244) {
            arrayList.add("ADAS_APA_REMOTE_DISTANCE");
            i2 |= ADAS_APA_REMOTE_DISTANCE;
        }
        if ((557847245 & i) == 557847245) {
            arrayList.add("ADAS_APA_SLOT_DISTANCE");
            i2 |= ADAS_APA_SLOT_DISTANCE;
        }
        if ((557847246 & i) == 557847246) {
            arrayList.add("ADAS_APA_SLOT_NOTICE_INFO");
            i2 |= ADAS_APA_SLOT_NOTICE_INFO;
        }
        if ((557847247 & i) == 557847247) {
            arrayList.add("ADAS_APA_SLOT_TYPE");
            i2 |= ADAS_APA_SLOT_TYPE;
        }
        if ((557847248 & i) == 557847248) {
            arrayList.add("ADAS_APA_SLOT_USER_STATE");
            i2 |= ADAS_APA_SLOT_USER_STATE;
        }
        if ((557847280 & i) == 557847280) {
            arrayList.add("ADAS_HDC_CONTROL_ON");
            i2 |= ADAS_HDC_CONTROL_ON;
        }
        if ((555750129 & i) == 555750129) {
            arrayList.add("ADAS_HDC_STATE");
            i2 |= ADAS_HDC_STATE;
        }
        if ((557847327 & i) == 557847327) {
            arrayList.add("ADAS_ACC_AUTOBRAKE_PEOPLE_ON");
            i2 |= ADAS_ACC_AUTOBRAKE_PEOPLE_ON;
        }
        if ((557847328 & i) == 557847328) {
            arrayList.add("ADAS_ACC_AUTOBRAKE_ON");
            i2 |= ADAS_ACC_AUTOBRAKE_ON;
        }
        if ((557847329 & i) == 557847329) {
            arrayList.add("ADAS_ACC_CRUISE_MODE");
            i2 |= ADAS_ACC_CRUISE_MODE;
        }
        if ((557847330 & i) == 557847330) {
            arrayList.add("ADAS_ACC_FCW_WARN_STATE");
            i2 |= ADAS_ACC_FCW_WARN_STATE;
        }
        if ((557847331 & i) == 557847331) {
            arrayList.add("ADAS_ACC_ISA_ON");
            i2 |= ADAS_ACC_ISA_ON;
        }
        if ((557847332 & i) == 557847332) {
            arrayList.add("ADAS_ACC_OBJENABLE_ON");
            i2 |= ADAS_ACC_OBJENABLE_ON;
        }
        if ((557847333 & i) == 557847333) {
            arrayList.add("ADAS_ACC_PARALLEL_LINE_ASSIST_MODE");
            i2 |= ADAS_ACC_PARALLEL_LINE_ASSIST_MODE;
        }
        if ((557847334 & i) == 557847334) {
            arrayList.add("ADAS_ACC_SPEED_VALUE");
            i2 |= ADAS_ACC_SPEED_VALUE;
        }
        if ((557847335 & i) == 557847335) {
            arrayList.add("ADAS_ACC_WORK_MODE");
            i2 |= ADAS_ACC_WORK_MODE;
        }
        if ((557847336 & i) == 557847336) {
            arrayList.add("DRIVE_ACC_FCW_OVERSPEED_INFO");
            i2 |= DRIVE_ACC_FCW_OVERSPEED_INFO;
        }
        if ((557847337 & i) == 557847337) {
            arrayList.add("ADAS_ACC_FCW_OVERSPEED_ON");
            i2 |= ADAS_ACC_FCW_OVERSPEED_ON;
        }
        if ((557847338 & i) == 557847338) {
            arrayList.add("ADAS_ACC_FCW_OVERSPEED_SOUND_ON");
            i2 |= ADAS_ACC_FCW_OVERSPEED_SOUND_ON;
        }
        if ((557847339 & i) == 557847339) {
            arrayList.add("ADAS_ACC_FCW_REAR_COLLISION_ON");
            i2 |= ADAS_ACC_FCW_REAR_COLLISION_ON;
        }
        if ((557847340 & i) == 557847340) {
            arrayList.add("ADAS_ACC_LDW_LAS_MODE");
            i2 |= ADAS_ACC_LDW_LAS_MODE;
        }
        if ((557847341 & i) == 557847341) {
            arrayList.add("ADAS_ACC_PCW_FRONT_LOWSPEED_PEDESTRIAN_ON");
            i2 |= ADAS_ACC_PCW_FRONT_LOWSPEED_PEDESTRIAN_ON;
        }
        if ((557847342 & i) == 557847342) {
            arrayList.add("ADAS_ACC_ULS_FKP_ON");
            i2 |= ADAS_ACC_ULS_FKP_ON;
        }
        if ((557847343 & i) == 557847343) {
            arrayList.add("ADAS_ACC_CTA_ON");
            i2 |= ADAS_ACC_CTA_ON;
        }
        if ((557847424 & i) == 557847424) {
            arrayList.add("ADAS_LAS_LDW_SHAKELEV_STATE");
            i2 |= ADAS_LAS_LDW_SHAKELEV_STATE;
        }
        if ((557847425 & i) == 557847425) {
            arrayList.add("ADAS_LAS_WARNINGMODE_STATE");
            i2 |= ADAS_LAS_WARNINGMODE_STATE;
        }
        if ((557848012 & i) == 557848012) {
            arrayList.add("ADAS_CTA_LEFT_ALERT");
            i2 |= ADAS_CTA_LEFT_ALERT;
        }
        if ((557848013 & i) == 557848013) {
            arrayList.add("ADAS_CTA_RIGHT_ALERT");
            i2 |= ADAS_CTA_RIGHT_ALERT;
        }
        if ((557848014 & i) == 557848014) {
            arrayList.add("ADAS_LAS_MODE_SELECTION");
            i2 |= ADAS_LAS_MODE_SELECTION;
        }
        if ((557848015 & i) == 557848015) {
            arrayList.add("ADAS_LAS_LDW_STATUS");
            i2 |= ADAS_LAS_LDW_STATUS;
        }
        if ((557848016 & i) == 557848016) {
            arrayList.add("ADAS_LAS_LDW_SENSITIVITY");
            i2 |= ADAS_LAS_LDW_SENSITIVITY;
        }
        if ((557848017 & i) == 557848017) {
            arrayList.add("ADAS_LAS_CALIBRATION_STATUS");
            i2 |= ADAS_LAS_CALIBRATION_STATUS;
        }
        if ((557848018 & i) == 557848018) {
            arrayList.add("ADAS_LAS_LEFT_WARNING_STATUS");
            i2 |= ADAS_LAS_LEFT_WARNING_STATUS;
        }
        if ((557848019 & i) == 557848019) {
            arrayList.add("ADAS_LAS_RIGHT_WARNING_STATUS");
            i2 |= ADAS_LAS_RIGHT_WARNING_STATUS;
        }
        if ((557848020 & i) == 557848020) {
            arrayList.add("ADAS_LAS_HOST_LANE_LEFT_STATUS");
            i2 |= ADAS_LAS_HOST_LANE_LEFT_STATUS;
        }
        if ((557848021 & i) == 557848021) {
            arrayList.add("ADAS_LAS_HOST_LANE_RIGHT_STATUS");
            i2 |= ADAS_LAS_HOST_LANE_RIGHT_STATUS;
        }
        if ((557848022 & i) == 557848022) {
            arrayList.add("ADAS_LAS_LLANE_MARKER_TYPE");
            i2 |= ADAS_LAS_LLANE_MARKER_TYPE;
        }
        if ((557848023 & i) == 557848023) {
            arrayList.add("ADAS_LAS_RLANE_MARKER_TYPE");
            i2 |= ADAS_LAS_RLANE_MARKER_TYPE;
        }
        if ((557848024 & i) == 557848024) {
            arrayList.add("DRIVE_LAS_FATIGUE_MONITOR_ON");
            i2 |= DRIVE_LAS_FATIGUE_MONITOR_ON;
        }
        if ((557848025 & i) == 557848025) {
            arrayList.add("DRIVE_LAEB_ON");
            i2 |= DRIVE_LAEB_ON;
        }
        if ((557848027 & i) == 557848027) {
            arrayList.add("ADAS_KEY_VEH_RELEVANCE_STATE");
            i2 |= ADAS_KEY_VEH_RELEVANCE_STATE;
        }
        if ((557848028 & i) == 557848028) {
            arrayList.add("ADAS_KEY_VEH_NUMBER_INFO");
            i2 |= ADAS_KEY_VEH_NUMBER_INFO;
        }
        if ((557848029 & i) == 557848029) {
            arrayList.add("ADAS_BLIND_AREA_DETECTION_ON");
            i2 |= ADAS_BLIND_AREA_DETECTION_ON;
        }
        if ((557848030 & i) == 557848030) {
            arrayList.add("ADAS_KEY_DETECTED_STATE");
            i2 |= ADAS_KEY_DETECTED_STATE;
        }
        if ((557848031 & i) == 557848031) {
            arrayList.add("ADAS_IACC_LAS_ON");
            i2 |= ADAS_IACC_LAS_ON;
        }
        if ((557848032 & i) == 557848032) {
            arrayList.add("VENDOR_AUDIO_MODE_REQ");
            i2 |= VENDOR_AUDIO_MODE_REQ;
        }
        if ((557848033 & i) == 557848033) {
            arrayList.add("VENDOR_BCALL_REQ");
            i2 |= VENDOR_BCALL_REQ;
        }
        if ((557848034 & i) == 557848034) {
            arrayList.add("VENDOR_BCALL_STATE");
            i2 |= VENDOR_BCALL_STATE;
        }
        if ((557848035 & i) == 557848035) {
            arrayList.add("VENDOR_BLE_USER_ID_INFO");
            i2 |= VENDOR_BLE_USER_ID_INFO;
        }
        if ((557848036 & i) == 557848036) {
            arrayList.add("VENDOR_DRIVER_ID_INFO");
            i2 |= VENDOR_DRIVER_ID_INFO;
        }
        if ((557848037 & i) == 557848037) {
            arrayList.add("VENDOR_ECALL_STATE");
            i2 |= VENDOR_ECALL_STATE;
        }
        if ((557848038 & i) == 557848038) {
            arrayList.add("VENDOR_CAR_FACTORY_RESET");
            i2 |= VENDOR_CAR_FACTORY_RESET;
        }
        if ((557848039 & i) == 557848039) {
            arrayList.add("VENDOR_LIGHT_MUSIC_ON");
            i2 |= VENDOR_LIGHT_MUSIC_ON;
        }
        if ((557848066 & i) == 557848066) {
            arrayList.add("VENDPR_LIGHT_MUSIC_PLAY_SETBACK_STATE");
            i2 |= VENDPR_LIGHT_MUSIC_PLAY_SETBACK_STATE;
        }
        if ((557848040 & i) == 557848040) {
            arrayList.add("VENDOR_LIGHT_MUSIC_MODE_SELECT_STATE");
            i2 |= VENDOR_LIGHT_MUSIC_MODE_SELECT_STATE;
        }
        if ((557848041 & i) == 557848041) {
            arrayList.add("VENDOR_DISPLAY_PCPD_SCREEN_MODE");
            i2 |= VENDOR_DISPLAY_PCPD_SCREEN_MODE;
        }
        if ((554702314 & i) == 554702314) {
            arrayList.add("VENDOR_MFD_VERSION");
            i2 |= VENDOR_MFD_VERSION;
        }
        if ((560993771 & i) == 560993771) {
            arrayList.add("VENDOR_MFD_RECV_DATA_INFO");
            i2 |= VENDOR_MFD_RECV_DATA_INFO;
        }
        if ((560993772 & i) == 560993772) {
            arrayList.add("VENDOR_MCU_RECV_DATA_INF0");
            i2 |= VENDOR_MCU_RECV_DATA_INF0;
        }
        if ((560993773 & i) == 560993773) {
            arrayList.add("VENDOR_MCU_SEND_DATA_INFO");
            i2 |= VENDOR_MCU_SEND_DATA_INFO;
        }
        if ((557848046 & i) == 557848046) {
            arrayList.add("VENDOR_MCU_VERSION_REQ");
            i2 |= VENDOR_MCU_VERSION_REQ;
        }
        if ((554702319 & i) == 554702319) {
            arrayList.add("VENDOR_MCU_VERSION");
            i2 |= VENDOR_MCU_VERSION;
        }
        if ((560993776 & i) == 560993776) {
            arrayList.add("VENDOR_MFD_SEND_DATA_INFO");
            i2 |= VENDOR_MFD_SEND_DATA_INFO;
        }
        if ((554702321 & i) == 554702321) {
            arrayList.add("VENDOR_MFD_VERSION_REQ");
            i2 |= VENDOR_MFD_VERSION_REQ;
        }
        if ((557848050 & i) == 557848050) {
            arrayList.add("VENDOR_UPDATE_BCM_CANCEL_SET");
            i2 |= VENDOR_UPDATE_BCM_CANCEL_SET;
        }
        if ((554702323 & i) == 554702323) {
            arrayList.add("VENDOR_UPDATE_BCM_REQ");
            i2 |= VENDOR_UPDATE_BCM_REQ;
        }
        if ((557913588 & i) == 557913588) {
            arrayList.add("VENDOR_UPDATE_BCM_STATE");
            i2 |= VENDOR_UPDATE_BCM_STATE;
        }
        if ((554702325 & i) == 554702325) {
            arrayList.add("VENDOR_UPDATE_BCM_VERSION_REQ");
            i2 |= VENDOR_UPDATE_BCM_VERSION_REQ;
        }
        if ((554702326 & i) == 554702326) {
            arrayList.add("VENDOR_UPDATE_BCM_VERSION");
            i2 |= VENDOR_UPDATE_BCM_VERSION;
        }
        if ((557848055 & i) == 557848055) {
            arrayList.add("VENDOR_UPDATE_HVAC_CANCEL_SET");
            i2 |= VENDOR_UPDATE_HVAC_CANCEL_SET;
        }
        if ((554702328 & i) == 554702328) {
            arrayList.add("VENDOR_UPDATE_HVAC_REQ");
            i2 |= VENDOR_UPDATE_HVAC_REQ;
        }
        if ((557913593 & i) == 557913593) {
            arrayList.add("VENDOR_UPDATE_HVAC_STATE");
            i2 |= VENDOR_UPDATE_HVAC_STATE;
        }
        if ((554702330 & i) == 554702330) {
            arrayList.add("VENDOR_UPDATE_HVAC_VERSION_REQ");
            i2 |= VENDOR_UPDATE_HVAC_VERSION_REQ;
        }
        if ((554702331 & i) == 554702331) {
            arrayList.add("VENDOR_UPDATE_HVAC_VERSION");
            i2 |= VENDOR_UPDATE_HVAC_VERSION;
        }
        if ((557848060 & i) == 557848060) {
            arrayList.add("VENDOR_UPDATE_MCU_CANCEL_SET");
            i2 |= VENDOR_UPDATE_MCU_CANCEL_SET;
        }
        if ((554702333 & i) == 554702333) {
            arrayList.add("VENDOR_UPDATE_MCU_REQ");
            i2 |= VENDOR_UPDATE_MCU_REQ;
        }
        if ((557913598 & i) == 557913598) {
            arrayList.add("VENDOR_UPDATE_MCU_STATE");
            i2 |= VENDOR_UPDATE_MCU_STATE;
        }
        if ((557848063 & i) == 557848063) {
            arrayList.add("VENDOR_UPDATE_MFD_CANCEL_SET");
            i2 |= VENDOR_UPDATE_MFD_CANCEL_SET;
        }
        if ((554702336 & i) == 554702336) {
            arrayList.add("VENDOR_UPDATE_MFD_REQ");
            i2 |= VENDOR_UPDATE_MFD_REQ;
        }
        if ((557913601 & i) == 557913601) {
            arrayList.add("VENDOR_UPDATE_MFD_STATE");
            i2 |= VENDOR_UPDATE_MFD_STATE;
        }
        if ((557848829 & i) == 557848829) {
            arrayList.add("AP_USER_STATE");
            i2 |= AP_USER_STATE;
        }
        if ((557848830 & i) == 557848830) {
            arrayList.add("AP_SCENE_MODE_CTL_ON");
            i2 |= AP_SCENE_MODE_CTL_ON;
        }
        if ((557848831 & i) == 557848831) {
            arrayList.add("AP_SCENE_MODE_ACT");
            i2 |= AP_SCENE_MODE_ACT;
        }
        if ((557848832 & i) == 557848832) {
            arrayList.add("HVAC_ACTIVE_VENTILATION_ON");
            i2 |= HVAC_ACTIVE_VENTILATION_ON;
        }
        if ((557848833 & i) == 557848833) {
            arrayList.add("HVAC_AIR_DRYIONG_ON");
            i2 |= HVAC_AIR_DRYIONG_ON;
        }
        if ((675289346 & i) == 675289346) {
            arrayList.add("HVAC_AIR_QUALITY_IN_OUT_CAR");
            i2 |= HVAC_AIR_QUALITY_IN_OUT_CAR;
        }
        if ((557848836 & i) == 557848836) {
            arrayList.add("HVAC_AIR_QUALITY_RATING_INNER");
            i2 |= HVAC_AIR_QUALITY_RATING_INNER;
        }
        if ((557848837 & i) == 557848837) {
            arrayList.add("HVAC_AQS_MODE_ON");
            i2 |= HVAC_AQS_MODE_ON;
        }
        if ((557848838 & i) == 557848838) {
            arrayList.add("HVAC_COMFORT_MODE");
            i2 |= HVAC_COMFORT_MODE;
        }
        if ((557848839 & i) == 557848839) {
            arrayList.add("HVAC_DISPLAY_REQ");
            i2 |= HVAC_DISPLAY_REQ;
        }
        if ((624957704 & i) == 624957704) {
            arrayList.add("HVAC_FAN_SPEED_ADJUST");
            i2 |= HVAC_FAN_SPEED_ADJUST;
        }
        if ((557848841 & i) == 557848841) {
            arrayList.add("HVAC_ION_GENERATOR_ON");
            i2 |= HVAC_ION_GENERATOR_ON;
        }
        if ((624957706 & i) == 624957706) {
            arrayList.add("HVAC_SETTING_LOCK");
            i2 |= HVAC_SETTING_LOCK;
        }
        if ((624957707 & i) == 624957707) {
            arrayList.add("HVAC_TEMPERATURE_LV_SET");
            i2 |= HVAC_TEMPERATURE_LV_SET;
        }
        if ((557848844 & i) == 557848844) {
            arrayList.add("HVAC_TIMING_VENTILATION_MODE");
            i2 |= HVAC_TIMING_VENTILATION_MODE;
        }
        if ((557848845 & i) == 557848845) {
            arrayList.add("HVAC_VENTILATION_USER_DEFINED_SETTING_AFTERNOON");
            i2 |= HVAC_VENTILATION_USER_DEFINED_SETTING_AFTERNOON;
        }
        if ((557848846 & i) == 557848846) {
            arrayList.add("HVAC_VENTILATION_USER_DEFINED_SETTING_EVENING");
            i2 |= HVAC_VENTILATION_USER_DEFINED_SETTING_EVENING;
        }
        if ((557848847 & i) == 557848847) {
            arrayList.add("HVAC_VENTILATION_USER_DEFINED_SETTING_MORNING");
            i2 |= HVAC_VENTILATION_USER_DEFINED_SETTING_MORNING;
        }
        if ((557848849 & i) == 557848849) {
            arrayList.add("HVAC_WINDOW_VENTILATION_ON");
            i2 |= HVAC_WINDOW_VENTILATION_ON;
        }
        if ((624957714 & i) == 624957714) {
            arrayList.add("HVAC_TEMPERATURE_ADJUST");
            i2 |= HVAC_TEMPERATURE_ADJUST;
        }
        if ((557848851 & i) == 557848851) {
            arrayList.add("HVAC_DUAL_STATE");
            i2 |= HVAC_DUAL_STATE;
        }
        if ((557848852 & i) == 557848852) {
            arrayList.add("HVAC_LOCK_VENTILATION_ON");
            i2 |= HVAC_LOCK_VENTILATION_ON;
        }
        if ((557903486 & i) == 557903486) {
            arrayList.add("DVR_CURRENT_MEDIA_COUNT");
            i2 |= DVR_CURRENT_MEDIA_COUNT;
        }
        if ((557903487 & i) == 557903487) {
            arrayList.add("DVR_REC_SWITCH");
            i2 |= DVR_REC_SWITCH;
        }
        if ((557903488 & i) == 557903488) {
            arrayList.add("DVR_SNAP_SHOOT");
            i2 |= DVR_SNAP_SHOOT;
        }
        if ((557903489 & i) == 557903489) {
            arrayList.add("DVR_SLIDE_PAGING");
            i2 |= DVR_SLIDE_PAGING;
        }
        if ((557903490 & i) == 557903490) {
            arrayList.add("DVR_DELETE_COMMAND");
            i2 |= DVR_DELETE_COMMAND;
        }
        if ((557903491 & i) == 557903491) {
            arrayList.add("DVR_EMERGENCY_COMMAND");
            i2 |= DVR_EMERGENCY_COMMAND;
        }
        if ((557903492 & i) == 557903492) {
            arrayList.add("DVR_EDIT_MODE_COMMAND");
            i2 |= DVR_EDIT_MODE_COMMAND;
        }
        if ((557903493 & i) == 557903493) {
            arrayList.add("DVR_REPLAY_MODE");
            i2 |= DVR_REPLAY_MODE;
        }
        if ((557903494 & i) == 557903494) {
            arrayList.add("DVR_MODE");
            i2 |= DVR_MODE;
        }
        if ((557903495 & i) == 557903495) {
            arrayList.add("DVR_CURRENT_VIDEO_COUNTS");
            i2 |= DVR_CURRENT_VIDEO_COUNTS;
        }
        if ((557969032 & i) == 557969032) {
            arrayList.add("DVR_EDIT_SELECTED");
            i2 |= DVR_EDIT_SELECTED;
        }
        if ((557903497 & i) == 557903497) {
            arrayList.add("DVR_STORGE_PERCENT");
            i2 |= DVR_STORGE_PERCENT;
        }
        if ((557903498 & i) == 557903498) {
            arrayList.add("DVR_MEDIA_SELECT");
            i2 |= DVR_MEDIA_SELECT;
        }
        if ((557903499 & i) == 557903499) {
            arrayList.add("DVR_REPLAY_SPEED");
            i2 |= DVR_REPLAY_SPEED;
        }
        if ((557903500 & i) == 557903500) {
            arrayList.add("DVR_REPLAY_COMMAND");
            i2 |= DVR_REPLAY_COMMAND;
        }
        if ((557903501 & i) == 557903501) {
            arrayList.add("DVR_PRINT_SCREEN");
            i2 |= DVR_PRINT_SCREEN;
        }
        if ((557903502 & i) == 557903502) {
            arrayList.add("DVR_DISPLAY_VISION");
            i2 |= DVR_DISPLAY_VISION;
        }
        if ((557903503 & i) == 557903503) {
            arrayList.add("DVR_VIDEO_PLAY_USER_TIME");
            i2 |= DVR_VIDEO_PLAY_USER_TIME;
        }
        if ((557903504 & i) == 557903504) {
            arrayList.add("DVR_VIDEO_CUR_TIME");
            i2 |= DVR_VIDEO_CUR_TIME;
        }
        if ((557903505 & i) == 557903505) {
            arrayList.add("DVR_VIDEO_TOTAL_TIME");
            i2 |= DVR_VIDEO_TOTAL_TIME;
        }
        if ((557903506 & i) == 557903506) {
            arrayList.add("DVR_RESOLUTION");
            i2 |= DVR_RESOLUTION;
        }
        if ((561049235 & i) == 561049235) {
            arrayList.add("DVR_SYSTEM_VERSION");
            i2 |= DVR_SYSTEM_VERSION;
        }
        if ((557903508 & i) == 557903508) {
            arrayList.add("DVR_SYSTEM_UPDATE");
            i2 |= DVR_SYSTEM_UPDATE;
        }
        if ((557903509 & i) == 557903509) {
            arrayList.add("DVR_SD_CAPACITY");
            i2 |= DVR_SD_CAPACITY;
        }
        if ((557903510 & i) == 557903510) {
            arrayList.add("DVR_TAPE");
            i2 |= DVR_TAPE;
        }
        if ((557903511 & i) == 557903511) {
            arrayList.add("DVR_LANGUAGE_SET");
            i2 |= DVR_LANGUAGE_SET;
        }
        if ((557903512 & i) == 557903512) {
            arrayList.add("DVR_FORMAT");
            i2 |= DVR_FORMAT;
        }
        if ((557903513 & i) == 557903513) {
            arrayList.add("DVR_RECOVERY");
            i2 |= DVR_RECOVERY;
        }
        if ((557903514 & i) == 557903514) {
            arrayList.add("DVR_EMERGENCY_RECORD");
            i2 |= DVR_EMERGENCY_RECORD;
        }
        if ((557903515 & i) == 557903515) {
            arrayList.add("DVR_RECORDING_CYCLE_SETTING");
            i2 |= DVR_RECORDING_CYCLE_SETTING;
        }
        if ((557903516 & i) == 557903516) {
            arrayList.add("DVR_ERROR");
            i2 |= DVR_ERROR;
        }
        if ((557903517 & i) == 557903517) {
            arrayList.add("DVR_STORAGE_STATUS");
            i2 |= DVR_STORAGE_STATUS;
        }
        if ((557903518 & i) == 557903518) {
            arrayList.add("DVR_SDCARDERROR_STATUS");
            i2 |= DVR_SDCARDERROR_STATUS;
        }
        if ((557903519 & i) == 557903519) {
            arrayList.add("DVR_SDCARDFULL_STATUS");
            i2 |= DVR_SDCARDFULL_STATUS;
        }
        if ((560066208 & i) == 560066208) {
            arrayList.add("DVR_CUR_LOCATION");
            i2 |= DVR_CUR_LOCATION;
        }
        if ((557903521 & i) == 557903521) {
            arrayList.add("DVR_DATE_SET_1");
            i2 |= DVR_DATE_SET_1;
        }
        if ((557903522 & i) == 557903522) {
            arrayList.add("DVR_DATE_SET_2");
            i2 |= DVR_DATE_SET_2;
        }
        if ((557903523 & i) == 557903523) {
            arrayList.add("DVR_TIME_SET");
            i2 |= DVR_TIME_SET;
        }
        if ((557903524 & i) == 557903524) {
            arrayList.add("DVR_DISTORT_CORRECT_SWITCH");
            i2 |= DVR_DISTORT_CORRECT_SWITCH;
        }
        if ((557903525 & i) == 557903525) {
            arrayList.add("DVR_NORMAL_TO_EMERGENCY_SWITCH");
            i2 |= DVR_NORMAL_TO_EMERGENCY_SWITCH;
        }
        if ((557903526 & i) == 557903526) {
            arrayList.add("DVR_REALTIME_DISPLAY");
            i2 |= DVR_REALTIME_DISPLAY;
        }
        if ((557903527 & i) == 557903527) {
            arrayList.add("DVR_REBROADCAST");
            i2 |= DVR_REBROADCAST;
        }
        if ((557903528 & i) == 557903528) {
            arrayList.add("DVR_360_REPLAY_MODE");
            i2 |= DVR_360_REPLAY_MODE;
        }
        if ((557903529 & i) == 557903529) {
            arrayList.add("AVM_FORMAT_FAT32_REQ");
            i2 |= AVM_FORMAT_FAT32_REQ;
        }
        if ((557903530 & i) == 557903530) {
            arrayList.add("DVR_VIDEO_CURRENT_TIME");
            i2 |= DVR_VIDEO_CURRENT_TIME;
        }
        if ((557903531 & i) == 557903531) {
            arrayList.add("DVR_SD_CAPACITY_PERCENT");
            i2 |= DVR_SD_CAPACITY_PERCENT;
        }
        if ((557903532 & i) == 557903532) {
            arrayList.add("DVR_BROWSE_CTRL");
            i2 |= DVR_BROWSE_CTRL;
        }
        if ((557903540 & i) == 557903540) {
            arrayList.add("AVM_DISPLAY_SWITCH");
            i2 |= AVM_DISPLAY_SWITCH;
        }
        if ((557903541 & i) == 557903541) {
            arrayList.add("AVM_TURNLIGHT_TRIGGER_SWITCH");
            i2 |= AVM_TURNLIGHT_TRIGGER_SWITCH;
        }
        if ((557903542 & i) == 557903542) {
            arrayList.add("AVM_RADAR_TRIGGER_SWITCH");
            i2 |= AVM_RADAR_TRIGGER_SWITCH;
        }
        if ((557903543 & i) == 557903543) {
            arrayList.add("AVM_LINE_SWITCH");
            i2 |= AVM_LINE_SWITCH;
        }
        if ((557969080 & i) == 557969080) {
            arrayList.add("AVM_COORDINATE_TRANSFER");
            i2 |= AVM_COORDINATE_TRANSFER;
        }
        if ((557903545 & i) == 557903545) {
            arrayList.add("AVM_DISPLAY_FORM");
            i2 |= AVM_DISPLAY_FORM;
        }
        if ((557903546 & i) == 557903546) {
            arrayList.add("AVM_VISION_ANGLE_REQUES");
            i2 |= AVM_VISION_ANGLE_REQUES;
        }
        if ((557903547 & i) == 557903547) {
            arrayList.add("AVM_3D_ENABLE");
            i2 |= AVM_3D_ENABLE;
        }
        if ((557903548 & i) == 557903548) {
            arrayList.add("AVM_3D_HOR_ANGLE");
            i2 |= AVM_3D_HOR_ANGLE;
        }
        if ((557903549 & i) == 557903549) {
            arrayList.add("AVM_MOD_ENABLE");
            i2 |= AVM_MOD_ENABLE;
        }
        if ((557903550 & i) == 557903550) {
            arrayList.add("AVM_CALIBRATION");
            i2 |= AVM_CALIBRATION;
        }
        if ((557903551 & i) == 557903551) {
            arrayList.add("AVM_CALIBRATION_CONFIRM");
            i2 |= AVM_CALIBRATION_CONFIRM;
        }
        if ((557903552 & i) == 557903552) {
            arrayList.add("AVM_OVERSPEED_WARNING");
            i2 |= AVM_OVERSPEED_WARNING;
        }
        if ((557903553 & i) == 557903553) {
            arrayList.add("AVM_TRANSPARENT_CAR_SWITCH");
            i2 |= AVM_TRANSPARENT_CAR_SWITCH;
        }
        if ((557903557 & i) == 557903557) {
            arrayList.add("AVM_REMOTE_DETECT_UPLOAD_STATUS");
            i2 |= AVM_REMOTE_DETECT_UPLOAD_STATUS;
        }
        if ((557903558 & i) == 557903558) {
            arrayList.add("AVM_BODY_COLOR_STATUS");
            i2 |= AVM_BODY_COLOR_STATUS;
        }
        if ((557903559 & i) == 557903559) {
            arrayList.add("AVM_WHEEL_HUB_STYLE");
            i2 |= AVM_WHEEL_HUB_STYLE;
        }
        if ((557903560 & i) == 557903560) {
            arrayList.add("AVM_SCREEN_OPERATE_STATUS");
            i2 |= AVM_SCREEN_OPERATE_STATUS;
        }
        if ((557969097 & i) == 557969097) {
            arrayList.add("AVM_LICENSE_NUMBER_SET");
            i2 |= AVM_LICENSE_NUMBER_SET;
        }
        if ((557969098 & i) == 557969098) {
            arrayList.add("AVM_LICENSE_NUMBER_ACK");
            i2 |= AVM_LICENSE_NUMBER_ACK;
        }
        if ((557903564 & i) == 557903564) {
            arrayList.add("AVM_BOOT_ANIMATION");
            i2 |= AVM_BOOT_ANIMATION;
        }
        if ((557903565 & i) == 557903565) {
            arrayList.add("AVM_DEF_OPEN");
            i2 |= AVM_DEF_OPEN;
        }
        if ((557903566 & i) == 557903566) {
            arrayList.add("AVM_RECORD_STATUS");
            i2 |= AVM_RECORD_STATUS;
        }
        if ((557969103 & i) == 557969103) {
            arrayList.add("SCREEN_INFO");
            i2 |= SCREEN_INFO;
        }
        if ((557903568 & i) == 557903568) {
            arrayList.add("AVM_OBJECT_DETECT");
            i2 |= AVM_OBJECT_DETECT;
        }
        if ((557903569 & i) == 557903569) {
            arrayList.add("AVM_OBJECT_DETECT_ALERT");
            i2 |= AVM_OBJECT_DETECT_ALERT;
        }
        if ((561049601 & i) == 561049601) {
            arrayList.add("VENDOR_MCU_INIT_INFO");
            i2 |= VENDOR_MCU_INIT_INFO;
        }
        if ((557903874 & i) == 557903874) {
            arrayList.add("VENDOR_MCU_POWER_MODE");
            i2 |= VENDOR_MCU_POWER_MODE;
        }
        if ((561049603 & i) == 561049603) {
            arrayList.add("VENDOR_OFF_LINE_STATUS");
            i2 |= VENDOR_OFF_LINE_STATUS;
        }
        if ((557903876 & i) == 557903876) {
            arrayList.add("SHUTDOWN_WARING_INFO");
            i2 |= SHUTDOWN_WARING_INFO;
        }
        if ((557969413 & i) == 557969413) {
            arrayList.add("VENDOR_MCU_TIME");
            i2 |= VENDOR_MCU_TIME;
        }
        if ((557903878 & i) == 557903878) {
            arrayList.add("VENDOR_MCU_TIME_REQ");
            i2 |= VENDOR_MCU_TIME_REQ;
        }
        if ((557903879 & i) == 557903879) {
            arrayList.add("VENDOR_AMPLIFIER_SWITCH_STATUS");
            i2 |= VENDOR_AMPLIFIER_SWITCH_STATUS;
        }
        if ((557903880 & i) == 557903880) {
            arrayList.add("VENDOR_MCU_RESTART_REQ");
            i2 |= VENDOR_MCU_RESTART_REQ;
        }
        if ((557903881 & i) == 557903881) {
            arrayList.add("VENDOR_POWER_ON_NOTICE");
            i2 |= VENDOR_POWER_ON_NOTICE;
        }
        if ((557903883 & i) == 557903883) {
            arrayList.add("FUEL_LEVEL_STATE");
            i2 |= FUEL_LEVEL_STATE;
        }
        if ((561049612 & i) == 561049612) {
            arrayList.add("TUID_INFO");
            i2 |= TUID_INFO;
        }
        if ((554758157 & i) == 554758157) {
            arrayList.add("VENDOR_ICCID_ACK");
            i2 |= VENDOR_ICCID_ACK;
        }
        if ((557903886 & i) == 557903886) {
            arrayList.add("VENDOR_ICCID_REQ");
            i2 |= VENDOR_ICCID_REQ;
        }
        if ((557903887 & i) == 557903887) {
            arrayList.add("TUID_REQ");
            i2 |= TUID_REQ;
        }
        if ((557903888 & i) == 557903888) {
            arrayList.add("BODY_LOCK_AUTO_CLS_WIN_SET");
            i2 |= BODY_LOCK_AUTO_CLS_WIN_SET;
        }
        if ((557903889 & i) == 557903889) {
            arrayList.add("BODY_REMOTE_CTRL_SUNROOF_SET");
            i2 |= BODY_REMOTE_CTRL_SUNROOF_SET;
        }
        if ((591523858 & i) == 591523858) {
            arrayList.add("BODY_SUNROOF_SUNVISOR_ON_OFF");
            i2 |= BODY_SUNROOF_SUNVISOR_ON_OFF;
        }
        if ((557903891 & i) == 557903891) {
            arrayList.add("BODY_REMOTE_CTRL_SW_SET");
            i2 |= BODY_REMOTE_CTRL_SW_SET;
        }
        if ((557903892 & i) == 557903892) {
            arrayList.add("BODY_SIRENS_SOUND_ALARM_MODE");
            i2 |= BODY_SIRENS_SOUND_ALARM_MODE;
        }
        if ((557903893 & i) == 557903893) {
            arrayList.add("HVAC_REMOTE_WINDOW_VENTILATION_ON");
            i2 |= HVAC_REMOTE_WINDOW_VENTILATION_ON;
        }
        if ((561049622 & i) == 561049622) {
            arrayList.add("SET_TUID_INFO");
            i2 |= SET_TUID_INFO;
        }
        if ((557903895 & i) == 557903895) {
            arrayList.add("VENDOR_IC_UPDATE_ACK");
            i2 |= VENDOR_IC_UPDATE_ACK;
        }
        if ((557903896 & i) == 557903896) {
            arrayList.add("VENDOR_IC_UPDATE_REQ");
            i2 |= VENDOR_IC_UPDATE_REQ;
        }
        if ((557903897 & i) == 557903897) {
            arrayList.add("VENDOR_BATTERY_CHARGE_SW");
            i2 |= VENDOR_BATTERY_CHARGE_SW;
        }
        if ((557903905 & i) == 557903905) {
            arrayList.add("BODY_SEAT_COMTIY_ON");
            i2 |= BODY_SEAT_COMTIY_ON;
        }
        if ((557903906 & i) == 557903906) {
            arrayList.add("HVAC_KEY_LOCK_WINDOW_VENTILATION_ON");
            i2 |= HVAC_KEY_LOCK_WINDOW_VENTILATION_ON;
        }
        if ((557903920 & i) == 557903920) {
            arrayList.add("VENDOR_DRIVER_MODE_SET");
            i2 |= VENDOR_DRIVER_MODE_SET;
        }
        if ((554758193 & i) == 554758193) {
            arrayList.add("VENDOR_HW_VERSION_ACK");
            i2 |= VENDOR_HW_VERSION_ACK;
        }
        if ((557903922 & i) == 557903922) {
            arrayList.add("VENDOR_HW_VERSION_REQ");
            i2 |= VENDOR_HW_VERSION_REQ;
        }
        if ((554758195 & i) == 554758195) {
            arrayList.add("VENDOR_IMEI_ACK");
            i2 |= VENDOR_IMEI_ACK;
        }
        if ((557903924 & i) == 557903924) {
            arrayList.add("VENDOR_IMEI_REQ");
            i2 |= VENDOR_IMEI_REQ;
        }
        if ((557903925 & i) == 557903925) {
            arrayList.add("VENDOR_SEND_TO_CAR_REQ");
            i2 |= VENDOR_SEND_TO_CAR_REQ;
        }
        if ((557903926 & i) == 557903926) {
            arrayList.add("BODY_WINDOW_SUNROOF_CONTROL_POS");
            i2 |= BODY_WINDOW_SUNROOF_CONTROL_POS;
        }
        if ((557903927 & i) == 557903927) {
            arrayList.add("BODY_WINDOW_SUNSHADE_POS");
            i2 |= BODY_WINDOW_SUNSHADE_POS;
        }
        if ((557903928 & i) == 557903928) {
            arrayList.add("BODY_DOOR_TRUNK_DOOR_POS");
            i2 |= BODY_DOOR_TRUNK_DOOR_POS;
        }
        if ((557903936 & i) == 557903936) {
            arrayList.add("VENDOR_SECRET_REQ");
            i2 |= VENDOR_SECRET_REQ;
        }
        if ((561049665 & i) == 561049665) {
            arrayList.add("VENDOR_CERTIFICATE_ACK");
            i2 |= VENDOR_CERTIFICATE_ACK;
        }
        if ((561049666 & i) == 561049666) {
            arrayList.add("VENDOR_SECRET_ACK");
            i2 |= VENDOR_SECRET_ACK;
        }
        if ((554758211 & i) == 554758211) {
            arrayList.add("VENDOR_SET_VIN_INFO");
            i2 |= VENDOR_SET_VIN_INFO;
        }
        if ((561049668 & i) == 561049668) {
            arrayList.add("VENDOR_OFF_LINE_SET");
            i2 |= VENDOR_OFF_LINE_SET;
        }
        if ((557969477 & i) == 557969477) {
            arrayList.add("KEY_QUERY_INFO");
            i2 |= KEY_QUERY_INFO;
        }
        if ((557903942 & i) == 557903942) {
            arrayList.add("AVM_LIVE_INFO");
            i2 |= AVM_LIVE_INFO;
        }
        if ((561049671 & i) == 561049671) {
            arrayList.add("VENDOR_CERTIFICATE_ID");
            i2 |= VENDOR_CERTIFICATE_ID;
        }
        if ((560066632 & i) == 560066632) {
            arrayList.add("VENDOR_TIRE_TEMPERATURE");
            i2 |= VENDOR_TIRE_TEMPERATURE;
        }
        if ((557903945 & i) == 557903945) {
            arrayList.add("TIRE_PRESSURE_SIGNAL");
            i2 |= TIRE_PRESSURE_SIGNAL;
        }
        if ((557903946 & i) == 557903946) {
            arrayList.add("VENDOR_OFF_LINE_REQ");
            i2 |= VENDOR_OFF_LINE_REQ;
        }
        if ((625012816 & i) == 625012816) {
            arrayList.add("VENDOR_PHOTO_REQ");
            i2 |= VENDOR_PHOTO_REQ;
        }
        if ((557903953 & i) == 557903953) {
            arrayList.add("VENDOR_TBOX_VERSION_REQ");
            i2 |= VENDOR_TBOX_VERSION_REQ;
        }
        if ((554758226 & i) == 554758226) {
            arrayList.add("VENDOR_TBOX_VERSION");
            i2 |= VENDOR_TBOX_VERSION;
        }
        if ((557903955 & i) == 557903955) {
            arrayList.add("VENDOR_TBOX_FW_VERSION_REQ");
            i2 |= VENDOR_TBOX_FW_VERSION_REQ;
        }
        if ((554758228 & i) == 554758228) {
            arrayList.add("VENDOR_TBOX_FW_VERSION");
            i2 |= VENDOR_TBOX_FW_VERSION;
        }
        if ((557903957 & i) == 557903957) {
            arrayList.add("REVERSE_SIGNAL");
            i2 |= REVERSE_SIGNAL;
        }
        if ((554758230 & i) == 554758230) {
            arrayList.add("VENDOR_ECU_MCU_VERSION");
            i2 |= VENDOR_ECU_MCU_VERSION;
        }
        if ((554758231 & i) == 554758231) {
            arrayList.add("VENDOR_PART_NUMBER");
            i2 |= VENDOR_PART_NUMBER;
        }
        if ((554758232 & i) == 554758232) {
            arrayList.add("VENDOR_SW_PART_NUMBER");
            i2 |= VENDOR_SW_PART_NUMBER;
        }
        if ((557969497 & i) == 557969497) {
            arrayList.add("CAR_VEHICLE_REQ");
            i2 |= CAR_VEHICLE_REQ;
        }
        if ((557903962 & i) == 557903962) {
            arrayList.add("REVERSE_CAMERA");
            i2 |= REVERSE_CAMERA;
        }
        if ((557903964 & i) == 557903964) {
            arrayList.add("VENDOR_LIGHT_NIGHT_MODE_STATE");
            i2 |= VENDOR_LIGHT_NIGHT_MODE_STATE;
        }
        if ((557903968 & i) == 557903968) {
            arrayList.add("SOC_POWER_OFF_SIGN");
            i2 |= SOC_POWER_OFF_SIGN;
        }
        if ((557903969 & i) == 557903969) {
            arrayList.add("SOC_SLEEP_SIGN");
            i2 |= SOC_SLEEP_SIGN;
        }
        if ((557903970 & i) == 557903970) {
            arrayList.add("SCREEN_BACKLIGHT_TOUCH_SWITCH");
            i2 |= SCREEN_BACKLIGHT_TOUCH_SWITCH;
        }
        if ((557903971 & i) == 557903971) {
            arrayList.add("CAR_VOLTAGE_STATUS");
            i2 |= CAR_VOLTAGE_STATUS;
        }
        if ((557903972 & i) == 557903972) {
            arrayList.add("BODY_WINDOW_SUNROOT_POS_DIRECTION");
            i2 |= BODY_WINDOW_SUNROOT_POS_DIRECTION;
        }
        if ((557903973 & i) == 557903973) {
            arrayList.add("SCREEN_POWER_ON");
            i2 |= SCREEN_POWER_ON;
        }
        if ((557903974 & i) == 557903974) {
            arrayList.add("SCREEN_LIGHT_AUTO");
            i2 |= SCREEN_LIGHT_AUTO;
        }
        if ((557903975 & i) == 557903975) {
            arrayList.add("VENDOR_FACTORY_RESET");
            i2 |= VENDOR_FACTORY_RESET;
        }
        if ((557906432 & i) == 557906432) {
            arrayList.add("HVAC_VIBRATION_FEEDBACK");
            i2 |= HVAC_VIBRATION_FEEDBACK;
        }
        if ((557906433 & i) == 557906433) {
            arrayList.add("VENDOR_UPDATE_STATE");
            i2 |= VENDOR_UPDATE_STATE;
        }
        if ((826286081 & i) == 826286081) {
            arrayList.add("ADAS_APA_ASP_SWITCH");
            i2 |= ADAS_APA_ASP_SWITCH;
        }
        if ((826286082 & i) == 826286082) {
            arrayList.add("ADAS_APA_SYSTEM_FAILURE_FLAG");
            i2 |= ADAS_APA_SYSTEM_FAILURE_FLAG;
        }
        if ((826286083 & i) == 826286083) {
            arrayList.add("ADAS_APA_LAEB_ENABLE_STATUS");
            i2 |= ADAS_APA_LAEB_ENABLE_STATUS;
        }
        if ((826286084 & i) == 826286084) {
            arrayList.add("ADAS_APA_ASP_MODE_SELECT");
            i2 |= ADAS_APA_ASP_MODE_SELECT;
        }
        if ((826286085 & i) == 826286085) {
            arrayList.add("ADAS_APA_CROSS_PARKING_MODE");
            i2 |= ADAS_APA_CROSS_PARKING_MODE;
        }
        if ((826286086 & i) == 826286086) {
            arrayList.add("ADAS_APA_RFBT_APA_MANEUVER_START");
            i2 |= ADAS_APA_RFBT_APA_MANEUVER_START;
        }
        if ((826286087 & i) == 826286087) {
            arrayList.add("ADAS_APA_RFBT_APA_MANEUVER_SUSPEND");
            i2 |= ADAS_APA_RFBT_APA_MANEUVER_SUSPEND;
        }
        if ((826286088 & i) == 826286088) {
            arrayList.add("ADAS_APA_RFBT_APA_MANEUVER_CANCEL");
            i2 |= ADAS_APA_RFBT_APA_MANEUVER_CANCEL;
        }
        if ((826286089 & i) == 826286089) {
            arrayList.add("ADAS_APA_RFBT_APA_POMODE_SELECT");
            i2 |= ADAS_APA_RFBT_APA_POMODE_SELECT;
        }
        if ((826286090 & i) == 826286090) {
            arrayList.add("ADAS_APA_RFBT_ASP_SWITCH");
            i2 |= ADAS_APA_RFBT_ASP_SWITCH;
        }
        if ((826286091 & i) == 826286091) {
            arrayList.add("ADAS_APA_RFBT_SLOT_USR_SELECTED");
            i2 |= ADAS_APA_RFBT_SLOT_USR_SELECTED;
        }
        if ((826286092 & i) == 826286092) {
            arrayList.add("ADAS_APA_RFBT_CELLPHONE_APA_MODULE_ENABLE");
            i2 |= ADAS_APA_RFBT_CELLPHONE_APA_MODULE_ENABLE;
        }
        if ((826286093 & i) == 826286093) {
            arrayList.add("ADAS_APA_RFBT_RPA_SWITCH");
            i2 |= ADAS_APA_RFBT_RPA_SWITCH;
        }
        if ((826286094 & i) == 826286094) {
            arrayList.add("ADAS_APA_RFBT_VERTICAL_FORWARD");
            i2 |= ADAS_APA_RFBT_VERTICAL_FORWARD;
        }
        if ((826286095 & i) == 826286095) {
            arrayList.add("ADAS_APA_RFBT_VERTICAL_BAKWARD");
            i2 |= ADAS_APA_RFBT_VERTICAL_BAKWARD;
        }
        if ((826286100 & i) == 826286100) {
            arrayList.add("ADAS_APA_PARK_NOTICE_5");
            i2 |= ADAS_APA_PARK_NOTICE_5;
        }
        if ((826286101 & i) == 826286101) {
            arrayList.add("ADAS_APA_LAEB_STATUS");
            i2 |= ADAS_APA_LAEB_STATUS;
        }
        if ((826286102 & i) == 826286102) {
            arrayList.add("ADAS_APA_BLECONNECTIONREMIND");
            i2 |= ADAS_APA_BLECONNECTIONREMIND;
        }
        if ((826286103 & i) == 826286103) {
            arrayList.add("ADAS_APA_LAEBNOTICE");
            i2 |= ADAS_APA_LAEBNOTICE;
        }
        if ((826286104 & i) == 826286104) {
            arrayList.add("ADAS_APA_REMOTEPARKINGUSINGREMIND");
            i2 |= ADAS_APA_REMOTEPARKINGUSINGREMIND;
        }
        if ((826286105 & i) == 826286105) {
            arrayList.add("ADAS_APA_ASPAVAILABLESTATUS");
            i2 |= ADAS_APA_ASPAVAILABLESTATUS;
        }
        if ((826286106 & i) == 826286106) {
            arrayList.add("ADAS_APA_CROSSMODESELECTREQ");
            i2 |= ADAS_APA_CROSSMODESELECTREQ;
        }
        if ((826286107 & i) == 826286107) {
            arrayList.add("ADAS_APA_ASPSTATUS");
            i2 |= ADAS_APA_ASPSTATUS;
        }
        if ((826286108 & i) == 826286108) {
            arrayList.add("ADAS_APA_BCMHORNCOMMAND");
            i2 |= ADAS_APA_BCMHORNCOMMAND;
        }
        if ((826286109 & i) == 826286109) {
            arrayList.add("ADAS_APA_VEHICLEFRONTDETECT");
            i2 |= ADAS_APA_VEHICLEFRONTDETECT;
        }
        if ((826286110 & i) == 826286110) {
            arrayList.add("ADAS_APA_PEPS_ENGINEOFF_LOCKREQUEST");
            i2 |= ADAS_APA_PEPS_ENGINEOFF_LOCKREQUEST;
        }
        if ((826286111 & i) == 826286111) {
            arrayList.add("ADAS_APA_TCUCLUTCHCOMBINATIONREQ");
            i2 |= ADAS_APA_TCUCLUTCHCOMBINATIONREQ;
        }
        if ((826286112 & i) == 826286112) {
            arrayList.add("ADAS_APA_NUMBEROFPARKINGSLOT");
            i2 |= ADAS_APA_NUMBEROFPARKINGSLOT;
        }
        if ((826286113 & i) == 826286113) {
            arrayList.add("ADAS_APA_PARKINGMODEAVAILABLESTS");
            i2 |= ADAS_APA_PARKINGMODEAVAILABLESTS;
        }
        if ((826286117 & i) == 826286117) {
            arrayList.add("ADAS_APA_EPBREQUEST");
            i2 |= ADAS_APA_EPBREQUEST;
        }
        if ((826286118 & i) == 826286118) {
            arrayList.add("ADAS_APA_EPBREQUESTVALID");
            i2 |= ADAS_APA_EPBREQUESTVALID;
        }
        if ((826286119 & i) == 826286119) {
            arrayList.add("ADAS_APA_TARGETACCELERATION");
            i2 |= ADAS_APA_TARGETACCELERATION;
        }
        if ((826286120 & i) == 826286120) {
            arrayList.add("ADAS_APA_TARGETACCELERATIONVALID");
            i2 |= ADAS_APA_TARGETACCELERATIONVALID;
        }
        if ((826286121 & i) == 826286121) {
            arrayList.add("ADAS_APA_TRANSPRNDSHIFTREQUEST");
            i2 |= ADAS_APA_TRANSPRNDSHIFTREQUEST;
        }
        if ((826286122 & i) == 826286122) {
            arrayList.add("ADAS_APA_TRANSPRNDSHIFTREQVALID");
            i2 |= ADAS_APA_TRANSPRNDSHIFTREQVALID;
        }
        if ((826286123 & i) == 826286123) {
            arrayList.add("ADAS_APA_ENGINETRQREQENABLE");
            i2 |= ADAS_APA_ENGINETRQREQENABLE;
        }
        if ((826286124 & i) == 826286124) {
            arrayList.add("ADAS_APA_ENGTORQREQ");
            i2 |= ADAS_APA_ENGTORQREQ;
        }
        if ((826286125 & i) == 826286125) {
            arrayList.add("ADAS_APA_ACTIVATION_STATUS");
            i2 |= ADAS_APA_ACTIVATION_STATUS;
        }
        if ((826286126 & i) == 826286126) {
            arrayList.add("ADAS_APA_TRANSPRNDSHIFTENABLE");
            i2 |= ADAS_APA_TRANSPRNDSHIFTENABLE;
        }
        if ((826286127 & i) == 826286127) {
            arrayList.add("ADAS_APA_LSCACTION");
            i2 |= ADAS_APA_LSCACTION;
        }
        if ((826286128 & i) == 826286128) {
            arrayList.add("ADAS_APA_HSAHDFORBIDDEN");
            i2 |= ADAS_APA_HSAHDFORBIDDEN;
        }
        if ((826286130 & i) == 826286130) {
            arrayList.add("ADAS_APA_PARKNOTICE_4");
            i2 |= ADAS_APA_PARKNOTICE_4;
        }
        if ((826286131 & i) == 826286131) {
            arrayList.add("ADAS_APA_PREFILLREQ");
            i2 |= ADAS_APA_PREFILLREQ;
        }
        if ((826286132 & i) == 826286132) {
            arrayList.add("ADAS_APA_ACCPEDSHIELDREQ");
            i2 |= ADAS_APA_ACCPEDSHIELDREQ;
        }
        if ((826286133 & i) == 826286133) {
            arrayList.add("ADAS_APA_ESPDECOMPRESSIONMODEL");
            i2 |= ADAS_APA_ESPDECOMPRESSIONMODEL;
        }
        if ((826286134 & i) == 826286134) {
            arrayList.add("ADAS_APA_ULS_FKP_WARNINGSOUNDSTATUS");
            i2 |= ADAS_APA_ULS_FKP_WARNINGSOUNDSTATUS;
        }
        if ((826286138 & i) == 826286138) {
            arrayList.add("ADAS_APA_STEERINGANGLEREQPROTECTION");
            i2 |= ADAS_APA_STEERINGANGLEREQPROTECTION;
        }
        if ((826286139 & i) == 826286139) {
            arrayList.add("ADAS_APA_ERRORSTATUS");
            i2 |= ADAS_APA_ERRORSTATUS;
        }
        if ((826286140 & i) == 826286140) {
            arrayList.add("ADAS_APA_INDICATION");
            i2 |= ADAS_APA_INDICATION;
        }
        if ((826286141 & i) == 826286141) {
            arrayList.add("ADAS_APA_EMERGENCEBRAKE");
            i2 |= ADAS_APA_EMERGENCEBRAKE;
        }
        if ((826286142 & i) == 826286142) {
            arrayList.add("ADAS_APA_STEERINGANGLEREQ");
            i2 |= ADAS_APA_STEERINGANGLEREQ;
        }
        if ((826286143 & i) == 826286143) {
            arrayList.add("ADAS_APA_REMOTEONOFF");
            i2 |= ADAS_APA_REMOTEONOFF;
        }
        if ((826286144 & i) == 826286144) {
            arrayList.add("ADAS_APA_BUTTONPRESS");
            i2 |= ADAS_APA_BUTTONPRESS;
        }
        if ((826286145 & i) == 826286145) {
            arrayList.add("ADAS_APA_INCREASEPRESSUREREQ");
            i2 |= ADAS_APA_INCREASEPRESSUREREQ;
        }
        if ((826286146 & i) == 826286146) {
            arrayList.add("ADAS_APA_PARKNOTICE");
            i2 |= ADAS_APA_PARKNOTICE;
        }
        if ((826286147 & i) == 826286147) {
            arrayList.add("ADAS_APA_TURNLIGHTSCOMMAND");
            i2 |= ADAS_APA_TURNLIGHTSCOMMAND;
        }
        if ((826286148 & i) == 826286148) {
            arrayList.add("ADAS_APA_EPASFAILED");
            i2 |= ADAS_APA_EPASFAILED;
        }
        if ((826286149 & i) == 826286149) {
            arrayList.add("ADAS_APA_ABORTFEEDBACK");
            i2 |= ADAS_APA_ABORTFEEDBACK;
        }
        if ((826286150 & i) == 826286150) {
            arrayList.add("ADAS_APA_CONTROLFEEDBACK");
            i2 |= ADAS_APA_CONTROLFEEDBACK;
        }
        if ((826351687 & i) == 826351687) {
            arrayList.add("ADAS_APA_COORDINATEANGLE");
            i2 |= ADAS_APA_COORDINATEANGLE;
        }
        if ((826286157 & i) == 826286157) {
            arrayList.add("ADAS_APA_TYPE");
            i2 |= ADAS_APA_TYPE;
        }
        if ((826286158 & i) == 826286158) {
            arrayList.add("ADAS_APA_SLOTVOICESELECT");
            i2 |= ADAS_APA_SLOTVOICESELECT;
        }
        if ((826351695 & i) == 826351695) {
            arrayList.add("ADAS_APA_SLOT_DISTANCE_ALL");
            i2 |= ADAS_APA_SLOT_DISTANCE_ALL;
        }
        if ((826351696 & i) == 826351696) {
            arrayList.add("ADAS_APA_SLOT_TYPE_ALL");
            i2 |= ADAS_APA_SLOT_TYPE_ALL;
        }
        if ((826286161 & i) == 826286161) {
            arrayList.add("ADAS_APA_CLOSEWINFEEDBACK");
            i2 |= ADAS_APA_CLOSEWINFEEDBACK;
        }
        if ((826286162 & i) == 826286162) {
            arrayList.add("ADAS_APA_ENGINEOFFFEEDBACK");
            i2 |= ADAS_APA_ENGINEOFFFEEDBACK;
        }
        if ((826286163 & i) == 826286163) {
            arrayList.add("ADAS_APA_LOCKFEEDBACK");
            i2 |= ADAS_APA_LOCKFEEDBACK;
        }
        if ((826286164 & i) == 826286164) {
            arrayList.add("ADAS_APA_REMOTEPARKINGON");
            i2 |= ADAS_APA_REMOTEPARKINGON;
        }
        if ((826286165 & i) == 826286165) {
            arrayList.add("ADAS_APA_REMOTEAPADONEKEYOFFREQ");
            i2 |= ADAS_APA_REMOTEAPADONEKEYOFFREQ;
        }
        if ((826286166 & i) == 826286166) {
            arrayList.add("ADAS_APA_REMOTEPARKINGOFF");
            i2 |= ADAS_APA_REMOTEPARKINGOFF;
        }
        if ((826286167 & i) == 826286167) {
            arrayList.add("ADAS_APA_MODE_SELECT");
            i2 |= ADAS_APA_MODE_SELECT;
        }
        if ((826351704 & i) == 826351704) {
            arrayList.add("ADAS_APA_ON_TOUCH");
            i2 |= ADAS_APA_ON_TOUCH;
        }
        if ((826286169 & i) == 826286169) {
            arrayList.add("AVM_TOP_LOOK_DOWN_SWITCH");
            i2 |= AVM_TOP_LOOK_DOWN_SWITCH;
        }
        if ((826286170 & i) == 826286170) {
            arrayList.add("AVM_360_AVM_DET_REQ");
            i2 |= AVM_360_AVM_DET_REQ;
        }
        if ((826286171 & i) == 826286171) {
            arrayList.add("TURN_TRIGGER_BLIND_ON");
            i2 |= TURN_TRIGGER_BLIND_ON;
        }
        if ((826286172 & i) == 826286172) {
            arrayList.add("RADAR_TRIGGER_BLIND_ON");
            i2 |= RADAR_TRIGGER_BLIND_ON;
        }
        if ((826286173 & i) == 826286173) {
            arrayList.add("BODY_BUZZER_WARNING_MODE");
            i2 |= BODY_BUZZER_WARNING_MODE;
        }
        if ((826286174 & i) == 826286174) {
            arrayList.add("CAN_DEV_DVR_IS_CONNECTED");
            i2 |= CAN_DEV_DVR_IS_CONNECTED;
        }
        if ((826286175 & i) == 826286175) {
            arrayList.add("CAN_DEV_AVM_IS_CONNECTED");
            i2 |= CAN_DEV_AVM_IS_CONNECTED;
        }
        if ((826286176 & i) == 826286176) {
            arrayList.add("CAN_DEV_AC_IS_CONNECTED");
            i2 |= CAN_DEV_AC_IS_CONNECTED;
        }
        if ((826286177 & i) == 826286177) {
            arrayList.add("CAN_DEV_APA_IS_CONNECTED");
            i2 |= CAN_DEV_APA_IS_CONNECTED;
        }
        if ((826286178 & i) == 826286178) {
            arrayList.add("ADAS_ACC_SENSITIVITY");
            i2 |= ADAS_ACC_SENSITIVITY;
        }
        if ((826286179 & i) == 826286179) {
            arrayList.add("ADAS_ACC_FCW_WARN_ON");
            i2 |= ADAS_ACC_FCW_WARN_ON;
        }
        if ((829431908 & i) == 829431908) {
            arrayList.add("TBOX_4G_ICCID_ID");
            i2 |= TBOX_4G_ICCID_ID;
        }
        if ((826286181 & i) == 826286181) {
            arrayList.add("TBOX_4G_PARKMODE_ID");
            i2 |= TBOX_4G_PARKMODE_ID;
        }
        if ((826286182 & i) == 826286182) {
            arrayList.add("TBOX_WORK_STATE_ID");
            i2 |= TBOX_WORK_STATE_ID;
        }
        if ((826286183 & i) == 826286183) {
            arrayList.add("TBOX_OS_F1F1_CONFIG_ID");
            i2 |= TBOX_OS_F1F1_CONFIG_ID;
        }
        if ((826286184 & i) == 826286184) {
            arrayList.add("TBOX_4G_AUDIO_REQ_ID");
            i2 |= TBOX_4G_AUDIO_REQ_ID;
        }
        if ((826286185 & i) == 826286185) {
            arrayList.add("TBOX_4G_CHANGE_PART_STATUS_ID");
            i2 |= TBOX_4G_CHANGE_PART_STATUS_ID;
        }
        if ((826286186 & i) == 826286186) {
            arrayList.add("TBOX_4G_BOX_TYPE_ID");
            i2 |= TBOX_4G_BOX_TYPE_ID;
        }
        if ((826286187 & i) == 826286187) {
            arrayList.add("TBOX_4G_CALL_STATUS_ID");
            i2 |= TBOX_4G_CALL_STATUS_ID;
        }
        if ((826286188 & i) == 826286188) {
            arrayList.add("TBOX_4G_BCALL_STATUS_ID");
            i2 |= TBOX_4G_BCALL_STATUS_ID;
        }
        if ((826286189 & i) == 826286189) {
            arrayList.add("TBOX_4G_SURROND_REQ_ID");
            i2 |= TBOX_4G_SURROND_REQ_ID;
        }
        if ((826286190 & i) == 826286190) {
            arrayList.add("TBOX_RESET_4G_ID");
            i2 |= TBOX_RESET_4G_ID;
        }
        if ((826286191 & i) == 826286191) {
            arrayList.add("TBOX_CTR_4G_UPGRADE_ID");
            i2 |= TBOX_CTR_4G_UPGRADE_ID;
        }
        if ((829431920 & i) == 829431920) {
            arrayList.add("BODY_OUTLIGHT_FLASH_CTL");
            i2 |= BODY_OUTLIGHT_FLASH_CTL;
        }
        if ((826286193 & i) == 826286193) {
            arrayList.add("VENDOR_APU_ILL_ON");
            i2 |= VENDOR_APU_ILL_ON;
        }
        if ((826286194 & i) == 826286194) {
            arrayList.add("BODY_CONTROL_PANEL_VOL_AJ_SENSSTIVE");
            i2 |= BODY_CONTROL_PANEL_VOL_AJ_SENSSTIVE;
        }
        if ((826286195 & i) == 826286195) {
            arrayList.add("BODY_CONTROL_PANEL_VOL_MUTE");
            i2 |= BODY_CONTROL_PANEL_VOL_MUTE;
        }
        if ((826286196 & i) == 826286196) {
            arrayList.add("BODY_CONTROL_PANEL_NAVI_VOL");
            i2 |= BODY_CONTROL_PANEL_NAVI_VOL;
        }
        if ((826286197 & i) == 826286197) {
            arrayList.add("BODY_CONTROL_PANEL_MEADIA_VOL");
            i2 |= BODY_CONTROL_PANEL_MEADIA_VOL;
        }
        if ((826286198 & i) == 826286198) {
            arrayList.add("BODY_CONTROL_PANEL_VOL_AJ_TYPE");
            i2 |= BODY_CONTROL_PANEL_VOL_AJ_TYPE;
        }
        if ((826286199 & i) == 826286199) {
            arrayList.add("BODY_CONTROL_PANEL_DIS_TYPE");
            i2 |= BODY_CONTROL_PANEL_DIS_TYPE;
        }
        if ((826286200 & i) == 826286200) {
            arrayList.add("BODY_CONTROL_PANEL_TTS_VOL");
            i2 |= BODY_CONTROL_PANEL_TTS_VOL;
        }
        if ((826286201 & i) == 826286201) {
            arrayList.add("BODY_CONTROL_PANEL_MEADIA_MUTE");
            i2 |= BODY_CONTROL_PANEL_MEADIA_MUTE;
        }
        if ((826286202 & i) == 826286202) {
            arrayList.add("BODY_CONTROL_PANEL_TBOX_PHONE_VOL");
            i2 |= BODY_CONTROL_PANEL_TBOX_PHONE_VOL;
        }
        if ((826286203 & i) == 826286203) {
            arrayList.add("BODY_CONTROL_PANEL_PHONE_MUTE");
            i2 |= BODY_CONTROL_PANEL_PHONE_MUTE;
        }
        if ((826286204 & i) == 826286204) {
            arrayList.add("POWER_WARNNING_STATE");
            i2 |= POWER_WARNNING_STATE;
        }
        if ((826286205 & i) == 826286205) {
            arrayList.add("VENDOR_POWER_OFF_ON");
            i2 |= VENDOR_POWER_OFF_ON;
        }
        if ((826351742 & i) == 826351742) {
            arrayList.add("FTYPE_BODY_ALL");
            i2 |= FTYPE_BODY_ALL;
        }
        if ((826351743 & i) == 826351743) {
            arrayList.add("FTYPE_AC_ALL");
            i2 |= FTYPE_AC_ALL;
        }
        if ((826351744 & i) == 826351744) {
            arrayList.add("FTYPE_FRONT_ASSIST_ALL");
            i2 |= FTYPE_FRONT_ASSIST_ALL;
        }
        if ((826286209 & i) == 826286209) {
            arrayList.add("AP_NAVI_NEXT_ROADCROSS_DISTANCE");
            i2 |= AP_NAVI_NEXT_ROADCROSS_DISTANCE;
        }
        if ((827400322 & i) == 827400322) {
            arrayList.add("AP_NAVI_STATE_INFO_LOCATION");
            i2 |= AP_NAVI_STATE_INFO_LOCATION;
        }
        if ((826351747 & i) == 826351747) {
            arrayList.add("AP_NAVI_STATE_INFO_ICON_NUMBER");
            i2 |= AP_NAVI_STATE_INFO_ICON_NUMBER;
        }
        if ((829427712 & i) == 829427712) {
            arrayList.add("PASSTHROUGH_DATA");
            i2 |= PASSTHROUGH_DATA;
        }
        if ((826281985 & i) == 826281985) {
            arrayList.add("TIME_SETTING");
            i2 |= TIME_SETTING;
        }
        if ((826281986 & i) == 826281986) {
            arrayList.add("SCREEN_BRIGHTNESS");
            i2 |= SCREEN_BRIGHTNESS;
        }
        if ((826281987 & i) == 826281987) {
            arrayList.add("MCU_UPDATE");
            i2 |= MCU_UPDATE;
        }
        if ((826281988 & i) == 826281988) {
            arrayList.add("PARAM_UNIT_SETTING");
            i2 |= PARAM_UNIT_SETTING;
        }
        if ((826281989 & i) == 826281989) {
            arrayList.add("SYSTEM_STATUS");
            i2 |= SYSTEM_STATUS;
        }
        if ((826281990 & i) == 826281990) {
            arrayList.add("DISP_MODE");
            i2 |= DISP_MODE;
        }
        if ((826281991 & i) == 826281991) {
            arrayList.add("APU_STATUS");
            i2 |= APU_STATUS;
        }
        if ((823136264 & i) == 823136264) {
            arrayList.add("INFO_MCU_VER");
            i2 |= INFO_MCU_VER;
        }
        if ((826281993 & i) == 826281993) {
            arrayList.add("FACTORY_RECOVERY");
            i2 |= FACTORY_RECOVERY;
        }
        if ((829427722 & i) == 829427722) {
            arrayList.add("PDEV_VEHICLE_INFO_FID_EOL");
            i2 |= PDEV_VEHICLE_INFO_FID_EOL;
        }
        if ((823136267 & i) == 823136267) {
            arrayList.add("PDEV_VEHICLE_INFO_FID_VIN");
            i2 |= PDEV_VEHICLE_INFO_FID_VIN;
        }
        if ((823136268 & i) == 823136268) {
            arrayList.add("PDEV_VEHICLE_INFO_FID_TUID");
            i2 |= PDEV_VEHICLE_INFO_FID_TUID;
        }
        if ((829427725 & i) == 829427725) {
            arrayList.add("PDEV_VEHICLE_INFO_FID_VSOL");
            i2 |= PDEV_VEHICLE_INFO_FID_VSOL;
        }
        if ((826281998 & i) == 826281998) {
            arrayList.add("MCU_SYSTEM_SET_DEVELOPER_MODE");
            i2 |= MCU_SYSTEM_SET_DEVELOPER_MODE;
        }
        if ((826347535 & i) == 826347535) {
            arrayList.add("NAVI_STATE_INFO");
            i2 |= NAVI_STATE_INFO;
        }
        if ((829427728 & i) == 829427728) {
            arrayList.add("NAVI_TEXT_INFO");
            i2 |= NAVI_TEXT_INFO;
        }
        if ((826282001 & i) == 826282001) {
            arrayList.add("AUDIO_SAMPLE_STATE");
            i2 |= AUDIO_SAMPLE_STATE;
        }
        if ((829427730 & i) == 829427730) {
            arrayList.add("AUDIO_SAMPLE_DATA");
            i2 |= AUDIO_SAMPLE_DATA;
        }
        if ((826282003 & i) == 826282003) {
            arrayList.add("PDEV_VEHICLE_INFO_LOGIN_TSP");
            i2 |= PDEV_VEHICLE_INFO_LOGIN_TSP;
        }
        if ((826282497 & i) == 826282497) {
            arrayList.add("AIR_CLEANER_STATUS_FID_FILTERLIFEEND");
            i2 |= AIR_CLEANER_STATUS_FID_FILTERLIFEEND;
        }
        if ((826282498 & i) == 826282498) {
            arrayList.add("AIR_CLEANER_STATUS_FID_WORK_STATUS");
            i2 |= AIR_CLEANER_STATUS_FID_WORK_STATUS;
        }
        if ((826282499 & i) == 826282499) {
            arrayList.add("AIR_CLEANER_STATUS_FID_AUTOSTATUS");
            i2 |= AIR_CLEANER_STATUS_FID_AUTOSTATUS;
        }
        if ((826282500 & i) == 826282500) {
            arrayList.add("AIR_CLEANER_STATUS_FID_INCAR_PM25_LVL");
            i2 |= AIR_CLEANER_STATUS_FID_INCAR_PM25_LVL;
        }
        if ((826282501 & i) == 826282501) {
            arrayList.add("AIR_CLEANER_STATUS_FID_OUTCAR_PM25_LVL");
            i2 |= AIR_CLEANER_STATUS_FID_OUTCAR_PM25_LVL;
        }
        if ((826282502 & i) == 826282502) {
            arrayList.add("AIR_CLEANER_STATUS_FID_INCAR_PM25_DATA");
            i2 |= AIR_CLEANER_STATUS_FID_INCAR_PM25_DATA;
        }
        if ((826282503 & i) == 826282503) {
            arrayList.add("AIR_CLEANER_STATUS_FID_OUTCAR_PM25_DATA");
            i2 |= AIR_CLEANER_STATUS_FID_OUTCAR_PM25_DATA;
        }
        if ((826282504 & i) == 826282504) {
            arrayList.add("AIR_CLEANER_STATUS_FID_FRAGRANCESW");
            i2 |= AIR_CLEANER_STATUS_FID_FRAGRANCESW;
        }
        if ((826282505 & i) == 826282505) {
            arrayList.add("AIR_CLEANER_STATUS_FID_PROMPTREQ");
            i2 |= AIR_CLEANER_STATUS_FID_PROMPTREQ;
        }
        if ((826282506 & i) == 826282506) {
            arrayList.add("AIR_CLEANER_STATUS_FID_DISPLAYACTIVE");
            i2 |= AIR_CLEANER_STATUS_FID_DISPLAYACTIVE;
        }
        if ((826282507 & i) == 826282507) {
            arrayList.add("AIR_CLEANER_STATUS_FID_WINDSET");
            i2 |= AIR_CLEANER_STATUS_FID_WINDSET;
        }
        if ((826282508 & i) == 826282508) {
            arrayList.add("AIR_CLEANER_COMM_FID_WIN");
            i2 |= AIR_CLEANER_COMM_FID_WIN;
        }
        if ((826282509 & i) == 826282509) {
            arrayList.add("AIR_CLEANER_COMM_FID_AUTO");
            i2 |= AIR_CLEANER_COMM_FID_AUTO;
        }
        if ((826282510 & i) == 826282510) {
            arrayList.add("AIR_CLEANER_COMM_FID_OFF");
            i2 |= AIR_CLEANER_COMM_FID_OFF;
        }
        if ((826282511 & i) == 826282511) {
            arrayList.add("AIR_CLEANER_COMM_FID_FRAGRANCE");
            i2 |= AIR_CLEANER_COMM_FID_FRAGRANCE;
        }
        if ((826282529 & i) == 826282529) {
            arrayList.add("AVM_STATUS_FID_CTRL_REQ");
            i2 |= AVM_STATUS_FID_CTRL_REQ;
        }
        if ((823136802 & i) == 823136802) {
            arrayList.add("AVM_STATUS_FID_MAIN_VERSION");
            i2 |= AVM_STATUS_FID_MAIN_VERSION;
        }
        if ((823136803 & i) == 823136803) {
            arrayList.add("AVM_STATUS_FID_DISP_VERSION");
            i2 |= AVM_STATUS_FID_DISP_VERSION;
        }
        if ((826282532 & i) == 826282532) {
            arrayList.add("AVM_STATUS_FID_CALIBRATION_STATE");
            i2 |= AVM_STATUS_FID_CALIBRATION_STATE;
        }
        if ((826282533 & i) == 826282533) {
            arrayList.add("AVM_STATUS_FID_CALI_TIME_REQ");
            i2 |= AVM_STATUS_FID_CALI_TIME_REQ;
        }
        if ((826282534 & i) == 826282534) {
            arrayList.add("AVM_STATUS_FID_FRONT_CAM_CALI_FAULT");
            i2 |= AVM_STATUS_FID_FRONT_CAM_CALI_FAULT;
        }
        if ((826282535 & i) == 826282535) {
            arrayList.add("AVM_STATUS_FID_REAR_CAM_CALI_FAULT");
            i2 |= AVM_STATUS_FID_REAR_CAM_CALI_FAULT;
        }
        if ((826282536 & i) == 826282536) {
            arrayList.add("AVM_STATUS_FID_LEFT_CAM_CALI_FAULT");
            i2 |= AVM_STATUS_FID_LEFT_CAM_CALI_FAULT;
        }
        if ((826282537 & i) == 826282537) {
            arrayList.add("AVM_STATUS_FID_RIGHT_CAM_CALI_FAULT");
            i2 |= AVM_STATUS_FID_RIGHT_CAM_CALI_FAULT;
        }
        if ((826282538 & i) == 826282538) {
            arrayList.add("AVM_COMM_FID_ACTIVE");
            i2 |= AVM_COMM_FID_ACTIVE;
        }
        if ((826282539 & i) == 826282539) {
            arrayList.add("AVM_COMM_FID_ASSIST_LINE");
            i2 |= AVM_COMM_FID_ASSIST_LINE;
        }
        if ((826282540 & i) == 826282540) {
            arrayList.add("AVM_COMM_FID_ANGLE");
            i2 |= AVM_COMM_FID_ANGLE;
        }
        if ((826282541 & i) == 826282541) {
            arrayList.add("AVM_COMM_FID_RADAR_TRIGGER");
            i2 |= AVM_COMM_FID_RADAR_TRIGGER;
        }
        if ((826282542 & i) == 826282542) {
            arrayList.add("AVM_COMM_FID_CAM_DISP_REQ");
            i2 |= AVM_COMM_FID_CAM_DISP_REQ;
        }
        if ((829428271 & i) == 829428271) {
            arrayList.add("AVM_COMM_FID_CALI_START_SN");
            i2 |= AVM_COMM_FID_CALI_START_SN;
        }
        if ((826282544 & i) == 826282544) {
            arrayList.add("AVM_COMM_FID_CALI_INFO_REQ");
            i2 |= AVM_COMM_FID_CALI_INFO_REQ;
        }
        if ((826282545 & i) == 826282545) {
            arrayList.add("AVM_COMM_FID_TIME_INFO");
            i2 |= AVM_COMM_FID_TIME_INFO;
        }
        if ((826282546 & i) == 826282546) {
            arrayList.add("AVM_COMM_FID_LANGUAGE");
            i2 |= AVM_COMM_FID_LANGUAGE;
        }
        if ((826282547 & i) == 826282547) {
            arrayList.add("AVM_COMM_FID_SCREEN_STATE");
            i2 |= AVM_COMM_FID_SCREEN_STATE;
        }
        if ((826282548 & i) == 826282548) {
            arrayList.add("AVM_COMM_FID_PURPOSE_KEY");
            i2 |= AVM_COMM_FID_PURPOSE_KEY;
        }
        if ((826348085 & i) == 826348085) {
            arrayList.add("AVM_COMM_FID_ORDINATE_OPERATION");
            i2 |= AVM_COMM_FID_ORDINATE_OPERATION;
        }
        if ((826282550 & i) == 826282550) {
            arrayList.add("AVM_COMM_FID_CALI_START");
            i2 |= AVM_COMM_FID_CALI_START;
        }
        if ((826282592 & i) == 826282592) {
            arrayList.add("DVR_STATUS_FID_ERROR");
            i2 |= DVR_STATUS_FID_ERROR;
        }
        if ((826282593 & i) == 826282593) {
            arrayList.add("DVR_STATUS_FID_SDCARDSTATUS");
            i2 |= DVR_STATUS_FID_SDCARDSTATUS;
        }
        if ((826282594 & i) == 826282594) {
            arrayList.add("DVR_STATUS_FID_ACTIVESTATUS");
            i2 |= DVR_STATUS_FID_ACTIVESTATUS;
        }
        if ((826282595 & i) == 826282595) {
            arrayList.add("DVR_STATUS_FID_STORAGEPERCENT");
            i2 |= DVR_STATUS_FID_STORAGEPERCENT;
        }
        if ((826282596 & i) == 826282596) {
            arrayList.add("DVR_STATUS_FID_STORAGESTATUS");
            i2 |= DVR_STATUS_FID_STORAGESTATUS;
        }
        if ((826282597 & i) == 826282597) {
            arrayList.add("DVR_STATUS_FID_CURRENTVIDEO_COUNTS");
            i2 |= DVR_STATUS_FID_CURRENTVIDEO_COUNTS;
        }
        if ((826282598 & i) == 826282598) {
            arrayList.add("DVR_STATUS_FID_FILELISTSTATUS");
            i2 |= DVR_STATUS_FID_FILELISTSTATUS;
        }
        if ((826282599 & i) == 826282599) {
            arrayList.add("DVR_COMM_FID_DVRDISTORTCORRECTSWITCH");
            i2 |= DVR_COMM_FID_DVRDISTORTCORRECTSWITCH;
        }
        if ((826282600 & i) == 826282600) {
            arrayList.add("DVR_COMM_FID_EMERGENCYRECORD");
            i2 |= DVR_COMM_FID_EMERGENCYRECORD;
        }
        if ((826282601 & i) == 826282601) {
            arrayList.add("DVR_COMM_FID_NORMALTOEMERGENCYSWITCH");
            i2 |= DVR_COMM_FID_NORMALTOEMERGENCYSWITCH;
        }
        if ((826282602 & i) == 826282602) {
            arrayList.add("DVR_COMM_FID_FORMATREQ");
            i2 |= DVR_COMM_FID_FORMATREQ;
        }
        if ((826282603 & i) == 826282603) {
            arrayList.add("DVR_COMM_FID_VEDIOSEEK");
            i2 |= DVR_COMM_FID_VEDIOSEEK;
        }
        if ((826282604 & i) == 826282604) {
            arrayList.add("DVR_COMM_FID_LANGUAGESET");
            i2 |= DVR_COMM_FID_LANGUAGESET;
        }
        if ((826282605 & i) == 826282605) {
            arrayList.add("DVR_COMM_FID_RESOLUTIONSET");
            i2 |= DVR_COMM_FID_RESOLUTIONSET;
        }
        if ((826282606 & i) == 826282606) {
            arrayList.add("DVR_COMM_FID_RECORDINGCYCLESETTING");
            i2 |= DVR_COMM_FID_RECORDINGCYCLESETTING;
        }
        if ((826282607 & i) == 826282607) {
            arrayList.add("DVR_COMM_FID_ENABLESET");
            i2 |= DVR_COMM_FID_ENABLESET;
        }
        if ((826282608 & i) == 826282608) {
            arrayList.add("DVR_COMM_FID_TAPESET");
            i2 |= DVR_COMM_FID_TAPESET;
        }
        if ((826282609 & i) == 826282609) {
            arrayList.add("DVR_COMM_FID_SHOOTCONTROL");
            i2 |= DVR_COMM_FID_SHOOTCONTROL;
        }
        if ((826282610 & i) == 826282610) {
            arrayList.add("DVR_COMM_FID_REPLAYMODE");
            i2 |= DVR_COMM_FID_REPLAYMODE;
        }
        if ((826282611 & i) == 826282611) {
            arrayList.add("DVR_COMM_FID_REPLAYSPEED");
            i2 |= DVR_COMM_FID_REPLAYSPEED;
        }
        if ((826282612 & i) == 826282612) {
            arrayList.add("DVR_COMM_FID_DISPLAYVISION");
            i2 |= DVR_COMM_FID_DISPLAYVISION;
        }
        if ((826282613 & i) == 826282613) {
            arrayList.add("DVR_COMM_FID_PRINTSCREEN");
            i2 |= DVR_COMM_FID_PRINTSCREEN;
        }
        if ((826282614 & i) == 826282614) {
            arrayList.add("DVR_COMM_FID_MODE");
            i2 |= DVR_COMM_FID_MODE;
        }
        if ((826282615 & i) == 826282615) {
            arrayList.add("DVR_COMM_FID_SDCAPACITY");
            i2 |= DVR_COMM_FID_SDCAPACITY;
        }
        if ((826282616 & i) == 826282616) {
            arrayList.add("DVR_COMM_FID_SYSTEMIMPRINT");
            i2 |= DVR_COMM_FID_SYSTEMIMPRINT;
        }
        if ((826282617 & i) == 826282617) {
            arrayList.add("DVR_COMM_FID_REBROADCASTREQ");
            i2 |= DVR_COMM_FID_REBROADCASTREQ;
        }
        if ((826282618 & i) == 826282618) {
            arrayList.add("DVR_COMM_FID_REALTIMEREQ");
            i2 |= DVR_COMM_FID_REALTIMEREQ;
        }
        if ((826282619 & i) == 826282619) {
            arrayList.add("DVR_COMM_FID_ERRORRECORD");
            i2 |= DVR_COMM_FID_ERRORRECORD;
        }
        if ((826282620 & i) == 826282620) {
            arrayList.add("DVR_COMM_FID_VIDEOPLAYPERCENTAGE");
            i2 |= DVR_COMM_FID_VIDEOPLAYPERCENTAGE;
        }
        if ((826282621 & i) == 826282621) {
            arrayList.add("DVR_COMM_FID_EDITSELECTED");
            i2 |= DVR_COMM_FID_EDITSELECTED;
        }
        if ((826282622 & i) == 826282622) {
            arrayList.add("DVR_COMM_FID_DATEFORMAT");
            i2 |= DVR_COMM_FID_DATEFORMAT;
        }
        if ((826282623 & i) == 826282623) {
            arrayList.add("DVR_COMM_FID_GSENSOR");
            i2 |= DVR_COMM_FID_GSENSOR;
        }
        if ((826282624 & i) == 826282624) {
            arrayList.add("DVR_COMM_FID_LOCK");
            i2 |= DVR_COMM_FID_LOCK;
        }
        if ((826282625 & i) == 826282625) {
            arrayList.add("DVR_COMM_FID_REPLAYDELREQ");
            i2 |= DVR_COMM_FID_REPLAYDELREQ;
        }
        if ((826282626 & i) == 826282626) {
            arrayList.add("DVR_COMM_FID_RECOVERYREQ");
            i2 |= DVR_COMM_FID_RECOVERYREQ;
        }
        if ((826282752 & i) == 826282752) {
            arrayList.add("VEHICLESET_CRUISE_CONTROLSET");
            i2 |= VEHICLESET_CRUISE_CONTROLSET;
        }
        if ((826282753 & i) == 826282753) {
            arrayList.add("VEHICLESET_CRUISE_INTEGRATED");
            i2 |= VEHICLESET_CRUISE_INTEGRATED;
        }
        if ((826282754 & i) == 826282754) {
            arrayList.add("VEHICLESET_CRUISE_MODE");
            i2 |= VEHICLESET_CRUISE_MODE;
        }
        if ((826282755 & i) == 826282755) {
            arrayList.add("VEHICLESET_CRUISE_ACCOBJENABLE");
            i2 |= VEHICLESET_CRUISE_ACCOBJENABLE;
        }
        if ((826282756 & i) == 826282756) {
            arrayList.add("VEHICLESET_BTC_DISTANCEALERTENABLE");
            i2 |= VEHICLESET_BTC_DISTANCEALERTENABLE;
        }
        if ((826282757 & i) == 826282757) {
            arrayList.add("VEHICLESET_BTC_FCWENABLE");
            i2 |= VEHICLESET_BTC_FCWENABLE;
        }
        if ((826282758 & i) == 826282758) {
            arrayList.add("VEHICLESET_BTC_FCWSENSITIVITY");
            i2 |= VEHICLESET_BTC_FCWSENSITIVITY;
        }
        if ((826282759 & i) == 826282759) {
            arrayList.add("VEHICLESET_BTC_AUTOBRAKEENABLE");
            i2 |= VEHICLESET_BTC_AUTOBRAKEENABLE;
        }
        if ((826282760 & i) == 826282760) {
            arrayList.add("VEHICLESET_BTC_FCWBEEP");
            i2 |= VEHICLESET_BTC_FCWBEEP;
        }
        if ((826282761 & i) == 826282761) {
            arrayList.add("VEHICLESET_RW_LCDAL_BSDLCAENABLESTATUS");
            i2 |= VEHICLESET_RW_LCDAL_BSDLCAENABLESTATUS;
        }
        if ((826282762 & i) == 826282762) {
            arrayList.add("VEHICLESET_RW_LCDAL_CTAENABLESTATUS");
            i2 |= VEHICLESET_RW_LCDAL_CTAENABLESTATUS;
        }
        if ((826282763 & i) == 826282763) {
            arrayList.add("VEHICLESET_RW_LCDAL_RCWENABLESTATU");
            i2 |= VEHICLESET_RW_LCDAL_RCWENABLESTATU;
        }
        if ((826282764 & i) == 826282764) {
            arrayList.add("VEHICLESET_RW_RCWSOUNDENABLE");
            i2 |= VEHICLESET_RW_RCWSOUNDENABLE;
        }
        if ((826282765 & i) == 826282765) {
            arrayList.add("VEHICLESET_RW_LCDAL_SEAENABLESTATUS");
            i2 |= VEHICLESET_RW_LCDAL_SEAENABLESTATUS;
        }
        if ((826282766 & i) == 826282766) {
            arrayList.add("VEHICLESET_RW_SEWENABLE");
            i2 |= VEHICLESET_RW_SEWENABLE;
        }
        if ((826282767 & i) == 826282767) {
            arrayList.add("VEHICLESET_RW_LCDAL_AUDIOWARNINGENABLESTATUS");
            i2 |= VEHICLESET_RW_LCDAL_AUDIOWARNINGENABLESTATUS;
        }
        if ((826282768 & i) == 826282768) {
            arrayList.add("VEHICLESET_RW_LCDAL_LOCKCTRLENABLESTATUS");
            i2 |= VEHICLESET_RW_LCDAL_LOCKCTRLENABLESTATUS;
        }
        if ((826282769 & i) == 826282769) {
            arrayList.add("VEHICLESET_LA_LAS_LASMODESELECTIONSTATUS");
            i2 |= VEHICLESET_LA_LAS_LASMODESELECTIONSTATUS;
        }
        if ((826282770 & i) == 826282770) {
            arrayList.add("VEHICLESET_LA_LAS_FUNCSEL");
            i2 |= VEHICLESET_LA_LAS_FUNCSEL;
        }
        if ((826282771 & i) == 826282771) {
            arrayList.add("VEHICLESET_LA_LASWARNINGMODESELECTSTAS");
            i2 |= VEHICLESET_LA_LASWARNINGMODESELECTSTAS;
        }
        if ((826282772 & i) == 826282772) {
            arrayList.add("VEHICLESET_LA_LAS_LDWSHAKELEVSTATUS");
            i2 |= VEHICLESET_LA_LAS_LDWSHAKELEVSTATUS;
        }
        if ((826282773 & i) == 826282773) {
            arrayList.add("VEHICLESET_LA_LAS_LDWSENSITIVITYSTATUS");
            i2 |= VEHICLESET_LA_LAS_LDWSENSITIVITYSTATUS;
        }
        if ((826282774 & i) == 826282774) {
            arrayList.add("VEHICLESET_LA_LAS_FATIGUEDECTIONENABLESTATUS");
            i2 |= VEHICLESET_LA_LAS_FATIGUEDECTIONENABLESTATUS;
        }
        if ((826282775 & i) == 826282775) {
            arrayList.add("VEHICLESET_LA_LASWARNINGBEEP");
            i2 |= VEHICLESET_LA_LASWARNINGBEEP;
        }
        if ((826282776 & i) == 826282776) {
            arrayList.add("VEHICLESET_SLA_LAS_SLASWITCHSTATUS");
            i2 |= VEHICLESET_SLA_LAS_SLASWITCHSTATUS;
        }
        if ((826282777 & i) == 826282777) {
            arrayList.add("VEHICLESET_SLA_LAS_OVERSPEEDWARNINGENABLESTATUS");
            i2 |= VEHICLESET_SLA_LAS_OVERSPEEDWARNINGENABLESTATUS;
        }
        if ((826282778 & i) == 826282778) {
            arrayList.add("VEHICLESET_SLA_LAS_OVERSPEEDSOUNDWARNENASTS");
            i2 |= VEHICLESET_SLA_LAS_OVERSPEEDSOUNDWARNENASTS;
        }
        if ((826282779 & i) == 826282779) {
            arrayList.add("VEHICLESET_SLA_LAS_OVERSPEEDWARNINGOFFSET");
            i2 |= VEHICLESET_SLA_LAS_OVERSPEEDWARNINGOFFSET;
        }
        if ((826282780 & i) == 826282780) {
            arrayList.add("VEHICLESET_PA_ULS_FKP_ACTIVATION");
            i2 |= VEHICLESET_PA_ULS_FKP_ACTIVATION;
        }
        if ((826282781 & i) == 826282781) {
            arrayList.add("VEHICLESET_PA_TURNLIGHTAVMSWITCH");
            i2 |= VEHICLESET_PA_TURNLIGHTAVMSWITCH;
        }
        if ((826282782 & i) == 826282782) {
            arrayList.add("VEHICLESET_PA_RADARAVMSWITCH");
            i2 |= VEHICLESET_PA_RADARAVMSWITCH;
        }
        if ((826282783 & i) == 826282783) {
            arrayList.add("VEHICLESET_PA_REMOTEDISTANCESET");
            i2 |= VEHICLESET_PA_REMOTEDISTANCESET;
        }
        if ((826282784 & i) == 826282784) {
            arrayList.add("VEHICLESET_PA_OBSTACLESAFEDISTANCESET");
            i2 |= VEHICLESET_PA_OBSTACLESAFEDISTANCESET;
        }
        if ((826282785 & i) == 826282785) {
            arrayList.add("VEHICLESET_PA_360AVMDETECTIONREQUEST");
            i2 |= VEHICLESET_PA_360AVMDETECTIONREQUEST;
        }
        if ((826282786 & i) == 826282786) {
            arrayList.add("VEHICLESET_PA_AVMCALIBRATIONSWITCH");
            i2 |= VEHICLESET_PA_AVMCALIBRATIONSWITCH;
        }
        if ((826282787 & i) == 826282787) {
            arrayList.add("VEHICLESET_PA_ESPMODE");
            i2 |= VEHICLESET_PA_ESPMODE;
        }
        if ((826282788 & i) == 826282788) {
            arrayList.add("VEHICLESET_PA_FRONTRADAR");
            i2 |= VEHICLESET_PA_FRONTRADAR;
        }
        if ((826282789 & i) == 826282789) {
            arrayList.add("VEHICLESET_PA_ASS");
            i2 |= VEHICLESET_PA_ASS;
        }
        if ((826282790 & i) == 826282790) {
            arrayList.add("VEHICLESET_PA_ESP");
            i2 |= VEHICLESET_PA_ESP;
        }
        if ((826282791 & i) == 826282791) {
            arrayList.add("VEHICLESET_PA_HDC");
            i2 |= VEHICLESET_PA_HDC;
        }
        if ((826282792 & i) == 826282792) {
            arrayList.add("VEHICLESET_BS_RKEUNLOCKDOORTYPESET");
            i2 |= VEHICLESET_BS_RKEUNLOCKDOORTYPESET;
        }
        if ((826282793 & i) == 826282793) {
            arrayList.add("VEHICLESET_BS_AUTOSPEEDLOCKSET");
            i2 |= VEHICLESET_BS_AUTOSPEEDLOCKSET;
        }
        if ((826282794 & i) == 826282794) {
            arrayList.add("VEHICLESET_BS_AUTOIGNOFFUNLOCKSET");
            i2 |= VEHICLESET_BS_AUTOIGNOFFUNLOCKSET;
        }
        if ((826282795 & i) == 826282795) {
            arrayList.add("VEHICLESET_BS_SUNROOFRAINSETSTATU");
            i2 |= VEHICLESET_BS_SUNROOFRAINSETSTATU;
        }
        if ((826282796 & i) == 826282796) {
            arrayList.add("VEHICLESET_BS_TWICELOCKDOORSETSTATUS");
            i2 |= VEHICLESET_BS_TWICELOCKDOORSETSTATUS;
        }
        if ((826282797 & i) == 826282797) {
            arrayList.add("VEHICLESET_BS_GETOFFAUTOLOCK");
            i2 |= VEHICLESET_BS_GETOFFAUTOLOCK;
        }
        if ((826282798 & i) == 826282798) {
            arrayList.add("VEHICLESET_BS_SMARTWELCOM");
            i2 |= VEHICLESET_BS_SMARTWELCOM;
        }
        if ((826282799 & i) == 826282799) {
            arrayList.add("VEHICLESET_BS_WELCOMLOCK");
            i2 |= VEHICLESET_BS_WELCOMLOCK;
        }
        if ((826282800 & i) == 826282800) {
            arrayList.add("VEHICLESET_BS_SMARTTRUNKULOCKSTAUS");
            i2 |= VEHICLESET_BS_SMARTTRUNKULOCKSTAUS;
        }
        if ((826282801 & i) == 826282801) {
            arrayList.add("VEHICLESET_BS_EASYENTRYSET");
            i2 |= VEHICLESET_BS_EASYENTRYSET;
        }
        if ((826282802 & i) == 826282802) {
            arrayList.add("VEHICLESET_BS_MIRRORAUTOFOLDSET");
            i2 |= VEHICLESET_BS_MIRRORAUTOFOLDSET;
        }
        if ((826282803 & i) == 826282803) {
            arrayList.add("VEHICLESET_BS_MIRRORSETSTATUS");
            i2 |= VEHICLESET_BS_MIRRORSETSTATUS;
        }
        if ((826282804 & i) == 826282804) {
            arrayList.add("VEHICLESET_BS_REARWIPERSET");
            i2 |= VEHICLESET_BS_REARWIPERSET;
        }
        if ((826282805 & i) == 826282805) {
            arrayList.add("VEHICLESET_BS_REARSEATBELTWARNINGENABLE");
            i2 |= VEHICLESET_BS_REARSEATBELTWARNINGENABLE;
        }
        if ((826282806 & i) == 826282806) {
            arrayList.add("VEHICLESET_BS_LOWSPEEDPEDESTRIANWARN");
            i2 |= VEHICLESET_BS_LOWSPEEDPEDESTRIANWARN;
        }
        if ((826282807 & i) == 826282807) {
            arrayList.add("VEHICLESET_BS_TPMSRESETSTATUS");
            i2 |= VEHICLESET_BS_TPMSRESETSTATUS;
        }
        if ((826282808 & i) == 826282808) {
            arrayList.add("VEHICLESET_BS_DRIVINGMODEMEMORYENABLE");
            i2 |= VEHICLESET_BS_DRIVINGMODEMEMORYENABLE;
        }
        if ((826282809 & i) == 826282809) {
            arrayList.add("VEHICLESET_BS_LOCKAUTOCLOSEWINDOW");
            i2 |= VEHICLESET_BS_LOCKAUTOCLOSEWINDOW;
        }
        if ((826282810 & i) == 826282810) {
            arrayList.add("VEHICLESET_BS_WIPERSET");
            i2 |= VEHICLESET_BS_WIPERSET;
        }
        if ((826282811 & i) == 826282811) {
            arrayList.add("VEHICLESET_BS_SUNROOFSET");
            i2 |= VEHICLESET_BS_SUNROOFSET;
        }
        if ((826282812 & i) == 826282812) {
            arrayList.add("VEHICLESET_BS_TAILGATESET");
            i2 |= VEHICLESET_BS_TAILGATESET;
        }
        if ((826282813 & i) == 826282813) {
            arrayList.add("VEHICLESET_BS_SUNSHADESET");
            i2 |= VEHICLESET_BS_SUNSHADESET;
        }
        if ((826282814 & i) == 826282814) {
            arrayList.add("VEHICLESET_BS_RKESUNROOFCTRLTYPE");
            i2 |= VEHICLESET_BS_RKESUNROOFCTRLTYPE;
        }
        if ((826282815 & i) == 826282815) {
            arrayList.add("VEHICLESET_BS_SUNROOFOPENSET");
            i2 |= VEHICLESET_BS_SUNROOFOPENSET;
        }
        if ((826282816 & i) == 826282816) {
            arrayList.add("VEHICLESET_BS_SUNSHADEOPENSET");
            i2 |= VEHICLESET_BS_SUNSHADEOPENSET;
        }
        if ((826282817 & i) == 826282817) {
            arrayList.add("VEHICLESET_BS_MIRRORHEAT");
            i2 |= VEHICLESET_BS_MIRRORHEAT;
        }
        if ((826282818 & i) == 826282818) {
            arrayList.add("VEHICLESET_BS_WD");
            i2 |= VEHICLESET_BS_WD;
        }
        if ((826282819 & i) == 826282819) {
            arrayList.add("VEHICLESET_BS_DRIVEMODE");
            i2 |= VEHICLESET_BS_DRIVEMODE;
        }
        if ((826282820 & i) == 826282820) {
            arrayList.add("VEHICLESET_LS_ATMLIGHTBRIGHTSET");
            i2 |= VEHICLESET_LS_ATMLIGHTBRIGHTSET;
        }
        if ((826282821 & i) == 826282821) {
            arrayList.add("VEHICLESET_LS_ATMOLIGHTAAENABLE");
            i2 |= VEHICLESET_LS_ATMOLIGHTAAENABLE;
        }
        if ((826282822 & i) == 826282822) {
            arrayList.add("VEHICLESET_LS_ATMOLIGHTAUTOADJUSTSTS");
            i2 |= VEHICLESET_LS_ATMOLIGHTAUTOADJUSTSTS;
        }
        if ((826282823 & i) == 826282823) {
            arrayList.add("VEHICLESET_LS_ATMLIGHTCOLORSET");
            i2 |= VEHICLESET_LS_ATMLIGHTCOLORSET;
        }
        if ((826282824 & i) == 826282824) {
            arrayList.add("VEHICLESET_LS_ATMOLIGHTAUTOADJUST");
            i2 |= VEHICLESET_LS_ATMOLIGHTAUTOADJUST;
        }
        if ((826282825 & i) == 826282825) {
            arrayList.add("VEHICLESET_LS_WELCOMEATMOLIGHTENABLE");
            i2 |= VEHICLESET_LS_WELCOMEATMOLIGHTENABLE;
        }
        if ((826282826 & i) == 826282826) {
            arrayList.add("VEHICLESET_LS_ATMOLIGHTLINKOVERSPEEDENABLE");
            i2 |= VEHICLESET_LS_ATMOLIGHTLINKOVERSPEEDENABLE;
        }
        if ((826282827 & i) == 826282827) {
            arrayList.add("VEHICLESET_LS_ATMOLIGHTSPEEDENABLE");
            i2 |= VEHICLESET_LS_ATMOLIGHTSPEEDENABLE;
        }
        if ((826282828 & i) == 826282828) {
            arrayList.add("VEHICLESET_LS_ATMOLIGHTLINKTOPLIGHTENABLE");
            i2 |= VEHICLESET_LS_ATMOLIGHTLINKTOPLIGHTENABLE;
        }
        if ((826282829 & i) == 826282829) {
            arrayList.add("VEHICLESET_LS_ATMOLIGHTLINKDOORENABLE");
            i2 |= VEHICLESET_LS_ATMOLIGHTLINKDOORENABLE;
        }
        if ((826282830 & i) == 826282830) {
            arrayList.add("VEHICLESET_LS_SEWENABLE");
            i2 |= VEHICLESET_LS_SEWENABLE;
        }
        if ((826282831 & i) == 826282831) {
            arrayList.add("VEHICLESET_LS_ATMOLIGHTVEHICLEENABLE");
            i2 |= VEHICLESET_LS_ATMOLIGHTVEHICLEENABLE;
        }
        if ((826282832 & i) == 826282832) {
            arrayList.add("VEHICLESET_LS_AMBIENTLIGHTCOLORFEED");
            i2 |= VEHICLESET_LS_AMBIENTLIGHTCOLORFEED;
        }
        if ((826282833 & i) == 826282833) {
            arrayList.add("VEHICLESET_LS_WELCOMELIGHTSTAUS");
            i2 |= VEHICLESET_LS_WELCOMELIGHTSTAUS;
        }
        if ((826282834 & i) == 826282834) {
            arrayList.add("VEHICLESET_LS_DRLSET");
            i2 |= VEHICLESET_LS_DRLSET;
        }
        if ((826282835 & i) == 826282835) {
            arrayList.add("VEHICLESET_LS_HMAENABLE");
            i2 |= VEHICLESET_LS_HMAENABLE;
        }
        if ((826282836 & i) == 826282836) {
            arrayList.add("VEHICLESET_LS_FOLLOWMEHOMELIGHTSETSTATUS");
            i2 |= VEHICLESET_LS_FOLLOWMEHOMELIGHTSETSTATUS;
        }
        if ((826282837 & i) == 826282837) {
            arrayList.add("VEHICLESET_LS_LANECHANGETURNLIGHTSETSTATUS");
            i2 |= VEHICLESET_LS_LANECHANGETURNLIGHTSETSTATUS;
        }
        if ((826282838 & i) == 826282838) {
            arrayList.add("VEHICLESET_LS_COPILOTDISPMODE");
            i2 |= VEHICLESET_LS_COPILOTDISPMODE;
        }
        if ((826282839 & i) == 826282839) {
            arrayList.add("VEHICLESET_LS_ATMOLIGHTMODE");
            i2 |= VEHICLESET_LS_ATMOLIGHTMODE;
        }
        if ((826282840 & i) == 826282840) {
            arrayList.add("VEHICLESET_LS_ATMOLIGHTAREA");
            i2 |= VEHICLESET_LS_ATMOLIGHTAREA;
        }
        if ((826282841 & i) == 826282841) {
            arrayList.add("VEHICLESET_LS_WELCOMELIGHTTIMESET");
            i2 |= VEHICLESET_LS_WELCOMELIGHTTIMESET;
        }
        if ((826282842 & i) == 826282842) {
            arrayList.add("VEHICLESET_LS_ATMOLIGHTFUNCSW");
            i2 |= VEHICLESET_LS_ATMOLIGHTFUNCSW;
        }
        if ((826282843 & i) == 826282843) {
            arrayList.add("VEHICLESET_HUD_ENABLE");
            i2 |= VEHICLESET_HUD_ENABLE;
        }
        if ((826282844 & i) == 826282844) {
            arrayList.add("VEHICLESET_HUD_POSITIONSET");
            i2 |= VEHICLESET_HUD_POSITIONSET;
        }
        if ((826282845 & i) == 826282845) {
            arrayList.add("VEHICLESET_HUD_LIGHTSET");
            i2 |= VEHICLESET_HUD_LIGHTSET;
        }
        if ((826282846 & i) == 826282846) {
            arrayList.add("VEHICLESET_HUD_LANGUAGE");
            i2 |= VEHICLESET_HUD_LANGUAGE;
        }
        if ((826282847 & i) == 826282847) {
            arrayList.add("VEHICLESET_HUD_DISPLAY");
            i2 |= VEHICLESET_HUD_DISPLAY;
        }
        if ((826282848 & i) == 826282848) {
            arrayList.add("VEHICLESET_HUD_HEIGHT");
            i2 |= VEHICLESET_HUD_HEIGHT;
        }
        if ((826282849 & i) == 826282849) {
            arrayList.add("VEHICLESET_IP_WELCOMESOUNDSET");
            i2 |= VEHICLESET_IP_WELCOMESOUNDSET;
        }
        if ((826282850 & i) == 826282850) {
            arrayList.add("VEHICLESET_IP_MESSAGEALERTSOUNDSET");
            i2 |= VEHICLESET_IP_MESSAGEALERTSOUNDSET;
        }
        if ((826282851 & i) == 826282851) {
            arrayList.add("VEHICLESET_IP_ALARMSOUNDSET");
            i2 |= VEHICLESET_IP_ALARMSOUNDSET;
        }
        if ((826282852 & i) == 826282852) {
            arrayList.add("VEHICLESET_IP_WARNINGVOLUMESETTING");
            i2 |= VEHICLESET_IP_WARNINGVOLUMESETTING;
        }
        if ((826282853 & i) == 826282853) {
            arrayList.add("VEHICLESET_LS_ATMLIGHTBRIGHTNESSCFG");
            i2 |= VEHICLESET_LS_ATMLIGHTBRIGHTNESSCFG;
        }
        if ((826282854 & i) == 826282854) {
            arrayList.add("VEHICLESET_LS_ATMLIGHTCOLORCFG");
            i2 |= VEHICLESET_LS_ATMLIGHTCOLORCFG;
        }
        if ((826282855 & i) == 826282855) {
            arrayList.add("VEHICLESET_LS_ATMLIGHTINSTRUMENTDESKCOLOR");
            i2 |= VEHICLESET_LS_ATMLIGHTINSTRUMENTDESKCOLOR;
        }
        if ((826282856 & i) == 826282856) {
            arrayList.add("VEHICLESET_LS_ATMLIGHTOTHERCOLOR");
            i2 |= VEHICLESET_LS_ATMLIGHTOTHERCOLOR;
        }
        if ((826282857 & i) == 826282857) {
            arrayList.add("VEHICLESET_LS_ATMLIGHTMUSICMODE");
            i2 |= VEHICLESET_LS_ATMLIGHTMUSICMODE;
        }
        if ((826283008 & i) == 826283008) {
            arrayList.add("SEAT_INFO");
            i2 |= SEAT_INFO;
        }
        if ((826283009 & i) == 826283009) {
            arrayList.add("DSM_POWERSTATUS");
            i2 |= DSM_POWERSTATUS;
        }
        if ((826283010 & i) == 826283010) {
            arrayList.add("DSM_INITSTATUS");
            i2 |= DSM_INITSTATUS;
        }
        if ((826283011 & i) == 826283011) {
            arrayList.add("DSM_RECALLSTATUS");
            i2 |= DSM_RECALLSTATUS;
        }
        if ((826283012 & i) == 826283012) {
            arrayList.add("DSM_FH_UP");
            i2 |= DSM_FH_UP;
        }
        if ((826283013 & i) == 826283013) {
            arrayList.add("DSM_FH_DN");
            i2 |= DSM_FH_DN;
        }
        if ((826283014 & i) == 826283014) {
            arrayList.add("DSM_RH_UP");
            i2 |= DSM_RH_UP;
        }
        if ((826283015 & i) == 826283015) {
            arrayList.add("DSM_RH_DN");
            i2 |= DSM_RH_DN;
        }
        if ((826283016 & i) == 826283016) {
            arrayList.add("DSM_SLIDE_FW");
            i2 |= DSM_SLIDE_FW;
        }
        if ((826283017 & i) == 826283017) {
            arrayList.add("DSM_SLIDE_BW");
            i2 |= DSM_SLIDE_BW;
        }
        if ((826283018 & i) == 826283018) {
            arrayList.add("DSM_BR_FW");
            i2 |= DSM_BR_FW;
        }
        if ((826283019 & i) == 826283019) {
            arrayList.add("DSM_BR_BW");
            i2 |= DSM_BR_BW;
        }
        if ((826283020 & i) == 826283020) {
            arrayList.add("DSM_LEFTBATVOLTSTS");
            i2 |= DSM_LEFTBATVOLTSTS;
        }
        if ((826283021 & i) == 826283021) {
            arrayList.add("DSM_RIGHTBATVOLTSTS");
            i2 |= DSM_RIGHTBATVOLTSTS;
        }
        if ((826283022 & i) == 826283022) {
            arrayList.add("DSM_LEFTHEATABNORMAL");
            i2 |= DSM_LEFTHEATABNORMAL;
        }
        if ((826283023 & i) == 826283023) {
            arrayList.add("DSM_RIGHTHEATABNORMAL");
            i2 |= DSM_RIGHTHEATABNORMAL;
        }
        if ((826283024 & i) == 826283024) {
            arrayList.add("DSM_DRVKNEADSTS");
            i2 |= DSM_DRVKNEADSTS;
        }
        if ((826283025 & i) == 826283025) {
            arrayList.add("DSM_COPILOTKNEADSTS");
            i2 |= DSM_COPILOTKNEADSTS;
        }
        if ((826283026 & i) == 826283026) {
            arrayList.add("SEAT_COMMAND_FID_DRIVE_HEAT");
            i2 |= SEAT_COMMAND_FID_DRIVE_HEAT;
        }
        if ((826283027 & i) == 826283027) {
            arrayList.add("SEAT_COMMAND_FID_PASS_HEAT");
            i2 |= SEAT_COMMAND_FID_PASS_HEAT;
        }
        if ((826283028 & i) == 826283028) {
            arrayList.add("SEAT_COMMAND_FID_DRIVE_VENT");
            i2 |= SEAT_COMMAND_FID_DRIVE_VENT;
        }
        if ((826283029 & i) == 826283029) {
            arrayList.add("SEAT_COMMAND_FID_PASS_VENT");
            i2 |= SEAT_COMMAND_FID_PASS_VENT;
        }
        if ((826283030 & i) == 826283030) {
            arrayList.add("SEAT_COMMAND_FID_DRIVE_SET1");
            i2 |= SEAT_COMMAND_FID_DRIVE_SET1;
        }
        if ((826283031 & i) == 826283031) {
            arrayList.add("SEAT_COMMAND_FID_DRIVE_SET2");
            i2 |= SEAT_COMMAND_FID_DRIVE_SET2;
        }
        if ((826283032 & i) == 826283032) {
            arrayList.add("SEAT_COMMAND_FID_PASS_SET1");
            i2 |= SEAT_COMMAND_FID_PASS_SET1;
        }
        if ((826283033 & i) == 826283033) {
            arrayList.add("SEAT_COMMAND_FID_PASS_SET2");
            i2 |= SEAT_COMMAND_FID_PASS_SET2;
        }
        if ((826283264 & i) == 826283264) {
            arrayList.add("RADAR_STATE_FL");
            i2 |= RADAR_STATE_FL;
        }
        if ((826283265 & i) == 826283265) {
            arrayList.add("RADAR_STATE_FLM");
            i2 |= RADAR_STATE_FLM;
        }
        if ((826283266 & i) == 826283266) {
            arrayList.add("RADAR_STATE_FR");
            i2 |= RADAR_STATE_FR;
        }
        if ((826283267 & i) == 826283267) {
            arrayList.add("RADAR_STATE_FRM");
            i2 |= RADAR_STATE_FRM;
        }
        if ((826283268 & i) == 826283268) {
            arrayList.add("RADAR_STATE_RL");
            i2 |= RADAR_STATE_RL;
        }
        if ((826283269 & i) == 826283269) {
            arrayList.add("RADAR_STATE_RLM");
            i2 |= RADAR_STATE_RLM;
        }
        if ((826283270 & i) == 826283270) {
            arrayList.add("RADAR_STATE_RR");
            i2 |= RADAR_STATE_RR;
        }
        if ((826283271 & i) == 826283271) {
            arrayList.add("RADAR_STATE_RRM");
            i2 |= RADAR_STATE_RRM;
        }
        if ((826283272 & i) == 826283272) {
            arrayList.add("RADAR_DATA_FL");
            i2 |= RADAR_DATA_FL;
        }
        if ((826283273 & i) == 826283273) {
            arrayList.add("RADAR_DATA_FLM");
            i2 |= RADAR_DATA_FLM;
        }
        if ((826283274 & i) == 826283274) {
            arrayList.add("RADAR_DATA_FR");
            i2 |= RADAR_DATA_FR;
        }
        if ((826283275 & i) == 826283275) {
            arrayList.add("RADAR_DATA_FRM");
            i2 |= RADAR_DATA_FRM;
        }
        if ((826283276 & i) == 826283276) {
            arrayList.add("RADAR_DATA_RL");
            i2 |= RADAR_DATA_RL;
        }
        if ((826283277 & i) == 826283277) {
            arrayList.add("RADAR_DATA_RLM");
            i2 |= RADAR_DATA_RLM;
        }
        if ((826283278 & i) == 826283278) {
            arrayList.add("RADAR_DATA_RR");
            i2 |= RADAR_DATA_RR;
        }
        if ((826283279 & i) == 826283279) {
            arrayList.add("RADAR_DATA_RRM");
            i2 |= RADAR_DATA_RRM;
        }
        if ((826283280 & i) == 826283280) {
            arrayList.add("RADAR_PDC_BUZZERALARMPATTERN");
            i2 |= RADAR_PDC_BUZZERALARMPATTERN;
        }
        if ((826283281 & i) == 826283281) {
            arrayList.add("RADAR_PDC_MODESTATUS");
            i2 |= RADAR_PDC_MODESTATUS;
        }
        if ((826283282 & i) == 826283282) {
            arrayList.add("RADAR_PDC_LED");
            i2 |= RADAR_PDC_LED;
        }
        if ((826283283 & i) == 826283283) {
            arrayList.add("RADAR_PDC_ECUFAULT");
            i2 |= RADAR_PDC_ECUFAULT;
        }
        if ((826283284 & i) == 826283284) {
            arrayList.add("RADAR_COMMAND_RR_SWITCH_STATE");
            i2 |= RADAR_COMMAND_RR_SWITCH_STATE;
        }
        if ((826283285 & i) == 826283285) {
            arrayList.add("RADAR_COMMAND_RR_BEEP_FRE");
            i2 |= RADAR_COMMAND_RR_BEEP_FRE;
        }
        if ((826283520 & i) == 826283520) {
            arrayList.add("VEHICLE_DOOR_FID_LF_DOOR");
            i2 |= VEHICLE_DOOR_FID_LF_DOOR;
        }
        if ((826283521 & i) == 826283521) {
            arrayList.add("VEHICLE_DOOR_FID_RF_DOOR");
            i2 |= VEHICLE_DOOR_FID_RF_DOOR;
        }
        if ((826283522 & i) == 826283522) {
            arrayList.add("VEHICLE_DOOR_FID_LR_DOOR");
            i2 |= VEHICLE_DOOR_FID_LR_DOOR;
        }
        if ((826283523 & i) == 826283523) {
            arrayList.add("VEHICLE_DOOR_FID_RR_DOOR");
            i2 |= VEHICLE_DOOR_FID_RR_DOOR;
        }
        if ((826283524 & i) == 826283524) {
            arrayList.add("VEHICLE_DOOR_FID_ENGINE_DOOR");
            i2 |= VEHICLE_DOOR_FID_ENGINE_DOOR;
        }
        if ((826283525 & i) == 826283525) {
            arrayList.add("VEHICLE_DOOR_FID_BACK_DOOR");
            i2 |= VEHICLE_DOOR_FID_BACK_DOOR;
        }
        if ((826283526 & i) == 826283526) {
            arrayList.add("VEHICLE_WINDOW_FID_LF_WINDOW");
            i2 |= VEHICLE_WINDOW_FID_LF_WINDOW;
        }
        if ((826283527 & i) == 826283527) {
            arrayList.add("VEHICLE_WINDOW_FID_RF_WINDOW");
            i2 |= VEHICLE_WINDOW_FID_RF_WINDOW;
        }
        if ((826283528 & i) == 826283528) {
            arrayList.add("VEHICLE_WINDOW_FID_LR_WINDOW");
            i2 |= VEHICLE_WINDOW_FID_LR_WINDOW;
        }
        if ((826283529 & i) == 826283529) {
            arrayList.add("VEHICLE_WINDOW_FID_RR_WINDOW");
            i2 |= VEHICLE_WINDOW_FID_RR_WINDOW;
        }
        if ((826283530 & i) == 826283530) {
            arrayList.add("VEHICLE_WINDOW_FID_SUNROOF");
            i2 |= VEHICLE_WINDOW_FID_SUNROOF;
        }
        if ((826283531 & i) == 826283531) {
            arrayList.add("VEHICLE_SPEED_FID_INFO");
            i2 |= VEHICLE_SPEED_FID_INFO;
        }
        if ((826283532 & i) == 826283532) {
            arrayList.add("VEHICLE_POWER_FID_STATUS");
            i2 |= VEHICLE_POWER_FID_STATUS;
        }
        if ((826283533 & i) == 826283533) {
            arrayList.add("VEHICLE_FID_STEE_ANGLE");
            i2 |= VEHICLE_FID_STEE_ANGLE;
        }
        if ((826283534 & i) == 826283534) {
            arrayList.add("VEHICLE_GEAR_FID_POSITION");
            i2 |= VEHICLE_GEAR_FID_POSITION;
        }
        if ((826283535 & i) == 826283535) {
            arrayList.add("VEHICLE_WIRELESS_CHARGE_CTRL");
            i2 |= VEHICLE_WIRELESS_CHARGE_CTRL;
        }
        if ((826283536 & i) == 826283536) {
            arrayList.add("VEHICLE_WIRELESS_CHARGE_CHARGINGSTATUS");
            i2 |= VEHICLE_WIRELESS_CHARGE_CHARGINGSTATUS;
        }
        if ((826283537 & i) == 826283537) {
            arrayList.add("VEHICLE_WIRELESS_CHARGE_PHONEREMINDER");
            i2 |= VEHICLE_WIRELESS_CHARGE_PHONEREMINDER;
        }
        if ((826283538 & i) == 826283538) {
            arrayList.add("VEHICLE_WIRELESS_CHARGE_SHUTDOWNFEEDBACK");
            i2 |= VEHICLE_WIRELESS_CHARGE_SHUTDOWNFEEDBACK;
        }
        if ((826283539 & i) == 826283539) {
            arrayList.add("VEHICLE_WIRELESS_CHARGE_SWMEMORYSTS");
            i2 |= VEHICLE_WIRELESS_CHARGE_SWMEMORYSTS;
        }
        if ((826283540 & i) == 826283540) {
            arrayList.add("VEHICLE_WIRELESS_CHARGE_FODWARMING");
            i2 |= VEHICLE_WIRELESS_CHARGE_FODWARMING;
        }
        if ((826283541 & i) == 826283541) {
            arrayList.add("VEHICLE_WIRELESS_CHARGINGERR");
            i2 |= VEHICLE_WIRELESS_CHARGINGERR;
        }
        if ((826349312 & i) == 826349312) {
            arrayList.add("IP_CURRENT_SRC");
            i2 |= IP_CURRENT_SRC;
        }
        if ((826283777 & i) == 826283777) {
            arrayList.add("IP_THEME");
            i2 |= IP_THEME;
        }
        if ((826283778 & i) == 826283778) {
            arrayList.add("IP_CURRENT_IMAGE");
            i2 |= IP_CURRENT_IMAGE;
        }
        if ((826283779 & i) == 826283779) {
            arrayList.add("IP_UPDATE_REQ");
            i2 |= IP_UPDATE_REQ;
        }
        if ((826283780 & i) == 826283780) {
            arrayList.add("IP_USB_LOAD");
            i2 |= IP_USB_LOAD;
        }
        if ((826283781 & i) == 826283781) {
            arrayList.add("IP_UPDATE_COPY");
            i2 |= IP_UPDATE_COPY;
        }
        if ((826283782 & i) == 826283782) {
            arrayList.add("IP_UPDATE_CHECK");
            i2 |= IP_UPDATE_CHECK;
        }
        if ((826283783 & i) == 826283783) {
            arrayList.add("IP_KEY_STATUS");
            i2 |= IP_KEY_STATUS;
        }
        if ((826283784 & i) == 826283784) {
            arrayList.add("IP_DISP_THEME_LINK");
            i2 |= IP_DISP_THEME_LINK;
        }
        if ((826283785 & i) == 826283785) {
            arrayList.add("IP_DISP_SYNC");
            i2 |= IP_DISP_SYNC;
        }
        if ((826284032 & i) == 826284032) {
            arrayList.add("DVR3RD_RECORDLOCK");
            i2 |= DVR3RD_RECORDLOCK;
        }
        if ((826284033 & i) == 826284033) {
            arrayList.add("DVR3RD_UPDATESTATUS");
            i2 |= DVR3RD_UPDATESTATUS;
        }
        if ((826284034 & i) == 826284034) {
            arrayList.add("DVR3RD_PLAYSTATE");
            i2 |= DVR3RD_PLAYSTATE;
        }
        if ((826284035 & i) == 826284035) {
            arrayList.add("DVR3RD_SDCARDSTATUS");
            i2 |= DVR3RD_SDCARDSTATUS;
        }
        if ((826284036 & i) == 826284036) {
            arrayList.add("DVR3RD_TIMEDISMODE");
            i2 |= DVR3RD_TIMEDISMODE;
        }
        if ((826349573 & i) == 826349573) {
            arrayList.add("DVR3RD_TIMEINFO");
            i2 |= DVR3RD_TIMEINFO;
        }
        if ((826284038 & i) == 826284038) {
            arrayList.add("DVR3RD_VIDEOPLAYSTATUS");
            i2 |= DVR3RD_VIDEOPLAYSTATUS;
        }
        if ((826284039 & i) == 826284039) {
            arrayList.add("DVR3RD_MODESTATUSREQ");
            i2 |= DVR3RD_MODESTATUSREQ;
        }
        if ((826284040 & i) == 826284040) {
            arrayList.add("DVR3RD_HARDKEYCMD");
            i2 |= DVR3RD_HARDKEYCMD;
        }
        if ((826284041 & i) == 826284041) {
            arrayList.add("DVR3RD_RESOLUTION");
            i2 |= DVR3RD_RESOLUTION;
        }
        if ((826284042 & i) == 826284042) {
            arrayList.add("DVR3RD_TIMEDISFORMAT");
            i2 |= DVR3RD_TIMEDISFORMAT;
        }
        if ((826284043 & i) == 826284043) {
            arrayList.add("DVR3RD_RECIRCLERECORDTIME");
            i2 |= DVR3RD_RECIRCLERECORDTIME;
        }
        if ((826284044 & i) == 826284044) {
            arrayList.add("DVR3RD_RECORDVOICE");
            i2 |= DVR3RD_RECORDVOICE;
        }
        if ((826284045 & i) == 826284045) {
            arrayList.add("DVR3RD_LANGUAGE");
            i2 |= DVR3RD_LANGUAGE;
        }
        if ((826284046 & i) == 826284046) {
            arrayList.add("DVR3RD_GRAVITYSENSOR");
            i2 |= DVR3RD_GRAVITYSENSOR;
        }
        if ((826284047 & i) == 826284047) {
            arrayList.add("DVR3RD_LIGHTFREQUENCY");
            i2 |= DVR3RD_LIGHTFREQUENCY;
        }
        if ((826284048 & i) == 826284048) {
            arrayList.add("DVR3RD_DATEFORMAT");
            i2 |= DVR3RD_DATEFORMAT;
        }
        if ((826349585 & i) == 826349585) {
            arrayList.add("DVR3RD_SYSTEMDATE");
            i2 |= DVR3RD_SYSTEMDATE;
        }
        if ((826349586 & i) == 826349586) {
            arrayList.add("DVR3RD_SYSTEMTIME");
            i2 |= DVR3RD_SYSTEMTIME;
        }
        if ((826349587 & i) == 826349587) {
            arrayList.add("DVR3RD_VERSIONINFO");
            i2 |= DVR3RD_VERSIONINFO;
        }
        if ((826284052 & i) == 826284052) {
            arrayList.add("DVR3RD_FORMATINFO");
            i2 |= DVR3RD_FORMATINFO;
        }
        if ((826284053 & i) == 826284053) {
            arrayList.add("DVR3RD_PARMSYSRESET");
            i2 |= DVR3RD_PARMSYSRESET;
        }
        if ((826349590 & i) == 826349590) {
            arrayList.add("DVR3RD_DATETIME");
            i2 |= DVR3RD_DATETIME;
        }
        if ((826349591 & i) == 826349591) {
            arrayList.add("DVR3RD_ALLPARMSSTATE");
            i2 |= DVR3RD_ALLPARMSSTATE;
        }
        if ((826349592 & i) == 826349592) {
            arrayList.add("DVR3RD_FILE_NAME");
            i2 |= DVR3RD_FILE_NAME;
        }
        if ((826284057 & i) == 826284057) {
            arrayList.add("DVR3RD_FILELISTINFO");
            i2 |= DVR3RD_FILELISTINFO;
        }
        if ((826349594 & i) == 826349594) {
            arrayList.add("DVR3RD_PLAYBYNAME");
            i2 |= DVR3RD_PLAYBYNAME;
        }
        if ((826284059 & i) == 826284059) {
            arrayList.add("DVR3RD_SHUTDOWN");
            i2 |= DVR3RD_SHUTDOWN;
        }
        if ((826349596 & i) == 826349596) {
            arrayList.add("DVR3RD_CURRECORDTIME");
            i2 |= DVR3RD_CURRECORDTIME;
        }
        if ((826349597 & i) == 826349597) {
            arrayList.add("DVR3RD_DVR_CURPLAYTIME");
            i2 |= DVR3RD_DVR_CURPLAYTIME;
        }
        if ((826349598 & i) == 826349598) {
            arrayList.add("DVR3RD_TOTALTIME");
            i2 |= DVR3RD_TOTALTIME;
        }
        if ((826349824 & i) == 826349824) {
            arrayList.add("DISP_SETTING_FID_SCENE_MODE");
            i2 |= DISP_SETTING_FID_SCENE_MODE;
        }
        if ((826283542 & i) == 826283542) {
            arrayList.add("VEHICLE_WINDOW_FID_ALL_WINDOW");
            i2 |= VEHICLE_WINDOW_FID_ALL_WINDOW;
        }
        if ((826283543 & i) == 826283543) {
            arrayList.add("VEHICLE_LIGH_FID_LOW_BEAM");
            i2 |= VEHICLE_LIGH_FID_LOW_BEAM;
        }
        if ((826283544 & i) == 826283544) {
            arrayList.add("VEHICLE_LIGH_FID_HIGH_BEAM");
            i2 |= VEHICLE_LIGH_FID_HIGH_BEAM;
        }
        if ((826283545 & i) == 826283545) {
            arrayList.add("VEHICLE_LIGH_FID_FRONT_FOG_LAMP");
            i2 |= VEHICLE_LIGH_FID_FRONT_FOG_LAMP;
        }
        if ((826283546 & i) == 826283546) {
            arrayList.add("VEHICLE_LIGH_FID_REAR_FOG_LAMP");
            i2 |= VEHICLE_LIGH_FID_REAR_FOG_LAMP;
        }
        if ((826283547 & i) == 826283547) {
            arrayList.add("VEHICLE_LIGH_FID_TURN_INDICATOR_LEFT");
            i2 |= VEHICLE_LIGH_FID_TURN_INDICATOR_LEFT;
        }
        if ((826283548 & i) == 826283548) {
            arrayList.add("VEHICLE_LIGH_FID_TURN_INDICATOR_RIGHT");
            i2 |= VEHICLE_LIGH_FID_TURN_INDICATOR_RIGHT;
        }
        if ((826283549 & i) == 826283549) {
            arrayList.add("VEHICLE_WINDOW_FID_SHADE");
            i2 |= VEHICLE_WINDOW_FID_SHADE;
        }
        if ((826282004 & i) == 826282004) {
            arrayList.add("AUDIO_MUTE");
            i2 |= AUDIO_MUTE;
        }
        if (i != i2) {
            arrayList.add("0x" + Integer.toHexString(i & (~i2)));
        }
        return String.join(" | ", arrayList);
    }
}
