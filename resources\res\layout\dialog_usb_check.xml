<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/transparent"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <com.incall.apps.caui.layout.CAUIConstraintLayout
        android:layout_gravity="center"
        android:background="@color/transparent"
        android:layout_width="880dp"
        android:layout_height="458dp">
        <TextView
            android:textSize="44sp"
            android:textColor="@color/caui_config_text_color_primary"
            android:gravity="center"
            android:id="@+id/tv_dialog_confirm_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:text="@string/dialog_usb_downgrade_title"
            app:layout_constraintTop_toTopOf="parent"/>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_confirm_top"
            android:background="@color/transparent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:layout_marginBottom="44dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_dialog_confirm_title">
            <TextView
                android:textSize="36sp"
                android:textColor="@color/caui_config_text_color_secondary"
                android:id="@+id/tv_random_show"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="54dp"
                android:layout_marginRight="54dp"
                android:text="@string/dialog_usb_downgrade_tip"
                android:lineSpacingMultiplier="1.2"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <EditText
                android:textSize="36sp"
                android:textColor="@color/caui_default_input_text_color"
                android:id="@+id/edit_text_random_input"
                android:background="@color/caui_config_input_bg_color_primary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="54dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="54dp"
                app:layout_constraintTop_toBottomOf="@+id/tv_random_show"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
            android:textSize="36sp"
            android:gravity="center"
            android:id="@+id/btn_dialog_random_yes"
            android:layout_width="344dp"
            android:layout_height="96dp"
            android:layout_marginBottom="56dp"
            android:text="@string/dialog_usb_new_version_confirm"
            android:layout_marginStart="64dp"
            app:caui_round_btn_type="main"
            app:caui_round_radius="@dimen/caui_config_corner_radius_small"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>
        <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
            android:textSize="36sp"
            android:gravity="center"
            android:id="@+id/btn_dialog_random_no"
            android:layout_width="344dp"
            android:layout_height="96dp"
            android:layout_marginBottom="56dp"
            android:text="@string/dialog_usb_new_version_cancel"
            android:layout_marginEnd="64dp"
            app:caui_round_btn_type="secondary"
            app:caui_round_radius="@dimen/caui_config_corner_radius_small"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>
    </com.incall.apps.caui.layout.CAUIConstraintLayout>
</FrameLayout>
