package com.ca.car.proxy.hardware.defs;

import android.annotation.SystemApi;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

@SystemApi
/* loaded from: classes.dex */
public final class VehicleUnit {
    public static final int AMPERE_HOURS = 100;
    public static final int BAR = 114;
    public static final int CELSIUS = 48;
    public static final int DEGREES = 128;
    public static final int FAHRENHEIT = 49;
    public static final int HERTZ = 3;
    public static final int IMPERIAL_GALLON = 67;
    public static final int KELVIN = 50;
    public static final int KILOMETER = 35;
    public static final int KILOMETERS_PER_HOUR = 145;
    public static final int KILOPASCAL = 112;
    public static final int KILOWATT_HOUR = 101;
    public static final int LITER = 65;
    public static final int METER = 33;
    public static final int METER_PER_SEC = 1;
    public static final int MILE = 36;
    public static final int MILES_PER_HOUR = 144;
    public static final int MILLIAMPERE = 97;
    public static final int MILLILITER = 64;
    public static final int MILLIMETER = 32;
    public static final int MILLIVOLT = 98;
    public static final int MILLIWATTS = 99;
    public static final int NANO_SECS = 80;
    public static final int PERCENTILE = 16;
    public static final int PSI = 113;
    public static final int RPM = 2;
    public static final int SECS = 83;
    public static final int SHOULD_NOT_USE = 0;
    public static final int US_GALLON = 66;
    public static final int WATT_HOUR = 96;
    public static final int YEAR = 89;

    @Retention(RetentionPolicy.SOURCE)
    public @interface Enum {
    }

    private VehicleUnit() {
    }
}
