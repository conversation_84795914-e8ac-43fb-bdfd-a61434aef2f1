<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    android:background="@color/transparent"
    android:layout_width="880dp"
    android:layout_height="wrap_content">
    <ImageView
        android:id="@+id/wifi_dialog_iv"
        android:background="@drawable/caui_icon_dialog_close"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="55dp"
        android:layout_marginStart="64dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="40sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/wifi_download_dialog_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="48dp"
        android:text="@string/wifi_download_confirm_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="32sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:gravity="center_horizontal"
        android:id="@+id/wifi_download_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="@string/dialog_download_confirm_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/wifi_download_dialog_title"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/btn_download"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="48dp"
        android:layout_marginBottom="56dp"
        android:text="@string/dialog_download_confirm"
        android:layout_marginStart="64dp"
        app:caui_round_btn_type="main"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/wifi_download_content"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/btn_cancel"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="48dp"
        android:layout_marginBottom="56dp"
        android:text="@string/dialog_download_cancel"
        android:layout_marginEnd="64dp"
        app:caui_round_btn_type="negative"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/wifi_download_content"/>
</androidx.constraintlayout.widget.ConstraintLayout>
