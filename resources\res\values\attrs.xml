<?xml version="1.0" encoding="utf-8"?>
<resources>
    <attr name="CAUIButtonStyle" format="reference">
    </attr>
    <attr name="CAUIColorSliderStyle" format="reference">
    </attr>
    <attr name="CAUICommonCardLayoutStyle" format="reference">
    </attr>
    <attr name="CAUICommonItemViewStyle" format="reference">
    </attr>
    <attr name="CAUILoadingStyle" format="reference">
    </attr>
    <attr name="CAUIRadiusImageViewStyle" format="reference">
    </attr>
    <attr name="CAUISliderStyle" format="reference">
    </attr>
    <attr name="CAUITipNewStyle" format="reference">
    </attr>
    <attr name="CAUITipPointStyle" format="reference">
    </attr>
    <attr name="CAUITopBarStyle" format="reference">
    </attr>
    <attr name="CAUIVehicleCardViewStyle" format="reference">
    </attr>
    <attr name="CAUIWheelStyle" format="reference">
    </attr>
    <attr name="DrawableTextLayoutStyle" format="reference">
    </attr>
    <attr name="MultiLineEditTextStyle" format="reference">
    </attr>
    <attr name="SharedValue" format="integer">
    </attr>
    <attr name="SharedValueId" format="reference">
    </attr>
    <attr name="VerifyCodeEditTextStyle" format="reference">
    </attr>
    <attr name="actionBarDivider" format="reference">
    </attr>
    <attr name="actionBarItemBackground" format="reference">
    </attr>
    <attr name="actionBarPopupTheme" format="reference">
    </attr>
    <attr name="actionBarSize" format="dimension">
        <enum name="wrap_content" value="0" />
    </attr>
    <attr name="actionBarSplitStyle" format="reference">
    </attr>
    <attr name="actionBarStyle" format="reference">
    </attr>
    <attr name="actionBarTabBarStyle" format="reference">
    </attr>
    <attr name="actionBarTabStyle" format="reference">
    </attr>
    <attr name="actionBarTabTextStyle" format="reference">
    </attr>
    <attr name="actionBarTheme" format="reference">
    </attr>
    <attr name="actionBarWidgetTheme" format="reference">
    </attr>
    <attr name="actionButtonStyle" format="reference">
    </attr>
    <attr name="actionDropDownStyle" format="reference">
    </attr>
    <attr name="actionLayout" format="reference">
    </attr>
    <attr name="actionMenuTextAppearance" format="reference">
    </attr>
    <attr name="actionMenuTextColor" format="reference|color">
    </attr>
    <attr name="actionModeBackground" format="reference">
    </attr>
    <attr name="actionModeCloseButtonStyle" format="reference">
    </attr>
    <attr name="actionModeCloseContentDescription" format="string">
    </attr>
    <attr name="actionModeCloseDrawable" format="reference">
    </attr>
    <attr name="actionModeCopyDrawable" format="reference">
    </attr>
    <attr name="actionModeCutDrawable" format="reference">
    </attr>
    <attr name="actionModeFindDrawable" format="reference">
    </attr>
    <attr name="actionModePasteDrawable" format="reference">
    </attr>
    <attr name="actionModePopupWindowStyle" format="reference">
    </attr>
    <attr name="actionModeSelectAllDrawable" format="reference">
    </attr>
    <attr name="actionModeShareDrawable" format="reference">
    </attr>
    <attr name="actionModeSplitBackground" format="reference">
    </attr>
    <attr name="actionModeStyle" format="reference">
    </attr>
    <attr name="actionModeTheme" format="reference">
    </attr>
    <attr name="actionModeWebSearchDrawable" format="reference">
    </attr>
    <attr name="actionOverflowButtonStyle" format="reference">
    </attr>
    <attr name="actionOverflowMenuStyle" format="reference">
    </attr>
    <attr name="actionProviderClass" format="string">
    </attr>
    <attr name="actionTextColorAlpha" format="float">
    </attr>
    <attr name="actionViewClass" format="string">
    </attr>
    <attr name="activityChooserViewStyle" format="reference">
    </attr>
    <attr name="alertDialogButtonGroupStyle" format="reference">
    </attr>
    <attr name="alertDialogCenterButtons" format="boolean">
    </attr>
    <attr name="alertDialogStyle" format="reference">
    </attr>
    <attr name="alertDialogTheme" format="reference">
    </attr>
    <attr name="allowStacking" format="boolean">
    </attr>
    <attr name="alpha" format="float">
    </attr>
    <attr name="alphabeticModifiers">
        <flag name="ALT" value="0x2" />
        <flag name="CTRL" value="0x1000" />
        <flag name="FUNCTION" value="0x8" />
        <flag name="META" value="0x10000" />
        <flag name="SHIFT" value="0x1" />
        <flag name="SYM" value="0x4" />
    </attr>
    <attr name="altSrc" format="reference">
    </attr>
    <attr name="animateCircleAngleTo">
        <enum name="antiClockwise" value="3" />
        <enum name="bestChoice" value="0" />
        <enum name="clockwise" value="2" />
        <enum name="closest" value="1" />
        <enum name="constraint" value="4" />
    </attr>
    <attr name="animateRelativeTo" format="reference">
    </attr>
    <attr name="animationMode">
        <enum name="fade" value="1" />
        <enum name="slide" value="0" />
    </attr>
    <attr name="appBarLayoutStyle" format="reference">
    </attr>
    <attr name="applyMotionScene" format="boolean">
    </attr>
    <attr name="arcMode">
        <enum name="flip" value="2" />
        <enum name="startHorizontal" value="1" />
        <enum name="startVertical" value="0" />
    </attr>
    <attr name="arrowHeadLength" format="dimension">
    </attr>
    <attr name="arrowShaftLength" format="dimension">
    </attr>
    <attr name="attributeName" format="string">
    </attr>
    <attr name="autoCompleteMode">
        <enum name="continuousVelocity" value="0" />
        <enum name="spring" value="1" />
    </attr>
    <attr name="autoCompleteTextViewStyle" format="reference">
    </attr>
    <attr name="autoSizeMaxTextSize" format="dimension">
    </attr>
    <attr name="autoSizeMinTextSize" format="dimension">
    </attr>
    <attr name="autoSizePresetSizes" format="reference">
    </attr>
    <attr name="autoSizeStepGranularity" format="dimension">
    </attr>
    <attr name="autoSizeTextType">
        <enum name="none" value="0" />
        <enum name="uniform" value="1" />
    </attr>
    <attr name="autoTransition">
        <enum name="animateToEnd" value="4" />
        <enum name="animateToStart" value="3" />
        <enum name="jumpToEnd" value="2" />
        <enum name="jumpToStart" value="1" />
        <enum name="none" value="0" />
    </attr>
    <attr name="background" format="reference">
    </attr>
    <attr name="backgroundColor" format="color">
    </attr>
    <attr name="backgroundInsetBottom" format="dimension">
    </attr>
    <attr name="backgroundInsetEnd" format="dimension">
    </attr>
    <attr name="backgroundInsetStart" format="dimension">
    </attr>
    <attr name="backgroundInsetTop" format="dimension">
    </attr>
    <attr name="backgroundOverlayColorAlpha" format="float">
    </attr>
    <attr name="backgroundSplit" format="reference|color">
    </attr>
    <attr name="backgroundStacked" format="reference|color">
    </attr>
    <attr name="backgroundTint" format="color">
    </attr>
    <attr name="backgroundTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="badgeGravity">
        <enum name="BOTTOM_END" value="8388693" />
        <enum name="BOTTOM_START" value="8388691" />
        <enum name="TOP_END" value="8388661" />
        <enum name="TOP_START" value="8388659" />
    </attr>
    <attr name="badgeRadius" format="dimension">
    </attr>
    <attr name="badgeStyle" format="reference">
    </attr>
    <attr name="badgeTextColor" format="color">
    </attr>
    <attr name="badgeWidePadding" format="dimension">
    </attr>
    <attr name="badgeWithTextRadius" format="dimension">
    </attr>
    <attr name="banner_vp_auto_play" format="boolean">
    </attr>
    <attr name="banner_vp_can_loop" format="boolean">
    </attr>
    <attr name="banner_vp_indicator_checked_color" format="color">
    </attr>
    <attr name="banner_vp_indicator_gaps" format="dimension">
    </attr>
    <attr name="banner_vp_indicator_gravity">
        <enum name="center" value="0" />
        <enum name="end" value="4" />
        <enum name="start" value="2" />
    </attr>
    <attr name="banner_vp_indicator_height" format="dimension">
    </attr>
    <attr name="banner_vp_indicator_normal_color" format="color">
    </attr>
    <attr name="banner_vp_indicator_radius" format="dimension">
    </attr>
    <attr name="banner_vp_indicator_slide_mode">
        <enum name="color" value="5" />
        <enum name="normal" value="0" />
        <enum name="scale" value="4" />
        <enum name="smooth" value="2" />
        <enum name="worm" value="3" />
    </attr>
    <attr name="banner_vp_indicator_style">
        <enum name="circle" value="0" />
        <enum name="dash" value="2" />
        <enum name="round_rect" value="4" />
    </attr>
    <attr name="banner_vp_indicator_visibility">
        <enum name="gone" value="8" />
        <enum name="invisible" value="4" />
        <enum name="visible" value="0" />
    </attr>
    <attr name="banner_vp_indicator_width" format="dimension">
    </attr>
    <attr name="banner_vp_indicator_width_checked" format="dimension">
    </attr>
    <attr name="banner_vp_interval" format="integer">
    </attr>
    <attr name="banner_vp_page_margin" format="dimension">
    </attr>
    <attr name="banner_vp_page_style">
        <enum name="multi_page" value="2" />
        <enum name="multi_page_overlap" value="4" />
        <enum name="multi_page_scale" value="8" />
        <enum name="normal" value="0" />
    </attr>
    <attr name="banner_vp_reveal_width" format="dimension">
    </attr>
    <attr name="banner_vp_round_corner" format="dimension">
    </attr>
    <attr name="banner_vp_scroll_duration" format="integer">
    </attr>
    <attr name="barLength" format="dimension">
    </attr>
    <attr name="barrierAllowsGoneWidgets" format="boolean">
    </attr>
    <attr name="barrierDirection">
        <enum name="bottom" value="3" />
        <enum name="end" value="6" />
        <enum name="left" value="0" />
        <enum name="right" value="1" />
        <enum name="start" value="5" />
        <enum name="top" value="2" />
    </attr>
    <attr name="barrierMargin" format="dimension">
    </attr>
    <attr name="behavior_autoHide" format="boolean">
    </attr>
    <attr name="behavior_autoShrink" format="boolean">
    </attr>
    <attr name="behavior_draggable" format="boolean">
    </attr>
    <attr name="behavior_expandedOffset" format="reference|dimension">
    </attr>
    <attr name="behavior_fitToContents" format="boolean">
    </attr>
    <attr name="behavior_halfExpandedRatio" format="reference|float">
    </attr>
    <attr name="behavior_hideable" format="boolean">
    </attr>
    <attr name="behavior_overlapTop" format="dimension">
    </attr>
    <attr name="behavior_peekHeight" format="dimension">
        <enum name="auto" value="-1" />
    </attr>
    <attr name="behavior_saveFlags">
        <flag name="all" value="-1" />
        <flag name="fitToContents" value="0x2" />
        <flag name="hideable" value="0x4" />
        <flag name="none" value="0" />
        <flag name="peekHeight" value="0x1" />
        <flag name="skipCollapsed" value="0x8" />
    </attr>
    <attr name="behavior_skipCollapsed" format="boolean">
    </attr>
    <attr name="bidirectionalCustomSliderThumb" format="reference">
    </attr>
    <attr name="bidirectionalSliderDividerColor" format="color">
    </attr>
    <attr name="bidirectionalSliderDividerHeight" format="dimension">
    </attr>
    <attr name="bidirectionalSliderDividerRadius" format="dimension">
    </attr>
    <attr name="bidirectionalSliderDividerWidth" format="dimension">
    </attr>
    <attr name="bidirectionalSliderThumbFillColor" format="color">
    </attr>
    <attr name="bidirectionalSliderThumbRadius" format="dimension">
    </attr>
    <attr name="bidirectionalSliderThumbSize" format="dimension">
    </attr>
    <attr name="bidirectionalSliderThumbStrokeColor" format="color">
    </attr>
    <attr name="bidirectionalSliderThumbStrokeWidth" format="dimension">
    </attr>
    <attr name="bidirectionalSlidingModeEnable" format="boolean">
    </attr>
    <attr name="bidirectionalStartPointValue" format="string|integer|color|float|dimension">
    </attr>
    <attr name="blendSrc" format="reference">
    </attr>
    <attr name="borderRound" format="dimension">
    </attr>
    <attr name="borderRoundPercent" format="float">
    </attr>
    <attr name="borderWidth" format="dimension">
    </attr>
    <attr name="borderlessButtonStyle" format="reference">
    </attr>
    <attr name="bottomAppBarStyle" format="reference">
    </attr>
    <attr name="bottomInsetScrimEnabled" format="boolean">
    </attr>
    <attr name="bottomNavigationStyle" format="reference">
    </attr>
    <attr name="bottomSheetDialogTheme" format="reference">
    </attr>
    <attr name="bottomSheetStyle" format="reference">
    </attr>
    <attr name="boxBackgroundColor" format="color">
    </attr>
    <attr name="boxBackgroundMode">
        <enum name="filled" value="1" />
        <enum name="none" value="0" />
        <enum name="outline" value="2" />
    </attr>
    <attr name="boxCollapsedPaddingTop" format="dimension">
    </attr>
    <attr name="boxCornerRadiusBottomEnd" format="dimension">
    </attr>
    <attr name="boxCornerRadiusBottomStart" format="dimension">
    </attr>
    <attr name="boxCornerRadiusTopEnd" format="dimension">
    </attr>
    <attr name="boxCornerRadiusTopStart" format="dimension">
    </attr>
    <attr name="boxStrokeColor" format="color">
    </attr>
    <attr name="boxStrokeErrorColor" format="color">
    </attr>
    <attr name="boxStrokeWidth" format="dimension">
    </attr>
    <attr name="boxStrokeWidthFocused" format="dimension">
    </attr>
    <attr name="brightness" format="float">
    </attr>
    <attr name="bubbleArrowSize" format="dimension">
    </attr>
    <attr name="bubbleColor" format="color">
    </attr>
    <attr name="bubbleElevation" format="dimension">
    </attr>
    <attr name="bubbleRadius" format="dimension">
    </attr>
    <attr name="bubbleStrokeColor" format="color">
    </attr>
    <attr name="bubbleStrokeWidth" format="dimension">
    </attr>
    <attr name="bubbleStyle" format="reference">
    </attr>
    <attr name="bubbleTextColor" format="color">
    </attr>
    <attr name="buttonAspectSize" format="dimension">
    </attr>
    <attr name="buttonBarButtonStyle" format="reference">
    </attr>
    <attr name="buttonBarNegativeButtonStyle" format="reference">
    </attr>
    <attr name="buttonBarNeutralButtonStyle" format="reference">
    </attr>
    <attr name="buttonBarPositiveButtonStyle" format="reference">
    </attr>
    <attr name="buttonBarStyle" format="reference">
    </attr>
    <attr name="buttonCompat" format="reference">
    </attr>
    <attr name="buttonGravity">
        <flag name="bottom" value="0x50" />
        <flag name="center_vertical" value="0x10" />
        <flag name="top" value="0x30" />
    </attr>
    <attr name="buttonIconDimen" format="dimension">
    </attr>
    <attr name="buttonPanelSideLayout" format="reference">
    </attr>
    <attr name="buttonStyle" format="reference">
    </attr>
    <attr name="buttonStyleSmall" format="reference">
    </attr>
    <attr name="buttonTint" format="color">
    </attr>
    <attr name="buttonTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="buttonType">
        <enum name="custom" value="2" />
        <enum name="withNone" value="0" />
        <enum name="withText" value="1" />
    </attr>
    <attr name="cardBackgroundColor" format="color">
    </attr>
    <attr name="cardCornerRadius" format="dimension">
    </attr>
    <attr name="cardElevation" format="dimension">
    </attr>
    <attr name="cardForegroundColor" format="color">
    </attr>
    <attr name="cardMaxElevation" format="dimension">
    </attr>
    <attr name="cardPreventCornerOverlap" format="boolean">
    </attr>
    <attr name="cardUseCompatPadding" format="boolean">
    </attr>
    <attr name="cardViewStyle" format="reference">
    </attr>
    <attr name="carousel_backwardTransition" format="reference">
    </attr>
    <attr name="carousel_emptyViewsBehavior">
        <enum name="gone" value="8" />
        <enum name="invisible" value="4" />
    </attr>
    <attr name="carousel_firstView" format="reference">
    </attr>
    <attr name="carousel_forwardTransition" format="reference">
    </attr>
    <attr name="carousel_infinite" format="boolean">
    </attr>
    <attr name="carousel_nextState" format="reference">
    </attr>
    <attr name="carousel_previousState" format="reference">
    </attr>
    <attr name="carousel_touchUpMode">
        <enum name="carryVelocity" value="2" />
        <enum name="immediateStop" value="1" />
    </attr>
    <attr name="carousel_touchUp_dampeningFactor" format="float">
    </attr>
    <attr name="carousel_touchUp_velocityThreshold" format="float">
    </attr>
    <attr name="cauiButtonStyle" format="reference">
    </attr>
    <attr name="cauiButtonToggleGroupStyle" format="reference">
    </attr>
    <attr name="caui_backgroundColor" format="color">
    </attr>
    <attr name="caui_borderColor" format="color">
    </attr>
    <attr name="caui_borderDashGap" format="dimension">
    </attr>
    <attr name="caui_borderDashWidth" format="dimension">
    </attr>
    <attr name="caui_borderWidth" format="dimension">
    </attr>
    <attr name="caui_bottomDividerColor" format="reference|color">
    </attr>
    <attr name="caui_bottomDividerHeight" format="dimension">
    </attr>
    <attr name="caui_bottomDividerInsetLeft" format="dimension">
    </attr>
    <attr name="caui_bottomDividerInsetRight" format="dimension">
    </attr>
    <attr name="caui_bottomLeftCornerRadius" format="dimension">
    </attr>
    <attr name="caui_bottomRightCornerRadius" format="dimension">
    </attr>
    <attr name="caui_buttonIcon" format="reference">
    </attr>
    <attr name="caui_buttonIconGravity">
        <flag name="end" value="0x3" />
        <flag name="start" value="0x1" />
        <flag name="textEnd" value="0x4" />
        <flag name="textStart" value="0x2" />
        <flag name="textTop" value="0x20" />
        <flag name="top" value="0x10" />
    </attr>
    <attr name="caui_buttonIconPadding" format="dimension">
    </attr>
    <attr name="caui_buttonIconSize" format="dimension">
    </attr>
    <attr name="caui_buttonIconTint" format="color">
    </attr>
    <attr name="caui_buttonToggleCheckedStateOnClick" format="boolean">
    </attr>
    <attr name="caui_change_alpha_when_press" format="boolean">
    </attr>
    <attr name="caui_checked" format="boolean">
    </attr>
    <attr name="caui_checkedButton" format="reference">
    </attr>
    <attr name="caui_checkedIcon" format="reference">
    </attr>
    <attr name="caui_checkedIconTint" format="color">
    </attr>
    <attr name="caui_cohesivelyTogether" format="boolean">
    </attr>
    <attr name="caui_common_item_accessory_margin_left" format="dimension">
    </attr>
    <attr name="caui_common_item_accessory_margin_right" format="dimension">
    </attr>
    <attr name="caui_common_item_chevron" format="reference">
    </attr>
    <attr name="caui_common_item_detail_color" format="reference|color">
    </attr>
    <attr name="caui_common_item_detail_h_margin_with_title" format="dimension">
    </attr>
    <attr name="caui_common_item_detail_size" format="dimension">
    </attr>
    <attr name="caui_common_item_detail_text" format="string">
    </attr>
    <attr name="caui_common_item_detail_v_margin_with_title" format="dimension">
    </attr>
    <attr name="caui_common_item_holder_margin_with_title" format="dimension">
    </attr>
    <attr name="caui_common_item_right_accessory_type">
        <enum name="chevron" value="1" />
        <enum name="custom" value="2" />
        <enum name="none" value="0" />
    </attr>
    <attr name="caui_common_item_switch" format="reference">
    </attr>
    <attr name="caui_common_item_title_color" format="reference|color">
    </attr>
    <attr name="caui_common_item_title_size" format="dimension">
    </attr>
    <attr name="caui_common_item_title_text" format="string">
    </attr>
    <attr name="caui_cornerRadius" format="dimension">
    </attr>
    <attr name="caui_dashGap" format="dimension">
    </attr>
    <attr name="caui_dashWidth" format="dimension">
    </attr>
    <attr name="caui_dialog_action_button_padding_horizontal" format="dimension">
    </attr>
    <attr name="caui_dialog_action_button_padding_vertical" format="dimension">
    </attr>
    <attr name="caui_dialog_action_container_style" format="reference">
    </attr>
    <attr name="caui_dialog_action_height" format="dimension">
    </attr>
    <attr name="caui_dialog_action_space" format="dimension">
    </attr>
    <attr name="caui_dialog_action_style" format="reference">
    </attr>
    <attr name="caui_dialog_container_style" format="reference">
    </attr>
    <attr name="caui_dialog_inset_hor" format="dimension">
    </attr>
    <attr name="caui_dialog_inset_ver" format="dimension">
    </attr>
    <attr name="caui_dialog_max_height" format="dimension">
    </attr>
    <attr name="caui_dialog_max_width" format="dimension">
    </attr>
    <attr name="caui_dialog_message_content_style" format="reference">
    </attr>
    <attr name="caui_dialog_min_width" format="dimension">
    </attr>
    <attr name="caui_dialog_only_one_action_width" format="dimension">
    </attr>
    <attr name="caui_dialog_paddingBottomWhenNotContent" format="dimension">
    </attr>
    <attr name="caui_dialog_paddingTopWhenNotTitle" format="dimension">
    </attr>
    <attr name="caui_dialog_title_close_icon_style" format="reference">
    </attr>
    <attr name="caui_dialog_title_container_style" format="reference">
    </attr>
    <attr name="caui_dialog_title_style" format="reference">
    </attr>
    <attr name="caui_dividerColor" format="color">
    </attr>
    <attr name="caui_dividerInsetEnd" format="dimension">
    </attr>
    <attr name="caui_dividerInsetStart" format="dimension">
    </attr>
    <attr name="caui_dividerThickness" format="dimension">
    </attr>
    <attr name="caui_ensureMinTouchTargetSize" format="boolean">
    </attr>
    <attr name="caui_et_autoValidate" format="boolean">
    </attr>
    <attr name="caui_et_background" format="reference">
    </attr>
    <attr name="caui_et_clearIcon" format="reference">
    </attr>
    <attr name="caui_et_clearIconSize" format="dimension">
    </attr>
    <attr name="caui_et_errorMessage" format="string">
    </attr>
    <attr name="caui_et_error_background" format="reference">
    </attr>
    <attr name="caui_et_regexp" format="string">
    </attr>
    <attr name="caui_et_searchIcon" format="reference">
    </attr>
    <attr name="caui_et_searchIconSize" format="dimension">
    </attr>
    <attr name="caui_et_showSearchIcon" format="boolean">
    </attr>
    <attr name="caui_et_textSelectHandleTintColor" format="color">
    </attr>
    <attr name="caui_fl_checkMode">
        <enum name="display" value="3" />
        <enum name="multi" value="2" />
        <enum name="none" value="0" />
        <enum name="single" value="1" />
    </attr>
    <attr name="caui_fl_childHorizontalSpacing" format="dimension">
    </attr>
    <attr name="caui_fl_childVerticalSpacing" format="dimension">
    </attr>
    <attr name="caui_fl_entries" format="reference">
    </attr>
    <attr name="caui_fl_maxNumber" format="integer">
    </attr>
    <attr name="caui_fl_selectedPositions" format="reference">
    </attr>
    <attr name="caui_fl_singleCancelable" format="boolean">
    </attr>
    <attr name="caui_hideRadiusSide">
        <enum name="bottom" value="3" />
        <enum name="left" value="4" />
        <enum name="none" value="0" />
        <enum name="right" value="2" />
        <enum name="top" value="1" />
    </attr>
    <attr name="caui_iv_border_color" format="color">
    </attr>
    <attr name="caui_iv_border_width" format="dimension">
    </attr>
    <attr name="caui_iv_corner_radius" format="dimension">
    </attr>
    <attr name="caui_iv_is_circle" format="boolean">
    </attr>
    <attr name="caui_iv_is_oval" format="boolean">
    </attr>
    <attr name="caui_iv_is_touch_select_mode_enabled" format="boolean">
    </attr>
    <attr name="caui_iv_selected_border_color" format="color">
    </attr>
    <attr name="caui_iv_selected_border_width" format="dimension">
    </attr>
    <attr name="caui_iv_selected_mask_color" format="color">
    </attr>
    <attr name="caui_layout_miniContentProtectionSize" format="dimension">
    </attr>
    <attr name="caui_layout_priority">
        <enum name="disposable" value="1" />
        <enum name="incompressible" value="3" />
        <enum name="mini_content_protection" value="2" />
    </attr>
    <attr name="caui_leftDividerColor" format="reference|color">
    </attr>
    <attr name="caui_leftDividerInsetBottom" format="dimension">
    </attr>
    <attr name="caui_leftDividerInsetTop" format="dimension">
    </attr>
    <attr name="caui_leftDividerWidth" format="dimension">
    </attr>
    <attr name="caui_loading_view_color" format="color">
    </attr>
    <attr name="caui_loading_view_size" format="dimension">
    </attr>
    <attr name="caui_minTouchTargetSize" format="dimension">
    </attr>
    <attr name="caui_outerNormalColor" format="reference|color">
    </attr>
    <attr name="caui_outlineExcludePadding" format="boolean">
    </attr>
    <attr name="caui_outlineInsetBottom" format="dimension">
    </attr>
    <attr name="caui_outlineInsetLeft" format="dimension">
    </attr>
    <attr name="caui_outlineInsetRight" format="dimension">
    </attr>
    <attr name="caui_outlineInsetTop" format="dimension">
    </attr>
    <attr name="caui_pb_complete_drawable" format="reference">
    </attr>
    <attr name="caui_pb_complete_drawable_margin" format="dimension">
    </attr>
    <attr name="caui_pb_complete_drawable_size" format="dimension">
    </attr>
    <attr name="caui_pb_cornerRadius" format="dimension">
    </attr>
    <attr name="caui_pb_error_bg_color" format="reference|color">
    </attr>
    <attr name="caui_pb_error_progress_color" format="reference|color">
    </attr>
    <attr name="caui_pb_loading_view_color" format="color">
    </attr>
    <attr name="caui_pb_loading_view_size" format="dimension">
    </attr>
    <attr name="caui_pb_max_value" format="integer">
    </attr>
    <attr name="caui_pb_normal_bg_color" format="reference|color">
    </attr>
    <attr name="caui_pb_progress_bg_color" format="reference|color">
    </attr>
    <attr name="caui_pb_progress_color" format="reference|color">
    </attr>
    <attr name="caui_pb_state" format="integer">
        <enum name="COMPLETE" value="4" />
        <enum name="ERROR" value="3" />
        <enum name="LOADING" value="1" />
        <enum name="NORMAL" value="0" />
        <enum name="PROGRESS" value="2" />
    </attr>
    <attr name="caui_pb_state_complete_text" format="string">
    </attr>
    <attr name="caui_pb_state_error_text" format="string">
    </attr>
    <attr name="caui_pb_state_normal_text" format="string">
    </attr>
    <attr name="caui_pb_value" format="integer">
    </attr>
    <attr name="caui_progress_color" format="color">
    </attr>
    <attr name="caui_progress_max_value" format="integer">
    </attr>
    <attr name="caui_progress_status_complete_color" format="reference">
    </attr>
    <attr name="caui_progress_status_complete_drawable" format="reference">
    </attr>
    <attr name="caui_progress_status_error_color" format="reference">
    </attr>
    <attr name="caui_progress_status_error_drawable" format="reference">
    </attr>
    <attr name="caui_progress_status_height" format="dimension">
    </attr>
    <attr name="caui_progress_status_warn_color" format="reference">
    </attr>
    <attr name="caui_progress_status_warn_drawable" format="reference">
    </attr>
    <attr name="caui_progress_status_width" format="dimension">
    </attr>
    <attr name="caui_progress_stroke_round_cap" format="boolean">
    </attr>
    <attr name="caui_progress_textColor" format="color">
    </attr>
    <attr name="caui_progress_textSize" format="dimension">
    </attr>
    <attr name="caui_progress_textVisibility" format="boolean">
    </attr>
    <attr name="caui_progress_track_color" format="color">
    </attr>
    <attr name="caui_progress_track_width" format="dimension">
    </attr>
    <attr name="caui_progress_type">
        <enum name="type_circle" value="2" />
        <enum name="type_fill_circle" value="3" />
        <enum name="type_rect" value="0" />
        <enum name="type_round_rect" value="1" />
    </attr>
    <attr name="caui_progress_value" format="integer">
    </attr>
    <attr name="caui_progress_width" format="dimension">
    </attr>
    <attr name="caui_radius" format="dimension">
    </attr>
    <attr name="caui_rightDividerColor" format="reference|color">
    </attr>
    <attr name="caui_rightDividerInsetBottom" format="dimension">
    </attr>
    <attr name="caui_rightDividerInsetTop" format="dimension">
    </attr>
    <attr name="caui_rightDividerWidth" format="dimension">
    </attr>
    <attr name="caui_round_bg_color" format="color">
    </attr>
    <attr name="caui_round_border_color" format="color">
    </attr>
    <attr name="caui_round_border_width" format="dimension">
    </attr>
    <attr name="caui_round_btn_icon" format="reference">
    </attr>
    <attr name="caui_round_btn_iconGravity">
        <flag name="end" value="0x3" />
        <flag name="start" value="0x1" />
        <flag name="textEnd" value="0x4" />
        <flag name="textStart" value="0x2" />
        <flag name="textTop" value="0x20" />
        <flag name="top" value="0x10" />
    </attr>
    <attr name="caui_round_btn_iconPadding" format="dimension">
    </attr>
    <attr name="caui_round_btn_iconSize" format="dimension">
    </attr>
    <attr name="caui_round_btn_iconTint" format="color">
    </attr>
    <attr name="caui_round_btn_iconTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="caui_round_btn_type">
        <enum name="custom" value="0" />
        <enum name="delete" value="7" />
        <enum name="functionMain" value="4" />
        <enum name="functionNormal" value="5" />
        <enum name="main" value="1" />
        <enum name="mask" value="8" />
        <enum name="negative" value="6" />
        <enum name="secondary" value="2" />
        <enum name="third" value="3" />
    </attr>
    <attr name="caui_round_dash_gap" format="dimension">
    </attr>
    <attr name="caui_round_dash_width" format="dimension">
    </attr>
    <attr name="caui_round_isRadiusAdjustBounds" format="boolean">
    </attr>
    <attr name="caui_round_radius" format="dimension">
    </attr>
    <attr name="caui_round_radiusBottomLeft" format="dimension">
    </attr>
    <attr name="caui_round_radiusBottomRight" format="dimension">
    </attr>
    <attr name="caui_round_radiusTopLeft" format="dimension">
    </attr>
    <attr name="caui_round_radiusTopRight" format="dimension">
    </attr>
    <attr name="caui_selectionRequired" format="boolean">
    </attr>
    <attr name="caui_shadowAlpha" format="fraction">
    </attr>
    <attr name="caui_shadowColor" format="reference|color">
    </attr>
    <attr name="caui_shadowElevation" format="dimension">
    </attr>
    <attr name="caui_shape_borderColor" format="color">
    </attr>
    <attr name="caui_shape_borderWidth" format="dimension">
    </attr>
    <attr name="caui_shape_bottom_left_radius" format="dimension">
    </attr>
    <attr name="caui_shape_bottom_right_radius" format="dimension">
    </attr>
    <attr name="caui_shape_color" format="reference|color">
    </attr>
    <attr name="caui_shape_radius" format="dimension">
    </attr>
    <attr name="caui_shape_shadowBitmapDrawingEnable" format="boolean">
    </attr>
    <attr name="caui_shape_shadowColor" format="reference|color">
    </attr>
    <attr name="caui_shape_shadowElevation" format="dimension">
    </attr>
    <attr name="caui_shape_top_left_radius" format="dimension">
    </attr>
    <attr name="caui_shape_top_right_radius" format="dimension">
    </attr>
    <attr name="caui_showBorderOnlyBeforeL" format="boolean">
    </attr>
    <attr name="caui_singleSelection" format="boolean">
    </attr>
    <attr name="caui_stl_bar_background_color" format="reference|color">
    </attr>
    <attr name="caui_stl_bar_corner_radius" format="dimension">
    </attr>
    <attr name="caui_stl_bar_stroke_color" format="reference|color">
    </attr>
    <attr name="caui_stl_bar_stroke_width" format="dimension">
    </attr>
    <attr name="caui_stl_divider_color" format="reference|color">
    </attr>
    <attr name="caui_stl_divider_padding" format="dimension">
    </attr>
    <attr name="caui_stl_divider_width" format="dimension">
    </attr>
    <attr name="caui_stl_indicator_anim_duration" format="integer">
    </attr>
    <attr name="caui_stl_indicator_anim_enable" format="boolean">
    </attr>
    <attr name="caui_stl_indicator_bounce_enable" format="boolean">
    </attr>
    <attr name="caui_stl_indicator_color" format="reference|color">
    </attr>
    <attr name="caui_stl_indicator_corner_radius" format="dimension">
    </attr>
    <attr name="caui_stl_indicator_height" format="dimension">
    </attr>
    <attr name="caui_stl_indicator_margin_bottom" format="dimension">
    </attr>
    <attr name="caui_stl_indicator_margin_left" format="dimension">
    </attr>
    <attr name="caui_stl_indicator_margin_right" format="dimension">
    </attr>
    <attr name="caui_stl_indicator_margin_top" format="dimension">
    </attr>
    <attr name="caui_stl_tab_padding" format="dimension">
    </attr>
    <attr name="caui_stl_tab_space_equal" format="boolean">
    </attr>
    <attr name="caui_stl_tab_width" format="dimension">
    </attr>
    <attr name="caui_stl_textAllCaps" format="boolean">
    </attr>
    <attr name="caui_stl_textBold">
        <enum name="BOTH" value="2" />
        <enum name="NONE" value="0" />
        <enum name="SELECT" value="1" />
    </attr>
    <attr name="caui_stl_textColor" format="reference|color">
    </attr>
    <attr name="caui_stl_textSelectColor" format="reference|color">
    </attr>
    <attr name="caui_stl_textSize" format="dimension">
    </attr>
    <attr name="caui_strokeColor" format="color">
    </attr>
    <attr name="caui_strokeWidth" format="dimension">
    </attr>
    <attr name="caui_tips_dialog_container_style" format="reference">
    </attr>
    <attr name="caui_topDividerColor" format="reference|color">
    </attr>
    <attr name="caui_topDividerHeight" format="dimension">
    </attr>
    <attr name="caui_topDividerInsetLeft" format="dimension">
    </attr>
    <attr name="caui_topDividerInsetRight" format="dimension">
    </attr>
    <attr name="caui_topLeftCornerRadius" format="dimension">
    </attr>
    <attr name="caui_topRightCornerRadius" format="dimension">
    </attr>
    <attr name="caui_topbar_clear_left_padding_when_add_left_back_view" format="boolean">
    </attr>
    <attr name="caui_topbar_height" format="dimension">
    </attr>
    <attr name="caui_topbar_image_btn_height" format="dimension">
    </attr>
    <attr name="caui_topbar_image_btn_width" format="dimension">
    </attr>
    <attr name="caui_topbar_left_back_drawable_id" format="reference">
    </attr>
    <attr name="caui_topbar_left_back_width" format="dimension">
    </attr>
    <attr name="caui_topbar_left_close_drawable_id" format="reference">
    </attr>
    <attr name="caui_topbar_subtitle_bold" format="boolean">
    </attr>
    <attr name="caui_topbar_subtitle_color_id" format="reference">
    </attr>
    <attr name="caui_topbar_subtitle_text_size" format="dimension">
    </attr>
    <attr name="caui_topbar_tab_segment_height" format="dimension">
    </attr>
    <attr name="caui_topbar_tab_segment_viewpager_type">
        <enum name="viewpager" value="0" />
        <enum name="viewpager2" value="1" />
    </attr>
    <attr name="caui_topbar_text_btn_bold" format="boolean">
    </attr>
    <attr name="caui_topbar_text_btn_color_state_list_id" format="reference">
    </attr>
    <attr name="caui_topbar_text_btn_padding_horizontal" format="dimension">
    </attr>
    <attr name="caui_topbar_text_btn_text_size" format="dimension">
    </attr>
    <attr name="caui_topbar_title_bold" format="boolean">
    </attr>
    <attr name="caui_topbar_title_color_id" format="reference">
    </attr>
    <attr name="caui_topbar_title_container_padding_horizontal" format="dimension">
    </attr>
    <attr name="caui_topbar_title_gravity">
        <enum name="center" value="17" />
        <enum name="left_center" value="19" />
    </attr>
    <attr name="caui_topbar_title_margin_horizontal_when_no_btn_aside" format="dimension">
    </attr>
    <attr name="caui_topbar_title_text_size" format="dimension">
    </attr>
    <attr name="caui_topbar_title_text_size_with_subtitle" format="dimension">
    </attr>
    <attr name="caui_trackColor" format="color">
    </attr>
    <attr name="caui_vehicle_card_desc_color" format="reference|color">
    </attr>
    <attr name="caui_vehicle_card_desc_h_margin_with_title" format="dimension">
    </attr>
    <attr name="caui_vehicle_card_desc_size" format="dimension">
    </attr>
    <attr name="caui_vehicle_card_desc_text" format="string">
    </attr>
    <attr name="caui_vehicle_card_desc_v_margin_with_title" format="dimension">
    </attr>
    <attr name="caui_vehicle_card_holder_margin_with_title" format="dimension">
    </attr>
    <attr name="caui_vehicle_card_left_accessory_margin_right" format="dimension">
    </attr>
    <attr name="caui_vehicle_card_left_accessory_type">
        <enum name="custom" value="2" />
        <enum name="image" value="1" />
        <enum name="none" value="0" />
    </attr>
    <attr name="caui_vehicle_card_left_image" format="reference">
    </attr>
    <attr name="caui_vehicle_card_right_accessory_margin_left" format="dimension">
    </attr>
    <attr name="caui_vehicle_card_right_accessory_type">
        <enum name="chevron" value="1" />
        <enum name="custom" value="2" />
        <enum name="none" value="0" />
    </attr>
    <attr name="caui_vehicle_card_title_color" format="reference|color">
    </attr>
    <attr name="caui_vehicle_card_title_size" format="dimension">
    </attr>
    <attr name="caui_vehicle_card_title_text" format="string">
    </attr>
    <attr name="caui_vehicle_card_top_right_triangle_show" format="boolean">
    </attr>
    <attr name="caui_wheel_atmosphericEnabled" format="reference|boolean">
    </attr>
    <attr name="caui_wheel_curtainColor" format="reference|color">
    </attr>
    <attr name="caui_wheel_curtainCorner">
        <enum name="all" value="1" />
        <enum name="bottom" value="3" />
        <enum name="left" value="4" />
        <enum name="none" value="0" />
        <enum name="right" value="5" />
        <enum name="top" value="2" />
    </attr>
    <attr name="caui_wheel_curtainEnabled" format="reference|boolean">
    </attr>
    <attr name="caui_wheel_curtainRadius" format="reference|dimension">
    </attr>
    <attr name="caui_wheel_curvedEnabled" format="reference|boolean">
    </attr>
    <attr name="caui_wheel_curvedIndicatorSpace" format="reference|dimension">
    </attr>
    <attr name="caui_wheel_curvedMaxAngle" format="reference|integer">
    </attr>
    <attr name="caui_wheel_cyclicEnabled" format="reference|boolean">
    </attr>
    <attr name="caui_wheel_dateMode">
        <enum name="month_day" value="2" />
        <enum name="none" value="-1" />
        <enum name="year_month" value="1" />
        <enum name="year_month_day" value="0" />
    </attr>
    <attr name="caui_wheel_dayLabel" format="reference|string">
    </attr>
    <attr name="caui_wheel_firstLabel" format="reference|string">
    </attr>
    <attr name="caui_wheel_firstVisible" format="reference|boolean">
    </attr>
    <attr name="caui_wheel_hourLabel" format="reference|string">
    </attr>
    <attr name="caui_wheel_indicatorColor" format="reference|color">
    </attr>
    <attr name="caui_wheel_indicatorEnabled" format="reference|boolean">
    </attr>
    <attr name="caui_wheel_indicatorSize" format="reference|dimension">
    </attr>
    <attr name="caui_wheel_isDecimal" format="reference|boolean">
    </attr>
    <attr name="caui_wheel_itemSpace" format="reference|dimension">
    </attr>
    <attr name="caui_wheel_itemTextAlign">
        <enum name="center" value="0" />
        <enum name="left" value="1" />
        <enum name="right" value="2" />
    </attr>
    <attr name="caui_wheel_itemTextBoldSelected" format="reference|boolean">
    </attr>
    <attr name="caui_wheel_itemTextColor" format="reference|color">
    </attr>
    <attr name="caui_wheel_itemTextColorSelected" format="reference|color">
    </attr>
    <attr name="caui_wheel_itemTextSize" format="reference|dimension">
    </attr>
    <attr name="caui_wheel_itemTextSizeSelected" format="reference|dimension">
    </attr>
    <attr name="caui_wheel_label" format="reference|string">
    </attr>
    <attr name="caui_wheel_maxNumber" format="reference|float">
    </attr>
    <attr name="caui_wheel_maxWidthText" format="reference|string">
    </attr>
    <attr name="caui_wheel_minNumber" format="reference|float">
    </attr>
    <attr name="caui_wheel_minuteLabel" format="reference|string">
    </attr>
    <attr name="caui_wheel_monthLabel" format="reference|string">
    </attr>
    <attr name="caui_wheel_sameWidthEnabled" format="reference|boolean">
    </attr>
    <attr name="caui_wheel_secondLabel" format="reference|string">
    </attr>
    <attr name="caui_wheel_stepNumber" format="reference|float">
    </attr>
    <attr name="caui_wheel_thirdLabel" format="reference|string">
    </attr>
    <attr name="caui_wheel_thirdVisible" format="reference|boolean">
    </attr>
    <attr name="caui_wheel_timeMode">
        <enum name="hour_12_has_second" value="3" />
        <enum name="hour_12_no_second" value="2" />
        <enum name="hour_24_has_second" value="1" />
        <enum name="hour_24_no_second" value="0" />
        <enum name="none" value="-1" />
    </attr>
    <attr name="caui_wheel_visibleItemCount" format="reference|integer">
    </attr>
    <attr name="caui_wheel_yearLabel" format="reference|string">
    </attr>
    <attr name="ccl_descColor" format="reference|color">
    </attr>
    <attr name="ccl_descSize" format="dimension">
    </attr>
    <attr name="ccl_descText" format="string">
    </attr>
    <attr name="ccl_leftBottomAccessoryType">
        <enum name="button" value="2" />
        <enum name="custom" value="3" />
        <enum name="none" value="0" />
        <enum name="switcher" value="1" />
    </attr>
    <attr name="ccl_showTitleTipsIcon" format="boolean">
    </attr>
    <attr name="ccl_titleColor" format="reference|color">
    </attr>
    <attr name="ccl_titleSize" format="dimension">
    </attr>
    <attr name="ccl_titleText" format="string">
    </attr>
    <attr name="ccl_titleTipsIconDrawable" format="reference">
    </attr>
    <attr name="ccl_topRightAccessoryType">
        <enum name="custom" value="3" />
        <enum name="delete" value="2" />
        <enum name="more" value="1" />
        <enum name="none" value="0" />
    </attr>
    <attr name="centerIfNoTextEnabled" format="boolean">
    </attr>
    <attr name="chainUseRtl" format="boolean">
    </attr>
    <attr name="checkMarkCompat" format="reference">
    </attr>
    <attr name="checkMarkTint" format="color">
    </attr>
    <attr name="checkMarkTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="checkboxStyle" format="reference">
    </attr>
    <attr name="checkedButton" format="reference">
    </attr>
    <attr name="checkedChip" format="reference">
    </attr>
    <attr name="checkedIcon" format="reference">
    </attr>
    <attr name="checkedIconEnabled" format="boolean">
    </attr>
    <attr name="checkedIconGravity">
        <enum name="BOTTOM_END" value="8388693" />
        <enum name="BOTTOM_START" value="8388691" />
        <enum name="TOP_END" value="8388661" />
        <enum name="TOP_START" value="8388659" />
    </attr>
    <attr name="checkedIconMargin" format="dimension">
    </attr>
    <attr name="checkedIconSize" format="dimension">
    </attr>
    <attr name="checkedIconTint" format="color">
    </attr>
    <attr name="checkedIconVisible" format="boolean">
    </attr>
    <attr name="checkedTextViewStyle" format="reference">
    </attr>
    <attr name="chipBackgroundColor" format="color">
    </attr>
    <attr name="chipCornerRadius" format="dimension">
    </attr>
    <attr name="chipEndPadding" format="dimension">
    </attr>
    <attr name="chipGroupStyle" format="reference">
    </attr>
    <attr name="chipIcon" format="reference">
    </attr>
    <attr name="chipIconEnabled" format="boolean">
    </attr>
    <attr name="chipIconSize" format="dimension">
    </attr>
    <attr name="chipIconTint" format="color">
    </attr>
    <attr name="chipIconVisible" format="boolean">
    </attr>
    <attr name="chipMinHeight" format="dimension">
    </attr>
    <attr name="chipMinTouchTargetSize" format="dimension">
    </attr>
    <attr name="chipSpacing" format="dimension">
    </attr>
    <attr name="chipSpacingHorizontal" format="dimension">
    </attr>
    <attr name="chipSpacingVertical" format="dimension">
    </attr>
    <attr name="chipStandaloneStyle" format="reference">
    </attr>
    <attr name="chipStartPadding" format="dimension">
    </attr>
    <attr name="chipStrokeColor" format="color">
    </attr>
    <attr name="chipStrokeWidth" format="dimension">
    </attr>
    <attr name="chipStyle" format="reference">
    </attr>
    <attr name="chipSurfaceColor" format="color">
    </attr>
    <attr name="circleRadius" format="dimension">
    </attr>
    <attr name="circularProgressIndicatorStyle" format="reference">
    </attr>
    <attr name="circularflow_angles" format="string">
    </attr>
    <attr name="circularflow_defaultAngle" format="float">
    </attr>
    <attr name="circularflow_defaultRadius" format="dimension">
    </attr>
    <attr name="circularflow_radiusInDP" format="string">
    </attr>
    <attr name="circularflow_viewCenter" format="reference">
    </attr>
    <attr name="clearsTag" format="reference">
    </attr>
    <attr name="clickAction">
        <flag name="jumpToEnd" value="0x100" />
        <flag name="jumpToStart" value="0x1000" />
        <flag name="toggle" value="0x11" />
        <flag name="transitionToEnd" value="0x1" />
        <flag name="transitionToStart" value="0x10" />
    </attr>
    <attr name="clockFaceBackgroundColor" format="color">
    </attr>
    <attr name="clockHandColor" format="color">
    </attr>
    <attr name="clockIcon" format="reference">
    </attr>
    <attr name="clockNumberTextColor" format="color">
    </attr>
    <attr name="closeIcon" format="reference">
    </attr>
    <attr name="closeIconEnabled" format="boolean">
    </attr>
    <attr name="closeIconEndPadding" format="dimension">
    </attr>
    <attr name="closeIconSize" format="dimension">
    </attr>
    <attr name="closeIconStartPadding" format="dimension">
    </attr>
    <attr name="closeIconTint" format="color">
    </attr>
    <attr name="closeIconVisible" format="boolean">
    </attr>
    <attr name="closeItemLayout" format="reference">
    </attr>
    <attr name="collapseContentDescription" format="string">
    </attr>
    <attr name="collapseIcon" format="reference">
    </attr>
    <attr name="collapsedSize" format="dimension">
    </attr>
    <attr name="collapsedTitleGravity">
        <flag name="bottom" value="0x50" />
        <flag name="center" value="0x11" />
        <flag name="center_horizontal" value="0x1" />
        <flag name="center_vertical" value="0x10" />
        <flag name="end" value="0x800005" />
        <flag name="fill_vertical" value="0x70" />
        <flag name="left" value="0x3" />
        <flag name="right" value="0x5" />
        <flag name="start" value="0x800003" />
        <flag name="top" value="0x30" />
    </attr>
    <attr name="collapsedTitleTextAppearance" format="reference">
    </attr>
    <attr name="collapsedTitleTextColor" format="reference|color">
    </attr>
    <attr name="collapsingToolbarLayoutLargeSize" format="reference">
    </attr>
    <attr name="collapsingToolbarLayoutLargeStyle" format="reference">
    </attr>
    <attr name="collapsingToolbarLayoutMediumSize" format="reference">
    </attr>
    <attr name="collapsingToolbarLayoutMediumStyle" format="reference">
    </attr>
    <attr name="collapsingToolbarLayoutStyle" format="reference">
    </attr>
    <attr name="color" format="color">
    </attr>
    <attr name="colorAccent" format="color">
    </attr>
    <attr name="colorBackgroundFloating" format="color">
    </attr>
    <attr name="colorButtonNormal" format="color">
    </attr>
    <attr name="colorContainer" format="color">
    </attr>
    <attr name="colorControlActivated" format="color">
    </attr>
    <attr name="colorControlHighlight" format="color">
    </attr>
    <attr name="colorControlNormal" format="color">
    </attr>
    <attr name="colorError" format="reference|color">
    </attr>
    <attr name="colorErrorContainer" format="color">
    </attr>
    <attr name="colorOnBackground" format="reference|string|integer|boolean|color|float|dimension|fraction">
    </attr>
    <attr name="colorOnContainer" format="color">
    </attr>
    <attr name="colorOnError" format="color">
    </attr>
    <attr name="colorOnErrorContainer" format="color">
    </attr>
    <attr name="colorOnPrimary" format="color">
    </attr>
    <attr name="colorOnPrimaryContainer" format="color">
    </attr>
    <attr name="colorOnPrimarySurface" format="color">
    </attr>
    <attr name="colorOnSecondary" format="color">
    </attr>
    <attr name="colorOnSecondaryContainer" format="color">
    </attr>
    <attr name="colorOnSurface" format="color">
    </attr>
    <attr name="colorOnSurfaceInverse" format="color">
    </attr>
    <attr name="colorOnSurfaceVariant" format="color">
    </attr>
    <attr name="colorOnTertiary" format="color">
    </attr>
    <attr name="colorOnTertiaryContainer" format="color">
    </attr>
    <attr name="colorOutline" format="color">
    </attr>
    <attr name="colorPrimary" format="color">
    </attr>
    <attr name="colorPrimaryContainer" format="color">
    </attr>
    <attr name="colorPrimaryDark" format="color">
    </attr>
    <attr name="colorPrimaryInverse" format="color">
    </attr>
    <attr name="colorPrimarySurface" format="color">
    </attr>
    <attr name="colorPrimaryVariant" format="color">
    </attr>
    <attr name="colorSecondary" format="color">
    </attr>
    <attr name="colorSecondaryContainer" format="color">
    </attr>
    <attr name="colorSecondaryVariant" format="color">
    </attr>
    <attr name="colorSliderChromatographyColorArray" format="reference">
    </attr>
    <attr name="colorSliderGradLineColorArray" format="reference">
    </attr>
    <attr name="colorSliderThumbElevation" format="dimension">
    </attr>
    <attr name="colorSliderThumbInBounds" format="boolean">
    </attr>
    <attr name="colorSliderThumbRadius" format="dimension">
    </attr>
    <attr name="colorSliderThumbShadowColor" format="reference">
    </attr>
    <attr name="colorSliderThumbSize" format="dimension">
    </attr>
    <attr name="colorSliderThumbStrokeColor" format="reference">
    </attr>
    <attr name="colorSliderThumbStrokeWidth" format="dimension">
    </attr>
    <attr name="colorSliderTrackHeight" format="dimension">
    </attr>
    <attr name="colorSliderTrackRadius" format="dimension">
    </attr>
    <attr name="colorSurface" format="color">
    </attr>
    <attr name="colorSurfaceInverse" format="color">
    </attr>
    <attr name="colorSurfaceVariant" format="color">
    </attr>
    <attr name="colorSwitchThumbNormal" format="color">
    </attr>
    <attr name="colorTertiary" format="color">
    </attr>
    <attr name="colorTertiaryContainer" format="color">
    </attr>
    <attr name="commitIcon" format="reference">
    </attr>
    <attr name="constraintRotate">
        <enum name="left" value="2" />
        <enum name="none" value="0" />
        <enum name="right" value="1" />
        <enum name="x_left" value="4" />
        <enum name="x_right" value="3" />
    </attr>
    <attr name="constraintSet" format="reference">
    </attr>
    <attr name="constraintSetEnd" format="reference">
    </attr>
    <attr name="constraintSetStart" format="reference">
    </attr>
    <attr name="constraint_referenced_ids" format="string">
    </attr>
    <attr name="constraint_referenced_tags" format="string">
    </attr>
    <attr name="constraints" format="reference">
    </attr>
    <attr name="content" format="reference">
    </attr>
    <attr name="contentDescription" format="string">
    </attr>
    <attr name="contentInsetEnd" format="dimension">
    </attr>
    <attr name="contentInsetEndWithActions" format="dimension">
    </attr>
    <attr name="contentInsetLeft" format="dimension">
    </attr>
    <attr name="contentInsetRight" format="dimension">
    </attr>
    <attr name="contentInsetStart" format="dimension">
    </attr>
    <attr name="contentInsetStartWithNavigation" format="dimension">
    </attr>
    <attr name="contentPadding" format="dimension">
    </attr>
    <attr name="contentPaddingBottom" format="dimension">
    </attr>
    <attr name="contentPaddingEnd" format="dimension">
    </attr>
    <attr name="contentPaddingLeft" format="dimension">
    </attr>
    <attr name="contentPaddingRight" format="dimension">
    </attr>
    <attr name="contentPaddingStart" format="dimension">
    </attr>
    <attr name="contentPaddingTop" format="dimension">
    </attr>
    <attr name="contentScrim" format="color">
    </attr>
    <attr name="contrast" format="float">
    </attr>
    <attr name="controlBackground" format="reference">
    </attr>
    <attr name="cookieIconDrawable" format="reference">
    </attr>
    <attr name="cookieIconSize" format="dimension">
    </attr>
    <attr name="cookieTextColor" format="color">
    </attr>
    <attr name="cookieTextContent" format="string">
    </attr>
    <attr name="cookieTextSize" format="dimension">
    </attr>
    <attr name="cookieTextWithIconMargin" format="dimension">
    </attr>
    <attr name="coordinatorLayoutStyle" format="reference">
    </attr>
    <attr name="cornerFamily">
        <enum name="cut" value="1" />
        <enum name="rounded" value="0" />
    </attr>
    <attr name="cornerFamilyBottomLeft">
        <enum name="cut" value="1" />
        <enum name="rounded" value="0" />
    </attr>
    <attr name="cornerFamilyBottomRight">
        <enum name="cut" value="1" />
        <enum name="rounded" value="0" />
    </attr>
    <attr name="cornerFamilyTopLeft">
        <enum name="cut" value="1" />
        <enum name="rounded" value="0" />
    </attr>
    <attr name="cornerFamilyTopRight">
        <enum name="cut" value="1" />
        <enum name="rounded" value="0" />
    </attr>
    <attr name="cornerRadius" format="dimension">
    </attr>
    <attr name="cornerSize" format="dimension|fraction">
    </attr>
    <attr name="cornerSizeBottomLeft" format="dimension|fraction">
    </attr>
    <attr name="cornerSizeBottomRight" format="dimension|fraction">
    </attr>
    <attr name="cornerSizeTopLeft" format="dimension|fraction">
    </attr>
    <attr name="cornerSizeTopRight" format="dimension|fraction">
    </attr>
    <attr name="counterEnabled" format="boolean">
    </attr>
    <attr name="counterMaxLength" format="integer">
    </attr>
    <attr name="counterOverflowTextAppearance" format="reference">
    </attr>
    <attr name="counterOverflowTextColor" format="reference">
    </attr>
    <attr name="counterTextAppearance" format="reference">
    </attr>
    <attr name="counterTextColor" format="reference">
    </attr>
    <attr name="crossfade" format="float">
    </attr>
    <attr name="currentState" format="reference">
    </attr>
    <attr name="curveFit">
        <enum name="linear" value="1" />
        <enum name="spline" value="0" />
    </attr>
    <attr name="customBoolean" format="boolean">
    </attr>
    <attr name="customColorDrawableValue" format="color">
    </attr>
    <attr name="customColorValue" format="color">
    </attr>
    <attr name="customDimension" format="dimension">
    </attr>
    <attr name="customFloatValue" format="float">
    </attr>
    <attr name="customIntegerValue" format="integer">
    </attr>
    <attr name="customNavigationLayout" format="reference">
    </attr>
    <attr name="customPixelDimension" format="dimension">
    </attr>
    <attr name="customReference" format="reference">
    </attr>
    <attr name="customStringValue" format="string">
    </attr>
    <attr name="dayInvalidStyle" format="reference">
    </attr>
    <attr name="daySelectedStyle" format="reference">
    </attr>
    <attr name="dayStyle" format="reference">
    </attr>
    <attr name="dayTodayStyle" format="reference">
    </attr>
    <attr name="defaultDuration" format="integer">
    </attr>
    <attr name="defaultQueryHint" format="string">
    </attr>
    <attr name="defaultState" format="reference">
    </attr>
    <attr name="deltaPolarAngle" format="float">
    </attr>
    <attr name="deltaPolarRadius" format="float">
    </attr>
    <attr name="deriveConstraintsFrom" format="reference">
    </attr>
    <attr name="dialogCornerRadius" format="dimension">
    </attr>
    <attr name="dialogPreferredPadding" format="dimension">
    </attr>
    <attr name="dialogTheme" format="reference">
    </attr>
    <attr name="displayOptions">
        <flag name="disableHome" value="0x20" />
        <flag name="homeAsUp" value="0x4" />
        <flag name="none" value="0" />
        <flag name="showCustom" value="0x10" />
        <flag name="showHome" value="0x2" />
        <flag name="showTitle" value="0x8" />
        <flag name="useLogo" value="0x1" />
    </attr>
    <attr name="divider" format="reference">
    </attr>
    <attr name="dividerColor" format="color">
    </attr>
    <attr name="dividerHorizontal" format="reference">
    </attr>
    <attr name="dividerInsetEnd" format="dimension">
    </attr>
    <attr name="dividerInsetStart" format="dimension">
    </attr>
    <attr name="dividerPadding" format="dimension">
    </attr>
    <attr name="dividerThickness" format="dimension">
    </attr>
    <attr name="dividerVertical" format="reference">
    </attr>
    <attr name="dragDirection">
        <enum name="dragAnticlockwise" value="7" />
        <enum name="dragClockwise" value="6" />
        <enum name="dragDown" value="1" />
        <enum name="dragEnd" value="5" />
        <enum name="dragLeft" value="2" />
        <enum name="dragRight" value="3" />
        <enum name="dragStart" value="4" />
        <enum name="dragUp" value="0" />
    </attr>
    <attr name="dragScale" format="float">
    </attr>
    <attr name="dragThreshold" format="float">
    </attr>
    <attr name="drawPath">
        <enum name="asConfigured" value="4" />
        <enum name="deltaRelative" value="3" />
        <enum name="none" value="0" />
        <enum name="path" value="1" />
        <enum name="pathRelative" value="2" />
        <enum name="rectangles" value="5" />
    </attr>
    <attr name="drawableBottomCompat" format="reference">
    </attr>
    <attr name="drawableEndCompat" format="reference">
    </attr>
    <attr name="drawableLeftCompat" format="reference">
    </attr>
    <attr name="drawableRightCompat" format="reference">
    </attr>
    <attr name="drawableSize" format="dimension">
    </attr>
    <attr name="drawableStartCompat" format="reference">
    </attr>
    <attr name="drawableTint" format="color">
    </attr>
    <attr name="drawableTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="drawableTopCompat" format="reference">
    </attr>
    <attr name="drawerArrowStyle" format="reference">
    </attr>
    <attr name="drawerLayoutCornerSize" format="dimension">
    </attr>
    <attr name="drawerLayoutStyle" format="reference">
    </attr>
    <attr name="dropDownListViewStyle" format="reference">
    </attr>
    <attr name="dropdownListPreferredItemHeight" format="dimension">
    </attr>
    <attr name="dtlContent" format="string">
    </attr>
    <attr name="dtlIconDrawable" format="reference">
    </attr>
    <attr name="dtlIconSize" format="dimension">
    </attr>
    <attr name="dtlLoadingColor" format="reference">
    </attr>
    <attr name="dtlLoadingSize" format="dimension">
    </attr>
    <attr name="dtlMarginIconText" format="dimension">
    </attr>
    <attr name="dtlShowTopRightIcon" format="boolean">
    </attr>
    <attr name="dtlTextColor" format="reference">
    </attr>
    <attr name="dtlTextSize" format="dimension">
    </attr>
    <attr name="duration" format="integer">
    </attr>
    <attr name="dynamicColorThemeOverlay" format="reference">
    </attr>
    <attr name="editTextBackground" format="reference">
    </attr>
    <attr name="editTextColor" format="reference|color">
    </attr>
    <attr name="editTextStyle" format="reference">
    </attr>
    <attr name="elevation" format="dimension">
    </attr>
    <attr name="elevationOverlayAccentColor" format="color">
    </attr>
    <attr name="elevationOverlayColor" format="color">
    </attr>
    <attr name="elevationOverlayEnabled" format="boolean">
    </attr>
    <attr name="emojiCompatEnabled" format="boolean">
    </attr>
    <attr name="enableEdgeToEdge" format="boolean">
    </attr>
    <attr name="endIconCheckable" format="boolean">
    </attr>
    <attr name="endIconContentDescription" format="string">
    </attr>
    <attr name="endIconDrawable" format="reference">
    </attr>
    <attr name="endIconMode">
        <enum name="clear_text" value="2" />
        <enum name="custom" value="-1" />
        <enum name="dropdown_menu" value="3" />
        <enum name="none" value="0" />
        <enum name="password_toggle" value="1" />
    </attr>
    <attr name="endIconTint" format="color">
    </attr>
    <attr name="endIconTintMode">
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="enforceMaterialTheme" format="boolean">
    </attr>
    <attr name="enforceTextAppearance" format="boolean">
    </attr>
    <attr name="ensureMinTouchTargetSize" format="boolean">
    </attr>
    <attr name="errorContentDescription" format="string">
    </attr>
    <attr name="errorEnabled" format="boolean">
    </attr>
    <attr name="errorIconDrawable" format="reference">
    </attr>
    <attr name="errorIconTint" format="reference">
    </attr>
    <attr name="errorIconTintMode">
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="errorTextAppearance" format="reference">
    </attr>
    <attr name="errorTextColor" format="color">
    </attr>
    <attr name="expandActivityOverflowButtonDrawable" format="reference">
    </attr>
    <attr name="expanded" format="boolean">
    </attr>
    <attr name="expandedHintEnabled" format="boolean">
    </attr>
    <attr name="expandedTitleGravity">
        <flag name="bottom" value="0x50" />
        <flag name="center" value="0x11" />
        <flag name="center_horizontal" value="0x1" />
        <flag name="center_vertical" value="0x10" />
        <flag name="end" value="0x800005" />
        <flag name="fill_vertical" value="0x70" />
        <flag name="left" value="0x3" />
        <flag name="right" value="0x5" />
        <flag name="start" value="0x800003" />
        <flag name="top" value="0x30" />
    </attr>
    <attr name="expandedTitleMargin" format="dimension">
    </attr>
    <attr name="expandedTitleMarginBottom" format="dimension">
    </attr>
    <attr name="expandedTitleMarginEnd" format="dimension">
    </attr>
    <attr name="expandedTitleMarginStart" format="dimension">
    </attr>
    <attr name="expandedTitleMarginTop" format="dimension">
    </attr>
    <attr name="expandedTitleTextAppearance" format="reference">
    </attr>
    <attr name="expandedTitleTextColor" format="reference|color">
    </attr>
    <attr name="extendMotionSpec" format="reference">
    </attr>
    <attr name="extendedFloatingActionButtonPrimaryStyle" format="reference">
    </attr>
    <attr name="extendedFloatingActionButtonSecondaryStyle" format="reference">
    </attr>
    <attr name="extendedFloatingActionButtonStyle" format="reference">
    </attr>
    <attr name="extendedFloatingActionButtonSurfaceStyle" format="reference">
    </attr>
    <attr name="extendedFloatingActionButtonTertiaryStyle" format="reference">
    </attr>
    <attr name="extraMultilineHeightEnabled" format="boolean">
    </attr>
    <attr name="fabAlignmentMode">
        <enum name="center" value="0" />
        <enum name="end" value="1" />
    </attr>
    <attr name="fabAnimationMode">
        <enum name="scale" value="0" />
        <enum name="slide" value="1" />
    </attr>
    <attr name="fabCradleMargin" format="dimension">
    </attr>
    <attr name="fabCradleRoundedCornerRadius" format="dimension">
    </attr>
    <attr name="fabCradleVerticalOffset" format="dimension">
    </attr>
    <attr name="fabCustomSize" format="dimension">
    </attr>
    <attr name="fabSize">
        <enum name="auto" value="-1" />
        <enum name="mini" value="1" />
        <enum name="normal" value="0" />
    </attr>
    <attr name="fastScrollEnabled" format="boolean">
    </attr>
    <attr name="fastScrollHorizontalThumbDrawable" format="reference">
    </attr>
    <attr name="fastScrollHorizontalTrackDrawable" format="reference">
    </attr>
    <attr name="fastScrollVerticalThumbDrawable" format="reference">
    </attr>
    <attr name="fastScrollVerticalTrackDrawable" format="reference">
    </attr>
    <attr name="firstBaselineToTopHeight" format="dimension">
    </attr>
    <attr name="floatingActionButtonLargePrimaryStyle" format="reference">
    </attr>
    <attr name="floatingActionButtonLargeSecondaryStyle" format="reference">
    </attr>
    <attr name="floatingActionButtonLargeStyle" format="reference">
    </attr>
    <attr name="floatingActionButtonLargeSurfaceStyle" format="reference">
    </attr>
    <attr name="floatingActionButtonLargeTertiaryStyle" format="reference">
    </attr>
    <attr name="floatingActionButtonPrimaryStyle" format="reference">
    </attr>
    <attr name="floatingActionButtonSecondaryStyle" format="reference">
    </attr>
    <attr name="floatingActionButtonStyle" format="reference">
    </attr>
    <attr name="floatingActionButtonSurfaceStyle" format="reference">
    </attr>
    <attr name="floatingActionButtonTertiaryStyle" format="reference">
    </attr>
    <attr name="flow_firstHorizontalBias" format="float">
    </attr>
    <attr name="flow_firstHorizontalStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="flow_firstVerticalBias" format="float">
    </attr>
    <attr name="flow_firstVerticalStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="flow_horizontalAlign">
        <enum name="center" value="2" />
        <enum name="end" value="1" />
        <enum name="start" value="0" />
    </attr>
    <attr name="flow_horizontalBias" format="float">
    </attr>
    <attr name="flow_horizontalGap" format="dimension">
    </attr>
    <attr name="flow_horizontalStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="flow_lastHorizontalBias" format="float">
    </attr>
    <attr name="flow_lastHorizontalStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="flow_lastVerticalBias" format="float">
    </attr>
    <attr name="flow_lastVerticalStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="flow_maxElementsWrap" format="integer">
    </attr>
    <attr name="flow_padding" format="dimension">
    </attr>
    <attr name="flow_verticalAlign">
        <enum name="baseline" value="3" />
        <enum name="bottom" value="1" />
        <enum name="center" value="2" />
        <enum name="top" value="0" />
    </attr>
    <attr name="flow_verticalBias" format="float">
    </attr>
    <attr name="flow_verticalGap" format="dimension">
    </attr>
    <attr name="flow_verticalStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="flow_wrapMode">
        <enum name="aligned" value="2" />
        <enum name="chain" value="1" />
        <enum name="chain2" value="3" />
        <enum name="none" value="0" />
    </attr>
    <attr name="font" format="reference">
    </attr>
    <attr name="fontFamily" format="string">
    </attr>
    <attr name="fontProviderAuthority" format="string">
    </attr>
    <attr name="fontProviderCerts" format="reference">
    </attr>
    <attr name="fontProviderFetchStrategy">
        <enum name="async" value="1" />
        <enum name="blocking" value="0" />
    </attr>
    <attr name="fontProviderFetchTimeout" format="integer">
        <enum name="forever" value="-1" />
    </attr>
    <attr name="fontProviderPackage" format="string">
    </attr>
    <attr name="fontProviderQuery" format="string">
    </attr>
    <attr name="fontProviderSystemFontFamily" format="string">
    </attr>
    <attr name="fontStyle">
        <enum name="italic" value="1" />
        <enum name="normal" value="0" />
    </attr>
    <attr name="fontVariationSettings" format="string">
    </attr>
    <attr name="fontWeight" format="integer">
    </attr>
    <attr name="forceApplySystemWindowInsetTop" format="boolean">
    </attr>
    <attr name="foregroundInsidePadding" format="boolean">
    </attr>
    <attr name="framePosition" format="integer">
    </attr>
    <attr name="gapBetweenBars" format="dimension">
    </attr>
    <attr name="gestureInsetBottomIgnored" format="boolean">
    </attr>
    <attr name="goIcon" format="reference">
    </attr>
    <attr name="guidelineUseRtl" format="boolean">
    </attr>
    <attr name="haloColor" format="color">
    </attr>
    <attr name="haloRadius" format="dimension">
    </attr>
    <attr name="headerLayout" format="reference">
    </attr>
    <attr name="height" format="dimension">
    </attr>
    <attr name="helperText" format="string">
    </attr>
    <attr name="helperTextEnabled" format="boolean">
    </attr>
    <attr name="helperTextTextAppearance" format="reference">
    </attr>
    <attr name="helperTextTextColor" format="color">
    </attr>
    <attr name="hideAnimationBehavior">
        <enum name="inward" value="2" />
        <enum name="none" value="0" />
        <enum name="outward" value="1" />
    </attr>
    <attr name="hideMotionSpec" format="reference">
    </attr>
    <attr name="hideOnContentScroll" format="boolean">
    </attr>
    <attr name="hideOnScroll" format="boolean">
    </attr>
    <attr name="hintAnimationEnabled" format="boolean">
    </attr>
    <attr name="hintEnabled" format="boolean">
    </attr>
    <attr name="hintTextAppearance" format="reference">
    </attr>
    <attr name="hintTextColor" format="color">
    </attr>
    <attr name="homeAsUpIndicator" format="reference">
    </attr>
    <attr name="homeLayout" format="reference">
    </attr>
    <attr name="horizontalOffset" format="dimension">
    </attr>
    <attr name="horizontalOffsetWithText" format="dimension">
    </attr>
    <attr name="hoveredFocusedTranslationZ" format="dimension">
    </attr>
    <attr name="icon" format="reference">
    </attr>
    <attr name="iconEndPadding" format="dimension">
    </attr>
    <attr name="iconGravity">
        <flag name="end" value="0x3" />
        <flag name="start" value="0x1" />
        <flag name="textEnd" value="0x4" />
        <flag name="textStart" value="0x2" />
        <flag name="textTop" value="0x20" />
        <flag name="top" value="0x10" />
    </attr>
    <attr name="iconPadding" format="dimension">
    </attr>
    <attr name="iconSize" format="dimension">
    </attr>
    <attr name="iconStartPadding" format="dimension">
    </attr>
    <attr name="iconTint" format="color">
    </attr>
    <attr name="iconTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="iconifiedByDefault" format="boolean">
    </attr>
    <attr name="ifTagNotSet" format="reference">
    </attr>
    <attr name="ifTagSet" format="reference">
    </attr>
    <attr name="imageButtonStyle" format="reference">
    </attr>
    <attr name="imagePanX" format="float">
    </attr>
    <attr name="imagePanY" format="float">
    </attr>
    <attr name="imageRotate" format="float">
    </attr>
    <attr name="imageZoom" format="float">
    </attr>
    <attr name="indeterminateAnimationType">
        <enum name="contiguous" value="0" />
        <enum name="disjoint" value="1" />
    </attr>
    <attr name="indeterminateProgressStyle" format="reference">
    </attr>
    <attr name="indicatorColor" format="reference|color">
    </attr>
    <attr name="indicatorDirectionCircular">
        <enum name="clockwise" value="0" />
        <enum name="counterclockwise" value="1" />
    </attr>
    <attr name="indicatorDirectionLinear">
        <enum name="endToStart" value="3" />
        <enum name="leftToRight" value="0" />
        <enum name="rightToLeft" value="1" />
        <enum name="startToEnd" value="2" />
    </attr>
    <attr name="indicatorInset" format="dimension">
    </attr>
    <attr name="indicatorSize" format="dimension">
    </attr>
    <attr name="indicator_slider_checked_color" format="color">
    </attr>
    <attr name="indicator_slider_gaps" format="dimension">
    </attr>
    <attr name="indicator_slider_height" format="dimension">
    </attr>
    <attr name="indicator_slider_mode">
        <enum name="color" value="5" />
        <enum name="normal" value="0" />
        <enum name="scale" value="4" />
        <enum name="smooth" value="2" />
        <enum name="worm" value="3" />
    </attr>
    <attr name="indicator_slider_normal_color" format="color">
    </attr>
    <attr name="indicator_slider_orientation">
        <enum name="horizontal" value="0" />
        <enum name="rtl" value="3" />
        <enum name="vertical" value="1" />
    </attr>
    <attr name="indicator_slider_radius" format="dimension">
    </attr>
    <attr name="indicator_slider_style">
        <enum name="circle" value="0" />
        <enum name="dash" value="2" />
        <enum name="round_rect" value="4" />
    </attr>
    <attr name="indicator_slider_width" format="dimension">
    </attr>
    <attr name="indicator_slider_width_checked" format="dimension">
    </attr>
    <attr name="initialActivityCount" format="string">
    </attr>
    <attr name="insetForeground" format="reference|color">
    </attr>
    <attr name="isLightTheme" format="boolean">
    </attr>
    <attr name="isMaterial3Theme" format="boolean">
    </attr>
    <attr name="isMaterialTheme" format="boolean">
    </attr>
    <attr name="itemActiveIndicatorStyle" format="reference">
    </attr>
    <attr name="itemBackground" format="reference">
    </attr>
    <attr name="itemFillColor" format="color">
    </attr>
    <attr name="itemHorizontalPadding" format="dimension">
    </attr>
    <attr name="itemHorizontalTranslationEnabled" format="boolean">
    </attr>
    <attr name="itemIconPadding" format="dimension">
    </attr>
    <attr name="itemIconSize" format="dimension">
    </attr>
    <attr name="itemIconTint" format="color">
    </attr>
    <attr name="itemMaxLines" format="integer" min="1">
    </attr>
    <attr name="itemMinHeight" format="dimension">
    </attr>
    <attr name="itemPadding" format="dimension">
    </attr>
    <attr name="itemPaddingBottom" format="dimension">
    </attr>
    <attr name="itemPaddingTop" format="dimension">
    </attr>
    <attr name="itemRippleColor" format="color">
    </attr>
    <attr name="itemShapeAppearance" format="reference">
    </attr>
    <attr name="itemShapeAppearanceOverlay" format="reference">
    </attr>
    <attr name="itemShapeFillColor" format="color">
    </attr>
    <attr name="itemShapeInsetBottom" format="dimension">
    </attr>
    <attr name="itemShapeInsetEnd" format="dimension">
    </attr>
    <attr name="itemShapeInsetStart" format="dimension">
    </attr>
    <attr name="itemShapeInsetTop" format="dimension">
    </attr>
    <attr name="itemSpacing" format="dimension">
    </attr>
    <attr name="itemStrokeColor" format="color">
    </attr>
    <attr name="itemStrokeWidth" format="dimension">
    </attr>
    <attr name="itemTextAppearance" format="reference">
    </attr>
    <attr name="itemTextAppearanceActive" format="reference">
    </attr>
    <attr name="itemTextAppearanceInactive" format="reference">
    </attr>
    <attr name="itemTextColor" format="color">
    </attr>
    <attr name="itemVerticalPadding" format="dimension">
    </attr>
    <attr name="keyPositionType">
        <enum name="deltaRelative" value="0" />
        <enum name="parentRelative" value="2" />
        <enum name="pathRelative" value="1" />
    </attr>
    <attr name="keyboardIcon" format="reference">
    </attr>
    <attr name="keylines" format="reference">
    </attr>
    <attr name="lStar" format="float">
    </attr>
    <attr name="labelBehavior">
        <enum name="floating" value="0" />
        <enum name="gone" value="2" />
        <enum name="visible" value="3" />
        <enum name="withinBounds" value="1" />
    </attr>
    <attr name="labelStyle" format="reference">
    </attr>
    <attr name="labelVisibilityMode">
        <enum name="auto" value="-1" />
        <enum name="labeled" value="1" />
        <enum name="selected" value="0" />
        <enum name="unlabeled" value="2" />
    </attr>
    <attr name="lastBaselineToBottomHeight" format="dimension">
    </attr>
    <attr name="lastItemDecorated" format="boolean">
    </attr>
    <attr name="layout" format="reference">
    </attr>
    <attr name="layoutDescription" format="reference">
    </attr>
    <attr name="layoutDuringTransition">
        <enum name="callMeasure" value="2" />
        <enum name="honorRequest" value="1" />
        <enum name="ignoreRequest" value="0" />
    </attr>
    <attr name="layoutManager" format="string">
    </attr>
    <attr name="layout_anchor" format="reference">
    </attr>
    <attr name="layout_anchorGravity">
        <flag name="bottom" value="0x50" />
        <flag name="center" value="0x11" />
        <flag name="center_horizontal" value="0x1" />
        <flag name="center_vertical" value="0x10" />
        <flag name="clip_horizontal" value="0x8" />
        <flag name="clip_vertical" value="0x80" />
        <flag name="end" value="0x800005" />
        <flag name="fill" value="0x77" />
        <flag name="fill_horizontal" value="0x7" />
        <flag name="fill_vertical" value="0x70" />
        <flag name="left" value="0x3" />
        <flag name="right" value="0x5" />
        <flag name="start" value="0x800003" />
        <flag name="top" value="0x30" />
    </attr>
    <attr name="layout_behavior" format="string">
    </attr>
    <attr name="layout_collapseMode">
        <enum name="none" value="0" />
        <enum name="parallax" value="2" />
        <enum name="pin" value="1" />
    </attr>
    <attr name="layout_collapseParallaxMultiplier" format="float">
    </attr>
    <attr name="layout_constrainedHeight" format="boolean">
    </attr>
    <attr name="layout_constrainedWidth" format="boolean">
    </attr>
    <attr name="layout_constraintBaseline_creator" format="integer">
    </attr>
    <attr name="layout_constraintBaseline_toBaselineOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBaseline_toBottomOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBaseline_toTopOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBottom_creator" format="integer">
    </attr>
    <attr name="layout_constraintBottom_toBottomOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBottom_toTopOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintCircle" format="reference">
    </attr>
    <attr name="layout_constraintCircleAngle" format="float">
    </attr>
    <attr name="layout_constraintCircleRadius" format="dimension">
    </attr>
    <attr name="layout_constraintDimensionRatio" format="string">
    </attr>
    <attr name="layout_constraintEnd_toEndOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintEnd_toStartOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintGuide_begin" format="dimension">
    </attr>
    <attr name="layout_constraintGuide_end" format="dimension">
    </attr>
    <attr name="layout_constraintGuide_percent" format="float">
    </attr>
    <attr name="layout_constraintHeight" format="string|dimension">
        <enum name="match_constraint" value="-3" />
        <enum name="match_parent" value="-1" />
        <enum name="wrap_content" value="-2" />
        <enum name="wrap_content_constrained" value="-4" />
    </attr>
    <attr name="layout_constraintHeight_default">
        <enum name="percent" value="2" />
        <enum name="spread" value="0" />
        <enum name="wrap" value="1" />
    </attr>
    <attr name="layout_constraintHeight_max" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintHeight_min" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintHeight_percent" format="float">
    </attr>
    <attr name="layout_constraintHorizontal_bias" format="float">
    </attr>
    <attr name="layout_constraintHorizontal_chainStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="layout_constraintHorizontal_weight" format="float">
    </attr>
    <attr name="layout_constraintLeft_creator" format="integer">
    </attr>
    <attr name="layout_constraintLeft_toLeftOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintLeft_toRightOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintRight_creator" format="integer">
    </attr>
    <attr name="layout_constraintRight_toLeftOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintRight_toRightOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintStart_toEndOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintStart_toStartOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintTag" format="string">
    </attr>
    <attr name="layout_constraintTop_creator" format="integer">
    </attr>
    <attr name="layout_constraintTop_toBottomOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintTop_toTopOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintVertical_bias" format="float">
    </attr>
    <attr name="layout_constraintVertical_chainStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="layout_constraintVertical_weight" format="float">
    </attr>
    <attr name="layout_constraintWidth" format="string|dimension">
        <enum name="match_constraint" value="-3" />
        <enum name="match_parent" value="-1" />
        <enum name="wrap_content" value="-2" />
        <enum name="wrap_content_constrained" value="-4" />
    </attr>
    <attr name="layout_constraintWidth_default">
        <enum name="percent" value="2" />
        <enum name="spread" value="0" />
        <enum name="wrap" value="1" />
    </attr>
    <attr name="layout_constraintWidth_max" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintWidth_min" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintWidth_percent" format="float">
    </attr>
    <attr name="layout_dodgeInsetEdges">
        <flag name="all" value="0x77" />
        <flag name="bottom" value="0x50" />
        <flag name="end" value="0x800005" />
        <flag name="left" value="0x3" />
        <flag name="none" value="0x0" />
        <flag name="right" value="0x5" />
        <flag name="start" value="0x800003" />
        <flag name="top" value="0x30" />
    </attr>
    <attr name="layout_editor_absoluteX" format="dimension">
    </attr>
    <attr name="layout_editor_absoluteY" format="dimension">
    </attr>
    <attr name="layout_goneMarginBaseline" format="dimension">
    </attr>
    <attr name="layout_goneMarginBottom" format="dimension">
    </attr>
    <attr name="layout_goneMarginEnd" format="dimension">
    </attr>
    <attr name="layout_goneMarginLeft" format="dimension">
    </attr>
    <attr name="layout_goneMarginRight" format="dimension">
    </attr>
    <attr name="layout_goneMarginStart" format="dimension">
    </attr>
    <attr name="layout_goneMarginTop" format="dimension">
    </attr>
    <attr name="layout_insetEdge">
        <enum name="bottom" value="0x50" />
        <enum name="end" value="0x800005" />
        <enum name="left" value="0x3" />
        <enum name="none" value="0x0" />
        <enum name="right" value="0x5" />
        <enum name="start" value="0x800003" />
        <enum name="top" value="0x30" />
    </attr>
    <attr name="layout_keyline" format="integer">
    </attr>
    <attr name="layout_marginBaseline" format="dimension">
    </attr>
    <attr name="layout_optimizationLevel">
        <flag name="barrier" value="2" />
        <flag name="cache_measures" value="256" />
        <flag name="chains" value="4" />
        <flag name="dependency_ordering" value="512" />
        <flag name="dimensions" value="8" />
        <flag name="direct" value="1" />
        <flag name="graph" value="64" />
        <flag name="graph_wrap" value="128" />
        <flag name="grouping" value="1024" />
        <flag name="groups" value="32" />
        <flag name="legacy" value="0" />
        <flag name="none" value="0" />
        <flag name="ratio" value="16" />
        <flag name="standard" value="257" />
    </attr>
    <attr name="layout_scrollEffect">
        <enum name="compress" value="1" />
        <enum name="none" value="0" />
    </attr>
    <attr name="layout_scrollFlags">
        <flag name="enterAlways" value="0x4" />
        <flag name="enterAlwaysCollapsed" value="0x8" />
        <flag name="exitUntilCollapsed" value="0x2" />
        <flag name="noScroll" value="0x0" />
        <flag name="scroll" value="0x1" />
        <flag name="snap" value="0x10" />
        <flag name="snapMargins" value="0x20" />
    </attr>
    <attr name="layout_scrollInterpolator" format="reference">
    </attr>
    <attr name="layout_srlBackgroundColor" format="color">
    </attr>
    <attr name="layout_srlSpinnerStyle">
        <enum name="FixedBehind" value="2" />
        <enum name="FixedFront" value="3" />
        <enum name="MatchLayout" value="4" />
        <enum name="Scale" value="1" />
        <enum name="Translate" value="0" />
    </attr>
    <attr name="layout_wrapBehaviorInParent">
        <enum name="horizontal_only" value="1" />
        <enum name="included" value="0" />
        <enum name="skipped" value="3" />
        <enum name="vertical_only" value="2" />
    </attr>
    <attr name="liftOnScroll" format="boolean">
    </attr>
    <attr name="liftOnScrollTargetViewId" format="reference">
    </attr>
    <attr name="limitBoundsTo" format="reference">
    </attr>
    <attr name="lineHeight" format="dimension">
    </attr>
    <attr name="lineSpacing" format="dimension">
    </attr>
    <attr name="linearProgressIndicatorStyle" format="reference">
    </attr>
    <attr name="listChoiceBackgroundIndicator" format="reference">
    </attr>
    <attr name="listChoiceIndicatorMultipleAnimated" format="reference">
    </attr>
    <attr name="listChoiceIndicatorSingleAnimated" format="reference">
    </attr>
    <attr name="listDividerAlertDialog" format="reference">
    </attr>
    <attr name="listItemLayout" format="reference">
    </attr>
    <attr name="listLayout" format="reference">
    </attr>
    <attr name="listMenuViewStyle" format="reference">
    </attr>
    <attr name="listPopupWindowStyle" format="reference">
    </attr>
    <attr name="listPreferredItemHeight" format="dimension">
    </attr>
    <attr name="listPreferredItemHeightLarge" format="dimension">
    </attr>
    <attr name="listPreferredItemHeightSmall" format="dimension">
    </attr>
    <attr name="listPreferredItemPaddingEnd" format="dimension">
    </attr>
    <attr name="listPreferredItemPaddingLeft" format="dimension">
    </attr>
    <attr name="listPreferredItemPaddingRight" format="dimension">
    </attr>
    <attr name="listPreferredItemPaddingStart" format="dimension">
    </attr>
    <attr name="logo" format="reference">
    </attr>
    <attr name="logoAdjustViewBounds" format="boolean">
    </attr>
    <attr name="logoDescription" format="string">
    </attr>
    <attr name="logoScaleType">
        <enum name="center" value="5" />
        <enum name="centerCrop" value="6" />
        <enum name="centerInside" value="7" />
        <enum name="fitCenter" value="3" />
        <enum name="fitEnd" value="4" />
        <enum name="fitStart" value="2" />
        <enum name="fitXY" value="1" />
        <enum name="matrix" value="0" />
    </attr>
    <attr name="lottieAnimationViewStyle" format="reference">
    </attr>
    <attr name="lottie_autoPlay" format="boolean">
    </attr>
    <attr name="lottie_cacheComposition" format="boolean">
    </attr>
    <attr name="lottie_clipToCompositionBounds" format="boolean">
    </attr>
    <attr name="lottie_colorFilter" format="color">
    </attr>
    <attr name="lottie_enableMergePathsForKitKatAndAbove" format="boolean">
    </attr>
    <attr name="lottie_fallbackRes" format="reference">
    </attr>
    <attr name="lottie_fileName" format="string">
    </attr>
    <attr name="lottie_ignoreDisabledSystemAnimations" format="boolean">
    </attr>
    <attr name="lottie_imageAssetsFolder" format="string">
    </attr>
    <attr name="lottie_loop" format="boolean">
    </attr>
    <attr name="lottie_progress" format="float">
    </attr>
    <attr name="lottie_rawRes" format="reference">
    </attr>
    <attr name="lottie_renderMode">
        <enum name="automatic" value="0" />
        <enum name="hardware" value="1" />
        <enum name="software" value="2" />
    </attr>
    <attr name="lottie_repeatCount" format="integer">
    </attr>
    <attr name="lottie_repeatMode">
        <enum name="restart" value="1" />
        <enum name="reverse" value="2" />
    </attr>
    <attr name="lottie_speed" format="float">
    </attr>
    <attr name="lottie_url" format="string">
    </attr>
    <attr name="marginHorizontal" format="dimension">
    </attr>
    <attr name="marginLeftSystemWindowInsets" format="boolean">
    </attr>
    <attr name="marginRightSystemWindowInsets" format="boolean">
    </attr>
    <attr name="marginTopSystemWindowInsets" format="boolean">
    </attr>
    <attr name="materialAlertDialogBodyTextStyle" format="reference">
    </attr>
    <attr name="materialAlertDialogButtonSpacerVisibility" format="integer">
    </attr>
    <attr name="materialAlertDialogTheme" format="reference">
    </attr>
    <attr name="materialAlertDialogTitleIconStyle" format="reference">
    </attr>
    <attr name="materialAlertDialogTitlePanelStyle" format="reference">
    </attr>
    <attr name="materialAlertDialogTitleTextStyle" format="reference">
    </attr>
    <attr name="materialButtonOutlinedStyle" format="reference">
    </attr>
    <attr name="materialButtonStyle" format="reference">
    </attr>
    <attr name="materialButtonToggleGroupStyle" format="reference">
    </attr>
    <attr name="materialCalendarDay" format="reference">
    </attr>
    <attr name="materialCalendarDayOfWeekLabel" format="reference">
    </attr>
    <attr name="materialCalendarFullscreenTheme" format="reference">
    </attr>
    <attr name="materialCalendarHeaderCancelButton" format="reference">
    </attr>
    <attr name="materialCalendarHeaderConfirmButton" format="reference">
    </attr>
    <attr name="materialCalendarHeaderDivider" format="reference">
    </attr>
    <attr name="materialCalendarHeaderLayout" format="reference">
    </attr>
    <attr name="materialCalendarHeaderSelection" format="reference">
    </attr>
    <attr name="materialCalendarHeaderTitle" format="reference">
    </attr>
    <attr name="materialCalendarHeaderToggleButton" format="reference">
    </attr>
    <attr name="materialCalendarMonth" format="reference">
    </attr>
    <attr name="materialCalendarMonthNavigationButton" format="reference">
    </attr>
    <attr name="materialCalendarStyle" format="reference">
    </attr>
    <attr name="materialCalendarTheme" format="reference">
    </attr>
    <attr name="materialCalendarYearNavigationButton" format="reference">
    </attr>
    <attr name="materialCardViewElevatedStyle" format="reference">
    </attr>
    <attr name="materialCardViewFilledStyle" format="reference">
    </attr>
    <attr name="materialCardViewOutlinedStyle" format="reference">
    </attr>
    <attr name="materialCardViewStyle" format="reference">
    </attr>
    <attr name="materialCircleRadius" format="dimension">
    </attr>
    <attr name="materialClockStyle" format="reference">
    </attr>
    <attr name="materialDisplayDividerStyle" format="reference">
    </attr>
    <attr name="materialDividerHeavyStyle" format="reference|string|integer|boolean|color|float|dimension|fraction">
    </attr>
    <attr name="materialDividerStyle" format="reference|string|integer|boolean|color|float|dimension|fraction">
    </attr>
    <attr name="materialThemeOverlay" format="reference">
    </attr>
    <attr name="materialTimePickerStyle" format="reference">
    </attr>
    <attr name="materialTimePickerTheme" format="reference">
    </attr>
    <attr name="materialTimePickerTitleStyle" format="reference">
    </attr>
    <attr name="maxAcceleration" format="float">
    </attr>
    <attr name="maxActionInlineWidth" format="dimension">
    </attr>
    <attr name="maxButtonHeight" format="dimension">
    </attr>
    <attr name="maxCharacterCount" format="integer">
    </attr>
    <attr name="maxHeight" format="dimension">
    </attr>
    <attr name="maxImageSize" format="dimension">
    </attr>
    <attr name="maxLines" format="integer">
    </attr>
    <attr name="maxVelocity" format="float">
    </attr>
    <attr name="maxWidth" format="dimension">
    </attr>
    <attr name="measureWithLargestChild" format="boolean">
    </attr>
    <attr name="menu" format="reference">
    </attr>
    <attr name="menuGravity">
        <enum name="bottom" value="81" />
        <enum name="center" value="17" />
        <enum name="top" value="49" />
    </attr>
    <attr name="methodName" format="string">
    </attr>
    <attr name="minHeight" format="dimension">
    </attr>
    <attr name="minHideDelay" format="integer">
    </attr>
    <attr name="minSeparation" format="dimension">
    </attr>
    <attr name="minTouchTargetSize" format="dimension">
    </attr>
    <attr name="minWidth" format="dimension">
    </attr>
    <attr name="mlet_contentPadding" format="dimension">
    </attr>
    <attr name="mlet_contentText" format="reference|string">
    </attr>
    <attr name="mlet_contentTextSize" format="reference|dimension">
    </attr>
    <attr name="mlet_contentViewHeight" format="reference|dimension">
    </attr>
    <attr name="mlet_hintText" format="reference|string">
    </attr>
    <attr name="mlet_ignoreCnOrEn" format="boolean">
    </attr>
    <attr name="mlet_isFixHeight" format="boolean">
    </attr>
    <attr name="mlet_maxCount" format="integer">
    </attr>
    <attr name="mlet_showSurplusNumber" format="boolean">
    </attr>
    <attr name="mlet_textSelectHandleTintColor" format="color">
    </attr>
    <attr name="mock_diagonalsColor" format="color">
    </attr>
    <attr name="mock_label" format="string">
    </attr>
    <attr name="mock_labelBackgroundColor" format="color">
    </attr>
    <attr name="mock_labelColor" format="color">
    </attr>
    <attr name="mock_showDiagonals" format="boolean">
    </attr>
    <attr name="mock_showLabel" format="boolean">
    </attr>
    <attr name="motionDebug">
        <enum name="NO_DEBUG" value="0" />
        <enum name="SHOW_ALL" value="3" />
        <enum name="SHOW_PATH" value="2" />
        <enum name="SHOW_PROGRESS" value="1" />
    </attr>
    <attr name="motionDurationLong1" format="integer">
    </attr>
    <attr name="motionDurationLong2" format="integer">
    </attr>
    <attr name="motionDurationMedium1" format="integer">
    </attr>
    <attr name="motionDurationMedium2" format="integer">
    </attr>
    <attr name="motionDurationShort1" format="integer">
    </attr>
    <attr name="motionDurationShort2" format="integer">
    </attr>
    <attr name="motionEasingAccelerated" format="string">
    </attr>
    <attr name="motionEasingDecelerated" format="string">
    </attr>
    <attr name="motionEasingEmphasized" format="string">
    </attr>
    <attr name="motionEasingLinear" format="string">
    </attr>
    <attr name="motionEasingStandard" format="string">
    </attr>
    <attr name="motionEffect_alpha" format="float">
    </attr>
    <attr name="motionEffect_end" format="integer">
    </attr>
    <attr name="motionEffect_move">
        <enum name="auto" value="-1" />
        <enum name="east" value="2" />
        <enum name="north" value="0" />
        <enum name="south" value="1" />
        <enum name="west" value="3" />
    </attr>
    <attr name="motionEffect_start" format="integer">
    </attr>
    <attr name="motionEffect_strict" format="boolean">
    </attr>
    <attr name="motionEffect_translationX" format="dimension">
    </attr>
    <attr name="motionEffect_translationY" format="dimension">
    </attr>
    <attr name="motionEffect_viewTransition" format="reference">
    </attr>
    <attr name="motionInterpolator" format="reference|string">
        <enum name="anticipate" value="6" />
        <enum name="bounce" value="4" />
        <enum name="easeIn" value="1" />
        <enum name="easeInOut" value="0" />
        <enum name="easeOut" value="2" />
        <enum name="linear" value="3" />
        <enum name="overshoot" value="5" />
    </attr>
    <attr name="motionPath" format="string">
        <enum name="arc" value="1" />
        <enum name="linear" value="0" />
    </attr>
    <attr name="motionPathRotate" format="float">
    </attr>
    <attr name="motionProgress" format="float">
    </attr>
    <attr name="motionStagger" format="float">
    </attr>
    <attr name="motionTarget" format="reference|string">
    </attr>
    <attr name="motion_postLayoutCollision" format="boolean">
    </attr>
    <attr name="motion_triggerOnCollision" format="reference">
    </attr>
    <attr name="moveWhenScrollAtTop" format="boolean">
    </attr>
    <attr name="multiChoiceItemLayout" format="reference">
    </attr>
    <attr name="navigationContentDescription" format="string">
    </attr>
    <attr name="navigationIcon" format="reference">
    </attr>
    <attr name="navigationIconTint" format="color">
    </attr>
    <attr name="navigationMode">
        <enum name="listMode" value="1" />
        <enum name="normal" value="0" />
        <enum name="tabMode" value="2" />
    </attr>
    <attr name="navigationRailStyle" format="reference">
    </attr>
    <attr name="navigationViewStyle" format="reference">
    </attr>
    <attr name="nestedScrollFlags">
        <flag name="disablePostScroll" value="1" />
        <flag name="disableScroll" value="2" />
        <flag name="none" value="0" />
        <flag name="supportScrollUp" value="4" />
    </attr>
    <attr name="nestedScrollViewStyle" format="reference">
    </attr>
    <attr name="nestedScrollable" format="boolean">
    </attr>
    <attr name="number" format="integer">
    </attr>
    <attr name="numericModifiers">
        <flag name="ALT" value="0x2" />
        <flag name="CTRL" value="0x1000" />
        <flag name="FUNCTION" value="0x8" />
        <flag name="META" value="0x10000" />
        <flag name="SHIFT" value="0x1" />
        <flag name="SYM" value="0x4" />
    </attr>
    <attr name="onCross" format="string">
    </attr>
    <attr name="onHide" format="boolean">
    </attr>
    <attr name="onNegativeCross" format="string">
    </attr>
    <attr name="onPositiveCross" format="string">
    </attr>
    <attr name="onShow" format="boolean">
    </attr>
    <attr name="onStateTransition">
        <enum name="actionDown" value="1" />
        <enum name="actionDownUp" value="3" />
        <enum name="actionUp" value="2" />
        <enum name="sharedValueSet" value="4" />
        <enum name="sharedValueUnset" value="5" />
    </attr>
    <attr name="onTouchUp">
        <enum name="autoComplete" value="0" />
        <enum name="autoCompleteToEnd" value="2" />
        <enum name="autoCompleteToStart" value="1" />
        <enum name="decelerate" value="4" />
        <enum name="decelerateAndComplete" value="5" />
        <enum name="neverCompleteToEnd" value="7" />
        <enum name="neverCompleteToStart" value="6" />
        <enum name="stop" value="3" />
    </attr>
    <attr name="overlapAnchor" format="boolean">
    </attr>
    <attr name="overlay" format="boolean">
    </attr>
    <attr name="paddingBottomNoButtons" format="dimension">
    </attr>
    <attr name="paddingBottomSystemWindowInsets" format="boolean">
    </attr>
    <attr name="paddingEnd" format="dimension">
    </attr>
    <attr name="paddingLeftSystemWindowInsets" format="boolean">
    </attr>
    <attr name="paddingRightSystemWindowInsets" format="boolean">
    </attr>
    <attr name="paddingStart" format="dimension">
    </attr>
    <attr name="paddingTopNoTitle" format="dimension">
    </attr>
    <attr name="paddingTopSystemWindowInsets" format="boolean">
    </attr>
    <attr name="panelBackground" format="reference">
    </attr>
    <attr name="panelMenuListTheme" format="reference">
    </attr>
    <attr name="panelMenuListWidth" format="dimension">
    </attr>
    <attr name="passwordToggleContentDescription" format="string">
    </attr>
    <attr name="passwordToggleDrawable" format="reference">
    </attr>
    <attr name="passwordToggleEnabled" format="boolean">
    </attr>
    <attr name="passwordToggleTint" format="color">
    </attr>
    <attr name="passwordToggleTintMode">
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="pathMotionArc">
        <enum name="flip" value="3" />
        <enum name="none" value="0" />
        <enum name="startHorizontal" value="2" />
        <enum name="startVertical" value="1" />
    </attr>
    <attr name="path_percent" format="float">
    </attr>
    <attr name="percentHeight" format="float">
    </attr>
    <attr name="percentWidth" format="float">
    </attr>
    <attr name="percentX" format="float">
    </attr>
    <attr name="percentY" format="float">
    </attr>
    <attr name="perpendicularPath_percent" format="float">
    </attr>
    <attr name="pivotAnchor" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="placeholderText" format="string">
    </attr>
    <attr name="placeholderTextAppearance" format="reference">
    </attr>
    <attr name="placeholderTextColor" format="color">
    </attr>
    <attr name="placeholder_emptyVisibility">
        <enum name="gone" value="8" />
        <enum name="invisible" value="4" />
        <enum name="visible" value="0" />
    </attr>
    <attr name="polarRelativeTo" format="reference">
    </attr>
    <attr name="popupMenuBackground" format="reference">
    </attr>
    <attr name="popupMenuStyle" format="reference">
    </attr>
    <attr name="popupTheme" format="reference">
    </attr>
    <attr name="popupWindowStyle" format="reference">
    </attr>
    <attr name="prefixText" format="string">
    </attr>
    <attr name="prefixTextAppearance" format="reference">
    </attr>
    <attr name="prefixTextColor" format="color">
    </attr>
    <attr name="preserveIconSpacing" format="boolean">
    </attr>
    <attr name="pressedTranslationZ" format="dimension">
    </attr>
    <attr name="progressBarPadding" format="dimension">
    </attr>
    <attr name="progressBarStyle" format="reference">
    </attr>
    <attr name="quantizeMotionInterpolator" format="reference|string">
        <enum name="bounce" value="4" />
        <enum name="easeIn" value="1" />
        <enum name="easeInOut" value="0" />
        <enum name="easeOut" value="2" />
        <enum name="linear" value="3" />
        <enum name="overshoot" value="5" />
    </attr>
    <attr name="quantizeMotionPhase" format="float">
    </attr>
    <attr name="quantizeMotionSteps" format="integer">
    </attr>
    <attr name="queryBackground" format="reference">
    </attr>
    <attr name="queryHint" format="string">
    </attr>
    <attr name="queryPatterns" format="reference">
    </attr>
    <attr name="radioButtonStyle" format="reference">
    </attr>
    <attr name="rangeFillColor" format="color">
    </attr>
    <attr name="ratingBarStyle" format="reference">
    </attr>
    <attr name="ratingBarStyleIndicator" format="reference">
    </attr>
    <attr name="ratingBarStyleSmall" format="reference">
    </attr>
    <attr name="reactiveGuide_animateChange" format="boolean">
    </attr>
    <attr name="reactiveGuide_applyToAllConstraintSets" format="boolean">
    </attr>
    <attr name="reactiveGuide_applyToConstraintSet" format="reference">
    </attr>
    <attr name="reactiveGuide_valueId" format="reference">
    </attr>
    <attr name="recyclerViewStyle" format="reference">
    </attr>
    <attr name="region_heightLessThan" format="dimension">
    </attr>
    <attr name="region_heightMoreThan" format="dimension">
    </attr>
    <attr name="region_widthLessThan" format="dimension">
    </attr>
    <attr name="region_widthMoreThan" format="dimension">
    </attr>
    <attr name="reverseLayout" format="boolean">
    </attr>
    <attr name="rippleColor" format="color">
    </attr>
    <attr name="rotationCenterId" format="reference">
    </attr>
    <attr name="round" format="dimension">
    </attr>
    <attr name="roundPercent" format="float">
    </attr>
    <attr name="rsb_gravity">
        <enum name="bottom" value="1" />
        <enum name="center" value="2" />
        <enum name="top" value="0" />
    </attr>
    <attr name="rsb_indicator_arrow_size" format="dimension">
    </attr>
    <attr name="rsb_indicator_background_color" format="color">
    </attr>
    <attr name="rsb_indicator_drawable" format="reference">
    </attr>
    <attr name="rsb_indicator_height" format="dimension">
        <enum name="wrap_content" value="-1" />
    </attr>
    <attr name="rsb_indicator_margin" format="dimension">
    </attr>
    <attr name="rsb_indicator_padding_bottom" format="dimension">
    </attr>
    <attr name="rsb_indicator_padding_left" format="dimension">
    </attr>
    <attr name="rsb_indicator_padding_right" format="dimension">
    </attr>
    <attr name="rsb_indicator_padding_top" format="dimension">
    </attr>
    <attr name="rsb_indicator_radius" format="dimension">
    </attr>
    <attr name="rsb_indicator_show_mode">
        <enum name="alwaysHide" value="1" />
        <enum name="alwaysShow" value="3" />
        <enum name="alwaysShowAfterTouch" value="2" />
        <enum name="showWhenTouch" value="0" />
    </attr>
    <attr name="rsb_indicator_text_color" format="color">
    </attr>
    <attr name="rsb_indicator_text_size" format="dimension">
    </attr>
    <attr name="rsb_indicator_width" format="dimension">
        <enum name="wrap_content" value="-1" />
    </attr>
    <attr name="rsb_max" format="float">
    </attr>
    <attr name="rsb_min" format="float">
    </attr>
    <attr name="rsb_min_interval" format="float">
    </attr>
    <attr name="rsb_mode">
        <enum name="range" value="2" />
        <enum name="single" value="1" />
    </attr>
    <attr name="rsb_progress_color" format="color">
    </attr>
    <attr name="rsb_progress_default_color" format="color">
    </attr>
    <attr name="rsb_progress_drawable" format="reference">
    </attr>
    <attr name="rsb_progress_drawable_default" format="reference">
    </attr>
    <attr name="rsb_progress_height" format="dimension">
    </attr>
    <attr name="rsb_progress_radius" format="dimension">
    </attr>
    <attr name="rsb_step_auto_bonding" format="boolean">
    </attr>
    <attr name="rsb_step_color" format="color">
    </attr>
    <attr name="rsb_step_drawable" format="reference">
    </attr>
    <attr name="rsb_step_height" format="dimension">
    </attr>
    <attr name="rsb_step_radius" format="dimension">
    </attr>
    <attr name="rsb_step_width" format="dimension">
    </attr>
    <attr name="rsb_steps" format="integer">
    </attr>
    <attr name="rsb_thumb_drawable" format="reference">
    </attr>
    <attr name="rsb_thumb_height" format="dimension">
    </attr>
    <attr name="rsb_thumb_inactivated_drawable" format="reference">
    </attr>
    <attr name="rsb_thumb_scale_ratio" format="float">
    </attr>
    <attr name="rsb_thumb_width" format="dimension">
    </attr>
    <attr name="rsb_tick_mark_gravity">
        <enum name="center" value="1" />
        <enum name="left" value="0" />
        <enum name="right" value="2" />
    </attr>
    <attr name="rsb_tick_mark_in_range_text_color" format="color">
    </attr>
    <attr name="rsb_tick_mark_layout_gravity">
        <enum name="bottom" value="1" />
        <enum name="top" value="0" />
    </attr>
    <attr name="rsb_tick_mark_mode">
        <enum name="number" value="0" />
        <enum name="other" value="1" />
    </attr>
    <attr name="rsb_tick_mark_number" format="integer">
    </attr>
    <attr name="rsb_tick_mark_text_array" format="reference">
    </attr>
    <attr name="rsb_tick_mark_text_color" format="color">
    </attr>
    <attr name="rsb_tick_mark_text_margin" format="dimension">
    </attr>
    <attr name="rsb_tick_mark_text_size" format="dimension">
    </attr>
    <attr name="saturation" format="float">
    </attr>
    <attr name="scaleFromTextSize" format="dimension">
    </attr>
    <attr name="scrimAnimationDuration" format="integer">
    </attr>
    <attr name="scrimBackground" format="reference|color">
    </attr>
    <attr name="scrimVisibleHeightTrigger" format="dimension">
    </attr>
    <attr name="searchHintIcon" format="reference">
    </attr>
    <attr name="searchIcon" format="reference">
    </attr>
    <attr name="searchViewStyle" format="reference">
    </attr>
    <attr name="seekBarStyle" format="reference">
    </attr>
    <attr name="selectableItemBackground" format="reference">
    </attr>
    <attr name="selectableItemBackgroundBorderless" format="reference">
    </attr>
    <attr name="selectionRequired" format="boolean">
    </attr>
    <attr name="selectorSize" format="dimension">
    </attr>
    <attr name="setsTag" format="reference">
    </attr>
    <attr name="shapeAppearance" format="reference">
    </attr>
    <attr name="shapeAppearanceLargeComponent" format="reference">
    </attr>
    <attr name="shapeAppearanceMediumComponent" format="reference">
    </attr>
    <attr name="shapeAppearanceOverlay" format="reference">
    </attr>
    <attr name="shapeAppearanceSmallComponent" format="reference">
    </attr>
    <attr name="shortcutMatchRequired" format="boolean">
    </attr>
    <attr name="showAnimationBehavior">
        <enum name="inward" value="2" />
        <enum name="none" value="0" />
        <enum name="outward" value="1" />
    </attr>
    <attr name="showAsAction">
        <flag name="always" value="2" />
        <flag name="collapseActionView" value="8" />
        <flag name="ifRoom" value="1" />
        <flag name="never" value="0" />
        <flag name="withText" value="4" />
    </attr>
    <attr name="showDelay" format="integer">
    </attr>
    <attr name="showDividers">
        <flag name="beginning" value="1" />
        <flag name="end" value="4" />
        <flag name="middle" value="2" />
        <flag name="none" value="0" />
    </attr>
    <attr name="showMotionSpec" format="reference">
    </attr>
    <attr name="showPaths" format="boolean">
    </attr>
    <attr name="showText" format="boolean">
    </attr>
    <attr name="showTitle" format="boolean">
    </attr>
    <attr name="shrinkMotionSpec" format="reference">
    </attr>
    <attr name="simpleItemLayout" format="reference">
    </attr>
    <attr name="simpleItems" format="reference">
    </attr>
    <attr name="singleChoiceItemLayout" format="reference">
    </attr>
    <attr name="singleLine" format="boolean">
    </attr>
    <attr name="singleSelection" format="boolean">
    </attr>
    <attr name="sizePercent" format="float">
    </attr>
    <attr name="sliderBubbleBehavior">
        <enum name="floating" value="0" />
        <enum name="gone" value="2" />
        <enum name="visible" value="3" />
        <enum name="withinBounds" value="1" />
    </attr>
    <attr name="sliderHaloColor" format="color">
    </attr>
    <attr name="sliderHaloEnable" format="boolean">
    </attr>
    <attr name="sliderHaloRadius" format="dimension">
    </attr>
    <attr name="sliderMinSeparation" format="dimension">
    </attr>
    <attr name="sliderMinTouchTargetSize" format="dimension">
    </attr>
    <attr name="sliderMinValue" format="string|integer|color|float|dimension">
    </attr>
    <attr name="sliderStyle" format="reference">
    </attr>
    <attr name="sliderThumbColor" format="color">
    </attr>
    <attr name="sliderThumbElevation" format="dimension">
    </attr>
    <attr name="sliderThumbEnable" format="boolean">
    </attr>
    <attr name="sliderThumbRadius" format="dimension">
    </attr>
    <attr name="sliderThumbStrokeColor" format="color">
    </attr>
    <attr name="sliderThumbStrokeWidth" format="dimension">
    </attr>
    <attr name="sliderTickColorActive" format="color">
    </attr>
    <attr name="sliderTickColorInactive" format="color">
    </attr>
    <attr name="sliderTickRadiusActive" format="dimension">
    </attr>
    <attr name="sliderTickRadiusInactive" format="dimension">
    </attr>
    <attr name="sliderTickVisible" format="boolean">
    </attr>
    <attr name="sliderTrackActiveDrawable" format="reference">
    </attr>
    <attr name="sliderTrackActiveEndColor" format="color">
    </attr>
    <attr name="sliderTrackActiveStartColor" format="color">
    </attr>
    <attr name="sliderTrackColorActive" format="color">
    </attr>
    <attr name="sliderTrackColorInactive" format="color">
    </attr>
    <attr name="sliderTrackColorShadeEffect" format="boolean">
    </attr>
    <attr name="sliderTrackHeight" format="dimension">
    </attr>
    <attr name="sliderTrackInActiveGrayColor" format="color">
    </attr>
    <attr name="sliderTrackInActiveGrayEnable" format="boolean">
    </attr>
    <attr name="sliderTrackInactiveDrawable" format="reference">
    </attr>
    <attr name="sliderTrackRadius" format="dimension">
    </attr>
    <attr name="sliderTrackRightAngleEnable" format="boolean">
    </attr>
    <attr name="snackbarButtonStyle" format="reference">
    </attr>
    <attr name="snackbarStyle" format="reference">
    </attr>
    <attr name="snackbarTextViewStyle" format="reference">
    </attr>
    <attr name="spanCount" format="integer">
    </attr>
    <attr name="spinBars" format="boolean">
    </attr>
    <attr name="spinnerDropDownItemStyle" format="reference">
    </attr>
    <attr name="spinnerStyle" format="reference">
    </attr>
    <attr name="splitTrack" format="boolean">
    </attr>
    <attr name="springBoundary">
        <flag name="bounceBoth" value="3" />
        <flag name="bounceEnd" value="2" />
        <flag name="bounceStart" value="1" />
        <flag name="overshoot" value="0" />
    </attr>
    <attr name="springDamping" format="float">
    </attr>
    <attr name="springMass" format="float">
    </attr>
    <attr name="springStiffness" format="float">
    </attr>
    <attr name="springStopThreshold" format="float">
    </attr>
    <attr name="srcCompat" format="reference">
    </attr>
    <attr name="srlAccentColor" format="color">
    </attr>
    <attr name="srlClassicsSpinnerStyle">
        <enum name="FixedBehind" value="2" />
        <enum name="Scale" value="1" />
        <enum name="Translate" value="0" />
    </attr>
    <attr name="srlDisableContentWhenLoading" format="boolean">
    </attr>
    <attr name="srlDisableContentWhenRefresh" format="boolean">
    </attr>
    <attr name="srlDragRate" format="float">
    </attr>
    <attr name="srlEnableAutoLoadMore" format="boolean">
    </attr>
    <attr name="srlEnableClipFooterWhenFixedBehind" format="boolean">
    </attr>
    <attr name="srlEnableClipHeaderWhenFixedBehind" format="boolean">
    </attr>
    <attr name="srlEnableFooterFollowWhenLoadFinished" format="boolean">
    </attr>
    <attr name="srlEnableFooterFollowWhenNoMoreData" format="boolean">
    </attr>
    <attr name="srlEnableFooterTranslationContent" format="boolean">
    </attr>
    <attr name="srlEnableHeaderTranslationContent" format="boolean">
    </attr>
    <attr name="srlEnableLoadMore" format="boolean">
    </attr>
    <attr name="srlEnableLoadMoreWhenContentNotFull" format="boolean">
    </attr>
    <attr name="srlEnableNestedScrolling" format="boolean">
    </attr>
    <attr name="srlEnableOverScrollBounce" format="boolean">
    </attr>
    <attr name="srlEnableOverScrollDrag" format="boolean">
    </attr>
    <attr name="srlEnablePreviewInEditMode" format="boolean">
    </attr>
    <attr name="srlEnablePureScrollMode" format="boolean">
    </attr>
    <attr name="srlEnableRefresh" format="boolean">
    </attr>
    <attr name="srlEnableScrollContentWhenLoaded" format="boolean">
    </attr>
    <attr name="srlEnableScrollContentWhenRefreshed" format="boolean">
    </attr>
    <attr name="srlFinishDuration" format="integer">
    </attr>
    <attr name="srlFixedFooterViewId" format="reference">
    </attr>
    <attr name="srlFixedHeaderViewId" format="reference">
    </attr>
    <attr name="srlFooterHeight" format="dimension">
    </attr>
    <attr name="srlFooterInsetStart" format="dimension">
    </attr>
    <attr name="srlFooterMaxDragRate" format="float">
    </attr>
    <attr name="srlFooterTranslationViewId" format="reference">
    </attr>
    <attr name="srlFooterTriggerRate" format="float">
    </attr>
    <attr name="srlHeaderHeight" format="dimension">
    </attr>
    <attr name="srlHeaderInsetStart" format="dimension">
    </attr>
    <attr name="srlHeaderMaxDragRate" format="float">
    </attr>
    <attr name="srlHeaderTranslationViewId" format="reference">
    </attr>
    <attr name="srlHeaderTriggerRate" format="float">
    </attr>
    <attr name="srlLoadingColor" format="color">
    </attr>
    <attr name="srlLoadingSize" format="dimension">
    </attr>
    <attr name="srlPrimaryColor" format="color">
    </attr>
    <attr name="srlReboundDuration" format="integer">
    </attr>
    <attr name="srlStyle" format="reference">
    </attr>
    <attr name="srlTextFailed" format="string">
    </attr>
    <attr name="srlTextFinish" format="string">
    </attr>
    <attr name="srlTextLoading" format="string">
    </attr>
    <attr name="srlTextNothing" format="string">
    </attr>
    <attr name="srlTextPulling" format="string">
    </attr>
    <attr name="srlTextRefreshing" format="string">
    </attr>
    <attr name="srlTextRelease" format="string">
    </attr>
    <attr name="srlTextSecondary" format="string">
    </attr>
    <attr name="srlTextSizeTitle" format="dimension">
    </attr>
    <attr name="srlTextUpdate" format="string">
    </attr>
    <attr name="stackFromEnd" format="boolean">
    </attr>
    <attr name="staggered" format="float">
    </attr>
    <attr name="startIconCheckable" format="boolean">
    </attr>
    <attr name="startIconContentDescription" format="string">
    </attr>
    <attr name="startIconDrawable" format="reference">
    </attr>
    <attr name="startIconTint" format="color">
    </attr>
    <attr name="startIconTintMode">
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="state_above_anchor" format="boolean">
    </attr>
    <attr name="state_collapsed" format="boolean">
    </attr>
    <attr name="state_collapsible" format="boolean">
    </attr>
    <attr name="state_dragged" format="boolean">
    </attr>
    <attr name="state_liftable" format="boolean">
    </attr>
    <attr name="state_lifted" format="boolean">
    </attr>
    <attr name="statusBarBackground" format="reference|color">
    </attr>
    <attr name="statusBarForeground" format="color">
    </attr>
    <attr name="statusBarScrim" format="color">
    </attr>
    <attr name="strokeColor" format="color">
    </attr>
    <attr name="strokeWidth" format="dimension">
    </attr>
    <attr name="subMenuArrow" format="reference">
    </attr>
    <attr name="subheaderColor" format="reference|color">
    </attr>
    <attr name="subheaderInsetEnd" format="dimension">
    </attr>
    <attr name="subheaderInsetStart" format="dimension">
    </attr>
    <attr name="subheaderTextAppearance" format="reference">
    </attr>
    <attr name="submitBackground" format="reference">
    </attr>
    <attr name="subtitle" format="string">
    </attr>
    <attr name="subtitleCentered" format="boolean">
    </attr>
    <attr name="subtitleTextAppearance" format="reference">
    </attr>
    <attr name="subtitleTextColor" format="color">
    </attr>
    <attr name="subtitleTextStyle" format="reference">
    </attr>
    <attr name="suffixText" format="string">
    </attr>
    <attr name="suffixTextAppearance" format="reference">
    </attr>
    <attr name="suffixTextColor" format="color">
    </attr>
    <attr name="suggestionRowLayout" format="reference">
    </attr>
    <attr name="swb_animationDuration" format="integer">
    </attr>
    <attr name="swb_auto_fill_description" format="boolean">
    </attr>
    <attr name="swb_automatic_switch" format="boolean">
    </attr>
    <attr name="swb_backColor" format="reference|color">
    </attr>
    <attr name="swb_backDrawable" format="reference">
    </attr>
    <attr name="swb_backRadius" format="reference|dimension">
    </attr>
    <attr name="swb_fadeBack" format="boolean">
    </attr>
    <attr name="swb_textAdjust" format="dimension">
    </attr>
    <attr name="swb_textExtra" format="dimension">
    </attr>
    <attr name="swb_textOff" format="string">
    </attr>
    <attr name="swb_textOn" format="string">
    </attr>
    <attr name="swb_textThumbInset" format="dimension">
    </attr>
    <attr name="swb_thumbColor" format="reference|color">
    </attr>
    <attr name="swb_thumbDrawable" format="reference">
    </attr>
    <attr name="swb_thumbHeight" format="reference|dimension">
    </attr>
    <attr name="swb_thumbMargin" format="reference|dimension">
    </attr>
    <attr name="swb_thumbMarginBottom" format="reference|dimension">
    </attr>
    <attr name="swb_thumbMarginLeft" format="reference|dimension">
    </attr>
    <attr name="swb_thumbMarginRight" format="reference|dimension">
    </attr>
    <attr name="swb_thumbMarginTop" format="reference|dimension">
    </attr>
    <attr name="swb_thumbRadius" format="reference|dimension">
    </attr>
    <attr name="swb_thumbRangeRatio" format="float">
    </attr>
    <attr name="swb_thumbWidth" format="reference|dimension">
    </attr>
    <attr name="swb_thumb_loading_color" format="reference|color">
    </attr>
    <attr name="swb_tintColor" format="reference|color">
    </attr>
    <attr name="switchMinWidth" format="dimension">
    </attr>
    <attr name="switchPadding" format="dimension">
    </attr>
    <attr name="switchStyle" format="reference">
    </attr>
    <attr name="switchTextAppearance" format="reference">
    </attr>
    <attr name="tabBackground" format="reference">
    </attr>
    <attr name="tabContentStart" format="dimension">
    </attr>
    <attr name="tabGravity">
        <enum name="center" value="1" />
        <enum name="fill" value="0" />
        <enum name="start" value="2" />
    </attr>
    <attr name="tabIconTint" format="color">
    </attr>
    <attr name="tabIconTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="tabIndicator" format="reference">
    </attr>
    <attr name="tabIndicatorAnimationDuration" format="integer">
    </attr>
    <attr name="tabIndicatorAnimationMode">
        <enum name="elastic" value="1" />
        <enum name="fade" value="2" />
        <enum name="linear" value="0" />
    </attr>
    <attr name="tabIndicatorColor" format="color">
    </attr>
    <attr name="tabIndicatorFullWidth" format="boolean">
    </attr>
    <attr name="tabIndicatorGravity">
        <enum name="bottom" value="0" />
        <enum name="center" value="1" />
        <enum name="stretch" value="3" />
        <enum name="top" value="2" />
    </attr>
    <attr name="tabIndicatorHeight" format="dimension">
    </attr>
    <attr name="tabInlineLabel" format="boolean">
    </attr>
    <attr name="tabMaxWidth" format="dimension">
    </attr>
    <attr name="tabMinWidth" format="dimension">
    </attr>
    <attr name="tabMode">
        <enum name="auto" value="2" />
        <enum name="fixed" value="1" />
        <enum name="scrollable" value="0" />
    </attr>
    <attr name="tabPadding" format="dimension">
    </attr>
    <attr name="tabPaddingBottom" format="dimension">
    </attr>
    <attr name="tabPaddingEnd" format="dimension">
    </attr>
    <attr name="tabPaddingStart" format="dimension">
    </attr>
    <attr name="tabPaddingTop" format="dimension">
    </attr>
    <attr name="tabRippleColor" format="color">
    </attr>
    <attr name="tabSecondaryStyle" format="reference">
    </attr>
    <attr name="tabSelectedTextColor" format="color">
    </attr>
    <attr name="tabStyle" format="reference">
    </attr>
    <attr name="tabTextAppearance" format="reference">
    </attr>
    <attr name="tabTextColor" format="color">
    </attr>
    <attr name="tabUnboundedRipple" format="boolean">
    </attr>
    <attr name="targetId" format="reference">
    </attr>
    <attr name="tblContent" format="string">
    </attr>
    <attr name="tblIconDrawable" format="reference">
    </attr>
    <attr name="tblIconGravity">
        <enum name="textStart" value="3" />
        <enum name="textTop" value="6" />
    </attr>
    <attr name="tblIconLeftMargin" format="dimension">
    </attr>
    <attr name="tblIconSize" format="dimension">
    </attr>
    <attr name="tblIconTint" format="reference">
    </attr>
    <attr name="tblMinValue" format="string|integer|color|float|dimension">
    </attr>
    <attr name="tblProgressColor" format="color">
    </attr>
    <attr name="tblProgressColorShadeEffect" format="boolean">
    </attr>
    <attr name="tblProgressEndColor" format="color">
    </attr>
    <attr name="tblProgressStartColor" format="color">
    </attr>
    <attr name="tblRadius" format="dimension">
    </attr>
    <attr name="tblShowTopRightIcon" format="boolean">
    </attr>
    <attr name="tblTextColor" format="color">
    </attr>
    <attr name="tblTextMarginToIcon" format="dimension">
    </attr>
    <attr name="tblTextSize" format="dimension">
    </attr>
    <attr name="telltales_tailColor" format="color">
    </attr>
    <attr name="telltales_tailScale" format="float">
    </attr>
    <attr name="telltales_velocityMode">
        <enum name="layout" value="0" />
        <enum name="postLayout" value="1" />
        <enum name="staticLayout" value="3" />
        <enum name="staticPostLayout" value="2" />
    </attr>
    <attr name="textAllCaps" format="reference|boolean">
    </attr>
    <attr name="textAppearanceBody1" format="reference">
    </attr>
    <attr name="textAppearanceBody2" format="reference">
    </attr>
    <attr name="textAppearanceBodyLarge" format="reference">
    </attr>
    <attr name="textAppearanceBodyMedium" format="reference">
    </attr>
    <attr name="textAppearanceBodySmall" format="reference">
    </attr>
    <attr name="textAppearanceButton" format="reference">
    </attr>
    <attr name="textAppearanceCaption" format="reference">
    </attr>
    <attr name="textAppearanceDisplayLarge" format="reference">
    </attr>
    <attr name="textAppearanceDisplayMedium" format="reference">
    </attr>
    <attr name="textAppearanceDisplaySmall" format="reference">
    </attr>
    <attr name="textAppearanceHeadline1" format="reference">
    </attr>
    <attr name="textAppearanceHeadline2" format="reference">
    </attr>
    <attr name="textAppearanceHeadline3" format="reference">
    </attr>
    <attr name="textAppearanceHeadline4" format="reference">
    </attr>
    <attr name="textAppearanceHeadline5" format="reference">
    </attr>
    <attr name="textAppearanceHeadline6" format="reference">
    </attr>
    <attr name="textAppearanceHeadlineLarge" format="reference">
    </attr>
    <attr name="textAppearanceHeadlineMedium" format="reference">
    </attr>
    <attr name="textAppearanceHeadlineSmall" format="reference">
    </attr>
    <attr name="textAppearanceLabelLarge" format="reference">
    </attr>
    <attr name="textAppearanceLabelMedium" format="reference">
    </attr>
    <attr name="textAppearanceLabelSmall" format="reference">
    </attr>
    <attr name="textAppearanceLargePopupMenu" format="reference">
    </attr>
    <attr name="textAppearanceLineHeightEnabled" format="boolean">
    </attr>
    <attr name="textAppearanceListItem" format="reference">
    </attr>
    <attr name="textAppearanceListItemSecondary" format="reference">
    </attr>
    <attr name="textAppearanceListItemSmall" format="reference">
    </attr>
    <attr name="textAppearanceOverline" format="reference">
    </attr>
    <attr name="textAppearancePopupMenuHeader" format="reference">
    </attr>
    <attr name="textAppearanceSearchResultSubtitle" format="reference">
    </attr>
    <attr name="textAppearanceSearchResultTitle" format="reference">
    </attr>
    <attr name="textAppearanceSmallPopupMenu" format="reference">
    </attr>
    <attr name="textAppearanceSubtitle1" format="reference">
    </attr>
    <attr name="textAppearanceSubtitle2" format="reference">
    </attr>
    <attr name="textAppearanceTitleLarge" format="reference">
    </attr>
    <attr name="textAppearanceTitleMedium" format="reference">
    </attr>
    <attr name="textAppearanceTitleSmall" format="reference">
    </attr>
    <attr name="textBackground" format="reference">
    </attr>
    <attr name="textBackgroundPanX" format="float">
    </attr>
    <attr name="textBackgroundPanY" format="float">
    </attr>
    <attr name="textBackgroundRotate" format="float">
    </attr>
    <attr name="textBackgroundZoom" format="float">
    </attr>
    <attr name="textColorAlertDialogListItem" format="reference|color">
    </attr>
    <attr name="textColorSearchUrl" format="reference|color">
    </attr>
    <attr name="textEndPadding" format="dimension">
    </attr>
    <attr name="textFillColor" format="color">
    </attr>
    <attr name="textInputFilledDenseStyle" format="reference">
    </attr>
    <attr name="textInputFilledExposedDropdownMenuStyle" format="reference">
    </attr>
    <attr name="textInputFilledStyle" format="reference">
    </attr>
    <attr name="textInputLayoutFocusedRectEnabled" format="boolean">
    </attr>
    <attr name="textInputOutlinedDenseStyle" format="reference">
    </attr>
    <attr name="textInputOutlinedExposedDropdownMenuStyle" format="reference">
    </attr>
    <attr name="textInputOutlinedStyle" format="reference">
    </attr>
    <attr name="textInputStyle" format="reference">
    </attr>
    <attr name="textLocale" format="string">
    </attr>
    <attr name="textOutlineColor" format="color">
    </attr>
    <attr name="textOutlineThickness" format="dimension">
    </attr>
    <attr name="textPanX" format="float">
    </attr>
    <attr name="textPanY" format="float">
    </attr>
    <attr name="textSelectHandleTintColor" format="color">
    </attr>
    <attr name="textStartPadding" format="dimension">
    </attr>
    <attr name="textureBlurFactor" format="integer">
    </attr>
    <attr name="textureEffect">
        <enum name="frost" value="1" />
        <enum name="none" value="0" />
    </attr>
    <attr name="textureHeight" format="dimension">
    </attr>
    <attr name="textureWidth" format="dimension">
    </attr>
    <attr name="theme" format="reference">
    </attr>
    <attr name="themeLineHeight" format="dimension">
    </attr>
    <attr name="thickness" format="dimension">
    </attr>
    <attr name="thumbColor" format="color">
    </attr>
    <attr name="thumbElevation" format="dimension">
    </attr>
    <attr name="thumbRadius" format="dimension">
    </attr>
    <attr name="thumbStrokeColor" format="color">
    </attr>
    <attr name="thumbStrokeWidth" format="dimension">
    </attr>
    <attr name="thumbTextPadding" format="dimension">
    </attr>
    <attr name="thumbTint" format="color">
    </attr>
    <attr name="thumbTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="tickColor" format="color">
    </attr>
    <attr name="tickColorActive" format="color">
    </attr>
    <attr name="tickColorInactive" format="color">
    </attr>
    <attr name="tickMark" format="reference">
    </attr>
    <attr name="tickMarkTint" format="color">
    </attr>
    <attr name="tickMarkTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="tickVisible" format="boolean">
    </attr>
    <attr name="tint" format="color">
    </attr>
    <attr name="tintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="tip_dialog_background" format="reference|color">
    </attr>
    <attr name="tip_dialog_icon_error_src" format="reference">
    </attr>
    <attr name="tip_dialog_icon_size" format="dimension">
    </attr>
    <attr name="tip_dialog_icon_success_src" format="reference">
    </attr>
    <attr name="tip_dialog_icon_warning_src" format="reference">
    </attr>
    <attr name="tip_dialog_loading_color" format="reference|color">
    </attr>
    <attr name="tip_dialog_loading_size" format="dimension">
    </attr>
    <attr name="tip_dialog_max_width" format="dimension">
    </attr>
    <attr name="tip_dialog_min_height" format="dimension">
    </attr>
    <attr name="tip_dialog_min_width" format="dimension">
    </attr>
    <attr name="tip_dialog_padding_horizontal" format="dimension">
    </attr>
    <attr name="tip_dialog_padding_vertical" format="dimension">
    </attr>
    <attr name="tip_dialog_radius" format="dimension">
    </attr>
    <attr name="tip_dialog_shadowAlpha" format="fraction">
    </attr>
    <attr name="tip_dialog_shadowColor" format="reference|color">
    </attr>
    <attr name="tip_dialog_shadowElevation" format="dimension">
    </attr>
    <attr name="tip_dialog_text_color" format="reference|color">
    </attr>
    <attr name="tip_dialog_text_margin_start" format="dimension">
    </attr>
    <attr name="tip_dialog_text_margin_top" format="dimension">
    </attr>
    <attr name="tip_dialog_text_size" format="dimension">
    </attr>
    <attr name="tips_dialog_style" format="reference">
    </attr>
    <attr name="title" format="string">
    </attr>
    <attr name="titleCentered" format="boolean">
    </attr>
    <attr name="titleCollapseMode">
        <enum name="fade" value="1" />
        <enum name="scale" value="0" />
    </attr>
    <attr name="titleEnabled" format="boolean">
    </attr>
    <attr name="titleMargin" format="dimension">
    </attr>
    <attr name="titleMarginBottom" format="dimension">
    </attr>
    <attr name="titleMarginEnd" format="dimension">
    </attr>
    <attr name="titleMarginStart" format="dimension">
    </attr>
    <attr name="titleMarginTop" format="dimension">
    </attr>
    <attr name="titleMargins" format="dimension">
    </attr>
    <attr name="titlePositionInterpolator" format="reference">
    </attr>
    <attr name="titleTextAppearance" format="reference">
    </attr>
    <attr name="titleTextColor" format="color">
    </attr>
    <attr name="titleTextStyle" format="reference">
    </attr>
    <attr name="toolbarId" format="reference">
    </attr>
    <attr name="toolbarNavigationButtonStyle" format="reference">
    </attr>
    <attr name="toolbarStyle" format="reference">
    </attr>
    <attr name="toolbarSurfaceStyle" format="reference">
    </attr>
    <attr name="tooltipForegroundColor" format="reference|color">
    </attr>
    <attr name="tooltipFrameBackground" format="reference">
    </attr>
    <attr name="tooltipStyle" format="reference">
    </attr>
    <attr name="tooltipText" format="string">
    </attr>
    <attr name="topInsetScrimEnabled" format="boolean">
    </attr>
    <attr name="touchAnchorId" format="reference">
    </attr>
    <attr name="touchAnchorSide">
        <enum name="bottom" value="3" />
        <enum name="end" value="6" />
        <enum name="left" value="1" />
        <enum name="middle" value="4" />
        <enum name="right" value="2" />
        <enum name="start" value="5" />
        <enum name="top" value="0" />
    </attr>
    <attr name="touchRegionId" format="reference">
    </attr>
    <attr name="track" format="reference">
    </attr>
    <attr name="trackColor" format="color">
    </attr>
    <attr name="trackColorActive" format="color">
    </attr>
    <attr name="trackColorInactive" format="color">
    </attr>
    <attr name="trackCornerRadius" format="dimension">
    </attr>
    <attr name="trackHeight" format="dimension">
    </attr>
    <attr name="trackThickness" format="dimension">
    </attr>
    <attr name="trackTint" format="color">
    </attr>
    <attr name="trackTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="transformPivotTarget" format="reference">
    </attr>
    <attr name="transitionDisable" format="boolean">
    </attr>
    <attr name="transitionEasing" format="string">
        <enum name="accelerate" value="1" />
        <enum name="decelerate" value="2" />
        <enum name="linear" value="3" />
        <enum name="standard" value="0" />
    </attr>
    <attr name="transitionFlags">
        <flag name="beginOnFirstDraw" value="1" />
        <flag name="disableIntraAutoTransition" value="2" />
        <flag name="none" value="0" />
        <flag name="onInterceptTouchReturnSwipe" value="4" />
    </attr>
    <attr name="transitionPathRotate" format="float">
    </attr>
    <attr name="transitionShapeAppearance" format="reference">
    </attr>
    <attr name="triggerId" format="reference">
    </attr>
    <attr name="triggerReceiver" format="reference">
    </attr>
    <attr name="triggerSlack" format="float">
    </attr>
    <attr name="ttcIndex" format="integer">
    </attr>
    <attr name="upDuration" format="integer">
    </attr>
    <attr name="useCompatPadding" format="boolean">
    </attr>
    <attr name="useMaterialThemeColors" format="boolean">
    </attr>
    <attr name="values" format="reference">
    </attr>
    <attr name="vcet_bg_focus" format="reference">
    </attr>
    <attr name="vcet_bg_normal" format="reference">
    </attr>
    <attr name="vcet_divider" format="reference">
    </attr>
    <attr name="vcet_divider_width" format="dimension">
    </attr>
    <attr name="vcet_height" format="reference|dimension">
    </attr>
    <attr name="vcet_is_divide_equally" format="boolean">
    </attr>
    <attr name="vcet_is_pwd" format="boolean">
    </attr>
    <attr name="vcet_number" format="integer">
    </attr>
    <attr name="vcet_pwd_radius" format="reference|dimension">
    </attr>
    <attr name="vcet_text_color" format="reference|color">
    </attr>
    <attr name="vcet_text_size" format="reference|dimension">
    </attr>
    <attr name="vcet_width" format="reference|dimension">
    </attr>
    <attr name="verticalOffset" format="dimension">
    </attr>
    <attr name="verticalOffsetWithText" format="dimension">
    </attr>
    <attr name="viewInflaterClass" format="string">
    </attr>
    <attr name="viewTransitionMode">
        <enum name="allStates" value="1" />
        <enum name="currentState" value="0" />
        <enum name="noState" value="2" />
    </attr>
    <attr name="viewTransitionOnCross" format="reference">
    </attr>
    <attr name="viewTransitionOnNegativeCross" format="reference">
    </attr>
    <attr name="viewTransitionOnPositiveCross" format="reference">
    </attr>
    <attr name="visibilityMode">
        <enum name="ignore" value="1" />
        <enum name="normal" value="0" />
    </attr>
    <attr name="voiceIcon" format="reference">
    </attr>
    <attr name="warmth" format="float">
    </attr>
    <attr name="waveDecay" format="integer">
    </attr>
    <attr name="waveOffset" format="float|dimension">
    </attr>
    <attr name="wavePeriod" format="float">
    </attr>
    <attr name="wavePhase" format="float">
    </attr>
    <attr name="waveShape" format="string">
        <enum name="bounce" value="6" />
        <enum name="cos" value="5" />
        <enum name="reverseSawtooth" value="4" />
        <enum name="sawtooth" value="3" />
        <enum name="sin" value="0" />
        <enum name="square" value="1" />
        <enum name="triangle" value="2" />
    </attr>
    <attr name="waveVariesBy">
        <enum name="path" value="1" />
        <enum name="position" value="0" />
    </attr>
    <attr name="wcelv_max_height" format="dimension">
    </attr>
    <attr name="wclv_max_height" format="dimension">
    </attr>
    <attr name="wcsv_max_height" format="dimension">
    </attr>
    <attr name="windowActionBar" format="boolean">
    </attr>
    <attr name="windowActionBarOverlay" format="boolean">
    </attr>
    <attr name="windowActionModeOverlay" format="boolean">
    </attr>
    <attr name="windowFixedHeightMajor" format="dimension|fraction">
    </attr>
    <attr name="windowFixedHeightMinor" format="dimension|fraction">
    </attr>
    <attr name="windowFixedWidthMajor" format="dimension|fraction">
    </attr>
    <attr name="windowFixedWidthMinor" format="dimension|fraction">
    </attr>
    <attr name="windowMinWidthMajor" format="dimension|fraction">
    </attr>
    <attr name="windowMinWidthMinor" format="dimension|fraction">
    </attr>
    <attr name="windowNoTitle" format="boolean">
    </attr>
    <attr name="yearSelectedStyle" format="reference">
    </attr>
    <attr name="yearStyle" format="reference">
    </attr>
    <attr name="yearTodayStyle" format="reference">
    </attr>
</resources>
