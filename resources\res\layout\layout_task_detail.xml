<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:tag="layout/layout_task_detail_0"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ImageView
        android:id="@+id/ready_img_car"
        android:tag="binding_1"
        android:layout_width="1240dp"
        android:layout_height="1240dp"
        android:layout_marginBottom="67dp"
        android:src="@drawable/fota_normal_model"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <View
        android:tag="binding_2"
        android:layout_width="1044dp"
        android:layout_height="820dp"
        android:layout_marginTop="185dp"
        android:layout_marginBottom="204dp"
        android:layout_marginStart="1412dp"
        android:layout_marginEnd="104dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <ImageView
        android:id="@+id/order_img_car"
        android:tag="binding_3"
        android:layout_width="1240dp"
        android:layout_height="1240dp"
        android:layout_marginBottom="67dp"
        android:src="@drawable/fota_normal_model"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <View
        android:tag="binding_4"
        android:layout_width="1044dp"
        android:layout_height="820dp"
        android:layout_marginTop="185dp"
        android:layout_marginBottom="204dp"
        android:layout_marginStart="1412dp"
        android:layout_marginEnd="104dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <ImageView
        android:id="@+id/lock_img_car"
        android:tag="binding_5"
        android:layout_width="1240dp"
        android:layout_height="1240dp"
        android:layout_marginBottom="67dp"
        android:src="@drawable/fota_normal_model"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <View
        android:tag="binding_6"
        android:layout_width="1044dp"
        android:layout_height="820dp"
        android:layout_marginTop="185dp"
        android:layout_marginBottom="204dp"
        android:layout_marginStart="1412dp"
        android:layout_marginEnd="104dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <ScrollView
        android:scrollbarThumbVertical="@drawable/fota_scroll_thumb"
        android:scrollbarStyle="outsideOverlay"
        android:scrollbars="vertical"
        android:layout_width="1412dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/triple_layout"
            android:tag="binding_7"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <include
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                layout="@layout/layout_ready_upgrade"/>
            <include
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                layout="@layout/layout_lock_car"/>
            <include
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                layout="@layout/layout_order"/>
            <TextView
                android:textSize="40sp"
                android:textColor="@color/caui_config_text_color_primary"
                android:id="@+id/new_version_tx"
                android:tag="binding_8"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="80dp"
                android:text="@string/upgrade_info_string"
                android:layout_marginStart="104dp"
                android:textFontWeight="600"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/triple_layout"/>
            <TextView
                android:textSize="32sp"
                android:tag="binding_9"
                android:paddingBottom="44dp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="48dp"
                android:layout_marginStart="104dp"
                android:layout_marginEnd="48dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/new_version_tx"/>
            <LinearLayout
                android:orientation="vertical"
                android:id="@+id/linear_version_detail"
                android:tag="binding_10"
                android:paddingBottom="44dp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="48dp"
                android:layout_marginStart="104dp"
                android:layout_marginEnd="48dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/new_version_tx"/>
        </LinearLayout>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>
