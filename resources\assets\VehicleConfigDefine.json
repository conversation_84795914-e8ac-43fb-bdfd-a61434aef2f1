{"vehicleSeries": "CD701", "didList": [{"didNum": "0x01FF", "didByteLength": 64, "functionConfigList": [{"functionName": "一体式大灯配置", "valueTable": "0x00=disable \n0x01=enable\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "光线传感器类型", "valueTable": "0x00= NotUesed\n0x01= Light Sensor on LIN\n0x02 = Light Sensor on HW\n0x03 = No Light Sensor\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "照地灯配置", "valueTable": "0x00=disable \n0x01=enable\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 8}, {"functionName": "转向灯流水灯配置", "valueTable": "0x00=disable \n0x01=enable\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 24, "bitLength": 8}, {"functionName": "制动灯联动后位置灯配置", "valueTable": "0x00=disable \n0x01=enable\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 32, "bitLength": 8}, {"functionName": "前雾灯配置", "valueTable": "0x00=disable \n0x01=enable\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 40, "bitLength": 8}, {"functionName": "转向辅助照明配置", "valueTable": "0x00=disable \n0x01=enable\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 48, "bitLength": 8}, {"functionName": "大灯组合开关类型", "valueTable": "0x00=硬开关\n0x01=软开关\n0x02 =BothCfg\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 56, "bitLength": 8}, {"functionName": "后雾灯开关类型", "valueTable": "0x00=硬开关\n0x01=软开关\n0x02 =BothCfg\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 64, "bitLength": 8}, {"functionName": "转向灯开关类型", "valueTable": "0x00=硬线自锁开关\n0x01=硬线自复位开关\n0x02=方向盘开关\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 72, "bitLength": 8}, {"functionName": "转向灯驱动类型", "valueTable": "0x00=VIU直驱\n0x01=CAN灯光模块控制\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 80, "bitLength": 8}, {"functionName": "远光灯开关类型", "valueTable": "0x00=硬线自锁开关\n0x01=硬线自复位开关\n0x02=方向盘开关\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 88, "bitLength": 8}, {"functionName": "倒车雷达配置", "valueTable": "0x00 = 4探头雷达\n0x01 = 6探头雷达\n0x02 = 8探头雷达\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 96, "bitLength": 8}, {"functionName": "前大灯驱动类型", "valueTable": "0x00=VIU直驱\n0x01=CAN灯光模块控制\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 104, "bitLength": 8}, {"functionName": "远光灯驱动类型", "valueTable": "0x00=VIU直驱\n0x01=CAN灯光模块控制\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 112, "bitLength": 8}, {"functionName": "前雾灯驱动类型", "valueTable": "0x00=VIU直驱\n0x01=CAN灯光模块控制\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 120, "bitLength": 8}, {"functionName": "日间行车灯驱动类型", "valueTable": "0x00=VIU直驱\n0x01=CAN灯光模块控制\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 128, "bitLength": 8}, {"functionName": "前贯穿灯驱动类型", "valueTable": "0x00=无前贯穿灯配置\n0x01=CAN灯光模块控制\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 136, "bitLength": 8}, {"functionName": "前Logo灯驱动类型", "valueTable": "0x00=无Logo灯配置\n0x01=CAN灯光模块控制\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 144, "bitLength": 8}, {"functionName": "后Logo灯驱动类型", "valueTable": "0x00=无Logo灯配置\n0x01=CAN灯光模块控制\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 152, "bitLength": 8}, {"functionName": "后雾灯驱动类型", "valueTable": "0x00=VIU直驱\n0x01=CAN灯光模块控制\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 160, "bitLength": 8}, {"functionName": "倒车灯驱动类型", "valueTable": "0x00=VIU直驱\n0x01=CAN灯光模块控制\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 168, "bitLength": 8}, {"functionName": "刹车灯驱动类型", "valueTable": "0x00=VIU直驱\n0x01=CAN灯光模块控制\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 176, "bitLength": 8}, {"functionName": "高位制动灯驱动类型", "valueTable": "0x00=VIU直驱\n0x01=CAN灯光模块控制\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 184, "bitLength": 8}, {"functionName": "后贯穿灯驱动类型", "valueTable": "0x00=无后贯穿灯配置\n0x01=CAN灯光模块控制\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 192, "bitLength": 8}, {"functionName": "格栅灯驱动类型", "valueTable": "0x00=无格栅灯配置\n0x01=CAN灯光模块控制\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 200, "bitLength": 8}, {"functionName": "大灯高度调节电机驱动类型", "valueTable": "0x0=VU直驱\n0x1=CAN灯光模块控制\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 208, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 216, "bitLength": 296}]}, {"didNum": "0x01E0", "didByteLength": 64, "functionConfigList": [{"functionName": "跟随回家功能开关", "valueTable": "0x00=disable \n0x01=enable\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "跟随回家时间设置", "valueTable": "0x00=10s\n0x01=15s\n0x02=30s\n0x03=60s\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "一键转向次数设置", "valueTable": "0x00~0x02 = NotUesed\n0x03 = 3 cycles\n0x04 = 4 cycles\n…\n0x0A = 10 cycles\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 8}, {"functionName": "转向请求自动关闭功能开关", "valueTable": "0x00=disable \n0x01=enable\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 24, "bitLength": 8}, {"functionName": "转向流水功能开关", "valueTable": "0x00=disable \n0x01=enable\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 32, "bitLength": 8}, {"functionName": "日行灯功能开关", "valueTable": "0x00=disable \n0x01=enable\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 40, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 48, "bitLength": 464}]}, {"didNum": "0x02FF", "didByteLength": 64, "functionConfigList": [{"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 512}]}, {"didNum": "0x0200", "didByteLength": 64, "functionConfigList": [{"functionName": "背景光亮度等级配置", "valueTable": "0x00：inactive\n0x01-0x64=1%~100%\n0XFF:无效值", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "顶灯DOOR档状态设置", "valueTable": "0x00=disable \n0x01=enable\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 496}]}, {"didNum": "0x03FF", "didByteLength": 64, "functionConfigList": [{"functionName": "车门结构类型", "valueTable": "0x00=电动门\n0x01=传统车门\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "电动门外开关类型", "valueTable": "0x00=按键式\n0x01=电容式\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "吸合电机热保护", "valueTable": "0x00=Disable \n0x01=Enable\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 8}, {"functionName": "释放电机热保护", "valueTable": "0x00=Disable \n0x01=Enable\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 24, "bitLength": 8}, {"functionName": "驱动器电机热保护", "valueTable": "0x00=Disable \n0x01=Enable\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 32, "bitLength": 8}, {"functionName": "堵转保护", "valueTable": "0x00=Disable \n0x01=Enable\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 40, "bitLength": 8}, {"functionName": "防夹滥用保护", "valueTable": "0x00=Disable \n0x01=Enable\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 48, "bitLength": 8}, {"functionName": "防玩保护", "valueTable": "0x00=Disable \n0x01=Enable\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 56, "bitLength": 8}, {"functionName": "防撞滥用保护", "valueTable": "0x00=Disable \n0x01=Enable\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 64, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 72, "bitLength": 440}]}, {"didNum": "0x0300", "didByteLength": 64, "functionConfigList": [{"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 80}, {"functionName": "左前门开启速度", "valueTable": "0x00:Fast//2s\n0x01:General//3.5s\n0x02:Slow//5s\n0xFF :Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 80, "bitLength": 8}, {"functionName": "右前门开启速度", "valueTable": "0x00:Fast//2s\n0x01:General//3.5s\n0x02:Slow//5s\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 88, "bitLength": 8}, {"functionName": "左后门开启速度", "valueTable": "0x00:Fast//2s\n0x01:General//3.5s\n0x02:Slow//5s\n0xFF :Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 96, "bitLength": 8}, {"functionName": "右后门开启速度", "valueTable": "0x00:Fast//2s\n0x01:General//3.5s\n0x02:Slow//5s\n0xFF :Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 104, "bitLength": 8}, {"functionName": "左前门最大开启角度", "valueTable": "0x00:Init\n0x01-0x65=0%~100%\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 112, "bitLength": 8}, {"functionName": "右前门最大开启角度", "valueTable": "0x00:Init\n0x01-0x65=0%~100%\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 120, "bitLength": 8}, {"functionName": "左后门最大开启角度", "valueTable": "0x00:Init\n0x01-0x65=0%~100%\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 128, "bitLength": 8}, {"functionName": "右后门最大开启角度", "valueTable": "0x00:Init\n0x01-0x65=0%~100%\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 136, "bitLength": 8}, {"functionName": "左前内开关锁止状态", "valueTable": "0x00=NoLockup\n0x01=Lockup\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 144, "bitLength": 8}, {"functionName": "右前内开关锁止状态", "valueTable": "0x00=NoLockup\n0x01=Lockup\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 152, "bitLength": 8}, {"functionName": "左后内开关锁止状态", "valueTable": "0x00=NoLockup\n0x01=Lockup\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 160, "bitLength": 8}, {"functionName": "右后内开关锁止状态", "valueTable": "0x00=NoLockup\n0x01=Lockup\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 168, "bitLength": 8}, {"functionName": "左前外开关锁止状态", "valueTable": "0x00=NoLockup\n0x01=Lockup\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 176, "bitLength": 8}, {"functionName": "右前外开关锁止状态", "valueTable": "0x00=NoLockup\n0x01=Lockup\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 184, "bitLength": 8}, {"functionName": "左后外开关锁止状态", "valueTable": "0x00=NoLockup\n0x01=Lockup\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 192, "bitLength": 8}, {"functionName": "右后外开关锁止状态", "valueTable": "0x00=NoLockup\n0x01=Lockup\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 200, "bitLength": 8}, {"functionName": "电动门操作模式", "valueTable": "0x00=电动\n0x01=手动\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 208, "bitLength": 8}, {"functionName": "左前车门当前位置", "valueTable": "0x00:Init 0x01-0xFFFE 0xFFFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 216, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 224, "bitLength": 288}]}, {"didNum": "0x04FF", "didByteLength": 64, "functionConfigList": [{"functionName": "后背门开关类型", "valueTable": "0x01=短暂的\n0x02=闭锁", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "后背门电动撑杆数量", "valueTable": "0x00：0 手动后背门；\n0x01：1 单边电动撑杆；\n0x02：2 双边电动撑杆", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "后背门吸合锁数量", "valueTable": "0x00：0 ；\n0x01：1 ；\n0x02：2", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 8}, {"functionName": "蜂鸣器数量", "valueTable": "0x00：0 ；\n0x01：1 ；\n0x02：2", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 24, "bitLength": 8}, {"functionName": "背门结构类型", "valueTable": "0x0：上翻；\n0x1：下翻；\n0x2：对开；\n0x3：异形701", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 32, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 40, "bitLength": 472}]}, {"didNum": "0x0401", "didByteLength": 64, "functionConfigList": [{"functionName": "主动解锁行李箱", "valueTable": "0x00=inactive 无效；\n0x01=On 开启；\n0x02=Off 关闭；\n0x03=Reserved预留", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "远程行李箱", "valueTable": "0x00:Disable;禁止 \n0x01:Enable使能", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "长按行李箱按键支持远程启动", "valueTable": "0x00:Disable;禁止\n0x01:Enable使能", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 8}, {"functionName": "背门运动超时时间阈值", "valueTable": "0x00:5s;\n0x01:6s;\n0x02:7s;\n0x03:8s;\n0x04:9s;\n0x05:10s;\n0x06:11s;\n0x07:12s;\n0x08:13s;\n0x09:14s;\n0x0A:15s", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 24, "bitLength": 8}, {"functionName": "背门吸合锁热保护是否开启", "valueTable": "0x01=启用;\n0x02=禁用", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 32, "bitLength": 8}, {"functionName": "背门防夹滥用保护是否开启", "valueTable": "0x01=启用;\n0x02=禁用", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 40, "bitLength": 8}, {"functionName": "背门防撞滥用保护是否开启", "valueTable": "0x01=启用;\n0x02=禁用", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 48, "bitLength": 8}, {"functionName": "背门防玩保护是否开启", "valueTable": "0x01=启用;\n0x02=禁用", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 56, "bitLength": 8}, {"functionName": "背门防玩保护时间阈值", "valueTable": "0x00:5s;\n0x01:6s;\n0x02:7s;\n0x03:8s;\n0x04:9s;\n0x05:10s;\n0x06:11s;\n0x07:12s;\n0x08:13s;\n0x09:14s;\n0x0A:15s", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 64, "bitLength": 8}, {"functionName": "背门撑杆电机热保护是否开启", "valueTable": "0x01=启用;\n0x02=禁用", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 72, "bitLength": 8}, {"functionName": "背门堵转保护是否开启", "valueTable": "0x01=启用;\n0x02=禁用", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 80, "bitLength": 8}, {"functionName": "背门最大开启角度", "valueTable": "0x01 至 0x65\n0x00 : 代表此参数无效", "resolution": 1, "bias": -1, "mainObject": "VIU_FL", "startbit": 88, "bitLength": 8}, {"functionName": "背门当前角度", "valueTable": "0x01 至 0x65\n0x00 : 代表此参数无效", "resolution": 1, "bias": -1, "mainObject": "VIU_FL", "startbit": 96, "bitLength": 8}, {"functionName": "背门吸合锁热保护是否开启", "valueTable": "0x01=启用;\n0x02=禁用", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 104, "bitLength": 8}, {"functionName": "背门玻璃热保护是否开启", "valueTable": "0x01=启用;\n0x02=禁用", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 112, "bitLength": 8}, {"functionName": "背门玻璃运动超时时间阈值", "valueTable": "0x00:5s;\n0x01:6s;\n0x02:7s;\n0x03:8s;\n0x04:9s;\n0x05:10s;\n0x06:11s;\n0x07:12s;\n0x08:13s;\n0x09:14s;\n0x0A:15s", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 120, "bitLength": 8}, {"functionName": "背门与天窗系统联动控制模式设置", "valueTable": "0x00:控制背门玻璃+背门+天窗（默认模式）\n0x01:只控制背门玻璃\n0x02:控制背门玻璃+天窗\n0x03:控制背门玻璃+背门\n0xFF:初始化态、未知态", "resolution": 1, "bias": -1, "mainObject": "VIU_FL", "startbit": 128, "bitLength": 8}, {"functionName": "背门蜂鸣器PWM驱动占空比", "valueTable": "0x00：idle\n0x01~0x65：PWM占空比为0%~100%\n0xFF：Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 136, "bitLength": 8}, {"functionName": "背门玻璃向上运动软停位置百分比", "valueTable": "0x00：无软停，堵转硬停\n0x01-0x65：0%-100%", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 144, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 152, "bitLength": 360}]}, {"didNum": "0x05FF", "didByteLength": 64, "functionConfigList": [{"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "前舱盖是否有电动撑杆", "valueTable": "0x00：无；\n0x01：有", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "前舱盖锁是否电动解锁", "valueTable": "0x00：否；\n0x01：是", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 8}, {"functionName": "前舱盖锁是否电动闭锁", "valueTable": "0x00：否；\n0x01：是", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 24, "bitLength": 8}, {"functionName": "四门门锁是否有硬线锁状态反馈", "valueTable": "0x0：四门门锁均无；\n0x1：仅主驾门锁有；\n0x2：仅主驾和副驾门锁有；\n0x3：四门门锁均有", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 32, "bitLength": 8}, {"functionName": "四门门锁类型", "valueTable": "0x0：普通电机解闭锁；\n0x1：仅支持电释放；\n0x2：仅支持电吸合；\n0x3：同时支持电释放电吸合", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 40, "bitLength": 8}, {"functionName": "儿童锁类型", "valueTable": "0x0：无儿童锁；\n0x1：虚拟儿童锁标志位；\n0x2：实体电子儿童锁；\n0x3：实体机械儿童锁", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 48, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 56, "bitLength": 456}]}, {"didNum": "0x0501", "didByteLength": 64, "functionConfigList": [{"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 40}, {"functionName": "速度落锁配置", "valueTable": "0x1=启用;\n0x2=禁用", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 40, "bitLength": 8}, {"functionName": "驻车解锁配置", "valueTable": "0x1=启用;\n0x2=禁用", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 48, "bitLength": 8}, {"functionName": "解锁模式配置", "valueTable": "0x01=所有门解锁;\n0x02=驾驶员门解锁", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 56, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 64, "bitLength": 64}, {"functionName": "门锁电机热保护是否开启", "valueTable": "0x01=启用\n0x02=禁用", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 128, "bitLength": 8}, {"functionName": "行车落锁车速配置", "valueTable": "0x00-0x04：Invalid\n0x05~0x1E：5km/h~30km/h", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 136, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 144, "bitLength": 368}]}, {"didNum": "0x06FF", "didByteLength": 64, "functionConfigList": [{"functionName": "左前门把手PE开关类型", "valueTable": "0x00：无开关；\n0x01：电容性开关\n0x02：按键开关；\n0x03：把手拉动触发开关", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "左前门把手是否电动", "valueTable": "0x0：手动；\n0x1：电动", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "左前门把手是否有指示灯", "valueTable": "0x0：无；\n0x1：有", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 8}, {"functionName": "右前门把手PE开关类型", "valueTable": "0x00：无开关；\n0x01：电容性开关\n0x02：按键开关；\n0x03：把手拉动触发开关", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 24, "bitLength": 8}, {"functionName": "右前门把手是否电动", "valueTable": "0x0：手动；\n0x1：电动", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 32, "bitLength": 8}, {"functionName": "右前门把手是否有指示灯", "valueTable": "0x0：无；\n0x1：有", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 40, "bitLength": 8}, {"functionName": "左后门把手PE开关类型", "valueTable": "0x00：无开关；\n0x01：电容性开关\n0x02：按键开关；\n0x03：把手拉动触发开关", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 48, "bitLength": 8}, {"functionName": "左后门把手是否电动", "valueTable": "0x0：手动；\n0x1：电动", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 56, "bitLength": 8}, {"functionName": "左后门把手是否有指示灯", "valueTable": "0x0：无；\n0x1：有", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 64, "bitLength": 8}, {"functionName": "右后门把手PE开关类型", "valueTable": "0x00：无开关；\n0x01：电容性开关\n0x02：按键开关；\n0x03：把手拉动触发开关", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 72, "bitLength": 8}, {"functionName": "右后门把手是否电动", "valueTable": "0x0：手动；\n0x1：电动", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 80, "bitLength": 8}, {"functionName": "右后门把手是否有指示灯", "valueTable": "0x0：无；\n0x1：有", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 88, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 96, "bitLength": 416}]}, {"didNum": "0x0601", "didByteLength": 64, "functionConfigList": [{"functionName": "隐藏式门把手自动收回时间阈值", "valueTable": "0x00:Invalid；\n0x01~0x1E：1min~30min", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "隐藏式门把手电机热保护是否开启", "valueTable": "0x1=启用;\n0x2=禁用", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 496}]}, {"didNum": "0x07FF", "didByteLength": 64, "functionConfigList": [{"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "主驾车窗开关类型", "valueTable": "0x00=车窗开关上LIN\n0x01=车窗开关硬线 \n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 8}, {"functionName": "车窗结构类型", "valueTable": "0x00=无框车窗\n0x01=有框车窗\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 24, "bitLength": 8}, {"functionName": "-", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 32, "bitLength": 480}]}, {"didNum": "0x0700", "didByteLength": 64, "functionConfigList": [{"functionName": "闭锁自动关闭车窗天窗（HU可设置）", "valueTable": "0x00=disable \n0x01=enable\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "雨天关闭车窗天窗（HU可设置）", "valueTable": "0x00=disable \n0x01=enable\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "内循环联动关窗（HU可设置）", "valueTable": "0x00=disable \n0x01=enable\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 24, "bitLength": 24}, {"functionName": "左前车窗最大打开位置（百分比）", "valueTable": "0x00:Init\n0x01-0x65=0%~100%\n0xFF:Invalid", "resolution": 1, "bias": -1, "mainObject": "VIU_FL", "startbit": 48, "bitLength": 8}, {"functionName": "右前车窗最大打开位置（百分比）", "valueTable": "0x00:Init\n0x01-0x65=0%~100%\n0xFF:Invalid", "resolution": 1, "bias": -1, "mainObject": "VIU_FL", "startbit": 56, "bitLength": 8}, {"functionName": "左后车窗最大打开位置（百分比）", "valueTable": "0x00:Init\n0x01-0x65=0%~100%\n0xFF:Invalid", "resolution": 1, "bias": -1, "mainObject": "VIU_FL", "startbit": 64, "bitLength": 8}, {"functionName": "右后车窗最大打开位置（百分比）", "valueTable": "0x00:Init\n0x01-0x65=0%~100%\n0xFF:Invalid", "resolution": 1, "bias": -1, "mainObject": "VIU_FL", "startbit": 72, "bitLength": 8}, {"functionName": "乘客席右前车窗开关锁止状态", "valueTable": "0x00=Nolockup\n0x01=Lockup\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 80, "bitLength": 8}, {"functionName": "乘客席左后车窗开关锁止状态", "valueTable": "0x00=Nolockup\n0x01=Lockup\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 88, "bitLength": 8}, {"functionName": "乘客席右后车窗开关锁止状态", "valueTable": "0x00=Nolockup\n0x01=Lockup\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 96, "bitLength": 8}, {"functionName": "驾驶席左前车窗开关锁止状态", "valueTable": "0x00=Nolockup\n0x01=Lockup\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 104, "bitLength": 8}, {"functionName": "驾驶席右前车窗开关锁止状态", "valueTable": "0x00=Nolockup\n0x01=Lockup\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 112, "bitLength": 8}, {"functionName": "驾驶席左后车窗开关锁止状态", "valueTable": "0x00=Nolockup\n0x01=Lockup\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 120, "bitLength": 8}, {"functionName": "驾驶席右后车窗开关锁止状态", "valueTable": "0x00=Nolockup\n0x01=Lockup\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 128, "bitLength": 8}, {"functionName": "乘客席隔断玻璃开关锁止状态", "valueTable": "0x00=Nolockup\n0x01=Lockup\n0xFF=Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 136, "bitLength": 8}, {"functionName": "车窗玻璃软停点（百分比）", "valueTable": "0x00:Init\n0x01-0x65=0%~100%\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 144, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 152, "bitLength": 360}]}, {"didNum": "0x08FF", "didByteLength": 64, "functionConfigList": [{"functionName": "天窗类型配置", "valueTable": "0x00=通过LIN控制天窗\n0x01=通过HW有线控制天窗\n0x02=遮阳板\n0x03=无天窗\n0x04=天窗无LIN水平控制\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "遮阳帘类型配置", "valueTable": "0x00=无遮阳帘\n0x01=手动遮阳帘\n0x02=电动遮阳帘\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 496}]}, {"didNum": "0x0800", "didByteLength": 64, "functionConfigList": [{"functionName": "天窗逻辑是否上移", "valueTable": "0x00：天窗逻辑主体在天窗控制器\n0x01：天窗逻辑主体上移至VIU", "resolution": 1, "bias": -1, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "天窗应用层热保护是否开启", "valueTable": "0x00：天窗热保护关闭\n0x01：天窗热保护开启", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 496}]}, {"didNum": "0x09FF", "didByteLength": 64, "functionConfigList": [{"functionName": "胎压自定位功能", "valueTable": "0x0:disable ；\n0x1:enable", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "胎压接收方式", "valueTable": "0x0：外置射频模块； \n0x1：内置射频模块", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 496}]}, {"didNum": "0x0901", "didByteLength": 1, "functionConfigList": [{"functionName": "前轮胎标准压力值（推荐压力值）", "valueTable": "Factor:1.373\nOffset:0\nUnit:Kpa\n例如：0x99对应的胎压值为210.069KPa", "resolution": 1.373, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}]}, {"didNum": "0x0902", "didByteLength": 1, "functionConfigList": [{"functionName": "后轮胎标准压力值（推荐压力值）", "valueTable": "Factor:1.373\nOffset:0\nUnit:Kpa\n例如：0x99对应的胎压值为210.069KPa", "resolution": 1.373, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}]}, {"didNum": "0x0AFF", "didByteLength": 64, "functionConfigList": [{"functionName": "是否有电动尾翼", "valueTable": "0x00=无；\n0x01=有", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 504}]}, {"didNum": "0x0A01", "didByteLength": 64, "functionConfigList": [{"functionName": "电动尾翼电机热保护是否开启", "valueTable": "0x0=禁用；\n0x1=启用", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "电动尾翼防玩保护是否开启", "valueTable": "0x0=禁用；\n0x1=启用", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "电动尾翼防玩保护时间阈值", "valueTable": "0x00:5s;\n0x01:6s;\n0x02:7s;\n0x03:8s;\n0x04:9s;\n0x05:10s;\n0x06:11s;\n0x07:12s;\n0x08:13s;\n0x09:14s;\n0x0A:15s", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 8}, {"functionName": "电动尾翼防夹防撞滥用保护是否开启", "valueTable": "0x0=禁用；\n0x1=启用", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 24, "bitLength": 8}, {"functionName": "电动尾翼堵转保护是否开启", "valueTable": "0x0=禁用；\n0x1=启用", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 32, "bitLength": 8}, {"functionName": "电动尾翼随速开闭功能是否开启", "valueTable": "0x0=禁用；\n0x1=启用", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 40, "bitLength": 8}, {"functionName": "电动尾翼下电折叠功能是否开启", "valueTable": "0x0=禁用；\n0x1=启用", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 48, "bitLength": 8}, {"functionName": "电动尾翼最大开度", "valueTable": "0x0：Idle；\n0x01-0x65：0%-100%", "resolution": 1, "bias": -1, "mainObject": "VIU_FL", "startbit": 56, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 64, "bitLength": 448}]}, {"didNum": "0x0BFF", "didByteLength": 64, "functionConfigList": [{"functionName": "主驾前后轴功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "主驾上下轴功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "主驾靠背轴功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 8}, {"functionName": "主驾坐垫轴功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 24, "bitLength": 8}, {"functionName": "主驾腿拖前后轴功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 32, "bitLength": 8}, {"functionName": "主驾腿拖上下轴功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 40, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 48, "bitLength": 8}, {"functionName": "主驾座椅腰托功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 56, "bitLength": 8}, {"functionName": "主驾座椅按摩功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 64, "bitLength": 8}, {"functionName": "主驾座椅加热功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 72, "bitLength": 8}, {"functionName": "主驾座椅通风功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 80, "bitLength": 8}, {"functionName": "主驾座椅按摩气袋数量选择", "valueTable": "0x00:2个\n0x01:4个\n0x02:6个\n0x03:8个\n0x04:10个\n0x05:12个\n0x06:14个\n0x07:16个\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 88, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 96, "bitLength": 32}, {"functionName": "副驾前后轴功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 128, "bitLength": 8}, {"functionName": "副驾上下轴功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 136, "bitLength": 8}, {"functionName": "副驾靠背轴功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 144, "bitLength": 8}, {"functionName": "副驾坐垫轴功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 152, "bitLength": 8}, {"functionName": "副驾腿拖前后轴功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 160, "bitLength": 8}, {"functionName": "副驾腿拖上下轴功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 168, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 176, "bitLength": 8}, {"functionName": "副驾座椅腰托功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 184, "bitLength": 8}, {"functionName": "副驾座椅按摩功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 192, "bitLength": 8}, {"functionName": "副驾座椅加热功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 200, "bitLength": 8}, {"functionName": "副驾座椅通风功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 208, "bitLength": 8}, {"functionName": "副驾座椅按摩气袋数量选择", "valueTable": "0x00:2个\n0x01:4个\n0x02:6个\n0x03:8个\n0x04:10个\n0x05:12个\n0x06:14个\n0x07:16个\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 216, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 224, "bitLength": 32}, {"functionName": "后排左座椅加热功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 256, "bitLength": 8}, {"functionName": "后排左座椅通风功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 264, "bitLength": 8}, {"functionName": "后排右座椅加热功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 272, "bitLength": 8}, {"functionName": "后排右座椅通风功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 280, "bitLength": 8}, {"functionName": "主驾FR防夹功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 288, "bitLength": 8}, {"functionName": "主驾Hei防夹功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 296, "bitLength": 8}, {"functionName": "主驾Br防夹功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 304, "bitLength": 8}, {"functionName": "主驾Cus防夹功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 312, "bitLength": 8}, {"functionName": "主驾LegFR防夹功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 320, "bitLength": 8}, {"functionName": "主驾LegHei防夹功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 328, "bitLength": 8}, {"functionName": "副驾FR防夹功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 336, "bitLength": 8}, {"functionName": "副驾Hei防夹功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 344, "bitLength": 8}, {"functionName": "副驾Br防夹功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 352, "bitLength": 8}, {"functionName": "副驾Cus防夹功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 360, "bitLength": 8}, {"functionName": "副驾LegFR防夹功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 368, "bitLength": 8}, {"functionName": "副驾LegHei防夹功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 376, "bitLength": 8}, {"functionName": "硬开关调水平，腿拖旋转随动功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 384, "bitLength": 8}, {"functionName": "硬开关调腿拖旋转，水平随动功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 392, "bitLength": 8}, {"functionName": "主驾目标位置调节禁用功能", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 400, "bitLength": 8}, {"functionName": "主驾目标位置调节禁用功能", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 408, "bitLength": 8}, {"functionName": "硬开关调腿拖旋转，水平随动功能配置", "valueTable": "0x00:Off\n0x01:On\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 416, "bitLength": 8}, {"functionName": "主驾目标位置调节禁用功能", "valueTable": "0x00:Idle\n0x01:BeltStsMode//安全带触发\n0x02:DoorStsMode//门状态触发\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 424, "bitLength": 8}, {"functionName": "主驾目标位置调节禁用功能", "valueTable": "0x00:Idle\n0x01:BeltStsMode//安全带触发\n0x02:DoorStsMode//门状态触发\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 432, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 440, "bitLength": 72}]}, {"didNum": "0x0B27", "didByteLength": 8, "functionConfigList": [{"functionName": "水平前后调节座椅位置", "valueTable": "0x00:Invalid\n0x01:0%\n0xC9:100%", "resolution": 0.5, "bias": -0.5, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "高度上下调节座椅位置", "valueTable": "0x00:Invalid\n0x01:0%\n0xC9:100%", "resolution": 0.5, "bias": -0.5, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "靠背前后调节座椅位置", "valueTable": "0x00:Invalid\n0x01:0%\n0xC9:100%", "resolution": 0.5, "bias": -0.5, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 8}, {"functionName": "坐垫上下调节座椅位置", "valueTable": "0x00:Invalid\n0x01:0%\n0xC9:100%", "resolution": 0.5, "bias": -0.5, "mainObject": "VIU_FL", "startbit": 24, "bitLength": 8}, {"functionName": "腿托前后调节座椅位置", "valueTable": "0x00:Invalid\n0x01:0%\n0xC9:100%", "resolution": 0.5, "bias": -0.5, "mainObject": "VIU_FL", "startbit": 32, "bitLength": 8}, {"functionName": "腿托上下调节座椅位置", "valueTable": "0x00:Invalid\n0x01:0%\n0xC9:100%", "resolution": 0.5, "bias": -0.5, "mainObject": "VIU_FL", "startbit": 40, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 48, "bitLength": 16}]}, {"didNum": "0x0B2C", "didByteLength": 8, "functionConfigList": [{"functionName": "水平前后调节座椅位置", "valueTable": "0x00:Invalid\n0x01:0%\n0xC9:100%", "resolution": 0.5, "bias": -0.5, "mainObject": "VIU_FR", "startbit": 0, "bitLength": 8}, {"functionName": "高度上下调节座椅位置", "valueTable": "0x00:Invalid\n0x01:0%\n0xC9:100%", "resolution": 0.5, "bias": -0.5, "mainObject": "VIU_FR", "startbit": 8, "bitLength": 8}, {"functionName": "靠背前后调节座椅位置", "valueTable": "0x00:Invalid\n0x01:0%\n0xC9:100%", "resolution": 0.5, "bias": -0.5, "mainObject": "VIU_FR", "startbit": 16, "bitLength": 8}, {"functionName": "坐垫上下调节座椅位置", "valueTable": "0x00:Invalid\n0x01:0%\n0xC9:100%", "resolution": 0.5, "bias": -0.5, "mainObject": "VIU_FR", "startbit": 24, "bitLength": 8}, {"functionName": "腿托前后调节座椅位置", "valueTable": "0x00:Invalid\n0x01:0%\n0xC9:100%", "resolution": 0.5, "bias": -0.5, "mainObject": "VIU_FR", "startbit": 32, "bitLength": 8}, {"functionName": "腿托上下调节座椅位置", "valueTable": "0x00:Invalid\n0x01:0%\n0xC9:100%", "resolution": 0.5, "bias": -0.5, "mainObject": "VIU_FR", "startbit": 40, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FR", "startbit": 48, "bitLength": 16}]}, {"didNum": "0x0C01", "didByteLength": 64, "functionConfigList": [{"functionName": "解/闭锁后视镜自动展开/折叠开关（HU可设置）", "valueTable": "0=disable \n1=enable\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "高速行驶后视镜自动展开开关（HU可设置）", "valueTable": "0=disable \n1=enable\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "后视镜倒车自动下翻开关（HU可设置）", "valueTable": "0=disable \n1=enable\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 24, "bitLength": 488}]}, {"didNum": "0x0DFF", "didByteLength": 64, "functionConfigList": [{"functionName": "方向盘前后轴调节功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "方向盘上下轴调节功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "方向盘加热功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 8}, {"functionName": "方向盘FR防夹功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 24, "bitLength": 8}, {"functionName": "方向盘Hei防夹功能配置", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 32, "bitLength": 8}, {"functionName": "多功能方向盘模式选择配置", "valueTable": "0x00:NoReq//默认无请求，没有调节选择需要软开关需要默认请求设置成0\n0x01:LeMirrAdjMode//左后视镜调节模式\n0x02:RiMirrAdjMode//右后视镜调节模式\n0x03:SteeringAdjMode//方向盘调节模式\n0x04:MovIsland//移动中岛模式\n0x05:ReverseMode1//预留模式1\n0x06:ReverseMode2//预留模式2\n0x07:ReverseMode3//预留模式3\n0x08:ReverseMode4//预留模式4\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 40, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 48, "bitLength": 464}]}, {"didNum": "0x0EFF", "didByteLength": 64, "functionConfigList": [{"functionName": "蓝牙定位SDK调试报文控制开关", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 504}]}, {"didNum": "0x0E20", "didByteLength": 64, "functionConfigList": [{"functionName": "迎宾灯光设置状态", "valueTable": "0x00:关闭\n0x01:打开\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "迎宾解闭设置状态", "valueTable": "0x00:/关闭\n0x01:打开\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "守护模式设置状态", "valueTable": "0x00:关闭\n0x01:打开\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 8}, {"functionName": "NFC钥匙有效时间", "valueTable": "1~60（分钟）", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 24, "bitLength": 8}, {"functionName": "迎宾灯光触发时间记录", "valueTable": "时间戳（分钟）", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 32, "bitLength": 32}, {"functionName": "蓝牙实体钥匙按键1自定义功能", "valueTable": "0x00:无效 0x01:后备箱 0x02:寻车 0x03:车窗 0x04:泊车 0x05:天窗", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 64, "bitLength": 8}, {"functionName": "蓝牙实体钥匙自定义按键2自定义功能", "valueTable": "-", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 72, "bitLength": 8}, {"functionName": "蓝牙认证预唤醒最大次数", "valueTable": "-", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 80, "bitLength": 8}, {"functionName": "蓝牙认证预唤醒已执行次数", "valueTable": "-", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 88, "bitLength": 8}, {"functionName": "蓝牙实体钥匙按键3自定义功能(壳解锁按键)", "valueTable": "-", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 96, "bitLength": 8}, {"functionName": "蓝牙实体钥匙按键4自定义功能(壳闭锁按键)", "valueTable": "-", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 104, "bitLength": 8}, {"functionName": "蓝牙实体钥匙按键5自定义功能(壳解锁按键)", "valueTable": "-", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 112, "bitLength": 8}, {"functionName": "蓝牙实体钥匙激活状态", "valueTable": "-", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 120, "bitLength": 8}, {"functionName": "载货模式", "valueTable": "-", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 128, "bitLength": 8}, {"functionName": "迎宾解锁触发次数", "valueTable": "-", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 136, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 144, "bitLength": 368}]}, {"didNum": "0x0FFF", "didByteLength": 64, "functionConfigList": [{"functionName": "IBS硬件配置", "valueTable": "0x00:无IBS硬件\n0x01:有IBS硬件", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "网络不休眠本地断电功能配置字", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "15min自动下电功能配置字", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 8}, {"functionName": "智能补电SCR唤醒方式配置字", "valueTable": "0x00:禁用唤醒\n0x01:定时唤醒\n0x02:低电压唤醒\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 24, "bitLength": 8}, {"functionName": "智能补电补电累计次数配置字", "valueTable": "0~255次", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 32, "bitLength": 8}, {"functionName": "智能补电异常结束累计次数配置字", "valueTable": "0~255次", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 40, "bitLength": 8}, {"functionName": "智能补电on至off延迟触发时间设置配置字", "valueTable": "0~250分钟", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 48, "bitLength": 8}, {"functionName": "智能补电补电时间设置配置字", "valueTable": "0~250分钟", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 56, "bitLength": 8}, {"functionName": "车载冰箱持续工作指令", "valueTable": "0x00:OFF\n0x01:ON", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 64, "bitLength": 8}, {"functionName": "车载冰箱持续工作时长", "valueTable": "0x0~0x5A0=0~1440(单位:Min)", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 72, "bitLength": 16}, {"functionName": "车载冰箱有效状态", "valueTable": "0x00:单次有效\n0x01:长期有效", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 88, "bitLength": 8}, {"functionName": "应急搭电控制表", "valueTable": "最高位控制指令，后七位IOID值", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 96, "bitLength": 48}, {"functionName": "12V电源接口控制", "valueTable": "-", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 144, "bitLength": 16}, {"functionName": "智能补电BCU长时放电功率监测时间", "valueTable": "0~2500s", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 160, "bitLength": 8}, {"functionName": "智能补电BCU剩余能量监测时间", "valueTable": "0~2500s", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 168, "bitLength": 8}, {"functionName": "智能补电低温补电时间设置配置字", "valueTable": "0~250分钟", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 176, "bitLength": 8}, {"functionName": "工厂模式里程上限", "valueTable": "0~100km", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 184, "bitLength": 8}, {"functionName": "蓄电池充电故障指示灯方案配置", "valueTable": "0x00:透传VCU信号\n0x01:使用VIU逻辑", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 192, "bitLength": 8}, {"functionName": "网络不休眠本地断电功能时间配置", "valueTable": "0x00~0x1E：0~30min", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 200, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 208, "bitLength": 304}]}, {"didNum": "0x10FF", "didByteLength": 64, "functionConfigList": [{"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 512}]}, {"didNum": "0x11FF", "didByteLength": 64, "functionConfigList": [{"functionName": "后雨刮功能配置", "valueTable": "0=disable \n1=enable\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "电子雨刮功能配置", "valueTable": "0=disable \n1=enable\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "自动雨刮功能配置", "valueTable": "0=disable \n1=enable\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 8}, {"functionName": "前雨刮点刮开关类型", "valueTable": "0=硬线自复位开关\n1=方向盘开关\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 24, "bitLength": 8}, {"functionName": "前雨刮洗涤开关类型", "valueTable": "0=硬线自复位开关\n1=方向盘开关\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 32, "bitLength": 8}, {"functionName": "挡风玻璃类型", "valueTable": "0x00:Green glas windscreen Screen type 0\n0x01:Clear glass windscreen Screen type 1\n0x02:Windscreen2 Screen type 2\n0x03:Windscreen3 Screen type 3\n0x04:Reserved\n0x05:Reserved\n0x06:Reserved\n0x07:Signal invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 40, "bitLength": 8}, {"functionName": "车辆类型", "valueTable": "0x00:Vehicle 0\n0x01:Vehicle 1\n0x02:Vehicle 2\n0x03:Vehicle 3\n0x04:Vehicle 4\n0x05:Vehicle 5\n0x06:Vehicle 6\n0x07:Vehicle 7", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 48, "bitLength": 8}, {"functionName": "前雨刮间歇时间设置开关类型", "valueTable": "0x00=硬开关\n0x01=软开关\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 56, "bitLength": 8}, {"functionName": "前雨刮灵敏度设置开关类型", "valueTable": "0x00=硬开关\n0x01=软开关\n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 64, "bitLength": 8}, {"functionName": "雨量环境光LIN是否支持唤醒", "valueTable": "0x00=不支持唤醒 \n0x01=支持唤醒 \n0xFF = Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 72, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 80, "bitLength": 432}]}, {"didNum": "0x1101", "didByteLength": 64, "functionConfigList": [{"functionName": "雨刮间隙时间设置（HU可设置）", "valueTable": "0x1:Interval1//(1.0s±5%）\n0x2:Interval2//(3.5s±5%）,默认值\n0x3:Interval3//(6.0S±5%）\n0x4:Interval4//(9.5S±5%)\n0x5:Interval5//(15.5S±5%）\n0x6:Interval6//(22S±5%）\n0xFF:Invalid ", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "雨刮灵敏度设置（HU可设置）", "valueTable": "0x00：Sensitive0//最低灵敏度 \n0x01：Sensitive1//灵敏度1\n0x02：Sensitive2//灵敏度2 \n0x03：Sensitive3//灵敏度3，默认灵敏度\n0x04：Sensitive4//最高灵敏度\n0xFF：Invalid ", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "倒挡自动刮刷设置（HU可设置）", "valueTable": "0x00:disable \n0x01:enable\n0xFF:Invalid", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 8}, {"functionName": "前洗涤禁用设置", "valueTable": "0x00:Inactive//未激活\n0x01:Active//激活\n0xFF::Invalid//无效值", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 24, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 32, "bitLength": 480}]}, {"didNum": "0x12FF", "didByteLength": 64, "functionConfigList": [{"functionName": "空调类型", "valueTable": "0x00=manu AC；\n0x01=Single auto AC；\n0x02=Dual auto AC；  \n0x03=Triple auto AC；\n0x04=Quadruple auto AC；", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "面板类型", "valueTable": "0x00=无;\n0x01=LIN & LCD;   \n0x02=LIN;\n0x03=CAN;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "发动机类型", "valueTable": "0x00=无\n0x01=自然吸气;                       \n0x02=涡轮增压;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 8}, {"functionName": "PTC", "valueTable": "0x00=无\n0x01=LIN(档位控制); \n0x02=LIN(目标温度控制); \n0x03=LIN(风暖);              \n0x04=CAN(档位控制);\n0x05=CAN(目标温度控制);\n0x06=CAN(风暖);\n0x07=reserve;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 24, "bitLength": 8}, {"functionName": "压缩机类型", "valueTable": "0x00=无;\n0x01=定排量;                            \n0x02=内控变排量;\n0x03=外控变排量;\n0x04=电动压缩机(lin);\n0x05=电动压缩机(CAN);", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 32, "bitLength": 8}, {"functionName": "外温传感器", "valueTable": "0x00=无\n0x01=平台化件;                     0x02=reserve;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 40, "bitLength": 8}, {"functionName": "内温传感器", "valueTable": "0x00=无;\n0x01=平台化件;                     0x02=reserve;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 48, "bitLength": 8}, {"functionName": "蒸发器温度传感器", "valueTable": "0x01=平台化件;                     0x02=reserve;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 56, "bitLength": 8}, {"functionName": "阳光传感器", "valueTable": "0x00=无;\n0x01=ADC;                               0x02=LIN（集成在雨量传感器）;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 64, "bitLength": 8}, {"functionName": "空调压力传感器", "valueTable": "0x00=无\n0x01=三态压力开关(I/O);                          0x02=压力传感器(ADC);  ", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 72, "bitLength": 8}, {"functionName": "离子发生器", "valueTable": "0x00=无;\n0x01=开关型(I/O)，不带诊断;                       \n0x02=开关型(I/O)，带诊断;\n0x03=LIN总线型，带诊断;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 80, "bitLength": 8}, {"functionName": "PM2.5传感器", "valueTable": "0x00=无\n0x01=LIN & single;                  \n0x02=LIN & dual;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 88, "bitLength": 8}, {"functionName": "AQS传感器", "valueTable": "0x00=无\n0x01=PWM;                  \n0x02=Reserve;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 96, "bitLength": 8}, {"functionName": "温湿度传感器", "valueTable": "0x00=无;\n0x01=ADC;\n0x02=LIN（集成在雨量传感器）;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 104, "bitLength": 8}, {"functionName": "智能格栅", "valueTable": "0x00=无\n0x01=LIN;                  \n0x02=CAN;\n0x03=reserve;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 112, "bitLength": 8}, {"functionName": "香氛发生器", "valueTable": "0x00=无\n0x01=LIN;                  \n0x02=CAN;\n0x03=reserve;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 120, "bitLength": 8}, {"functionName": "电动出风口", "valueTable": "0x00=无\n0x01=LIN;                  \n0x02=CAN;\n0x03=reserve;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 128, "bitLength": 8}, {"functionName": "电池规格", "valueTable": "0x00=无\n0x01=短续航;                  \n0x02=长续航;\n0x03=reserve;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 136, "bitLength": 8}, {"functionName": "电池冷却类型", "valueTable": "0x00=无\n0x01=液冷液热;                  \n0x02=直冷膜热;\n0x03=reserve;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 144, "bitLength": 8}, {"functionName": "冷却风扇类型", "valueTable": "0x00=无\n0x01=挡位风扇;                  \n0x02=PWM风扇;\n0x03=LIN风扇;\n0x04=reserve;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 152, "bitLength": 8}, {"functionName": "电池水泵类型", "valueTable": "0x00=无                 \n0x01=PWM水泵;\n0x02=LIN水泵;\n0x03=reserve;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 160, "bitLength": 8}, {"functionName": "电驱水泵类型", "valueTable": "0x00=无                 \n0x01=PWM水泵;\n0x02=LIN水泵;\n0x03=reserve;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 168, "bitLength": 8}, {"functionName": "暖通水泵类型", "valueTable": "0x00=无                 \n0x01=PWM水泵;\n0x02=LIN水泵;\n0x03=reserve;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 176, "bitLength": 8}, {"functionName": "中冷器水泵类型", "valueTable": "0x00=无                 \n0x01=PWM水泵;\n0x02=LIN水泵;\n0x03=reserve;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 184, "bitLength": 8}, {"functionName": "空调分区", "valueTable": "0x00=无                 \n0x01=单区;\n0x02=双温区;\n0x03=三温区（前空调箱）;\n0x04=三温区（后空调箱）;\n0x05=reserve", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 192, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 200, "bitLength": 312}]}, {"didNum": "0x14FF", "didByteLength": 64, "functionConfigList": [{"functionName": "防盗开启功能", "valueTable": "0x00:关闭；\n0x01:开启；", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "从节点配置信息", "valueTable": "0x00:无从节点；\n0x01:从节点0；\n0x02:从节点1；\n0x03:从节点0，从节点1；\n0x04:从节点2；\n0x05:从节点0，从节点2；\n0x06:从节点1，从节点2；\n0x07:从节点0，从节点1，从节点2；\n0x08:从节点3；\n0x09:从节点0，从节点3；\n0x0A:从节点1，从节点3；\n0x0B:从节点0，从节点1，从节点3；\n0x0C:从节点2，从节点3；\n0x0D:从节点0，从节点2，从节点3；\n0x0E:从节点1，从节点2，从节点3；\n0x0F:从节点0，从节点1，从节点2，从节点3；\n(bit0 代表有无从节点0,bit1 代表有无从节点1,bit2 代表有无从节点2,bit3 代表有无从节点3)", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 496}]}, {"didNum": "0x1402", "didByteLength": 2, "functionConfigList": [{"functionName": "预设防时间配置", "valueTable": "秒为单位，0x000F则代表15s", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 16}]}, {"didNum": "0x1403", "didByteLength": 2, "functionConfigList": [{"functionName": "防盗激活声光报警时间配置", "valueTable": "秒为单位，0x012C则代表300s", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 16}]}, {"didNum": "0x15FF", "didByteLength": 64, "functionConfigList": [{"functionName": "是否电动开合", "valueTable": "0x0：否；\n0x1：是", "resolution": 1, "bias": 0, "mainObject": "VIU_R", "startbit": 0, "bitLength": 8}, {"functionName": "是否电动解锁", "valueTable": "0x0：否；\n0x1：是", "resolution": 1, "bias": 0, "mainObject": "VIU_R", "startbit": 8, "bitLength": 8}, {"functionName": "是否电动闭锁", "valueTable": "0x0：否；\n0x1：是", "resolution": 1, "bias": 0, "mainObject": "VIU_R", "startbit": 16, "bitLength": 8}, {"functionName": "是否有开闭状态反馈", "valueTable": "0x0：否；\n0x1：是", "resolution": 1, "bias": 0, "mainObject": "VIU_R", "startbit": 24, "bitLength": 8}, {"functionName": "是否有锁状态反馈", "valueTable": "0x0：否；\n0x1：是", "resolution": 1, "bias": 0, "mainObject": "VIU_R", "startbit": 32, "bitLength": 8}, {"functionName": "油箱盖是否电动开合", "valueTable": "0x0：否；\n0x1：是", "resolution": 1, "bias": 0, "mainObject": "VIU_R", "startbit": 40, "bitLength": 8}, {"functionName": "油箱盖是否电动解锁", "valueTable": "0x0：否；\n0x1：是", "resolution": 1, "bias": 0, "mainObject": "VIU_R", "startbit": 48, "bitLength": 8}, {"functionName": "油箱盖是否电动闭锁", "valueTable": "0x0：否；\n0x1：是", "resolution": 1, "bias": 0, "mainObject": "VIU_R", "startbit": 56, "bitLength": 8}, {"functionName": "油箱盖是否有开闭状态反馈", "valueTable": "0x0：否；\n0x1：是", "resolution": 1, "bias": 0, "mainObject": "VIU_R", "startbit": 64, "bitLength": 8}, {"functionName": "油箱盖是否有锁状态反馈", "valueTable": "0x0：否；\n0x1：是", "resolution": 1, "bias": 0, "mainObject": "VIU_R", "startbit": 72, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_R", "startbit": 80, "bitLength": 432}]}, {"didNum": "0x1501", "didByteLength": 64, "functionConfigList": [{"functionName": "充电口盖自动收回时间阈值", "valueTable": "0x00:1min;\n0x01:2min;\n0x02:3min;\n0x03:4min;\n0x04:5min;\n0x05:6min;\n0x06:7min;\n0x07:8min;\n0x08:9min;\n0x09:10min", "resolution": 1, "bias": 0, "mainObject": "VIU_R", "startbit": 0, "bitLength": 8}, {"functionName": "充电口盖电机热保护是否开启", "valueTable": "0x1=启用;\n0x2=禁用", "resolution": 1, "bias": 0, "mainObject": "VIU_R", "startbit": 8, "bitLength": 8}, {"functionName": "油箱盖自动收回时间阈值", "valueTable": "0x00:1min;\n0x01:2min;\n0x02:3min;\n0x03:4min;\n0x04:5min;\n0x05:6min;\n0x06:7min;\n0x07:8min;\n0x08:9min;\n0x09:10min", "resolution": 1, "bias": 0, "mainObject": "VIU_R", "startbit": 16, "bitLength": 8}, {"functionName": "油箱盖电机热保护是否开启", "valueTable": "0x1=启用;\n0x2=禁用", "resolution": 1, "bias": 0, "mainObject": "VIU_R", "startbit": 24, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_R", "startbit": 32, "bitLength": 480}]}, {"didNum": "0x30FF", "didByteLength": 64, "functionConfigList": [{"functionName": "中控屏换挡", "valueTable": "0：无\n1：有", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 1}, {"functionName": "方向盘换挡", "valueTable": "0：无\n1：有", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 1, "bitLength": 1}, {"functionName": "中控箱换挡", "valueTable": "0：无\n1：有", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 2, "bitLength": 1}, {"functionName": "怀挡换挡", "valueTable": "0：无\n1：有", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 3, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 4, "bitLength": 4}, {"functionName": "智驾方案配置", "valueTable": "0x00：C2架构    \n0x01：小域控架构", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 8}, {"functionName": "P档", "valueTable": "0：假P档\n1：真P档", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 24, "bitLength": 8}, {"functionName": "怀挡P挡开关电阻", "valueTable": "0:10kΩ   \n1: 2kΩ", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 32, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 40, "bitLength": 32}, {"functionName": "驾驶模式", "valueTable": "0：无\n1：有", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 72, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 80, "bitLength": 56}, {"functionName": "滑行电液分配", "valueTable": "0：无\n1：有", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 136, "bitLength": 8}, {"functionName": "动力电池类型", "valueTable": "0：三元锂电\n1：磷酸铁锂\n2：燃料电池\n3：固态锂电池", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 144, "bitLength": 8}, {"functionName": "驱动形式", "valueTable": "0：F 2WD\n1：R 2WD\n2：4WD", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 152, "bitLength": 8}, {"functionName": "动力系统类型", "valueTable": "0：EV\n1：PHEV\n2：REEV\n3：FCV\n4：非EV", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 160, "bitLength": 8}, {"functionName": "前后向误加速保护", "valueTable": "0：无\n1：有", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 168, "bitLength": 8}, {"functionName": "APA", "valueTable": "0：无APA功能,\n1：有APA功能", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 176, "bitLength": 8}, {"functionName": "ACC", "valueTable": "0：无ACC功能,\n1：有ACC功能", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 184, "bitLength": 8}, {"functionName": "ADBF", "valueTable": "0：无ADBF功能,\n1：有ADBF功能", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 192, "bitLength": 8}, {"functionName": "220V对外放电功能", "valueTable": "0：无\n1：有", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 200, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 208, "bitLength": 304}]}, {"didNum": "0x3005", "didByteLength": 64, "functionConfigList": [{"functionName": "充电SOC设置", "valueTable": "Factor:1\nOffset:0", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "预约充电状态", "valueTable": "0x0:即插即充；0x1:预约充电；0x2:Reserved；0x3:Reserved", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 8}, {"functionName": "预约充电结束时间（分）", "valueTable": "Factor:1\nOffset:0", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 24, "bitLength": 8}, {"functionName": "预约充电结束时间（时）", "valueTable": "Factor:1\nOffset:0", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 32, "bitLength": 8}, {"functionName": "预约充电开始时间（小时）", "valueTable": "Factor:1\nOffset:0", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 40, "bitLength": 8}, {"functionName": "预约充电开始时间（分钟）", "valueTable": "Factor:1\nOffset:0", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 48, "bitLength": 8}, {"functionName": "预约充电模式", "valueTable": "0x0:未设置\n0x1:设置开始,结束时间\n0x2:仅设置开始时间\n0x3:仅设置结束时间", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 56, "bitLength": 8}, {"functionName": "预约充电需求信号", "valueTable": "0x0:无请求；\n0x1:充电启动请求", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 64, "bitLength": 8}, {"functionName": "能耗选项", "valueTable": "0x0=无请求；\n0x1=CLTC工况；\n0x2=WLTC工况", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 72, "bitLength": 8}, {"functionName": "无线充电设置", "valueTable": "0x0=inactive；\n0x1=off；\n0x2=on；\n0x3=Reserved", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 80, "bitLength": 8}, {"functionName": "油门辅助设置", "valueTable": "0x0:No Request\n0x1:Request On\n0x2:Request Off", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 88, "bitLength": 8}, {"functionName": "挡位辅助设置", "valueTable": "0x0:No Request\n0x1:Request On\n0x2:Request Off", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 96, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 104, "bitLength": 408}]}, {"didNum": "0x4105", "didByteLength": 64, "functionConfigList": [{"functionName": "电子悬架高度模式设置", "valueTable": "Normal=0x0；sport=0x1； Off road=0x2；Comfort=0x3;  Auto=0x4;   eCDCAirApringAccess=0x5;            \nLoad=0x6;     reserved=0x7;  Trailer mode=0x8; maintenance=0x9;0xA:10%；\n0xB:20%；0xC:30%；0xD:40%；0xE:50%；\n0xF:60%；0x10:70%；0x11:80%；0x12:90%；0x13:100%；", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 0, "bitLength": 8}, {"functionName": "电子悬架强度模式设置", "valueTable": "0x0:Init； 0x1:Comfort；  0x2:Normal；0x3:Sport;     0x4:reserved; ", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 8, "bitLength": 8}, {"functionName": "电子悬架自适应模式设置", "valueTable": "0x0:No Request; 0x1:Request On;0x2:Request Off;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 16, "bitLength": 8}, {"functionName": "整车电子悬架模式设置", "valueTable": "0x0:NotSet;0x1:reserved;0x2:Repair;0x3: Pull;0x4:Unload;0x5:reserved;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 24, "bitLength": 8}, {"functionName": "助力转向模式设置", "valueTable": "0x0:Standar Mode；0x1:Comfort Mode；0x2:Sport Mode；0x3:reserved", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 32, "bitLength": 8}, {"functionName": "迎宾模式设置", "valueTable": "0x0:No Request; 0x1:Request On;0x2:Request Off;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 40, "bitLength": 8}, {"functionName": "经济模式对应的转向助力模式设置", "valueTable": "0x0:Standar Mode；0x1:Comfort Mode；0x2:Sport Mode；0x3:reserved", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 48, "bitLength": 8}, {"functionName": "经济模式对应的悬架强度模式设置", "valueTable": "0x0:Init； 0x1:Comfort；  0x2:Normal；0x3:Sport;     0x4:reserved; ", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 56, "bitLength": 8}, {"functionName": "舒适模式对应的制动踏板感模式设置", "valueTable": "0x0:no request；0x1:Comfort；0x2:Sport；0x3:Reserved", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 64, "bitLength": 8}, {"functionName": "运动模式对应的制动踏板感模式设置", "valueTable": "0x0:no request；0x1:Comfort；0x2:Sport；0x3:Reserved", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 72, "bitLength": 8}, {"functionName": "经济模式对应的制动踏板感模式设置", "valueTable": "0x0:no request；0x1:Comfort；0x2:Sport；0x3:Reserved", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 80, "bitLength": 8}, {"functionName": "自定义模式对应的制动踏板感模式设置", "valueTable": "0x0:no request；0x1:Comfort；0x2:Sport；0x3:Reserved", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 88, "bitLength": 8}, {"functionName": "舒适模式对应的加速模式设置", "valueTable": "0x0:Not set；0x1:Normal；0x2:Fast；0x3:Slow；0x4:Super Slow;0x5:ATS;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 96, "bitLength": 8}, {"functionName": "舒适模式对应的能量回收模式设置", "valueTable": "0x0:Not set；0x1:normal；0x2:strong；0x3~0x67：0%~100%", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 104, "bitLength": 8}, {"functionName": "运动模式对应的能量回收设置", "valueTable": "0x0:Not set；0x1:normal；0x2:strong；0x3~0x67：0%~100%", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 112, "bitLength": 8}, {"functionName": "目标加速模式设置", "valueTable": "0x0:Not set；0x1:Normal；0x2:Fast；0x3:Slow；0x4:Super Slow;0x5:ATS;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 120, "bitLength": 8}, {"functionName": "能量回收模式设置", "valueTable": "0x0:Not set；0x1:normal；0x2:strong；0x3~0x67：0%~100%", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 128, "bitLength": 8}, {"functionName": "驾驶模式设置", "valueTable": "0x0:Not set；0x1:Comfort；\n0x2:Sport；0x3:Eco；\n0x4:SuperEco；0x5:ATS； 0x6:Selfdefined；\n0x7~0xE:Reserved; 0xF:Invalid;", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 136, "bitLength": 8}, {"functionName": "蠕行功能设置", "valueTable": "0x0:Not set；0x1:on；0x2:Off；0x3:Reserved", "resolution": 1, "bias": 0, "mainObject": "VIU_FL", "startbit": 144, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "VIU_FL", "startbit": 152, "bitLength": 360}]}, {"didNum": "0x60FF", "didByteLength": 64, "functionConfigList": [{"functionName": "中控屏类型", "valueTable": "0：无\n1：1920*1080\n2：2560*1440\n3：2560*1600", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 0, "bitLength": 8}, {"functionName": "W-HUD屏类型", "valueTable": "0：无\n1：800*480", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 8, "bitLength": 8}, {"functionName": "AR-HUD屏类型", "valueTable": "0：无\n", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 16, "bitLength": 8}, {"functionName": "副驾屏类型", "valueTable": "0：无\n1：1920*1080\n2：2560*1440\n3：2560*1600", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 24, "bitLength": 8}, {"functionName": "娱乐控制面板按键类型", "valueTable": "0：无\n1：类型1:\n2：类型2:\n3：类型3", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 32, "bitLength": 8}, {"functionName": "后排娱乐屏类型", "valueTable": "0：无\n1:", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 40, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 48, "bitLength": 64}, {"functionName": "太网3", "valueTable": "0：无\n1：连接交互屏\n2：连接5G-TBOX", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 112, "bitLength": 8}, {"functionName": "T-BOX类型", "valueTable": "0：无T-BOX\n1；EDC_MCU集成4G-TOX\n2：下挂5G-TOX", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 120, "bitLength": 8}, {"functionName": "以太网1（百兆以太网）", "valueTable": "0：无\n1：1-连接VIU", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 128, "bitLength": 8}, {"functionName": "以太网2", "valueTable": "0：无\n1：连接C2\n2：连接小域控", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 136, "bitLength": 8}, {"functionName": "USB2.0", "valueTable": "0：无\n1：1-连接DVR存储卡", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 144, "bitLength": 8}, {"functionName": "USB3.0", "valueTable": "0：无\n1：1", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 152, "bitLength": 8}, {"functionName": "蓝牙", "valueTable": "0：无\n1：1个蓝牙模组", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 160, "bitLength": 8}, {"functionName": "导航定位类型", "valueTable": "0：无\n1：GPS\n2：GPS+陀螺仪", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 168, "bitLength": 8}, {"functionName": "天线类型", "valueTable": "0：无\n1：GPS天线\n2：GPS+GNSS天线", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 176, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 184, "bitLength": 32}, {"functionName": "手机互联", "valueTable": "0：无\n1：Hicar\n2：Carlife\n3：Carplay", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 216, "bitLength": 8}, {"functionName": "无线局域网（WIFI）", "valueTable": "0：无\n1：1个蓝牙/WIFI模组", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 224, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 232, "bitLength": 280}]}, {"didNum": "0x6001", "didByteLength": 64, "functionConfigList": [{"functionName": "行车记录仪", "valueTable": "0x00:无\n0x01:1路前视+4路环视\n0x02:4路环视", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 0, "bitLength": 8}, {"functionName": "流媒体后视辅助", "valueTable": "0x00:无\n0x01:后视\n0x02:环视后向", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 8, "bitLength": 8}, {"functionName": "后碰撞预警-360全景影像", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 16, "bitLength": 8}, {"functionName": "自动侧向摄像头辅助", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 24, "bitLength": 8}, {"functionName": "相机", "valueTable": "0x00:无\n0x01:1路前视+1路IMS\n0x02:1路IMS", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 32, "bitLength": 8}, {"functionName": "行车记录仪视频查看-相册", "valueTable": "0x00:无\n0x01:1路前视+4路环视\n0x02:4路环视", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 40, "bitLength": 8}, {"functionName": "哨塔视频查看-相册", "valueTable": "0x00:无\n0x01:4路环视+3D还原\n0x02:前广+周视+后视+3D还原", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 48, "bitLength": 8}, {"functionName": "营地守护视频查看-相册", "valueTable": "0x00:无\n0x01:4路环视+3D还原\n0x02:前广+周视+后视+3D还原", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 56, "bitLength": 8}, {"functionName": "录制视频-哨塔模式", "valueTable": "0x00:无\n0x01:4路环视\n0x02:前广+4路周视+后视", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 64, "bitLength": 8}, {"functionName": "闪灯灯效-哨塔模式", "valueTable": "0x00:无\n0x01:车外灯灯效\n0x02：车外灯+交互灯灯效", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 72, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 80, "bitLength": 8}, {"functionName": "事件触发-营地守护", "valueTable": "0x00:无\n0x01:环视+雷达融合感知\n0x02:环视+周视+前视+后视+雷达融合感知", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 88, "bitLength": 8}, {"functionName": "录制视频-营地守护", "valueTable": "0x00:无\n0x01:4路环视\n0x02:前广+4路周视+后视", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 96, "bitLength": 8}, {"functionName": "闪灯灯效-营地守护", "valueTable": "0x00:无\n0x01:车外灯灯效\n0x02：车外灯+交互灯灯效", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 104, "bitLength": 8}, {"functionName": "守护范围-营地守护", "valueTable": "0x00:可设置3-15m，步长为3m一单位，默认15m\n0x01:可设置3-30m，步长为5m一单位，默认30m", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 112, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 120, "bitLength": 64}, {"functionName": "副驾屏-中控屏控制", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 184, "bitLength": 8}, {"functionName": "主驾座椅加热-中控屏控制", "valueTable": "0x00:无\n0x01:坐垫加热\n0x02:坐垫加热+靠背加热", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 192, "bitLength": 8}, {"functionName": "副驾座椅加热-中控屏控制", "valueTable": "0x00:无\n0x01:坐垫加热\n0x02:坐垫加热+靠背加热", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 200, "bitLength": 8}, {"functionName": "第二排座椅加热-中控屏控制", "valueTable": "0x00:无\n0x01:坐垫加热\n0x02:坐垫加热+靠背加热", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 208, "bitLength": 8}, {"functionName": "主驾座椅通风-中控屏控制", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 216, "bitLength": 8}, {"functionName": "副驾座椅通风-中控屏控制", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 224, "bitLength": 8}, {"functionName": "副驾座椅加热-副驾屏控制", "valueTable": "0x00:无\n0x01:坐垫加热\n0x02:坐垫加热+靠背加热", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 232, "bitLength": 8}, {"functionName": "第二排座椅加热-副驾屏控制", "valueTable": "0x00:无\n0x01:坐垫加热\n0x02:坐垫加热+靠背加热", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 240, "bitLength": 8}, {"functionName": "副驾座椅通风-副驾屏控制", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 248, "bitLength": 8}, {"functionName": "后排独立温区空调-副驾屏控制", "valueTable": "0x00：无\n0x01：双温区\n0x02：三温区\n0x03：其他", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 256, "bitLength": 8}, {"functionName": "香氛系统-副驾屏控制", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 264, "bitLength": 8}, {"functionName": "主驾座椅位置调节-后排PAD控制", "valueTable": "0x00：无\n0x01：4向调节\n0x02: 其他", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 272, "bitLength": 8}, {"functionName": "主驾靠背调节-后排PAD控制", "valueTable": "0x00：无\n0x01：有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 280, "bitLength": 8}, {"functionName": "主驾座椅通风-后排PAD控制", "valueTable": "0x00：无\n0x01：有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 288, "bitLength": 8}, {"functionName": "主驾座椅加热-后排PAD控制", "valueTable": "0x00:无\n0x01:坐垫加热\n0x02:坐垫加热+靠背加热", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 296, "bitLength": 8}, {"functionName": "副驾位置调节-后排PAD控制", "valueTable": "0x00：无\n0x01：4向调节\n0x02: 其他", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 304, "bitLength": 8}, {"functionName": "副驾坐垫调节-后排PAD控制", "valueTable": "0x00：无\n0x01：有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 312, "bitLength": 8}, {"functionName": "副驾腰托调节-后排PAD控制", "valueTable": "0x00：无\n0x01：有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 320, "bitLength": 8}, {"functionName": "副驾腿托调节-后排PAD控制", "valueTable": "0x00：无\n0x01：有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 328, "bitLength": 8}, {"functionName": "副驾座椅通风-后排PAD控制", "valueTable": "0x00：无\n0x01：有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 336, "bitLength": 8}, {"functionName": "副驾座椅加热-后排PAD控制", "valueTable": "0x00:无\n0x01:坐垫加热\n0x02:坐垫加热+靠背加热", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 344, "bitLength": 8}, {"functionName": "二排座椅加热-后排PAD控制", "valueTable": "0x00:无\n0x01:坐垫加热\n0x02:坐垫加热+靠背加热", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 352, "bitLength": 8}, {"functionName": "香氛控制-后排PAD控制", "valueTable": "0x00：无香氛\n0x01：有香氛", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 360, "bitLength": 8}, {"functionName": "氛围灯自定义控制-后排PAD控制", "valueTable": "0x00：无\n0x01：不支持流水，仅支持呼吸、闪烁、常亮、多色渐变等模式\n0x02：支持流水、流星、流光、无序点光等模式", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 368, "bitLength": 8}, {"functionName": "空调控制-后排PAD控制", "valueTable": "0x00：无\n0x01：双温区\n0x02：三温区\n0x03：其他", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 376, "bitLength": 8}, {"functionName": "EQ调节-后排PAD APP音效控制", "valueTable": "0x00:9段EQ调节\n0x01:圆盘EQ调节", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 384, "bitLength": 8}, {"functionName": "音效模式->寰宇影院-后排PAD APP音效控制", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 392, "bitLength": 8}, {"functionName": "自定义音效-后排PAD APP音效控制", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 400, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 408, "bitLength": 104}]}, {"didNum": "0x6002", "didByteLength": 64, "functionConfigList": [{"functionName": "前电机-维保无忧", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 0, "bitLength": 8}, {"functionName": "激光雷达-维保无忧", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 8, "bitLength": 8}, {"functionName": "前视30°摄像头-维保无忧", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 16, "bitLength": 8}, {"functionName": "后视摄像头-维保无忧", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 24, "bitLength": 8}, {"functionName": "周视右后摄像头-维保无忧", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 32, "bitLength": 8}, {"functionName": "周视右前摄像头-维保无忧", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 40, "bitLength": 8}, {"functionName": "周视左后摄像头-维保无忧", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 48, "bitLength": 8}, {"functionName": "周视左前摄像头-维保无忧", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 56, "bitLength": 8}, {"functionName": "左前门中音扬声器-维保无忧", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 64, "bitLength": 8}, {"functionName": "右前门中音扬声器-维保无忧", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 72, "bitLength": 8}, {"functionName": "右前顶棚扬声器-维保无忧", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 80, "bitLength": 8}, {"functionName": "右后顶棚扬声器-维保无忧", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 88, "bitLength": 8}, {"functionName": "左前顶棚扬声器-维保无忧", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 96, "bitLength": 8}, {"functionName": "左后顶棚扬声器-维保无忧", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 104, "bitLength": 8}, {"functionName": "后排右环绕扬声器-维保无忧", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 112, "bitLength": 8}, {"functionName": "后排左环绕扬声器-维保无忧", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 120, "bitLength": 8}, {"functionName": "重低音扬声器-维保无忧", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 128, "bitLength": 8}, {"functionName": "悬架迎宾-智慧悬架", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 136, "bitLength": 8}, {"functionName": "经济模式调节-悬架强度调节-驾驶模式", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 144, "bitLength": 8}, {"functionName": "自定义模式调节-悬架高度调节、悬架强度调节-驾驶模式", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 152, "bitLength": 8}, {"functionName": "维修模式-智慧悬架", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 160, "bitLength": 8}, {"functionName": "轻松载物-智慧悬架", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 168, "bitLength": 8}, {"functionName": "尊享副驾-副驾屏", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 176, "bitLength": 8}, {"functionName": "一键找平-智慧悬架", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 184, "bitLength": 8}, {"functionName": "挂托模式-智慧悬架", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 192, "bitLength": 8}, {"functionName": "能量流显示", "valueTable": "0x00：两驱能量流显示\n0x01：四驱能量流显示", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 200, "bitLength": 8}, {"functionName": "超级省电-文案说明", "valueTable": "0x00：不带空悬说明\n0x01：带空悬说明", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 208, "bitLength": 8}, {"functionName": "灯光魔典-交互灯", "valueTable": "0x00:前贯穿灯\n0x01:交互屏", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 216, "bitLength": 8}, {"functionName": "音乐灯光秀-交互灯", "valueTable": "0x00:前贯穿灯\n0x01:交互屏", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 224, "bitLength": 8}, {"functionName": "车辆状态显示-驾驶", "valueTable": "0x00:无（两驱）\n0x01:有（四驱）", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 232, "bitLength": 8}, {"functionName": "悬架提示-智慧悬架", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 240, "bitLength": 8}, {"functionName": "氛围灯模式", "valueTable": "0x00：无\n0x01：不支持流水，仅支持呼吸、闪烁、常亮、多色渐变等模式\n0x02：支持流水、流星、流光、无序点光等模式", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 248, "bitLength": 8}, {"functionName": "香氛主题切换联动氛围灯", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 256, "bitLength": 8}, {"functionName": "EQ调节", "valueTable": "0x00:9段EQ调节\n0x01:圆盘EQ调节", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 264, "bitLength": 8}, {"functionName": "音效模式-寰宇影院", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 272, "bitLength": 8}, {"functionName": "调音大师（自定义音效）", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 280, "bitLength": 8}, {"functionName": "香氛系统", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 288, "bitLength": 8}, {"functionName": "接续算路-电池容量", "valueTable": "0x00:70.54kWh\n0x02:89.98kWh", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 296, "bitLength": 8}, {"functionName": "行人礼让-交互灯", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 304, "bitLength": 8}, {"functionName": "电量显示-交互灯", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 312, "bitLength": 8}, {"functionName": "紧急求救-交互灯", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 320, "bitLength": 8}, {"functionName": "车外语音-交互灯", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 328, "bitLength": 8}, {"functionName": "自动驾驶-交互灯", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 336, "bitLength": 8}, {"functionName": "电池热失控-交互灯", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 344, "bitLength": 8}, {"functionName": "歌词同步-交互灯", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 352, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 360, "bitLength": 8}, {"functionName": "泊车辅助-自定义车位-智能泊车", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 368, "bitLength": 8}, {"functionName": "位置灯皮肤-交互灯", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 376, "bitLength": 8}, {"functionName": "尊享副驾-副驾座椅靠背加热", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 384, "bitLength": 8}, {"functionName": "泊车辅助-泊车速度显示", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 392, "bitLength": 8}, {"functionName": "安全辅助-倒车碰撞辅助", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 400, "bitLength": 8}, {"functionName": "行车辅助-自学习领航", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 408, "bitLength": 8}, {"functionName": "行车辅助-路口辅助", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 416, "bitLength": 8}, {"functionName": "行车辅助-熟路模式智能换道", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 424, "bitLength": 8}, {"functionName": "行车辅助-智驾偏好-领航智驾服务", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 432, "bitLength": 8}, {"functionName": "行车辅助-交互提示偏好-陪练助手", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 440, "bitLength": 8}, {"functionName": "行车辅助-换道频率", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 448, "bitLength": 8}, {"functionName": "行车辅助-智驾风格", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 456, "bitLength": 8}, {"functionName": "安全辅助-紧急避让辅助", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 464, "bitLength": 8}, {"functionName": "行车辅助-车道偏离辅助-智能模式", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 472, "bitLength": 8}, {"functionName": "泊车辅助-无感泊车", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 480, "bitLength": 8}, {"functionName": "泊车辅助-记忆泊车", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 488, "bitLength": 8}, {"functionName": "泊车辅助-泊入方式偏好性", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 496, "bitLength": 8}, {"functionName": "泊车辅助-车位推荐偏好性", "valueTable": "0x00:无\n0x01:有", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 504, "bitLength": 8}]}, {"didNum": "0x6003", "didByteLength": 64, "functionConfigList": [{"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 0, "bitLength": 512}]}, {"didNum": "0x6004", "didByteLength": 64, "functionConfigList": [{"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 0, "bitLength": 512}]}, {"didNum": "0x6005", "didByteLength": 64, "functionConfigList": [{"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 0, "bitLength": 512}]}, {"didNum": "0x6006", "didByteLength": 64, "functionConfigList": [{"functionName": "功放类型", "valueTable": "0：无\n1：内置功放\n2：外置数字功放", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 0, "bitLength": 8}, {"functionName": "外置功放音频输入", "valueTable": "0：模拟\n1：A2B\n2：同轴\n3：光纤", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 8, "bitLength": 8}, {"functionName": "前扬声器总成", "valueTable": "0：无\n1：4", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 16, "bitLength": 8}, {"functionName": "高音扬声器总成", "valueTable": "0：无\n1：2\n2：4", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 24, "bitLength": 8}, {"functionName": "中置扬声器总成", "valueTable": "0：无\n1：1（单通道）", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 32, "bitLength": 8}, {"functionName": "报警扬声器总成", "valueTable": "0：无\n1：1", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 40, "bitLength": 8}, {"functionName": "重低音扬声器总成", "valueTable": "0：无\n1：1（单通道驱动）\n2：1（双通道驱动）\n3：2（双通道驱动）\n4：2（四通道驱动）", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 48, "bitLength": 8}, {"functionName": "中音扬声器总成", "valueTable": "0：无\n1：2", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 56, "bitLength": 8}, {"functionName": "环绕扬声器总成", "valueTable": "0：无\n1：2（单通道）\n2：2（双通道）", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 64, "bitLength": 8}, {"functionName": "顶置扬声器总成", "valueTable": "0：无\n1：2\n2：4", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 72, "bitLength": 8}, {"functionName": "头枕扬声器总成", "valueTable": "0：无\n1：2（单通道）\n2：2（双通道）", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 80, "bitLength": 8}, {"functionName": "车外扬声器数量", "valueTable": "0：无\n1：1\n2：2\n3：3\n4：4", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 88, "bitLength": 8}, {"functionName": "车内麦克风类型", "valueTable": "0：模拟麦克风\n1：数字麦克风", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 96, "bitLength": 8}, {"functionName": "车内麦克风数量", "valueTable": "0：无\n1：2\n2：4", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 104, "bitLength": 8}, {"functionName": "车外麦克风类型", "valueTable": "0：模拟麦克风\n1：数字麦克风", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 112, "bitLength": 8}, {"functionName": "车外麦克风数量", "valueTable": "0：无\n1：1\n2：2\n3：4", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 120, "bitLength": 8}, {"functionName": "RNC麦克风数量", "valueTable": "0：无\n1：2\n2：4", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 128, "bitLength": 8}, {"functionName": "RNC震动传感器数量", "valueTable": "0：无\n1：2\n2：4", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 136, "bitLength": 8}, {"functionName": "AVAS支持", "valueTable": "0：无\n1：1（单通道驱动）\n2：2（单通道驱动）", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 144, "bitLength": 8}, {"functionName": "整车报警音支持", "valueTable": "0：不支持\n1：DSP合成\n2：FLASH存储音频文件\n3：DSP合成+FLASH存储音频文件", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 152, "bitLength": 8}, {"functionName": "EQ data select（初始值）", "valueTable": "0: HIFI原声\n1:澎湃音效\n2:沉浸式环绕\n3:音乐厅\n4:House\n5.车外嗨唱", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 160, "bitLength": 8}, {"functionName": "整车报警音开关使能", "valueTable": "0：关闭\n1：开启", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 168, "bitLength": 8}, {"functionName": "四座、五座RNC功能区分", "valueTable": "0：四座\n1：五座", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 176, "bitLength": 8}, {"functionName": "功放网络管理", "valueTable": "0：无\n1：直接网络管理\n2：间接网络管理", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 184, "bitLength": 8}, {"functionName": "中前左扬声器", "valueTable": "0：无\n1：单通道驱动一个扬声器", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 192, "bitLength": 8}, {"functionName": "中前右扬声器", "valueTable": "0：无\n1：单通道驱动一个扬声器", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 200, "bitLength": 8}, {"functionName": "门中低音/全频扬声器", "valueTable": "0：无\n1：四通道驱动四门", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 208, "bitLength": 8}, {"functionName": "高音扬声器驱动通道描述", "valueTable": "0：无\n1：门板(包含A柱/三角窗位置)高音与门中低音/全频扬声器并联\n2：门板独立通道驱动", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 216, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 224, "bitLength": 120}, {"functionName": "报警提示音扬声器配置：\n头枕模式---警示提示", "valueTable": "0：无此模式\n1：除头枕外其余扬声器发音（如果实车配置无头枕，就代表全车扬声器）\n2：四门扬声器发音（根据方位CAN信号判断是否有方位）\n3：主驾头枕扬声器发音（根据方位CAN信号判断是否有方位）\n4-15：预留", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 344, "bitLength": 8}, {"functionName": "报警提示音扬声器配置：\n头枕辅助模式---警示提示", "valueTable": "0：无此模式\n1：除头枕外其余扬声器发音（如果实车配置无头枕，就代表全车扬声器）\n2：四门扬声器发音（根据方位CAN信号判断是否有方位）\n3：主驾头枕扬声器发音（根据方位CAN信号判断是否有方位）\n4-15：预留", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 352, "bitLength": 8}, {"functionName": "报警提示音扬声器配置：\n头枕关闭模式---警示提示", "valueTable": "0：无此模式\n1：除头枕外其余扬声器发音（如果实车配置无头枕，就代表全车扬声器）\n2：四门扬声器发音（根据方位CAN信号判断是否有方位）\n3：主驾头枕扬声器发音（根据方位CAN信号判断是否有方位）\n4-15：预留", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 360, "bitLength": 8}, {"functionName": "报警提示音扬声器配置：\n头枕模式---安全未系类", "valueTable": "0：无此模式\n1：除头枕外其余扬声器发音（如果实车配置无头枕，就代表全车扬声器）\n2：四门扬声器发音（根据方位CAN信号判断是否有方位）\n3：主驾头枕扬声器发音（根据方位CAN信号判断是否有方位）\n4-15：预留", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 368, "bitLength": 8}, {"functionName": "报警提示音扬声器配置：\n头枕辅助模式---安全未系类", "valueTable": "0：无此模式\n1：除头枕外其余扬声器发音（如果实车配置无头枕，就代表全车扬声器）\n2：四门扬声器发音（根据方位CAN信号判断是否有方位）\n3：主驾头枕扬声器发音（根据方位CAN信号判断是否有方位）\n4-15：预留", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 376, "bitLength": 8}, {"functionName": "报警提示音扬声器配置：\n头枕关闭模式---安全未系类", "valueTable": "0：无此模式\n1：除头枕外其余扬声器发音（如果实车配置无头枕，就代表全车扬声器）\n2：四门扬声器发音（根据方位CAN信号判断是否有方位）\n3：主驾头枕扬声器发音（根据方位CAN信号判断是否有方位）\n4-15：预留", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 384, "bitLength": 8}, {"functionName": "报警提示音扬声器配置：\n头枕模式---门未关提示", "valueTable": "0：无此模式\n1：除头枕外其余扬声器发音（如果实车配置无头枕，就代表全车扬声器）\n2：四门扬声器发音（根据方位CAN信号判断是否有方位）\n3：主驾头枕扬声器发音（根据方位CAN信号判断是否有方位）\n4-15：预留", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 392, "bitLength": 8}, {"functionName": "报警提示音扬声器配置：\n头枕辅助模式---门未关提示", "valueTable": "0：无此模式\n1：除头枕外其余扬声器发音（如果实车配置无头枕，就代表全车扬声器）\n2：四门扬声器发音（根据方位CAN信号判断是否有方位）\n3：主驾头枕扬声器发音（根据方位CAN信号判断是否有方位）\n4-15：预留", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 400, "bitLength": 8}, {"functionName": "报警提示音扬声器配置：\n头枕关闭模式---门未关提示", "valueTable": "0：无此模式\n1：除头枕外其余扬声器发音（如果实车配置无头枕，就代表全车扬声器）\n2：四门扬声器发音（根据方位CAN信号判断是否有方位）\n3：主驾头枕扬声器发音（根据方位CAN信号判断是否有方位）\n4-15：预留", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 408, "bitLength": 8}, {"functionName": "报警提示音扬声器配置：\n头枕模式---雷达提示", "valueTable": "0：无此模式\n1：除头枕外其余扬声器发音（如果实车配置无头枕，就代表全车扬声器）\n2：四门扬声器发音（根据方位CAN信号判断是否有方位）\n3：主驾头枕扬声器发音（根据方位CAN信号判断是否有方位）\n4-15：预留", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 416, "bitLength": 8}, {"functionName": "报警提示音扬声器配置：\n头枕辅助模式---雷达提示", "valueTable": "0：无此模式\n1：除头枕外其余扬声器发音（如果实车配置无头枕，就代表全车扬声器）\n2：四门扬声器发音（根据方位CAN信号判断是否有方位）\n3：主驾头枕扬声器发音（根据方位CAN信号判断是否有方位）\n4-15：预留", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 424, "bitLength": 8}, {"functionName": "报警提示音扬声器配置：\n头枕关闭模式---雷达提示", "valueTable": "0：无此模式\n1：除头枕外其余扬声器发音（如果实车配置无头枕，就代表全车扬声器）\n2：四门扬声器发音（根据方位CAN信号判断是否有方位）\n3：主驾头枕扬声器发音（根据方位CAN信号判断是否有方位）\n4-15：预留", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 432, "bitLength": 8}, {"functionName": "报警提示音扬声器配置：\n头枕模式---信息提示", "valueTable": "0：无此模式\n1：除头枕外其余扬声器发音（如果实车配置无头枕，就代表全车扬声器）\n2：四门扬声器发音（根据方位CAN信号判断是否有方位）\n3：主驾头枕扬声器发音（根据方位CAN信号判断是否有方位）\n4-15：预留", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 440, "bitLength": 8}, {"functionName": "报警提示音扬声器配置：\n头枕辅助模式---信息提示", "valueTable": "0：无此模式\n1：除头枕外其余扬声器发音（如果实车配置无头枕，就代表全车扬声器）\n2：四门扬声器发音（根据方位CAN信号判断是否有方位）\n3：主驾头枕扬声器发音（根据方位CAN信号判断是否有方位）\n4-15：预留", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 448, "bitLength": 8}, {"functionName": "报警提示音扬声器配置：\n头枕关闭模式---信息提示", "valueTable": "0：无此模式\n1：除头枕外其余扬声器发音（如果实车配置无头枕，就代表全车扬声器）\n2：四门扬声器发音（根据方位CAN信号判断是否有方位）\n3：主驾头枕扬声器发音（根据方位CAN信号判断是否有方位）\n4-15：预留", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 456, "bitLength": 8}, {"functionName": "报警提示音扬声器配置：\n头枕模式---迎宾、上下电", "valueTable": "0：无此模式\n1：除头枕外其余扬声器发音（如果实车配置无头枕，就代表全车扬声器）\n2：四门扬声器发音（根据方位CAN信号判断是否有方位）\n3：主驾头枕扬声器发音（根据方位CAN信号判断是否有方位）\n4-15：预留", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 464, "bitLength": 8}, {"functionName": "报警提示音扬声器配置：\n头枕辅助模式---迎宾、上下电", "valueTable": "0：无此模式\n1：除头枕外其余扬声器发音（如果实车配置无头枕，就代表全车扬声器）\n2：四门扬声器发音（根据方位CAN信号判断是否有方位）\n3：主驾头枕扬声器发音（根据方位CAN信号判断是否有方位）\n4-15：预留", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 472, "bitLength": 8}, {"functionName": "报警提示音扬声器配置：\n头枕关闭模式---迎宾、上下电", "valueTable": "0：无此模式\n1：除头枕外其余扬声器发音（如果实车配置无头枕，就代表全车扬声器）\n2：四门扬声器发音（根据方位CAN信号判断是否有方位）\n3：主驾头枕扬声器发音（根据方位CAN信号判断是否有方位）\n4-15：预留", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 480, "bitLength": 8}, {"functionName": "报警提示音扬声器配置：\n头枕模式---转向提示", "valueTable": "0：无此模式\n1：除头枕外其余扬声器发音（如果实车配置无头枕，就代表全车扬声器）\n2：四门扬声器发音（根据方位CAN信号判断是否有方位）\n3：主驾头枕扬声器发音（根据方位CAN信号判断是否有方位）\n4-15：预留", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 488, "bitLength": 8}, {"functionName": "报警提示音扬声器配置：\n头枕辅助模式---转向提示", "valueTable": "0：无此模式\n1：除头枕外其余扬声器发音（如果实车配置无头枕，就代表全车扬声器）\n2：四门扬声器发音（根据方位CAN信号判断是否有方位）\n3：主驾头枕扬声器发音（根据方位CAN信号判断是否有方位）\n4-15：预留", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 496, "bitLength": 8}, {"functionName": "报警提示音扬声器配置：\n头枕关闭模式---转向提示", "valueTable": "0：无此模式\n1：除头枕外其余扬声器发音（如果实车配置无头枕，就代表全车扬声器）\n2：四门扬声器发音（根据方位CAN信号判断是否有方位）\n3：主驾头枕扬声器发音（根据方位CAN信号判断是否有方位）\n4-15：预留", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 504, "bitLength": 8}]}, {"didNum": "0x6007", "didByteLength": 64, "functionConfigList": [{"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 0, "bitLength": 512}]}, {"didNum": "0x6008", "didByteLength": 64, "functionConfigList": [{"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 0, "bitLength": 512}]}, {"didNum": "0x6100", "didByteLength": 32, "functionConfigList": [{"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 0, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 8, "bitLength": 240}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 248, "bitLength": 8}]}, {"didNum": "0x6101", "didByteLength": 64, "functionConfigList": [{"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 0, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 8, "bitLength": 496}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 504, "bitLength": 8}]}, {"didNum": "0x6103", "didByteLength": 96, "functionConfigList": [{"functionName": "D29087\nEDC_MCU_5G_以太网非预期的链接断开", "valueTable": "0x00=OFF；\n0x90=ON； ", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 0, "bitLength": 8}, {"functionName": "D29287\nEDC_MCU_5G_以太网链接复位", "valueTable": "0x00=OFF；\n0x92=ON；", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 8, "bitLength": 8}, {"functionName": "D29386\nEDC_MCU_5G_以太网CRC错误", "valueTable": "0x00=OFF；\n0x93=ON；", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 16, "bitLength": 8}, {"functionName": "D29587\nEDC_MCU_5G_链接稳定后以太网信号质量低", "valueTable": "0x00=OFF；\n0x95=ON；", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 24, "bitLength": 8}, {"functionName": "D29688\nEDC_MCU_5G_以太网线缆故障", "valueTable": "0x00=OFF；\n0x96=ON；", "resolution": 1, "bias": 0, "mainObject": "EDC_MCU", "startbit": 32, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 40, "bitLength": 720}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 760, "bitLength": 8}]}, {"didNum": "0x6106", "didByteLength": 32, "functionConfigList": [{"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 0, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 8, "bitLength": 240}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 248, "bitLength": 8}]}, {"didNum": "0x6107", "didByteLength": 32, "functionConfigList": [{"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 0, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 8, "bitLength": 240}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EDC_MCU", "startbit": 248, "bitLength": 8}]}, {"didNum": "0x50FF", "didByteLength": 64, "functionConfigList": [{"functionName": "车辆是否配置IMS传感器", "valueTable": "0x0=OFF；\n0x1=ON； ", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 0, "bitLength": 8}, {"functionName": "车辆是否配置HOD", "valueTable": "0x0=OFF；\n0x1=ON； ", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 8, "bitLength": 8}, {"functionName": "车辆是否配置制动冗余", "valueTable": "0x0=OFF；\n0x1=ON； ", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 16, "bitLength": 8}, {"functionName": "车辆是否配置转向冗余", "valueTable": "0x0=OFF；\n0x1=ON； ", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 24, "bitLength": 8}, {"functionName": "车辆是否配置安全功能冗余", "valueTable": "0x0=OFF；\n0x1=ON； ", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 32, "bitLength": 8}, {"functionName": "车辆类型", "valueTable": "0x0=EV标准版\n0x1=EV两驱版\n0x2=EV四驱版\n0x3=REEV两驱版\n0x4=REEV四驱版 （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 40, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "C2_M_MCU", "startbit": 48, "bitLength": 464}]}, {"didNum": "0x5001", "didByteLength": 96, "functionConfigList": [{"functionName": "ACC产品功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （高低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 0, "bitLength": 8}, {"functionName": "IACC产品功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （高低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 8, "bitLength": 8}, {"functionName": "领航功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 16, "bitLength": 8}, {"functionName": "NDA产品功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 24, "bitLength": 8}, {"functionName": "TJP产品功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 32, "bitLength": 8}, {"functionName": "FIDA功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 40, "bitLength": 8}, {"functionName": "ALC功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （高低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 48, "bitLength": 8}, {"functionName": "UDLC功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （高低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 56, "bitLength": 8}, {"functionName": "智慧偏移功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （高低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 64, "bitLength": 8}, {"functionName": "失能辅助产品功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 72, "bitLength": 8}, {"functionName": "SEA功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 80, "bitLength": 8}, {"functionName": "BSD/LCA功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 88, "bitLength": 8}, {"functionName": "REW功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 96, "bitLength": 8}, {"functionName": "TSR功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （高低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 104, "bitLength": 8}, {"functionName": "TMR功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （高低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 112, "bitLength": 8}, {"functionName": "TLR功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （高低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 120, "bitLength": 8}, {"functionName": "CNA产品功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 128, "bitLength": 8}, {"functionName": "APA功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （高低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 136, "bitLength": 8}, {"functionName": "AVP功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 144, "bitLength": 8}, {"functionName": "HZP功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 152, "bitLength": 8}, {"functionName": "Summon功能配置 ", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 160, "bitLength": 8}, {"functionName": "RMA功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （高低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 168, "bitLength": 8}, {"functionName": "RADS功能配置 ", "valueTable": "0x0=OFF；\n0x1=ON； （高低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 176, "bitLength": 8}, {"functionName": "RPA功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （高低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 184, "bitLength": 8}, {"functionName": "RIO功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （高低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 192, "bitLength": 8}, {"functionName": "SPA功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 200, "bitLength": 8}, {"functionName": "IDA路口辅助功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 208, "bitLength": 8}, {"functionName": "OA智能绕障功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 216, "bitLength": 8}, {"functionName": "REB后向自动紧急制动辅助功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 224, "bitLength": 8}, {"functionName": "DES驾驶员触发紧急转向功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （高低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 232, "bitLength": 8}, {"functionName": "CES车道内紧急转向功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （高低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 240, "bitLength": 8}, {"functionName": "AES自动紧急转向功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （高低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 248, "bitLength": 8}, {"functionName": "AEB功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （高低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 256, "bitLength": 8}, {"functionName": "LAEB功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （高低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 264, "bitLength": 8}, {"functionName": "DIY Parking自定义车位功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 272, "bitLength": 8}, {"functionName": "Self-learning Park自学习泊车功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 280, "bitLength": 8}, {"functionName": "ELK路沿功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 288, "bitLength": 8}, {"functionName": "ELK对向来车功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 296, "bitLength": 8}, {"functionName": "ELK变道辅助功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 304, "bitLength": 8}, {"functionName": "AEB车辆功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 312, "bitLength": 8}, {"functionName": "AEB行人功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 320, "bitLength": 8}, {"functionName": "AEB二轮车功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 328, "bitLength": 8}, {"functionName": "AEB特殊车辆功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 336, "bitLength": 8}, {"functionName": "AEB其他目标功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 344, "bitLength": 8}, {"functionName": "FCW车辆功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 352, "bitLength": 8}, {"functionName": "FCW行人功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 360, "bitLength": 8}, {"functionName": "FCW二轮车功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 368, "bitLength": 8}, {"functionName": "FCW特殊车辆功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 376, "bitLength": 8}, {"functionName": "FCW其他目标功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 384, "bitLength": 8}, {"functionName": "AEB横穿低速功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 392, "bitLength": 8}, {"functionName": "AEB横穿高速功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 400, "bitLength": 8}, {"functionName": "FCW横穿低速功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 408, "bitLength": 8}, {"functionName": "FCW横穿高速功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 416, "bitLength": 8}, {"functionName": "AEB左转功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 424, "bitLength": 8}, {"functionName": "AEB右转功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 432, "bitLength": 8}, {"functionName": "AEB转弯对向车辆功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 440, "bitLength": 8}, {"functionName": "AEB转弯对向行人功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 448, "bitLength": 8}, {"functionName": "AEB转弯对向二轮车功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 456, "bitLength": 8}, {"functionName": "LAEB前向目标功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 464, "bitLength": 8}, {"functionName": "LAEB前向障碍物功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 472, "bitLength": 8}, {"functionName": "LAEB后向目标功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 480, "bitLength": 8}, {"functionName": "LAEB后向障碍物功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 488, "bitLength": 8}, {"functionName": "IRLC功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅低配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 496, "bitLength": 8}, {"functionName": "自学习领航功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 504, "bitLength": 8}, {"functionName": "LDW功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 512, "bitLength": 8}, {"functionName": "LKA功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 520, "bitLength": 8}, {"functionName": "ELK功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 528, "bitLength": 8}, {"functionName": "RCW功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 536, "bitLength": 8}, {"functionName": "RRLW功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 544, "bitLength": 8}, {"functionName": "FCW功能配置", "valueTable": "0x0=OFF；\n0x1=ON； （仅高配）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 552, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "C2_M_MCU", "startbit": 560, "bitLength": 208}]}, {"didNum": "0x5002", "didByteLength": 64, "functionConfigList": [{"functionName": "本车长度 ", "valueTable": "Phy=Hex", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 0, "bitLength": 16}, {"functionName": "本车宽度", "valueTable": "Phy=Hex", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 16, "bitLength": 16}, {"functionName": "本车高度 ", "valueTable": "Phy=Hex", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 32, "bitLength": 16}, {"functionName": "轴距", "valueTable": "Phy=Hex", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 48, "bitLength": 16}, {"functionName": "转向比 ", "valueTable": "Phy=Hex*0.1", "resolution": 0.1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 64, "bitLength": 8}, {"functionName": "转弯半径", "valueTable": "Phy=Hex*0.1", "resolution": 0.1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 72, "bitLength": 8}, {"functionName": "轮胎型号", "valueTable": "0：无效\n1：245/50 R19\n2：245/45 R20\n3：255/55 R20\n4：265/45 R21", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 80, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "C2_M_MCU", "startbit": 88, "bitLength": 40}, {"functionName": "后轴到车尾距离", "valueTable": "Phy=Hex", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 128, "bitLength": 16}, {"functionName": "外后视镜中心到后轴距离", "valueTable": "Phy=Hex", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 144, "bitLength": 16}, {"functionName": "前大灯配置", "valueTable": "0：高配前灯（带交互灯）\n1：低配前灯\n2：出口前灯", "resolution": 0.1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 160, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "C2_M_MCU", "startbit": 168, "bitLength": 184}, {"functionName": "车型项目", "valueTable": "0：无效\n1：D705\n2：CD701\n3：CD985\n4：C857\n5：D587\n6: C518\n7: P866（国内）\n8：C798\n9：C390\n10：P866（泰国）\n11: E11\n12: E11-ASE（泰国）\n13: E12\n14: E15\n15: E15中东\n16: J90A EV\n17: J90A海外\n18: J90K\n19: 236ICA\n20: C928\n21: C657\n22: CX810\n23：J90A REEV", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 352, "bitLength": 8}, {"functionName": "自驾系统方案", "valueTable": "0:C2L（m_ADC)\n1:C2M\n2:C2M+\n3:C2H\n4:C3B\n5:C3C\n6:华为MDC\nFF：无智驾", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 360, "bitLength": 8}, {"functionName": "悬架类型", "valueTable": "0：普通悬架\n1：VIU_R集成空气悬架\n2：下挂CDC控制器（C518）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 368, "bitLength": 8}, {"functionName": "语言", "valueTable": "0：中文\n1：英文", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 376, "bitLength": 8}, {"functionName": "销售区域", "valueTable": "0：中国大陆\n1：日本\n2：海湾地区\n3：东南亚\n4：西欧\n5：东欧\n6：北美\n7：南美\n8：非洲\n9：澳洲", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 384, "bitLength": 8}, {"functionName": "车身颜色", "valueTable": "0：无效\n1：WD7(冰川白）\n2：SL6（墨石灰）\n3：SL7（哑光墨石灰）\n4：GJ8（雪松绿）\n5：GJ7（竹海绿）\n6: GJ9（哑光竹海绿）\n7: YG2（大漠黄）\n8：YG3（赤霞黄）", "resolution": 1, "bias": 0, "mainObject": "C2_M_MCU", "startbit": 392, "bitLength": 8}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "C2_M_MCU", "startbit": 400, "bitLength": 112}]}, {"didNum": "0x5003", "didByteLength": 64, "functionConfigList": [{"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "C2_M_MCU", "startbit": 0, "bitLength": 512}]}, {"didNum": "0x70FF", "didByteLength": 8, "functionConfigList": [{"functionName": "驾驶员侧正面一级安全气囊(AB1FD) ", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 0, "bitLength": 1}, {"functionName": "乘员侧正面一级安全气囊(AB1FP)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 1, "bitLength": 1}, {"functionName": "驾驶员侧正面二级安全气囊(AB2FD) ", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 2, "bitLength": 1}, {"functionName": "乘员侧正面二级安全气囊(AB2FP)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 3, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "SRS", "startbit": 4, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "SRS", "startbit": 5, "bitLength": 1}, {"functionName": "前排驾驶员侧一级安全带预紧(BT1FD)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 6, "bitLength": 1}, {"functionName": "前排乘员侧一级安全带预紧(BT1FP)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 7, "bitLength": 1}, {"functionName": "前排左侧锚点安全带预紧(BT2FD)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 8, "bitLength": 1}, {"functionName": "前排右侧锚点安全带预紧(BT2FP)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 9, "bitLength": 1}, {"functionName": "前排驾驶员侧侧面一级安全气囊(SA1FD)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 10, "bitLength": 1}, {"functionName": "前排乘员侧侧面一级安全气囊(SA1FP)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 11, "bitLength": 1}, {"functionName": "前排驾驶员侧一级安全气帘 (IC1FD)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 12, "bitLength": 1}, {"functionName": "前排乘员侧一级安全气帘(IC1FP)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 13, "bitLength": 1}, {"functionName": "后排驾驶员侧侧面一级安全气囊(SA1RD)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 14, "bitLength": 1}, {"functionName": "后排乘员侧侧面一级安全气囊(SA1RP)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 15, "bitLength": 1}, {"functionName": "后排驾驶员侧一级安全带预紧(BT1RD)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 16, "bitLength": 1}, {"functionName": "后排中间侧一级安全带预紧(BT1RC)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 17, "bitLength": 1}, {"functionName": "后排乘员侧一级安全带预紧(BT1RP)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 18, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "SRS", "startbit": 19, "bitLength": 1}, {"functionName": "驾驶员远端气囊 (FSA1FD)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 20, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "SRS", "startbit": 21, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "SRS", "startbit": 22, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "SRS", "startbit": 23, "bitLength": 1}, {"functionName": "驾驶员侧前碰传感(UFSD)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 24, "bitLength": 1}, {"functionName": "中间侧前碰传感(UFSC)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 25, "bitLength": 1}, {"functionName": "乘员侧前碰传感(UFSP)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 26, "bitLength": 1}, {"functionName": "驾驶员侧侧碰传感器 (PASFD)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 27, "bitLength": 1}, {"functionName": "乘员侧侧碰传感器(PASFP)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 28, "bitLength": 1}, {"functionName": "左侧压力传感器 (PPSFD)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 29, "bitLength": 1}, {"functionName": "右侧压力传感器 (PPSFP)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 30, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "SRS", "startbit": 31, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "SRS", "startbit": 32, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "SRS", "startbit": 33, "bitLength": 1}, {"functionName": "左侧边C柱加速度传感器(PASMD)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 34, "bitLength": 1}, {"functionName": "右侧边C柱加速度传感器(PASMP)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 35, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "SRS", "startbit": 36, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "SRS", "startbit": 37, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "SRS", "startbit": 38, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "SRS", "startbit": 39, "bitLength": 1}, {"functionName": "驾驶员侧安全带锁扣开关 (BLFD) ", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 40, "bitLength": 1}, {"functionName": "乘员侧安全带锁扣开关(BLFP)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 41, "bitLength": 1}, {"functionName": "单独乘员侧安全气囊开关(PADS)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 42, "bitLength": 1}, {"functionName": "前排乘员检测(OPSFP)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 43, "bitLength": 1}, {"functionName": "后排驾驶员侧安全带锁扣开关(BLRD)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 44, "bitLength": 1}, {"functionName": "后排中间安全带锁扣开关 (BLRC)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 45, "bitLength": 1}, {"functionName": "后排乘员侧安全带锁扣开关 (BLRP)", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 46, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 47, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 48, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 49, "bitLength": 1}, {"functionName": "驾驶员侧占位传感器OPSFD ", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 50, "bitLength": 1}, {"functionName": "后排左侧乘员占位传感器OPSRD ", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 51, "bitLength": 1}, {"functionName": "后排中间乘员占位传感器 OPSRC ", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 52, "bitLength": 1}, {"functionName": "后排右侧乘员占位传感器OPSRP ", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 53, "bitLength": 1}, {"functionName": "PAB ON灯", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 54, "bitLength": 1}, {"functionName": "PAB OFF灯", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 55, "bitLength": 1}, {"functionName": "硬线故障灯线路", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 56, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "SRS", "startbit": 57, "bitLength": 1}, {"functionName": "硬线碰撞输出", "valueTable": "0:<PERSON><PERSON><PERSON>（关闭）\n1:<PERSON> （开启）", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 58, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "SRS", "startbit": 59, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "SRS", "startbit": 60, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "SRS", "startbit": 61, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "SRS", "startbit": 62, "bitLength": 1}, {"functionName": "预留", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "SRS", "startbit": 63, "bitLength": 1}]}, {"didNum": "0xFC00", "didByteLength": 1, "functionConfigList": [{"functionName": "开启/关闭IDF功能", "valueTable": "0x00: IDF function Disable\n0x01: IDF function Enable", "resolution": 1, "bias": 0, "mainObject": "SRS", "startbit": 0, "bitLength": 8}]}, {"didNum": "0x0100", "didByteLength": 3, "functionConfigList": [{"functionName": "Desired Tuning set", "valueTable": "车辆配置类型，转向自定义配置，默认0x01", "resolution": 1, "bias": 0, "mainObject": "EPS", "startbit": 0, "bitLength": 8}, {"functionName": "ChangAn DMC Enable", "valueTable": "0x0=Disable\n0x1=Enable", "resolution": 1, "bias": 0, "mainObject": "EPS", "startbit": 8, "bitLength": 1}, {"functionName": "ChangAn WIR Enable", "valueTable": "0x0=Disable\n0x1=Enable", "resolution": 1, "bias": 0, "mainObject": "EPS", "startbit": 9, "bitLength": 1}, {"functionName": "ChangAn APA Enable", "valueTable": "0x0=Disable\n0x1=Enable", "resolution": 1, "bias": 0, "mainObject": "EPS", "startbit": 10, "bitLength": 1}, {"functionName": "ChangAn L3 ADAS Enable", "valueTable": "0x0=Disable\n0x1=Enable", "resolution": 1, "bias": 0, "mainObject": "EPS", "startbit": 11, "bitLength": 1}, {"functionName": "ChangAn LDW Enable", "valueTable": "0x0=Disable\n0x1=Enable", "resolution": 1, "bias": 0, "mainObject": "EPS", "startbit": 12, "bitLength": 1}, {"functionName": "ChangAn ESS Enable", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EPS", "startbit": 13, "bitLength": 1}, {"functionName": "ChangAn TSM Enable", "valueTable": "0x0=Disable\n0x1=Enable", "resolution": 1, "bias": 0, "mainObject": "EPS", "startbit": 14, "bitLength": 1}, {"functionName": "Reserved", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EPS", "startbit": 15, "bitLength": 1}, {"functionName": "ChangAn Vehicle Type", "valueTable": "0x0=燃油车\n0x1=电动车", "resolution": 1, "bias": 0, "mainObject": "EPS", "startbit": 16, "bitLength": 1}, {"functionName": "Reserved", "valueTable": "-", "resolution": 1, "bias": 1, "mainObject": "EPS", "startbit": 17, "bitLength": 7}]}, {"didNum": "0xF1A8", "didByteLength": 1, "functionConfigList": [{"functionName": "IBCU_RBM配置变量", "valueTable": "0x00=无效\n0x01=（75KWH+RWD+普通悬架）\n0x02=（94KWH+RWD+普通悬架）\n0x03=（94KWH+RWD+空气悬架）\n0x04=（94KWH+4WD+普通悬架）\n0x05=（94KWH+4WD+空气悬架）", "resolution": 1, "bias": 0, "mainObject": "IBCU", "startbit": 0, "bitLength": 8}]}], "ecuList": [{"name": "VIU_FL", "didList": "0x01FF,0x01E0,0x02FF,0x0200,0x03FF,0x0300,0x04FF,0x0401,0x05FF,0x0501,0x06FF,0x0601,0x07FF,0x0700,0x08FF,0x0800,0x09FF,0x0901,0x0902,0x0AFF,0x0A01,0x0BFF,0x0B27,0x0B2C,0x0C01,0x0DFF,0x0EFF,0x0E20,0x0FFF,0x10FF,0x11FF,0x1101,0x12FF,0x14FF,0x1402,0x1403,0x15FF,0x1501,0x30FF,0x3005,0x4105"}, {"name": "VIU_FR", "didList": "0x01FF,0x01E0,0x02FF,0x0200,0x03FF,0x0300,0x04FF,0x0401,0x05FF,0x0501,0x06FF,0x0601,0x07FF,0x0700,0x08FF,0x0800,0x09FF,0x0901,0x0902,0x0AFF,0x0A01,0x0BFF,0x0B27,0x0B2C,0x0C01,0x0DFF,0x0EFF,0x0E20,0x0FFF,0x10FF,0x11FF,0x1101,0x12FF,0x14FF,0x1402,0x1403,0x15FF,0x1501,0x30FF,0x3005,0x4105"}, {"name": "VIU_R", "didList": "0x01FF,0x01E0,0x02FF,0x0200,0x03FF,0x0300,0x04FF,0x0401,0x05FF,0x0501,0x06FF,0x0601,0x07FF,0x0700,0x08FF,0x0800,0x09FF,0x0901,0x0902,0x0AFF,0x0A01,0x0BFF,0x0B27,0x0B2C,0x0C01,0x0DFF,0x0EFF,0x0E20,0x0FFF,0x10FF,0x11FF,0x1101,0x12FF,0x14FF,0x1402,0x1403,0x15FF,0x1501,0x30FF,0x3005,0x4105"}, {"name": "EDC_MCU", "didList": "0x60FF,0x6001,0x6002,0x6003,0x6004,0x6005,0x6006,0x6007,0x6008,0x6100,0x6101,0x6103,0x6106,0x6107,0x5001,0x5002"}, {"name": "C2_M_MCU", "didList": "0x30FF,0x50FF,0x5001,0x5002,0x5003"}, {"name": "M_ADC_MCU", "didList": "0x50FF"}, {"name": "ITMS", "didList": "0x12FF,0x30FF,0x5002"}, {"name": "SRS", "didList": "0x70FF,0xFC00"}, {"name": "EPS", "didList": "0x0100"}, {"name": "IBCU", "didList": "0xF1A8"}, {"name": "RBM", "didList": "0xF1A8"}, {"name": "FMIPU", "didList": "0x5002"}, {"name": "RMIPU", "didList": "0x30FF,0x5002"}, {"name": "AMP", "didList": "0x6006,0x5002"}, {"name": "FLR", "didList": "0x5002"}, {"name": "FRR", "didList": "0x5002"}, {"name": "RLR", "didList": "0x5002"}, {"name": "RRR", "didList": "0x5002"}, {"name": "RLR_L", "didList": "0x5002"}, {"name": "RRR_L", "didList": "0x5002"}, {"name": "FLR_L", "didList": "0x5002"}, {"name": "FRR_L", "didList": "0x5002"}, {"name": "FR", "didList": "0x5002"}, {"name": "HCM", "didList": "0x5002"}]}