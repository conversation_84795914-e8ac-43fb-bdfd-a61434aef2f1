package android.hardware.automotive.cavehicle.V1_0;

import android.os.HidlSupport;
import android.os.HwBlob;
import android.os.HwParcel;
import java.util.ArrayList;
import java.util.Objects;

/* loaded from: classes.dex */
public final class CreateUserRequest {
    public int requestId = 0;
    public UserInfo newUserInfo = new UserInfo();
    public String newUserName = new String();
    public UsersInfo usersInfo = new UsersInfo();

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || obj.getClass() != CreateUserRequest.class) {
            return false;
        }
        CreateUserRequest createUserRequest = (CreateUserRequest) obj;
        return this.requestId == createUserRequest.requestId && HidlSupport.deepEquals(this.newUserInfo, createUserRequest.newUserInfo) && HidlSupport.deepEquals(this.newUserName, createUserRequest.newUserName) && HidlSupport.deepEquals(this.usersInfo, createUserRequest.usersInfo);
    }

    public final int hashCode() {
        return Objects.hash(Integer.valueOf(HidlSupport.deepHashCode(Integer.valueOf(this.requestId))), Integer.valueOf(HidlSupport.deepHashCode(this.newUserInfo)), Integer.valueOf(HidlSupport.deepHashCode(this.newUserName)), Integer.valueOf(HidlSupport.deepHashCode(this.usersInfo)));
    }

    public final String toString() {
        return "{.requestId = " + this.requestId + ", .newUserInfo = " + this.newUserInfo + ", .newUserName = " + this.newUserName + ", .usersInfo = " + this.usersInfo + "}";
    }

    public final void readFromParcel(HwParcel hwParcel) {
        readEmbeddedFromParcel(hwParcel, hwParcel.readBuffer(64L), 0L);
    }

    public static final ArrayList<CreateUserRequest> readVectorFromParcel(HwParcel hwParcel) {
        ArrayList<CreateUserRequest> arrayList = new ArrayList<>();
        HwBlob readBuffer = hwParcel.readBuffer(16L);
        int int32 = readBuffer.getInt32(8L);
        HwBlob readEmbeddedBuffer = hwParcel.readEmbeddedBuffer(int32 * 64, readBuffer.handle(), 0L, true);
        arrayList.clear();
        for (int i = 0; i < int32; i++) {
            CreateUserRequest createUserRequest = new CreateUserRequest();
            createUserRequest.readEmbeddedFromParcel(hwParcel, readEmbeddedBuffer, i * 64);
            arrayList.add(createUserRequest);
        }
        return arrayList;
    }

    public final void readEmbeddedFromParcel(HwParcel hwParcel, HwBlob hwBlob, long j) {
        this.requestId = hwBlob.getInt32(j + 0);
        this.newUserInfo.readEmbeddedFromParcel(hwParcel, hwBlob, j + 4);
        long j2 = j + 16;
        this.newUserName = hwBlob.getString(j2);
        hwParcel.readEmbeddedBuffer(r5.getBytes().length + 1, hwBlob.handle(), j2 + 0, false);
        this.usersInfo.readEmbeddedFromParcel(hwParcel, hwBlob, j + 32);
    }

    public final void writeToParcel(HwParcel hwParcel) {
        HwBlob hwBlob = new HwBlob(64);
        writeEmbeddedToBlob(hwBlob, 0L);
        hwParcel.writeBuffer(hwBlob);
    }

    public static final void writeVectorToParcel(HwParcel hwParcel, ArrayList<CreateUserRequest> arrayList) {
        HwBlob hwBlob = new HwBlob(16);
        int size = arrayList.size();
        hwBlob.putInt32(8L, size);
        hwBlob.putBool(12L, false);
        HwBlob hwBlob2 = new HwBlob(size * 64);
        for (int i = 0; i < size; i++) {
            arrayList.get(i).writeEmbeddedToBlob(hwBlob2, i * 64);
        }
        hwBlob.putBlob(0L, hwBlob2);
        hwParcel.writeBuffer(hwBlob);
    }

    public final void writeEmbeddedToBlob(HwBlob hwBlob, long j) {
        hwBlob.putInt32(0 + j, this.requestId);
        this.newUserInfo.writeEmbeddedToBlob(hwBlob, 4 + j);
        hwBlob.putString(16 + j, this.newUserName);
        this.usersInfo.writeEmbeddedToBlob(hwBlob, j + 32);
    }
}
