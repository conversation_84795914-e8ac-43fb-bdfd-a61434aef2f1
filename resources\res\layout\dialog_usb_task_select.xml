<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/transparent"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:background="@color/transparent"
        android:layout_width="match_parent"
        android:layout_height="1024dp"
        android:minHeight="1024dp">
        <ImageView
            android:id="@+id/usb_task_back_iv"
            android:background="@drawable/caui_icon_dialog_close"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginTop="55dp"
            android:layout_marginStart="64dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <TextView
            android:textSize="44sp"
            android:textColor="@color/caui_config_text_color_primary"
            android:gravity="center"
            android:layout_width="wrap_content"
            android:layout_height="62dp"
            android:layout_marginTop="52dp"
            android:text="@string/dialog_usb_task_select"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.499"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="150dp"
            android:layout_marginBottom="199dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <androidx.recyclerview.widget.RecyclerView
                android:scrollbarThumbVertical="@drawable/fota_scroll_thumb"
                android:scrollbarStyle="outsideOverlay"
                android:id="@+id/rv_task_list"
                android:scrollbars="vertical"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
            android:textSize="36sp"
            android:gravity="center"
            android:id="@+id/btn_usb_task_confirm"
            android:layout_width="544dp"
            android:layout_height="96dp"
            android:layout_marginBottom="56dp"
            android:text="@string/dialog_usb_new_version_confirm"
            android:layout_marginStart="64dp"
            app:caui_round_btn_type="main"
            app:caui_round_radius="@dimen/caui_config_corner_radius_small"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>
        <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
            android:textSize="36sp"
            android:gravity="center"
            android:id="@+id/btn_usb_task_cancel"
            android:layout_width="544dp"
            android:layout_height="96dp"
            android:layout_marginBottom="56dp"
            android:text="@string/dialog_usb_new_version_cancel"
            android:layout_marginEnd="64dp"
            app:caui_round_btn_type="negative"
            app:caui_round_radius="@dimen/caui_config_corner_radius_small"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
