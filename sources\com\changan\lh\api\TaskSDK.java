package com.changan.lh.api;

import com.incall.apps.libbase.utils.FileUtils;
import java.io.File;
import java.util.List;

/* loaded from: classes.dex */
public class TaskSDK {

    public static class Api {
        public static String getUTaskJsonByJsonStr(String str, String str2, String str3, List<String> list, boolean z) {
            return TaskUtil2.createUpdateListByStr(str, str2, str3, list, z);
        }

        public static String getUTaskFileByJsonStr(String str, String str2, String str3, String str4, String str5, List<String> list, boolean z) {
            String uTaskJsonByJsonStr = getUTaskJsonByJsonStr(str, str2, str3, list, z);
            String str6 = str4 + File.separator + str5;
            FileUtils.writeTxtFile(str6, uTaskJsonByJsonStr);
            return str6;
        }

        public static String getUTaskAbsolutePathByJsonStr(String str, String str2, String str3, String str4, String str5) {
            return getUTaskFileByJsonStr(str, str2, str3, str4, str5, null, false);
        }

        public static String getUTaskAbsolutePathByJsonStrDispatch(String str, String str2, String str3, String str4, String str5) {
            return getUTaskFileByJsonStr(str, str2, str3, str4, str5, null, true);
        }

        public static String getUTaskAbsolutePathByJsonStr(String str, String str2, String str3, String str4, String str5, List<String> list) {
            return getUTaskFileByJsonStr(str, str2, str3, str4, str5, list, false);
        }

        public static String getRollbackTaskJsonByJsonStr(String str, String str2, String str3) {
            return TaskUtil2.createRollbackListByStr(str, str2, str3);
        }

        public static String getRollbackTaskFileByJsonStr(String str, String str2, String str3, String str4, String str5) {
            String rollbackTaskJsonByJsonStr = getRollbackTaskJsonByJsonStr(str, str2, str3);
            String str6 = str4 + File.separator + str5;
            FileUtils.writeTxtFile(str6, rollbackTaskJsonByJsonStr);
            return str6;
        }

        public static String getRollbackTaskAbsolutePathByJsonStr(String str, String str2, String str3, String str4, String str5) {
            return getRollbackTaskFileByJsonStr(str, str2, str3, str4, str5);
        }
    }
}
