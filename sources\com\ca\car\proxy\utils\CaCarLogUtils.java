package com.ca.car.proxy.utils;

import android.util.Log;

/* loaded from: classes.dex */
public class CaCarLogUtils {
    private static final String PreTag = "CaCarProxy: ";
    private static boolean isDebug = true;

    public static void w(String str, String str2) {
        if (isDebug) {
            Log.w(PreTag + str, str2);
        }
    }

    public static void e(String str, String str2) {
        if (isDebug) {
            Log.e(PreTag + str, str2);
        }
    }

    public static void i(String str, String str2) {
        if (isDebug) {
            Log.d(PreTag + str, str2);
        }
    }

    public static void d(String str, String str2) {
        if (isDebug) {
            Log.d(PreTag + str, str2);
        }
    }

    public static void v(String str, String str2) {
        if (isDebug) {
            Log.v(PreTag + str, str2);
        }
    }
}
