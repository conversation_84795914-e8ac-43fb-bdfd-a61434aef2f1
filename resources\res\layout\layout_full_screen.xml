<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@android:color/black"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <com.incall.apps.caui.layout.CAUIImageView
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:adjustViewBounds="true"
        android:layout_alignParentBottom="true"
        android:layout_alignParentStart="true"
        app:caui_shape_radius="@dimen/caui_config_corner_radius_large"/>
    <ImageView
        android:id="@+id/back"
        android:visibility="gone"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginTop="180dp"
        android:src="@drawable/fota_full_back"
        android:scaleType="fitXY"
        android:layout_alignParentTop="true"
        android:layout_marginStart="43dp"
        android:layout_alignParentStart="true"/>
</RelativeLayout>
