<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:tag="layout/item_res_list_0"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <androidx.cardview.widget.CardView
        android:background="@drawable/fota_image_corner"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="3dp"
        android:layout_marginEnd="12dp">
        <com.incall.apps.softmanager.base.view.StyleVideoPlayer
            android:id="@+id/sp_res_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
        <com.incall.apps.softmanager.base.view.FullScreenImageView
            android:id="@+id/iv_res_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"/>
    </androidx.cardview.widget.CardView>
</FrameLayout>
