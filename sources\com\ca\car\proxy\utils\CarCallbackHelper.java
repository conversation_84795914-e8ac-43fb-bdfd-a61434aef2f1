package com.ca.car.proxy.utils;

import android.content.Context;
import android.database.ContentObserver;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;

/* loaded from: classes.dex */
public abstract class Car<PERSON>allbackHelper {
    private final String TAG = "CarCallbackHelper";
    protected Context mContext = null;
    protected Handler mMainHandler = null;
    protected Handler mTaskHandler = null;

    protected abstract void initCallback();

    protected abstract void parseMsg(Message message);

    protected abstract void registerObserver(Context context, String str, ContentObserver contentObserver);

    protected abstract void unregisterObserver(ContentObserver contentObserver);

    public class CarObserver extends ContentObserver {
        private Handler mHandler;
        private int mMsg;

        public CarObserver(Handler handler, int i) {
            super(null);
            this.mHandler = null;
            this.mMsg = -1;
            this.mHandler = handler;
            this.mMsg = i;
        }

        @Override // android.database.ContentObserver
        public void onChange(boolean z) {
            this.mHandler.sendEmptyMessage(this.mMsg);
        }
    }

    protected Handler getMainHandler() {
        if (this.mMainHandler == null) {
            this.mMainHandler = new Handler(Looper.getMainLooper());
        }
        return this.mMainHandler;
    }

    protected Handler getTaskHandler(String str) {
        if (this.mTaskHandler == null) {
            HandlerThread handlerThread = new HandlerThread("CARCALLBACK#" + str);
            handlerThread.start();
            this.mTaskHandler = new Handler(handlerThread.getLooper()) { // from class: com.ca.car.proxy.utils.CarCallbackHelper.1
                @Override // android.os.Handler
                public void handleMessage(Message message) {
                    try {
                        CarCallbackHelper.this.parseMsg(message);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            };
        }
        return this.mTaskHandler;
    }
}
