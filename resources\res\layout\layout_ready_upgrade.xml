<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:tag="layout/layout_ready_upgrade_0"
    android:layout_width="1412dp"
    android:layout_height="wrap_content"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">
    <com.incall.apps.caui.layout.CAUITextView
        android:textSize="24sp"
        android:textColor="@color/caui_config_text_color_secondary"
        android:gravity="center"
        android:id="@+id/new_version_tip_tx"
        android:background="@color/caui_config_tag_bg_green"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginTop="40dp"
        android:text="@string/new_version_tip"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:layout_marginStart="104dp"
        app:caui_radius="@dimen/caui_config_corner_radius_middle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="56sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/tv_download_upgrade_title"
        android:tag="binding_1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginStart="104dp"
        android:textFontWeight="600"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/new_version_tip_tx"/>
    <TextView
        android:textSize="40sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/ready_upgrade_estimatedTime_tx"
        android:tag="binding_2"
        android:layout_width="1260dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginStart="104dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_download_upgrade_title"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/lock_car_btn"
        android:tag="binding_3"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="80dp"
        android:text="@string/lock_install"
        android:contentDescription="锁车升级"
        android:layout_marginStart="104dp"
        app:caui_round_btn_type="main"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ready_upgrade_estimatedTime_tx"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:tag="binding_4"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="80dp"
        android:text="@string/start_install"
        android:contentDescription="立即升级"
        android:layout_marginStart="480dp"
        app:caui_round_btn_type="third"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ready_upgrade_estimatedTime_tx"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/usb_upgrade_btn"
        android:tag="binding_5"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="80dp"
        android:text="@string/start_usb_install"
        android:layout_marginStart="104dp"
        app:caui_round_btn_type="main"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ready_upgrade_estimatedTime_tx"/>
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="1260dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="80dp"
        android:layout_marginStart="104dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/usb_upgrade_btn">
        <com.incall.apps.softmanager.base.view.AutoDownloadSwitchBar
            android:id="@+id/auto_download_sw_ready_upgrade_page"
            android:tag="binding_6"
            android:layout_width="1260dp"
            android:layout_height="wrap_content"/>
        <com.incall.apps.softmanager.base.view.AutoUpdateSwitchBar
            android:id="@+id/auto_sw_ready_upgrade_page"
            android:tag="binding_7"
            android:layout_width="1260dp"
            android:layout_height="wrap_content"/>
        <View
            android:background="@color/caui_config_divider_color_primary"
            android:layout_width="1260dp"
            android:layout_height="2dp"
            android:layout_marginTop="16dp"/>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
