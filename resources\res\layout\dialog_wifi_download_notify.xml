<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/transparent"
    android:layout_width="880dp"
    android:layout_height="wrap_content">
    <ImageView
        android:id="@+id/wifi_download_close_img"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="55dp"
        android:src="@drawable/caui_icon_dialog_close"
        android:layout_marginStart="64dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="40sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:gravity="center"
        android:id="@+id/detected_new_version_dialog_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="48dp"
        android:text="@string/wifi_download_dialog_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="40sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/tx_wifi_download_dialog_version"
        android:layout_width="752dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:text="@string/wifi_download_dialog_new_version"
        android:layout_marginStart="64dp"
        android:textFontWeight="500"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/detected_new_version_dialog_title"/>
    <TextView
        android:textSize="32sp"
        android:textColor="@color/caui_config_text_color_secondary"
        android:id="@+id/tx_wifi_download_package_size"
        android:layout_width="752dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/wifi_download_dialog_package_size"
        android:textFontWeight="500"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tx_wifi_download_dialog_version"/>
    <TextView
        android:textSize="32sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/tx_wifi_download_dialog_update_tips"
        android:layout_width="752dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="@string/wifi_download_dialog_tips"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tx_wifi_download_package_size"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/btn_wifi_download"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="48dp"
        android:layout_marginBottom="56dp"
        android:text="@string/wifi_download_dialog_btn_download"
        android:contentDescription="下载"
        android:layout_marginStart="64dp"
        app:caui_round_btn_type="main"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tx_wifi_download_dialog_update_tips"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/btn_later"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="48dp"
        android:layout_marginBottom="56dp"
        android:text="@string/wifi_download_dialog_btn_later"
        android:contentDescription="下次提醒"
        android:layout_marginEnd="64dp"
        app:caui_round_btn_type="negative"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tx_wifi_download_dialog_update_tips"/>
</androidx.constraintlayout.widget.ConstraintLayout>
