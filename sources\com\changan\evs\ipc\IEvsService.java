package com.changan.evs.ipc;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;
import com.changan.evs.ipc.IEvsServiceCallback;

/* loaded from: classes.dex */
public interface IEvsService extends IInterface {

    public static class Default implements IEvsService {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.changan.evs.ipc.IEvsService
        public int regEvsServiceCallback(String str, IEvsServiceCallback iEvsServiceCallback) throws RemoteException {
            return 0;
        }

        @Override // com.changan.evs.ipc.IEvsService
        public void responseToEvs(EvsMessage evsMessage) throws RemoteException {
        }

        @Override // com.changan.evs.ipc.IEvsService
        public void sendMessage(EvsMessage evsMessage, int i) throws RemoteException {
        }

        @Override // com.changan.evs.ipc.IEvsService
        public boolean subscribe(int[] iArr) throws RemoteException {
            return false;
        }

        @Override // com.changan.evs.ipc.IEvsService
        public void unregEvsServiceCallback() throws RemoteException {
        }

        @Override // com.changan.evs.ipc.IEvsService
        public void unsubscribe(int[] iArr) throws RemoteException {
        }
    }

    int regEvsServiceCallback(String str, IEvsServiceCallback iEvsServiceCallback) throws RemoteException;

    void responseToEvs(EvsMessage evsMessage) throws RemoteException;

    void sendMessage(EvsMessage evsMessage, int i) throws RemoteException;

    boolean subscribe(int[] iArr) throws RemoteException;

    void unregEvsServiceCallback() throws RemoteException;

    void unsubscribe(int[] iArr) throws RemoteException;

    public static abstract class Stub extends Binder implements IEvsService {
        private static final String DESCRIPTOR = "com.changan.evs.ipc.IEvsService";
        static final int TRANSACTION_regEvsServiceCallback = 1;
        static final int TRANSACTION_responseToEvs = 6;
        static final int TRANSACTION_sendMessage = 5;
        static final int TRANSACTION_subscribe = 3;
        static final int TRANSACTION_unregEvsServiceCallback = 2;
        static final int TRANSACTION_unsubscribe = 4;

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static IEvsService asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof IEvsService)) {
                return (IEvsService) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i == 1598968902) {
                parcel2.writeString(DESCRIPTOR);
                return true;
            }
            switch (i) {
                case 1:
                    parcel.enforceInterface(DESCRIPTOR);
                    int regEvsServiceCallback = regEvsServiceCallback(parcel.readString(), IEvsServiceCallback.Stub.asInterface(parcel.readStrongBinder()));
                    parcel2.writeNoException();
                    parcel2.writeInt(regEvsServiceCallback);
                    return true;
                case 2:
                    parcel.enforceInterface(DESCRIPTOR);
                    unregEvsServiceCallback();
                    parcel2.writeNoException();
                    return true;
                case 3:
                    parcel.enforceInterface(DESCRIPTOR);
                    boolean subscribe = subscribe(parcel.createIntArray());
                    parcel2.writeNoException();
                    parcel2.writeInt(subscribe ? 1 : 0);
                    return true;
                case 4:
                    parcel.enforceInterface(DESCRIPTOR);
                    unsubscribe(parcel.createIntArray());
                    parcel2.writeNoException();
                    return true;
                case 5:
                    parcel.enforceInterface(DESCRIPTOR);
                    sendMessage(parcel.readInt() != 0 ? EvsMessage.CREATOR.createFromParcel(parcel) : null, parcel.readInt());
                    parcel2.writeNoException();
                    return true;
                case 6:
                    parcel.enforceInterface(DESCRIPTOR);
                    responseToEvs(parcel.readInt() != 0 ? EvsMessage.CREATOR.createFromParcel(parcel) : null);
                    parcel2.writeNoException();
                    return true;
                default:
                    return super.onTransact(i, parcel, parcel2, i2);
            }
        }

        private static class Proxy implements IEvsService {
            public static IEvsService sDefaultImpl;
            private IBinder mRemote;

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            @Override // com.changan.evs.ipc.IEvsService
            public int regEvsServiceCallback(String str, IEvsServiceCallback iEvsServiceCallback) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeStrongBinder(iEvsServiceCallback != null ? iEvsServiceCallback.asBinder() : null);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().regEvsServiceCallback(str, iEvsServiceCallback);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.changan.evs.ipc.IEvsService
            public void unregEvsServiceCallback() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregEvsServiceCallback();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.changan.evs.ipc.IEvsService
            public boolean subscribe(int[] iArr) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeIntArray(iArr);
                    if (!this.mRemote.transact(3, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().subscribe(iArr);
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.changan.evs.ipc.IEvsService
            public void unsubscribe(int[] iArr) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeIntArray(iArr);
                    if (!this.mRemote.transact(4, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unsubscribe(iArr);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.changan.evs.ipc.IEvsService
            public void sendMessage(EvsMessage evsMessage, int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (evsMessage != null) {
                        obtain.writeInt(1);
                        evsMessage.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(5, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().sendMessage(evsMessage, i);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.changan.evs.ipc.IEvsService
            public void responseToEvs(EvsMessage evsMessage) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (evsMessage != null) {
                        obtain.writeInt(1);
                        evsMessage.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    if (!this.mRemote.transact(6, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().responseToEvs(evsMessage);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public static boolean setDefaultImpl(IEvsService iEvsService) {
            if (Proxy.sDefaultImpl != null) {
                throw new IllegalStateException("setDefaultImpl() called twice");
            }
            if (iEvsService == null) {
                return false;
            }
            Proxy.sDefaultImpl = iEvsService;
            return true;
        }

        public static IEvsService getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }
    }
}
