<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/transparent"
    android:layout_width="880dp"
    android:layout_height="wrap_content"
    android:minWidth="880dp"
    android:minHeight="390dp">
    <ImageView
        android:id="@+id/close_dial_dialog"
        android:background="@color/transparent"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="55dp"
        android:src="@drawable/caui_icon_dialog_close"
        android:layout_marginStart="64dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="40sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:gravity="center"
        android:id="@+id/dial_dialog_title"
        android:layout_width="wrap_content"
        android:layout_height="56dp"
        android:layout_marginTop="48dp"
        android:text="@string/ready_to_dial"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="32sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:gravity="center"
        android:id="@+id/phone_number_tx"
        android:layout_width="wrap_content"
        android:layout_height="54dp"
        android:layout_marginTop="32dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/dial_dialog_title"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/confirm_dial_btn"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="48dp"
        android:layout_marginBottom="56dp"
        android:text="@string/dial"
        android:contentDescription="拨打"
        android:layout_marginStart="64dp"
        app:caui_round_btn_type="main"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/phone_number_tx"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/cancel_dial_btn"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="48dp"
        android:layout_marginBottom="56dp"
        android:text="@string/cancel_dial"
        android:contentDescription="取消"
        android:layout_marginStart="472dp"
        app:caui_round_btn_type="secondary"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/phone_number_tx"/>
</androidx.constraintlayout.widget.ConstraintLayout>
