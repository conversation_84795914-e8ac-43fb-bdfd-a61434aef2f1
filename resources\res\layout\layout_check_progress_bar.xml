<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="180dp"
    android:layout_height="180dp">
    <com.incall.apps.softmanager.base.view.CircleProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <TextView
        android:textSize="56sp"
        android:textColor="@color/caui_config_text_color_third"
        android:id="@+id/tx_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="24sp"
        android:textColor="@color/caui_config_text_color_third"
        android:id="@+id/tx_percent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:text="%"
        app:layout_constraintStart_toEndOf="@+id/tx_progress"
        app:layout_constraintTop_toTopOf="@+id/tx_progress"/>
    <ImageView
        android:id="@+id/img_meet"
        android:visibility="gone"
        android:layout_width="72dp"
        android:layout_height="72dp"
        android:src="@drawable/caui_icon_progress_status_success"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
