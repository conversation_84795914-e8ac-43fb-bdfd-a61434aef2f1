package android.hardware.automotive.cavehicle.V1_0;

import android.os.HidlSupport;
import android.os.HwBlob;
import android.os.HwParcel;
import java.util.ArrayList;
import java.util.Objects;

/* loaded from: classes.dex */
public final class SwitchUserResponse {
    public int requestId = 0;
    public int messageType = 0;
    public int status = 0;
    public String errorMessage = new String();

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || obj.getClass() != SwitchUserResponse.class) {
            return false;
        }
        SwitchUserResponse switchUserResponse = (SwitchUserResponse) obj;
        return this.requestId == switchUserResponse.requestId && this.messageType == switchUserResponse.messageType && this.status == switchUserResponse.status && HidlSupport.deepEquals(this.errorMessage, switchUserResponse.errorMessage);
    }

    public final int hashCode() {
        return Objects.hash(Integer.valueOf(HidlSupport.deepHashCode(Integer.valueOf(this.requestId))), Integer.valueOf(HidlSupport.deepHashCode(Integer.valueOf(this.messageType))), Integer.valueOf(HidlSupport.deepHashCode(Integer.valueOf(this.status))), Integer.valueOf(HidlSupport.deepHashCode(this.errorMessage)));
    }

    public final String toString() {
        return "{.requestId = " + this.requestId + ", .messageType = " + SwitchUserMessageType.toString(this.messageType) + ", .status = " + SwitchUserStatus.toString(this.status) + ", .errorMessage = " + this.errorMessage + "}";
    }

    public final void readFromParcel(HwParcel hwParcel) {
        readEmbeddedFromParcel(hwParcel, hwParcel.readBuffer(32L), 0L);
    }

    public static final ArrayList<SwitchUserResponse> readVectorFromParcel(HwParcel hwParcel) {
        ArrayList<SwitchUserResponse> arrayList = new ArrayList<>();
        HwBlob readBuffer = hwParcel.readBuffer(16L);
        int int32 = readBuffer.getInt32(8L);
        HwBlob readEmbeddedBuffer = hwParcel.readEmbeddedBuffer(int32 * 32, readBuffer.handle(), 0L, true);
        arrayList.clear();
        for (int i = 0; i < int32; i++) {
            SwitchUserResponse switchUserResponse = new SwitchUserResponse();
            switchUserResponse.readEmbeddedFromParcel(hwParcel, readEmbeddedBuffer, i * 32);
            arrayList.add(switchUserResponse);
        }
        return arrayList;
    }

    public final void readEmbeddedFromParcel(HwParcel hwParcel, HwBlob hwBlob, long j) {
        this.requestId = hwBlob.getInt32(j + 0);
        this.messageType = hwBlob.getInt32(4 + j);
        this.status = hwBlob.getInt32(8 + j);
        long j2 = j + 16;
        this.errorMessage = hwBlob.getString(j2);
        hwParcel.readEmbeddedBuffer(r2.getBytes().length + 1, hwBlob.handle(), j2 + 0, false);
    }

    public final void writeToParcel(HwParcel hwParcel) {
        HwBlob hwBlob = new HwBlob(32);
        writeEmbeddedToBlob(hwBlob, 0L);
        hwParcel.writeBuffer(hwBlob);
    }

    public static final void writeVectorToParcel(HwParcel hwParcel, ArrayList<SwitchUserResponse> arrayList) {
        HwBlob hwBlob = new HwBlob(16);
        int size = arrayList.size();
        hwBlob.putInt32(8L, size);
        hwBlob.putBool(12L, false);
        HwBlob hwBlob2 = new HwBlob(size * 32);
        for (int i = 0; i < size; i++) {
            arrayList.get(i).writeEmbeddedToBlob(hwBlob2, i * 32);
        }
        hwBlob.putBlob(0L, hwBlob2);
        hwParcel.writeBuffer(hwBlob);
    }

    public final void writeEmbeddedToBlob(HwBlob hwBlob, long j) {
        hwBlob.putInt32(0 + j, this.requestId);
        hwBlob.putInt32(4 + j, this.messageType);
        hwBlob.putInt32(8 + j, this.status);
        hwBlob.putString(j + 16, this.errorMessage);
    }
}
