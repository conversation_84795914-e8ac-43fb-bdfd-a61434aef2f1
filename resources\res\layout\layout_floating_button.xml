<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <TextView
        android:textSize="32sp"
        android:textStyle="bold"
        android:textColor="@color/caui_config_text_color_primary"
        android:gravity="center"
        android:id="@+id/floating_button"
        android:background="@drawable/button_shape"
        android:clickable="true"
        android:layout_width="150dp"
        android:layout_height="150dp"
        android:text="工程\10模式"
        android:alpha="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
