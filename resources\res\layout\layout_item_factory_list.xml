<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:tag="layout/layout_item_factory_list_0"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <TextView
        android:textSize="24sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/tv_item_info"
        android:tag="binding_1"
        android:paddingBottom="12dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
