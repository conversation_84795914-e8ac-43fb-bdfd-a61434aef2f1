<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:tag="layout/layout_order_0"
    android:layout_width="1412dp"
    android:layout_height="wrap_content">
    <com.incall.apps.caui.layout.CAUITextView
        android:textSize="24sp"
        android:textColor="@color/caui_config_text_color_secondary"
        android:gravity="center"
        android:id="@+id/new_version_tip_tx"
        android:background="@color/caui_config_tag_bg_green"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginTop="40dp"
        android:text="@string/new_version_tip"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:layout_marginStart="104dp"
        app:caui_radius="@dimen/caui_config_corner_radius_middle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="56sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/big_version_tx"
        android:tag="binding_1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginStart="104dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/new_version_tip_tx"/>
    <TextView
        android:textSize="40sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/layout_order_desc"
        android:tag="binding_2"
        android:layout_width="1260dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginStart="104dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/big_version_tx"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/order_time_button"
        android:tag="binding_3"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="80dp"
        android:text="@string/auto_order_install_change"
        android:layout_marginStart="104dp"
        app:caui_round_btn_icon="@drawable/order_time_icon"
        app:caui_round_btn_iconGravity="textStart"
        app:caui_round_btn_iconPadding="28sp"
        app:caui_round_btn_iconSize="40dp"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layout_order_desc"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:tag="binding_4"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="80dp"
        android:text="@string/auto_order_install_cancel"
        android:layout_marginStart="480dp"
        app:caui_round_btn_type="secondary"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layout_order_desc"/>
    <View
        android:id="@+id/layout_order_view_line"
        android:background="@color/caui_config_divider_color_primary"
        android:layout_width="1260dp"
        android:layout_height="2dp"
        android:layout_marginTop="80dp"
        android:layout_marginStart="104dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/order_time_button"/>
</androidx.constraintlayout.widget.ConstraintLayout>
