<?xml version="1.0" encoding="utf-8"?>
<com.incall.apps.caui.layout.CAUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/caui_config_content_bg_color_primary"
    android:clipToPadding="false"
    android:layout_width="1152dp"
    android:layout_height="wrap_content"
    android:layout_marginBottom="24dp"
    android:minHeight="188dp"
    android:layout_marginStart="64dp"
    android:layout_marginEnd="64dp"
    app:caui_radius="@dimen/caui_config_corner_radius_middle"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">
    <CheckBox
        android:id="@+id/cb_task_title"
        android:background="@drawable/fota_bg_checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:button="@null"
        android:layout_marginStart="48dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="40sp"
        android:textColor="@color/caui_config_text_color_secondary"
        android:id="@+id/tv_task_title"
        android:layout_width="960dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:text="2022年06月30日   版本：12.3.0"
        android:layout_marginStart="48dp"
        app:layout_constraintStart_toEndOf="@+id/cb_task_title"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="32sp"
        android:textColor="@color/caui_config_text_color_secondary"
        android:id="@+id/tv_task_content"
        android:layout_width="960dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:layout_marginBottom="24dp"
        android:text="2022年06月30日   版本：12.3.0"
        android:layout_marginStart="48dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/cb_task_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_task_title"/>
</com.incall.apps.caui.layout.CAUIConstraintLayout>
