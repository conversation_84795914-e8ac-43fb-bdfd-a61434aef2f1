package com.changan.evs.ipc.proxy.base;

import android.content.Context;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.RemoteException;
import com.changan.evs.ipc.EvsMessage;
import com.changan.evs.ipc.IEvsService;
import com.changan.evs.ipc.IEvsServiceCallback;
import com.changan.evs.ipc.proxy.callback.AsyncMessageCallback;
import com.changan.evs.ipc.proxy.callback.EvsProxyInterface;
import com.changan.evs.ipc.proxy.callback.IAppMessageCallback;
import com.changan.evs.ipc.proxy.callback.IEvsServiceConnectionCallback;
import com.changan.evs.ipc.proxy.constant.Constant;
import com.changan.evs.ipc.proxy.util.AppLock;
import com.changan.evs.ipc.proxy.util.AsyncUtils;
import com.changan.evs.ipc.proxy.util.LogUtil;
import com.changan.evs.ipc.proxy.util.ProxyUtil;
import com.incall.apps.master.status.upgrade.UpgradeAction;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/* loaded from: classes.dex */
public abstract class EvsBaseProxy extends ProxyUtil<IEvsService> implements EvsProxyInterface {
    private static final int MSG_TIME_OUT_CHECK_INTERVAL = 1000;
    protected static String TAG = "EvsBaseProxy";
    private Context mContext;
    private volatile int mRequestId;
    private int mShortCycleRetryTimes;
    private String packageName;
    private Handler mHandler = new Handler(Looper.getMainLooper());
    private boolean isRetrying = false;
    private ConcurrentHashMap<String, IEvsServiceConnectionCallback> mConnectionCallbackList = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Integer, MessageBean> mRequestMap = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Integer, MessageBean> mUpRequestMap = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Integer, Handler> mSubscribeMap = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Integer, CopyOnWriteArrayList<IAppMessageCallback>> mSubscribeCallbackMap = new ConcurrentHashMap<>();
    private Handler handler = new Handler();
    private Object objLock = new Object();
    private Runnable timeoutCheckRunnable = new Runnable() { // from class: com.changan.evs.ipc.proxy.base.EvsBaseProxy.1
        @Override // java.lang.Runnable
        public void run() {
            synchronized (EvsBaseProxy.this.objLock) {
                Iterator it = EvsBaseProxy.this.mRequestMap.entrySet().iterator();
                while (it.hasNext()) {
                    final MessageBean messageBean = (MessageBean) ((Map.Entry) it.next()).getValue();
                    if (messageBean != null && System.currentTimeMillis() - messageBean.getMessage().getTimeStamp() > messageBean.getTimeoutMs()) {
                        if (messageBean.getHandler() != null) {
                            messageBean.setResponseMessage(messageBean.getMessage().getEvsMessage());
                            messageBean.getResponseMessage().setCode(203);
                            final AsyncMessageCallback messageCallback = messageBean.getMessageCallback();
                            if (messageCallback == null) {
                                LogUtil.getInstance().eL(EvsBaseProxy.TAG, "timeout message messageId: " + messageBean.getResponseMessage().getMessageId() + ", messageCode: " + messageBean.getResponseMessage().getCode());
                            } else {
                                messageBean.getHandler().post(new Runnable() { // from class: com.changan.evs.ipc.proxy.base.EvsBaseProxy.1.1
                                    @Override // java.lang.Runnable
                                    public void run() {
                                        messageCallback.onResult(messageBean.getResponseMessage());
                                    }
                                });
                            }
                        }
                        it.remove();
                    }
                }
                if (!EvsBaseProxy.this.mRequestMap.isEmpty()) {
                    EvsBaseProxy.this.handler.postDelayed(this, 1000L);
                } else {
                    EvsBaseProxy.this.handler.removeCallbacks(null);
                }
            }
        }
    };
    private final IEvsServiceCallback.Stub iCaEvsServiceCallback = new IEvsServiceCallback.Stub() { // from class: com.changan.evs.ipc.proxy.base.EvsBaseProxy.3
        @Override // com.changan.evs.ipc.IEvsServiceCallback
        public void onBroadcastMessageReceive(final EvsMessage evsMessage) {
            LogUtil.getInstance().dL(EvsBaseProxy.TAG, "onBroadcastMessageReceive");
            if (evsMessage != null) {
                LogUtil.getInstance().dL(EvsBaseProxy.TAG, "messageId is " + evsMessage.getMessageId());
                CopyOnWriteArrayList copyOnWriteArrayList = (CopyOnWriteArrayList) EvsBaseProxy.this.mSubscribeCallbackMap.get(Integer.valueOf(evsMessage.getMessageId()));
                if (copyOnWriteArrayList == null || copyOnWriteArrayList.isEmpty()) {
                    return;
                }
                Iterator it = copyOnWriteArrayList.iterator();
                while (it.hasNext()) {
                    final IAppMessageCallback iAppMessageCallback = (IAppMessageCallback) it.next();
                    Handler handler = (Handler) EvsBaseProxy.this.mSubscribeMap.get(Integer.valueOf(evsMessage.getMessageId()));
                    if (handler != null) {
                        handler.post(new Runnable() { // from class: com.changan.evs.ipc.proxy.base.EvsBaseProxy.3.1
                            @Override // java.lang.Runnable
                            public void run() {
                                iAppMessageCallback.onBroadcastMessageReceive(evsMessage);
                            }
                        });
                    } else {
                        iAppMessageCallback.onBroadcastMessageReceive(evsMessage);
                    }
                }
                return;
            }
            LogUtil.getInstance().dL(EvsBaseProxy.TAG, "message is null");
        }

        @Override // com.changan.evs.ipc.IEvsServiceCallback
        public void onResponseEvent(final EvsMessage evsMessage) {
            LogUtil.getInstance().dL(EvsBaseProxy.TAG, "onResponseEvent");
            if (evsMessage == null) {
                return;
            }
            LogUtil.getInstance().dL(EvsBaseProxy.TAG, "msgId:" + evsMessage.getMessageId());
            LogUtil.getInstance().dL(EvsBaseProxy.TAG, "code:" + evsMessage.getCode());
            LogUtil.getInstance().dL(EvsBaseProxy.TAG, "requestId:" + evsMessage.getRequestId());
            MessageBean messageBean = (MessageBean) EvsBaseProxy.this.mRequestMap.get(Integer.valueOf(evsMessage.getRequestId()));
            if (messageBean != null) {
                Handler handler = messageBean.getHandler();
                LogUtil.getInstance().dL(EvsBaseProxy.TAG, "handler == " + handler);
                if (handler == null) {
                    AppLock appLock = messageBean.getAppLock();
                    messageBean.setResponseMessage(evsMessage);
                    if (appLock != null) {
                        appLock.notifyAllEvent();
                    }
                } else {
                    final AsyncMessageCallback messageCallback = messageBean.getMessageCallback();
                    LogUtil.getInstance().eL(EvsBaseProxy.TAG, "onResponseEvent messageId: " + evsMessage.getMessageId() + ", messageCode: " + evsMessage.getCode() + ", messageCallback is " + messageCallback);
                    if (messageCallback != null) {
                        handler.post(new Runnable() { // from class: com.changan.evs.ipc.proxy.base.EvsBaseProxy.3.2
                            @Override // java.lang.Runnable
                            public void run() {
                                messageCallback.onResult(evsMessage);
                            }
                        });
                    }
                }
            } else {
                LogUtil.getInstance().dL(EvsBaseProxy.TAG, "msgBean is null");
            }
            EvsBaseProxy.this.clearTimeoutRequest(evsMessage.getRequestId());
        }

        @Override // com.changan.evs.ipc.IEvsServiceCallback
        public void onPrivateMessageReceive(final EvsMessage evsMessage) {
            LogUtil.getInstance().dL(EvsBaseProxy.TAG, "onPrivateMessageReceive");
            MessageBean messageBean = new MessageBean();
            messageBean.setMessage(evsMessage.getEvsMessage());
            EvsBaseProxy.this.mUpRequestMap.put(Integer.valueOf(evsMessage.getRequestId()), messageBean);
            CopyOnWriteArrayList copyOnWriteArrayList = (CopyOnWriteArrayList) EvsBaseProxy.this.mSubscribeCallbackMap.get(Integer.valueOf(evsMessage.getMessageId()));
            if (copyOnWriteArrayList != null && !copyOnWriteArrayList.isEmpty()) {
                Iterator it = copyOnWriteArrayList.iterator();
                while (it.hasNext()) {
                    final IAppMessageCallback iAppMessageCallback = (IAppMessageCallback) it.next();
                    Handler handler = (Handler) EvsBaseProxy.this.mSubscribeMap.get(Integer.valueOf(evsMessage.getMessageId()));
                    if (handler != null) {
                        handler.post(new Runnable() { // from class: com.changan.evs.ipc.proxy.base.EvsBaseProxy.3.3
                            @Override // java.lang.Runnable
                            public void run() {
                                iAppMessageCallback.onPrivateMessageReceive(evsMessage);
                            }
                        });
                    } else {
                        iAppMessageCallback.onPrivateMessageReceive(evsMessage);
                    }
                }
            } else {
                LogUtil.getInstance().eL(EvsBaseProxy.TAG, "user has no subscribe this message. messageId is " + evsMessage.getMessageId());
            }
            Iterator it2 = EvsBaseProxy.this.mUpRequestMap.entrySet().iterator();
            while (it2.hasNext()) {
                if (((MessageBean) ((Map.Entry) it2.next()).getValue()) != null && System.currentTimeMillis() - r5.getTimeoutMs() > r5.getTimeoutMs()) {
                    it2.remove();
                }
            }
        }
    };

    protected abstract String getServiceName();

    protected String getVersionName() {
        return Constant.VERSION_NAME;
    }

    static /* synthetic */ int access$508(EvsBaseProxy evsBaseProxy) {
        int i = evsBaseProxy.mShortCycleRetryTimes;
        evsBaseProxy.mShortCycleRetryTimes = i + 1;
        return i;
    }

    @Override // com.changan.evs.ipc.proxy.util.ProxyUtil
    protected void dealServiceDied() {
        LogUtil.getInstance().dL(TAG, "dealServiceDied");
        this.mService = null;
        notifyServiceDisconnected(101, Constant.HintText.SERVICE_DISCONNECTION_TEXT);
        connectionService();
        clearUserSubscribeInfo();
    }

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // com.changan.evs.ipc.proxy.util.ProxyUtil
    public IEvsService linkToService(IBinder iBinder) {
        try {
            this.mService = IEvsService.Stub.asInterface(iBinder);
            registerServiceCallback();
        } catch (Exception e) {
            e.printStackTrace();
            this.mService = null;
        }
        return (IEvsService) this.mService;
    }

    @Override // com.changan.evs.ipc.proxy.util.ProxyUtil
    public boolean assertService() {
        if (this.mService == 0) {
            LogUtil.getInstance().dL(TAG, "mService is null");
            this.mService = getService(getServiceName());
        }
        return this.mService != 0;
    }

    private void connectionService() {
        this.mShortCycleRetryTimes = 0;
        AsyncUtils.getInstance().getAsyncHandler().postDelayed(new Runnable() { // from class: com.changan.evs.ipc.proxy.base.EvsBaseProxy.2
            @Override // java.lang.Runnable
            public void run() {
                if (EvsBaseProxy.this.assertService()) {
                    EvsBaseProxy.this.notifyServiceConnected(100, Constant.HintText.SERVICE_CONNECTION_TEXT);
                    LogUtil.getInstance().dL(EvsBaseProxy.TAG, "retry connectionService success");
                    EvsBaseProxy.this.isRetrying = false;
                    AsyncUtils.getInstance().getAsyncHandler().removeCallbacks(this);
                    return;
                }
                EvsBaseProxy.this.isRetrying = true;
                if (EvsBaseProxy.this.mShortCycleRetryTimes < 119) {
                    EvsBaseProxy.access$508(EvsBaseProxy.this);
                    AsyncUtils.getInstance().getAsyncHandler().postDelayed(this, 500L);
                } else {
                    AsyncUtils.getInstance().getAsyncHandler().postDelayed(this, UpgradeAction.OTA_MODE_END_DELAY);
                }
            }
        }, 500L);
    }

    private int registerServiceCallback() {
        if (this.mService == 0) {
            return 101;
        }
        try {
            if (this.mContext == null) {
                return ((IEvsService) this.mService).regEvsServiceCallback(this.packageName, this.iCaEvsServiceCallback);
            }
            return ((IEvsService) this.mService).regEvsServiceCallback(this.mContext.getPackageName(), this.iCaEvsServiceCallback);
        } catch (RemoteException e) {
            e.printStackTrace();
            return 101;
        }
    }

    private void unregisterServiceCallback() {
        if (this.mService != 0) {
            try {
                ((IEvsService) this.mService).unregEvsServiceCallback();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    private void clearUserSubscribeInfo() {
        this.mRequestMap.clear();
        this.mUpRequestMap.clear();
        this.mRequestId = 0;
        this.mSubscribeCallbackMap.clear();
        this.mSubscribeMap.clear();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void clearTimeoutRequest(int i) {
        Iterator<Map.Entry<Integer, MessageBean>> it = this.mRequestMap.entrySet().iterator();
        while (it.hasNext()) {
            if (i == it.next().getValue().getMessage().getRequestId()) {
                it.remove();
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void notifyServiceConnected(int i, String str) {
        LogUtil.getInstance().dL(TAG, "notifyServiceConnected");
        Iterator<Map.Entry<String, IEvsServiceConnectionCallback>> it = this.mConnectionCallbackList.entrySet().iterator();
        while (it.hasNext()) {
            it.next().getValue().onServiceConnected(i, str);
        }
    }

    private void notifyServiceDisconnected(int i, String str) {
        LogUtil.getInstance().dL(TAG, "notifyServiceDisconnected");
        Iterator<Map.Entry<String, IEvsServiceConnectionCallback>> it = this.mConnectionCallbackList.entrySet().iterator();
        while (it.hasNext()) {
            it.next().getValue().onServiceDisconnected(i, str);
        }
    }

    @Override // com.changan.evs.ipc.proxy.callback.EvsProxyInterface
    public void connect(Context context, IEvsServiceConnectionCallback iEvsServiceConnectionCallback) {
        LogUtil.getInstance().dL(TAG, "connect packName is " + context.getPackageName());
        this.mContext = context;
        connect(context.getPackageName(), iEvsServiceConnectionCallback);
    }

    @Override // com.changan.evs.ipc.proxy.callback.EvsProxyInterface
    public void connect(String str, IEvsServiceConnectionCallback iEvsServiceConnectionCallback) {
        this.packageName = str;
        synchronized (this.mConnectionCallbackList) {
            if (this.mConnectionCallbackList.contains(str)) {
                this.mConnectionCallbackList.remove(str);
            }
            synchronized (this.mConnectionCallbackList) {
                this.mConnectionCallbackList.put(str, iEvsServiceConnectionCallback);
            }
            connectionService();
        }
    }

    @Override // com.changan.evs.ipc.proxy.callback.EvsProxyInterface
    public void responseToEvs(EvsMessage evsMessage) {
        synchronized (this) {
            if (this.mService != 0) {
                try {
                    ((IEvsService) this.mService).responseToEvs(evsMessage);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            } else {
                LogUtil.getInstance().dL(TAG, "responseToEvs mService is null");
            }
        }
    }

    @Override // com.changan.evs.ipc.proxy.callback.EvsProxyInterface
    public EvsMessage sendMessageSync(int i) {
        return sendMessageSync(i, 3000);
    }

    @Override // com.changan.evs.ipc.proxy.callback.EvsProxyInterface
    public EvsMessage sendMessageSync(int i, int i2) {
        return sendMessageSync(new EvsMessage(i), i2);
    }

    @Override // com.changan.evs.ipc.proxy.callback.EvsProxyInterface
    public EvsMessage sendMessageSync(EvsMessage evsMessage) {
        return sendMessageSync(evsMessage, 3000);
    }

    @Override // com.changan.evs.ipc.proxy.callback.EvsProxyInterface
    public EvsMessage sendMessageSync(EvsMessage evsMessage, int i) {
        EvsMessage responseMessage;
        synchronized (this) {
            EvsMessage evsMessage2 = new EvsMessage();
            MessageBean messageBean = new MessageBean();
            messageBean.setResponseMessage(evsMessage2);
            if (this.mService != 0) {
                try {
                    int i2 = this.mRequestId + 1;
                    this.mRequestId = i2;
                    evsMessage.setRequestId(i2);
                    evsMessage.setTimeStamp(Long.valueOf(System.currentTimeMillis()));
                    evsMessage2 = evsMessage.getEvsMessage();
                    evsMessage2.setCode(203);
                    AppLock appLock = new AppLock();
                    messageBean.setMessage(evsMessage);
                    messageBean.setTimeoutMs(i > 0 ? i : 3000);
                    messageBean.setAppLock(appLock);
                    this.mRequestMap.put(Integer.valueOf(evsMessage.getRequestId()), messageBean);
                    LogUtil.getInstance().dL(TAG, "messageId is " + evsMessage.getMessageId());
                    LogUtil.getInstance().dL(TAG, "requestId is " + evsMessage.getRequestId());
                    LogUtil.getInstance().dL(TAG, "timeStamp is " + evsMessage.getTimeStamp());
                    ((IEvsService) this.mService).sendMessage(evsMessage, i > 0 ? i : 3000);
                    appLock.waitForEvent(i > 0 ? i : 3000L);
                } catch (RemoteException e) {
                    e.printStackTrace();
                    evsMessage2.setCode(101);
                }
            } else {
                evsMessage2.setCode(101);
                LogUtil.getInstance().dL(TAG, "sendMessageSync mService is null");
            }
            this.timeoutCheckRunnable.run();
            responseMessage = messageBean.getResponseMessage();
        }
        return responseMessage;
    }

    @Override // com.changan.evs.ipc.proxy.callback.EvsProxyInterface
    public void sendMessageAsync(int i) {
        sendMessageAsync(i, (AsyncMessageCallback) null);
    }

    @Override // com.changan.evs.ipc.proxy.callback.EvsProxyInterface
    public void sendMessageAsync(int i, AsyncMessageCallback asyncMessageCallback) {
        sendMessageAsync(i, this.mHandler, asyncMessageCallback);
    }

    @Override // com.changan.evs.ipc.proxy.callback.EvsProxyInterface
    public void sendMessageAsync(int i, Handler handler, AsyncMessageCallback asyncMessageCallback) {
        sendMessageAsync(new EvsMessage(i), handler, asyncMessageCallback);
    }

    @Override // com.changan.evs.ipc.proxy.callback.EvsProxyInterface
    public void sendMessageAsync(EvsMessage evsMessage) {
        sendMessageAsync(evsMessage, (AsyncMessageCallback) null);
    }

    @Override // com.changan.evs.ipc.proxy.callback.EvsProxyInterface
    public void sendMessageAsync(EvsMessage evsMessage, AsyncMessageCallback asyncMessageCallback) {
        sendMessageAsync(evsMessage, this.mHandler, asyncMessageCallback);
    }

    @Override // com.changan.evs.ipc.proxy.callback.EvsProxyInterface
    public void sendMessageAsync(EvsMessage evsMessage, Handler handler, AsyncMessageCallback asyncMessageCallback) {
        if (this.mService != 0) {
            try {
                int i = this.mRequestId + 1;
                this.mRequestId = i;
                evsMessage.setRequestId(i);
                evsMessage.setTimeStamp(Long.valueOf(System.currentTimeMillis()));
                MessageBean messageBean = new MessageBean();
                messageBean.setMessage(evsMessage);
                messageBean.setMessageCallback(asyncMessageCallback);
                if (handler == null) {
                    handler = this.mHandler;
                }
                messageBean.setHandler(handler);
                messageBean.setTimeoutMs(3000);
                messageBean.setResponseMessage(evsMessage.getEvsMessage());
                this.mRequestMap.put(Integer.valueOf(evsMessage.getRequestId()), messageBean);
                LogUtil.getInstance().dL(TAG, "messageId is " + evsMessage.getMessageId());
                LogUtil.getInstance().dL(TAG, "requestId is " + evsMessage.getRequestId());
                LogUtil.getInstance().dL(TAG, "timeStamp is " + evsMessage.getTimeStamp());
                ((IEvsService) this.mService).sendMessage(evsMessage, 3000);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        } else {
            LogUtil.getInstance().dL(TAG, "responseToEvs mService is null");
        }
        this.timeoutCheckRunnable.run();
    }

    @Override // com.changan.evs.ipc.proxy.callback.EvsProxyInterface
    public boolean subscribe(int i, IAppMessageCallback iAppMessageCallback) {
        return subscribe(i, this.mHandler, iAppMessageCallback);
    }

    @Override // com.changan.evs.ipc.proxy.callback.EvsProxyInterface
    public boolean subscribe(int i, Handler handler, IAppMessageCallback iAppMessageCallback) {
        ArrayList arrayList = new ArrayList();
        arrayList.add(Integer.valueOf(i));
        return subscribe(arrayList, handler, iAppMessageCallback);
    }

    @Override // com.changan.evs.ipc.proxy.callback.EvsProxyInterface
    public boolean subscribe(List<Integer> list, IAppMessageCallback iAppMessageCallback) {
        return subscribe(list, this.mHandler, iAppMessageCallback);
    }

    @Override // com.changan.evs.ipc.proxy.callback.EvsProxyInterface
    public boolean subscribe(List<Integer> list, Handler handler, IAppMessageCallback iAppMessageCallback) {
        if (this.mService != 0) {
            try {
                for (Integer num : list) {
                    this.mSubscribeMap.put(num, handler);
                    CopyOnWriteArrayList<IAppMessageCallback> copyOnWriteArrayList = this.mSubscribeCallbackMap.get(num);
                    if (copyOnWriteArrayList == null) {
                        CopyOnWriteArrayList<IAppMessageCallback> copyOnWriteArrayList2 = new CopyOnWriteArrayList<>();
                        copyOnWriteArrayList2.add(iAppMessageCallback);
                        this.mSubscribeCallbackMap.put(num, copyOnWriteArrayList2);
                    } else {
                        copyOnWriteArrayList.add(iAppMessageCallback);
                    }
                }
                return ((IEvsService) this.mService).subscribe(list.stream().mapToInt($$Lambda$EvsBaseProxy$uBsCIBAbAW1p84hPJ6ULJah0SPE.INSTANCE).toArray());
            } catch (RemoteException e) {
                e.printStackTrace();
                return false;
            }
        }
        LogUtil.getInstance().dL(TAG, "subscribe mService is null");
        return false;
    }

    @Override // com.changan.evs.ipc.proxy.callback.EvsProxyInterface
    public boolean unSubscribe(int i, IAppMessageCallback iAppMessageCallback) {
        ArrayList arrayList = new ArrayList();
        arrayList.add(Integer.valueOf(i));
        return unSubscribe(arrayList, iAppMessageCallback);
    }

    @Override // com.changan.evs.ipc.proxy.callback.EvsProxyInterface
    public boolean unSubscribe(List<Integer> list, IAppMessageCallback iAppMessageCallback) {
        if (this.mService != 0) {
            try {
                for (Integer num : list) {
                    this.mSubscribeMap.remove(num);
                    this.mSubscribeCallbackMap.remove(num);
                }
                ((IEvsService) this.mService).unsubscribe(list.stream().mapToInt($$Lambda$EvsBaseProxy$uBsCIBAbAW1p84hPJ6ULJah0SPE.INSTANCE).toArray());
                return true;
            } catch (RemoteException e) {
                e.printStackTrace();
                return false;
            }
        }
        LogUtil.getInstance().dL(TAG, "unSubscribe mService is null");
        return false;
    }

    @Override // com.changan.evs.ipc.proxy.callback.EvsProxyInterface
    public void disconnect(Context context) {
        disconnect(context.getPackageName());
    }

    @Override // com.changan.evs.ipc.proxy.callback.EvsProxyInterface
    public void disconnect(String str) {
        LogUtil.getInstance().dL(TAG, "disconnect");
        this.mService = null;
        notifyServiceDisconnected(101, Constant.HintText.SERVICE_DISCONNECTION_TEXT);
        clearUserSubscribeInfo();
    }
}
