<?xml version="1.0" encoding="utf-8"?>
<com.incall.apps.caui.shape.CAUIShapeConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/transparent"
    android:layout_width="1280dp"
    android:layout_height="match_parent">
    <ImageView
        android:id="@+id/history_detail_close_img"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="55dp"
        android:src="@drawable/caui_icon_dialog_close"
        android:layout_marginStart="64dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="44sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:gravity="center"
        android:id="@+id/history_title"
        android:layout_width="wrap_content"
        android:layout_height="62dp"
        android:layout_marginTop="48dp"
        android:text="@string/history_dialog_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <ScrollView
        android:scrollbarSize="12dp"
        android:scrollbarThumbVertical="@drawable/fota_scroll_thumb"
        android:scrollbarStyle="outsideOverlay"
        android:background="@color/transparent"
        android:scrollbars="vertical"
        android:layout_width="1280dp"
        android:layout_height="819dp"
        android:layout_marginTop="150dp"
        android:layout_marginBottom="55dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:background="@color/transparent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="64dp"
            android:layout_marginEnd="64dp">
            <TextView
                android:textSize="32sp"
                android:textColor="@color/caui_config_text_color_primary"
                android:id="@+id/stick_version_tx"
                android:layout_width="wrap_content"
                android:layout_height="56dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <TextView
                android:textSize="28sp"
                android:textColor="@color/caui_config_text_color_disabled"
                android:id="@+id/published_time_tx"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="32dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <TextView
                android:textSize="32sp"
                android:textColor="@color/caui_config_text_color_primary"
                android:gravity="center"
                android:id="@+id/new_version_detail_tx"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/stick_version_tip"
                app:layout_constraintStart_toStartOf="@+id/stick_version_tx"
                app:layout_constraintTop_toBottomOf="@+id/stick_version_tx"/>
            <TextView
                android:textSize="32sp"
                android:id="@+id/car_version_desc_tx"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/new_version_detail_tx"/>
            <LinearLayout
                android:orientation="vertical"
                android:id="@+id/linear_history_version_detail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/new_version_detail_tx"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
</com.incall.apps.caui.shape.CAUIShapeConstraintLayout>
