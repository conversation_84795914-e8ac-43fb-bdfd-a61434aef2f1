<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:tag="layout/layout_item_condition_not_meet_0"
    android:layout_width="match_parent"
    android:layout_height="77dp">
    <ImageView
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:src="@drawable/caui_icon_toast_warning"
        android:layout_marginStart="48dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="36sp"
        android:textColor="@color/caui_config_text_color_secondary"
        android:id="@+id/tv_condition_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lines="1"
        android:layout_marginStart="114dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
