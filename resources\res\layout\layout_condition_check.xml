<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:tag="layout/layout_condition_check_0"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <TextView
        android:textSize="56sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/condition_check_tip"
        android:layout_width="wrap_content"
        android:layout_height="78dp"
        android:layout_marginTop="104dp"
        android:text="@string/condition_upgrade_readying"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="40sp"
        android:textColor="@color/caui_config_text_color_third"
        android:id="@+id/condition_check_content"
        android:layout_width="wrap_content"
        android:layout_height="56dp"
        android:layout_marginTop="32dp"
        android:text="@string/condition_checking_tip3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/condition_check_tip"/>
    <com.incall.apps.caui.widget.CAUILoadingView
        android:id="@+id/condition_check_view"
        android:layout_width="120dp"
        android:layout_height="96dp"
        android:layout_marginTop="255dp"
        app:caui_loading_view_color="@color/caui_config_theme_color_normal"
        app:caui_loading_view_size="200dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/condition_check_content"/>
    <TextView
        android:textSize="36sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:layout_width="wrap_content"
        android:layout_height="56dp"
        android:layout_marginTop="64dp"
        android:text="@string/condition_checking"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/condition_check_view"/>
</androidx.constraintlayout.widget.ConstraintLayout>
