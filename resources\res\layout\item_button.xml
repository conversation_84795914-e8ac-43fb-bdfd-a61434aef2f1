<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:tag="layout/item_button_0"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <Button
        android:textSize="24sp"
        android:id="@+id/btn_item"
        android:background="@drawable/button_selector"
        android:layout_width="200dp"
        android:layout_height="68dp"
        android:layout_marginTop="36dp"
        android:layout_marginStart="44dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
