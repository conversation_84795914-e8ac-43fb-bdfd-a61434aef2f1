package com.changan.evs.ipc.proxy.base;

import android.os.Handler;
import com.changan.evs.ipc.EvsMessage;
import com.changan.evs.ipc.proxy.callback.AsyncMessageCallback;
import com.changan.evs.ipc.proxy.util.AppLock;

/* loaded from: classes.dex */
public class MessageBean {
    private AppLock appLock;
    private Handler handler;
    private EvsMessage message;
    private AsyncMessageCallback messageCallback;
    private EvsMessage responseMessage;
    private int timeoutMs;

    public EvsMessage getMessage() {
        return this.message;
    }

    public void setMessage(EvsMessage evsMessage) {
        this.message = evsMessage;
    }

    public EvsMessage getResponseMessage() {
        return this.responseMessage;
    }

    public void setResponseMessage(EvsMessage evsMessage) {
        this.responseMessage = evsMessage;
    }

    public int getTimeoutMs() {
        return this.timeoutMs;
    }

    public void setTimeoutMs(int i) {
        this.timeoutMs = i;
    }

    public AsyncMessageCallback getMessageCallback() {
        return this.messageCallback;
    }

    public void setMessageCallback(AsyncMessageCallback asyncMessageCallback) {
        this.messageCallback = asyncMessageCallback;
    }

    public Handler getHandler() {
        return this.handler;
    }

    public void setHandler(Handler handler) {
        this.handler = handler;
    }

    public AppLock getAppLock() {
        return this.appLock;
    }

    public void setAppLock(AppLock appLock) {
        this.appLock = appLock;
    }
}
