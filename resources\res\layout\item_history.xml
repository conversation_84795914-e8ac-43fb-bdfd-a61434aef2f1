<?xml version="1.0" encoding="utf-8"?>
<com.incall.apps.caui.layout.CAUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/caui_config_content_bg_color_primary"
    android:clipToPadding="false"
    android:layout_width="match_parent"
    android:layout_height="152dp"
    android:layout_marginBottom="24dp"
    android:layout_marginStart="64dp"
    android:layout_marginEnd="64dp"
    app:caui_radius="@dimen/caui_config_corner_radius_middle"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">
    <TextView
        android:textSize="40sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:gravity="center_vertical"
        android:id="@+id/tv_history_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="48dp"
        android:maxWidth="800dp"
        android:text="xx12.3.1"
        android:layout_marginStart="48dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="28sp"
        android:textColor="@color/caui_config_text_color_disabled"
        android:gravity="center_vertical"
        android:id="@+id/publish_time_tx"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginTop="56dp"
        android:text="2023-01-15"
        android:layout_marginEnd="48dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="32sp"
        android:textColor="@color/caui_config_text_color_secondary"
        android:gravity="center_vertical"
        android:id="@+id/collapse_tx"
        android:layout_width="1088dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:layout_marginBottom="32dp"
        android:layout_marginEnd="32dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tv_history_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_history_title"/>
</com.incall.apps.caui.layout.CAUIConstraintLayout>
