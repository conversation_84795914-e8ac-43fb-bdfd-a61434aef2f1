package androidx.viewpager;

/* loaded from: classes.dex */
public final class R {

    public static final class attr {
        public static final int alpha = 0x7f04003b;
        public static final int font = 0x7f04033e;
        public static final int fontProviderAuthority = 0x7f040340;
        public static final int fontProviderCerts = 0x7f040341;
        public static final int fontProviderFetchStrategy = 0x7f040342;
        public static final int fontProviderFetchTimeout = 0x7f040343;
        public static final int fontProviderPackage = 0x7f040344;
        public static final int fontProviderQuery = 0x7f040345;
        public static final int fontStyle = 0x7f040347;
        public static final int fontVariationSettings = 0x7f040348;
        public static final int fontWeight = 0x7f040349;
        public static final int ttcIndex = 0x7f04069b;

        private attr() {
        }
    }

    public static final class color {
        public static final int notification_action_color_filter = 0x7f06032f;
        public static final int notification_icon_bg_color = 0x7f060330;
        public static final int ripple_material_light = 0x7f060343;
        public static final int secondary_text_default_material_light = 0x7f060345;

        private color() {
        }
    }

    public static final class dimen {
        public static final int compat_button_inset_horizontal_material = 0x7f070075;
        public static final int compat_button_inset_vertical_material = 0x7f070076;
        public static final int compat_button_padding_horizontal_material = 0x7f070077;
        public static final int compat_button_padding_vertical_material = 0x7f070078;
        public static final int compat_control_corner_material = 0x7f070079;
        public static final int compat_notification_large_icon_max_height = 0x7f07007a;
        public static final int compat_notification_large_icon_max_width = 0x7f07007b;
        public static final int notification_action_icon_size = 0x7f070250;
        public static final int notification_action_text_size = 0x7f070251;
        public static final int notification_big_circle_margin = 0x7f070252;
        public static final int notification_content_margin_start = 0x7f070253;
        public static final int notification_large_icon_height = 0x7f070254;
        public static final int notification_large_icon_width = 0x7f070255;
        public static final int notification_main_column_padding_top = 0x7f070256;
        public static final int notification_media_narrow_margin = 0x7f070257;
        public static final int notification_right_icon_size = 0x7f070258;
        public static final int notification_right_side_padding_top = 0x7f070259;
        public static final int notification_small_icon_background_padding = 0x7f07025a;
        public static final int notification_small_icon_size_as_large = 0x7f07025b;
        public static final int notification_subtext_size = 0x7f07025c;
        public static final int notification_top_pad = 0x7f07025d;
        public static final int notification_top_pad_large_text = 0x7f07025e;

        private dimen() {
        }
    }

    public static final class drawable {
        public static final int notification_action_background = 0x7f080113;
        public static final int notification_bg = 0x7f080114;
        public static final int notification_bg_low = 0x7f080115;
        public static final int notification_bg_low_normal = 0x7f080116;
        public static final int notification_bg_low_pressed = 0x7f080117;
        public static final int notification_bg_normal = 0x7f080118;
        public static final int notification_bg_normal_pressed = 0x7f080119;
        public static final int notification_icon_background = 0x7f08011a;
        public static final int notification_template_icon_bg = 0x7f08011b;
        public static final int notification_template_icon_low_bg = 0x7f08011c;
        public static final int notification_tile_bg = 0x7f08011d;
        public static final int notify_panel_notification_icon_bg = 0x7f08011e;

        private drawable() {
        }
    }

    public static final class id {
        public static final int action_container = 0x7f0a0047;
        public static final int action_divider = 0x7f0a0049;
        public static final int action_image = 0x7f0a004a;
        public static final int action_text = 0x7f0a0050;
        public static final int actions = 0x7f0a0051;
        public static final int async = 0x7f0a0062;
        public static final int blocking = 0x7f0a0082;
        public static final int chronometer = 0x7f0a00f6;
        public static final int forever = 0x7f0a018f;
        public static final int icon = 0x7f0a01b5;
        public static final int icon_group = 0x7f0a01b6;
        public static final int info = 0x7f0a01c1;
        public static final int italic = 0x7f0a01c9;
        public static final int line1 = 0x7f0a01ef;
        public static final int line3 = 0x7f0a01f0;
        public static final int normal = 0x7f0a0263;
        public static final int notification_background = 0x7f0a0265;
        public static final int notification_main_column = 0x7f0a0266;
        public static final int notification_main_column_container = 0x7f0a0267;
        public static final int right_icon = 0x7f0a02ac;
        public static final int right_side = 0x7f0a02ad;
        public static final int tag_transition_group = 0x7f0a0316;
        public static final int tag_unhandled_key_event_manager = 0x7f0a0317;
        public static final int tag_unhandled_key_listeners = 0x7f0a0318;
        public static final int text = 0x7f0a0326;
        public static final int text2 = 0x7f0a0327;
        public static final int time = 0x7f0a0339;
        public static final int title = 0x7f0a0345;

        private id() {
        }
    }

    public static final class integer {
        public static final int status_bar_notification_info_maxnum = 0x7f0b0034;

        private integer() {
        }
    }

    public static final class layout {
        public static final int notification_action = 0x7f0d00d0;
        public static final int notification_action_tombstone = 0x7f0d00d1;
        public static final int notification_template_custom_big = 0x7f0d00d2;
        public static final int notification_template_icon_group = 0x7f0d00d3;
        public static final int notification_template_part_chronometer = 0x7f0d00d4;
        public static final int notification_template_part_time = 0x7f0d00d5;

        private layout() {
        }
    }

    public static final class string {
        public static final int status_bar_notification_info_overflow = 0x7f1101da;

        private string() {
        }
    }

    public static final class style {
        public static final int TextAppearance_Compat_Notification = 0x7f1201f9;
        public static final int TextAppearance_Compat_Notification_Info = 0x7f1201fa;
        public static final int TextAppearance_Compat_Notification_Line2 = 0x7f1201fb;
        public static final int TextAppearance_Compat_Notification_Time = 0x7f1201fc;
        public static final int TextAppearance_Compat_Notification_Title = 0x7f1201fd;
        public static final int Widget_Compat_NotificationActionContainer = 0x7f120365;
        public static final int Widget_Compat_NotificationActionText = 0x7f120366;

        private style() {
        }
    }

    public static final class styleable {
        public static final int ColorStateListItem_alpha = 0x00000003;
        public static final int ColorStateListItem_android_alpha = 0x00000001;
        public static final int ColorStateListItem_android_color = 0x00000000;
        public static final int ColorStateListItem_android_lStar = 0x00000002;
        public static final int ColorStateListItem_lStar = 0x00000004;
        public static final int FontFamilyFont_android_font = 0x00000000;
        public static final int FontFamilyFont_android_fontStyle = 0x00000002;
        public static final int FontFamilyFont_android_fontVariationSettings = 0x00000004;
        public static final int FontFamilyFont_android_fontWeight = 0x00000001;
        public static final int FontFamilyFont_android_ttcIndex = 0x00000003;
        public static final int FontFamilyFont_font = 0x00000005;
        public static final int FontFamilyFont_fontStyle = 0x00000006;
        public static final int FontFamilyFont_fontVariationSettings = 0x00000007;
        public static final int FontFamilyFont_fontWeight = 0x00000008;
        public static final int FontFamilyFont_ttcIndex = 0x00000009;
        public static final int FontFamily_fontProviderAuthority = 0x00000000;
        public static final int FontFamily_fontProviderCerts = 0x00000001;
        public static final int FontFamily_fontProviderFetchStrategy = 0x00000002;
        public static final int FontFamily_fontProviderFetchTimeout = 0x00000003;
        public static final int FontFamily_fontProviderPackage = 0x00000004;
        public static final int FontFamily_fontProviderQuery = 0x00000005;
        public static final int FontFamily_fontProviderSystemFontFamily = 0x00000006;
        public static final int GradientColorItem_android_color = 0x00000000;
        public static final int GradientColorItem_android_offset = 0x00000001;
        public static final int GradientColor_android_centerColor = 0x00000007;
        public static final int GradientColor_android_centerX = 0x00000003;
        public static final int GradientColor_android_centerY = 0x00000004;
        public static final int GradientColor_android_endColor = 0x00000001;
        public static final int GradientColor_android_endX = 0x0000000a;
        public static final int GradientColor_android_endY = 0x0000000b;
        public static final int GradientColor_android_gradientRadius = 0x00000005;
        public static final int GradientColor_android_startColor = 0x00000000;
        public static final int GradientColor_android_startX = 0x00000008;
        public static final int GradientColor_android_startY = 0x00000009;
        public static final int GradientColor_android_tileMode = 0x00000006;
        public static final int GradientColor_android_type = 0x00000002;
        public static final int[] ColorStateListItem = {android.R.attr.color, android.R.attr.alpha, android.R.attr.lStar, com.incall.apps.softmanager.R.attr.alpha, com.incall.apps.softmanager.R.attr.lStar};
        public static final int[] FontFamily = {com.incall.apps.softmanager.R.attr.fontProviderAuthority, com.incall.apps.softmanager.R.attr.fontProviderCerts, com.incall.apps.softmanager.R.attr.fontProviderFetchStrategy, com.incall.apps.softmanager.R.attr.fontProviderFetchTimeout, com.incall.apps.softmanager.R.attr.fontProviderPackage, com.incall.apps.softmanager.R.attr.fontProviderQuery, com.incall.apps.softmanager.R.attr.fontProviderSystemFontFamily};
        public static final int[] FontFamilyFont = {android.R.attr.font, android.R.attr.fontWeight, android.R.attr.fontStyle, android.R.attr.ttcIndex, android.R.attr.fontVariationSettings, com.incall.apps.softmanager.R.attr.font, com.incall.apps.softmanager.R.attr.fontStyle, com.incall.apps.softmanager.R.attr.fontVariationSettings, com.incall.apps.softmanager.R.attr.fontWeight, com.incall.apps.softmanager.R.attr.ttcIndex};
        public static final int[] GradientColor = {android.R.attr.startColor, android.R.attr.endColor, android.R.attr.type, android.R.attr.centerX, android.R.attr.centerY, android.R.attr.gradientRadius, android.R.attr.tileMode, android.R.attr.centerColor, android.R.attr.startX, android.R.attr.startY, android.R.attr.endX, android.R.attr.endY};
        public static final int[] GradientColorItem = {android.R.attr.color, android.R.attr.offset};

        private styleable() {
        }
    }

    private R() {
    }
}
