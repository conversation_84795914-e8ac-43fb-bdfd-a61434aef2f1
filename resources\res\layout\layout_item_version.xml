<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:tag="layout/layout_item_version_0"
    android:paddingTop="15dp"
    android:paddingBottom="15dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <TextView
        android:textSize="36sp"
        android:textColor="@color/caui_config_text_color_secondary"
        android:id="@+id/tv_item_version_title"
        android:tag="binding_1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="64dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tv_item_version_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.incall.apps.softmanager.base.view.MarqueeTextView
        android:textSize="36sp"
        android:textColor="@color/caui_config_text_color_secondary"
        android:gravity="end"
        android:id="@+id/tv_item_version_content"
        android:tag="binding_2"
        android:focusableInTouchMode="true"
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:maxLines="2"
        android:layout_marginEnd="64dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
