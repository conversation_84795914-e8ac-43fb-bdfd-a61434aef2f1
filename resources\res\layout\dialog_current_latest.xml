<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    android:background="@drawable/fota_bg_toast"
    android:layout_width="448dp"
    android:layout_height="146dp">
    <TextView
        android:textSize="36sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/dialog_current_latest"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
