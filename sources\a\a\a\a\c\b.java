package a.a.a.a.c;

import kotlin.io.encoding.Base64;
import okio.Utf8;

/* loaded from: classes.dex */
public class b {

    /* renamed from: a, reason: collision with root package name */
    public static byte[] f6a = {-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, -1, -1, -1, Utf8.REPLACEMENT_BYTE, 52, 53, 54, 55, 56, 57, 58, 59, 60, Base64.padSymbol, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, -1, -1, -1, -1, -1, -1, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, -1, -1, -1, -1, -1};

    /* JADX WARN: Code restructure failed: missing block: B:23:0x0057, code lost:
    
        if (r5 != (-1)) goto L30;
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x005a, code lost:
    
        r1.write(((r4 & 15) << 4) | ((r5 & 60) >>> 2));
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x0066, code lost:
    
        r4 = r2 + 1;
        r2 = r8[r2];
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x006a, code lost:
    
        if (r2 != 61) goto L35;
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x0071, code lost:
    
        r2 = a.a.a.a.c.b.f6a[r2];
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x0075, code lost:
    
        if (r4 >= r0) goto L58;
     */
    /* JADX WARN: Code restructure failed: missing block: B:29:0x0077, code lost:
    
        if (r2 == (-1)) goto L39;
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x007a, code lost:
    
        r2 = r4;
     */
    /* JADX WARN: Code restructure failed: missing block: B:32:0x007c, code lost:
    
        if (r2 != (-1)) goto L42;
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x007f, code lost:
    
        r1.write(r2 | ((r5 & 3) << 6));
        r2 = r4;
     */
    /* JADX WARN: Code restructure failed: missing block: B:41:0x0070, code lost:
    
        return r1.toByteArray();
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static byte[] a(java.lang.String r8) {
        /*
            java.lang.String r0 = "GBK"
            byte[] r8 = r8.getBytes(r0)
            int r0 = r8.length
            java.io.ByteArrayOutputStream r1 = new java.io.ByteArrayOutputStream
            r1.<init>(r0)
            r2 = 0
        Ld:
            if (r2 >= r0) goto L89
        Lf:
            byte[] r3 = a.a.a.a.c.b.f6a
            int r4 = r2 + 1
            r2 = r8[r2]
            r2 = r3[r2]
            r3 = -1
            if (r4 >= r0) goto L1f
            if (r2 == r3) goto L1d
            goto L1f
        L1d:
            r2 = r4
            goto Lf
        L1f:
            if (r2 != r3) goto L23
            goto L89
        L23:
            byte[] r5 = a.a.a.a.c.b.f6a
            int r6 = r4 + 1
            r4 = r8[r4]
            r4 = r5[r4]
            if (r6 >= r0) goto L32
            if (r4 == r3) goto L30
            goto L32
        L30:
            r4 = r6
            goto L23
        L32:
            if (r4 != r3) goto L35
            goto L89
        L35:
            int r2 = r2 << 2
            r5 = r4 & 48
            int r5 = r5 >>> 4
            r2 = r2 | r5
            r1.write(r2)
        L3f:
            int r2 = r6 + 1
            r5 = r8[r6]
            r6 = 61
            if (r5 != r6) goto L4c
            byte[] r8 = r1.toByteArray()
            return r8
        L4c:
            byte[] r7 = a.a.a.a.c.b.f6a
            r5 = r7[r5]
            if (r2 >= r0) goto L57
            if (r5 == r3) goto L55
            goto L57
        L55:
            r6 = r2
            goto L3f
        L57:
            if (r5 != r3) goto L5a
            goto L89
        L5a:
            r4 = r4 & 15
            int r4 = r4 << 4
            r7 = r5 & 60
            int r7 = r7 >>> 2
            r4 = r4 | r7
            r1.write(r4)
        L66:
            int r4 = r2 + 1
            r2 = r8[r2]
            if (r2 != r6) goto L71
            byte[] r8 = r1.toByteArray()
            return r8
        L71:
            byte[] r7 = a.a.a.a.c.b.f6a
            r2 = r7[r2]
            if (r4 >= r0) goto L7c
            if (r2 == r3) goto L7a
            goto L7c
        L7a:
            r2 = r4
            goto L66
        L7c:
            if (r2 != r3) goto L7f
            goto L89
        L7f:
            r3 = r5 & 3
            int r3 = r3 << 6
            r2 = r2 | r3
            r1.write(r2)
            r2 = r4
            goto Ld
        L89:
            byte[] r8 = r1.toByteArray()
            return r8
        */
        throw new UnsupportedOperationException("Method not decompiled: a.a.a.a.c.b.a(java.lang.String):byte[]");
    }
}
