<?xml version="1.0" encoding="utf-8"?>
<com.incall.apps.caui.shape.CAUIShapeConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="880dp"
    android:layout_height="wrap_content"
    android:minWidth="880dp"
    android:minHeight="390dp"
    app:caui_shape_borderColor="@color/caui_config_pop_up_stroke_color"
    app:caui_shape_borderWidth="1dp"
    app:caui_shape_color="@color/caui_config_pop_up_bg_color"
    app:caui_shape_radius="@dimen/caui_config_corner_radius_large">
    <ImageView
        android:id="@+id/close_img_dialog_install_success"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="55dp"
        android:src="@drawable/caui_icon_dialog_close"
        android:layout_marginStart="64dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="40sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/dialog_install_success_title"
        android:layout_width="wrap_content"
        android:layout_height="56dp"
        android:layout_marginTop="48dp"
        android:text="@string/dialog_install_success"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="32sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/dialog_install_success_content"
        android:layout_width="752dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/dialog_install_success_title"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:gravity="center"
        android:id="@+id/btn_dialog_install_success_dialog"
        android:layout_width="400dp"
        android:layout_height="96dp"
        android:layout_marginTop="48dp"
        android:layout_marginBottom="56dp"
        android:text="@string/do_not_power_off"
        app:caui_round_btn_type="main"
        app:caui_round_radius="@dimen/caui_config_corner_radius_middle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/dialog_install_success_content"/>
</com.incall.apps.caui.shape.CAUIShapeConstraintLayout>
