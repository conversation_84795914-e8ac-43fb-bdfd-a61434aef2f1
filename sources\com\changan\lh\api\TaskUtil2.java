package com.changan.lh.api;

import com.changan.lh.api.TaskUtil2;
import com.changan.lh.bean.Action;
import com.changan.lh.bean.ApiType;
import com.changan.lh.bean.OTAConfig;
import com.changan.lh.bean.OtaType;
import com.incall.apps.libbase.OtaMasterShareData;
import com.incall.apps.libbase.jsonutil.config.ConfigInfo;
import com.incall.apps.libbase.jsonutil.upgradetask.bean.InstallTask;
import com.incall.apps.libbase.jsonutil.upgradetask.bean.SubmasterInfo;
import com.incall.apps.libbase.jsonutil.upgradetask.bean.UpgradeTaskBean;
import com.incall.apps.libbase.jsonutil.usbtask.bean.UsbTaskBean;
import com.incall.apps.libbase.utils.GsonUtils;
import com.incall.apps.libbase.utils.StringUtil;
import com.incall.apps.softmanager.util.CollectionUtil;
import com.incall.apps.softmanager.util.log.LogUtil;
import com.incall.apps.testvehicle.bean.SotaObjectBean;
import com.incall.apps.testvehicle.bean.TargetUpgradeBean;
import com.incall.apps.testvehicle.bean.TargetUpgradeList;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes.dex */
public class TaskUtil2 {
    public static final String TAG = "TaskUtil2";

    public static boolean checkParameter(String str, String str2, String str3) {
        if (!StringUtil.isEmpty(str) && !StringUtil.isEmpty(str2) && !StringUtil.isEmpty(str3)) {
            return true;
        }
        LogUtil.e(TAG, "Parameter Should not be null!");
        return false;
    }

    public static List<SubmasterObjectList> getAllList(TargetUpgradeList targetUpgradeList, UsbTaskBean usbTaskBean, List<ConfigInfo.SubMasterConfig> list, List<String> list2, boolean z) {
        ArrayList arrayList = new ArrayList();
        if (targetUpgradeList != null && !CollectionUtil.isEmpty(targetUpgradeList.getObjList())) {
            List<UpgradeObjInfo> otaSubmasterMap = getOtaSubmasterMap(list);
            LogUtil.i(TAG, "otaMap:" + GsonUtils.getInstance().toJson(otaSubmasterMap));
            for (int i = 0; i < targetUpgradeList.getObjList().size(); i++) {
                final TargetUpgradeBean targetUpgradeBean = targetUpgradeList.getObjList().get(i);
                if (targetUpgradeBean != null && !StringUtil.isEmpty(targetUpgradeBean.getUpgradeObj())) {
                    if (!StringUtil.isEmpty(targetUpgradeBean.getVersion())) {
                        SubmasterInfo.ObjectInfo objectInfo = new SubmasterInfo.ObjectInfo();
                        objectInfo.setName(targetUpgradeBean.getUpgradeObj());
                        objectInfo.setOtaSortType(OtaType.FOTA.getValue());
                        objectInfo.setRollbackVer(targetUpgradeBean.getCurrentVersion());
                        objectInfo.setUpdateVer(targetUpgradeBean.getVersion());
                        objectInfo.setUpdateTimes(usbTaskBean.getMaxInstallTimes());
                        objectInfo.setRollbackTimes(usbTaskBean.getMaxRollbackTimes());
                        objectInfo.setEncrypt(0);
                        if (z) {
                            objectInfo.setSameVerUpgrade(1);
                        }
                        objectInfo.setSoftwareNumber(targetUpgradeBean.getSoftNumber());
                        if (!StringUtil.isEmpty((String) CollectionUtil.findItem(list2, new CollectionUtil.Query() { // from class: com.changan.lh.api.-$$Lambda$TaskUtil2$u5ZKVwlmh_QzFQfIXIv-2srS_MQ
                            @Override // com.incall.apps.softmanager.util.CollectionUtil.Query
                            public final boolean match(Object obj) {
                                boolean equals;
                                equals = ((String) obj).equals(TargetUpgradeBean.this.getUpgradeObj());
                                return equals;
                            }
                        }))) {
                            objectInfo.setPackageType(1);
                        } else {
                            objectInfo.setPackageType(0);
                        }
                        UpgradeObjInfo upgradeObjInfo = (UpgradeObjInfo) CollectionUtil.findItem(otaSubmasterMap, new CollectionUtil.Query() { // from class: com.changan.lh.api.-$$Lambda$TaskUtil2$oQh2Zq6UDPPDUH7VOkE8zUH8ZzE
                            @Override // com.incall.apps.softmanager.util.CollectionUtil.Query
                            public final boolean match(Object obj) {
                                boolean equals;
                                equals = ((TaskUtil2.UpgradeObjInfo) obj).name.equals(TargetUpgradeBean.this.getUpgradeObj());
                                return equals;
                            }
                        });
                        objectInfo.setDualBack(upgradeObjInfo != null ? upgradeObjInfo.dualBank : 1);
                        objectInfo.setPreFlashFlag(upgradeObjInfo != null && upgradeObjInfo.preFlashFlag);
                        final String str = upgradeObjInfo != null ? upgradeObjInfo.updateSubmaster : "";
                        SubmasterObjectList submasterObjectList = (SubmasterObjectList) CollectionUtil.findItem(arrayList, new CollectionUtil.Query() { // from class: com.changan.lh.api.-$$Lambda$TaskUtil2$bvk2yYVNqtyCFtEu00GYrBaLABE
                            @Override // com.incall.apps.softmanager.util.CollectionUtil.Query
                            public final boolean match(Object obj) {
                                boolean equals;
                                equals = str.equals(((TaskUtil2.SubmasterObjectList) obj).updateSubmaster);
                                return equals;
                            }
                        });
                        if (submasterObjectList == null) {
                            submasterObjectList = new SubmasterObjectList();
                            submasterObjectList.updateSubmaster = str;
                            submasterObjectList.objectInfoList = new ArrayList();
                            arrayList.add(submasterObjectList);
                        }
                        submasterObjectList.objectInfoList.add(objectInfo);
                        if (!CollectionUtil.isEmpty(targetUpgradeBean.getSotaList())) {
                            for (final SotaObjectBean sotaObjectBean : targetUpgradeBean.getSotaList()) {
                                if (!StringUtil.isEmpty(sotaObjectBean.getAppId())) {
                                    SubmasterInfo.ObjectInfo objectInfo2 = new SubmasterInfo.ObjectInfo();
                                    objectInfo2.setName(sotaObjectBean.getAppId());
                                    objectInfo2.setOtaSortType(OtaType.SOTA.getValue());
                                    objectInfo2.setRollbackVer(sotaObjectBean.getCurrentVersion());
                                    objectInfo2.setSoftwareNumber(sotaObjectBean.getSotaCode());
                                    LogUtil.e(TAG, "SOTA-CODE----->  sotaCode = " + sotaObjectBean.getSotaCode() + " appId = " + sotaObjectBean.getAppId() + " currentVersion = " + sotaObjectBean.getCurrentVersion());
                                    objectInfo2.setUpdateVer(sotaObjectBean.getVersion());
                                    objectInfo2.setUpdateTimes(usbTaskBean.getMaxInstallTimes());
                                    objectInfo2.setRollbackTimes(usbTaskBean.getMaxRollbackTimes());
                                    UpgradeObjInfo upgradeObjInfo2 = (UpgradeObjInfo) CollectionUtil.findItem(otaSubmasterMap, new CollectionUtil.Query() { // from class: com.changan.lh.api.-$$Lambda$TaskUtil2$uWfm-ziRfkpktcx7lFZGday9s6I
                                        @Override // com.incall.apps.softmanager.util.CollectionUtil.Query
                                        public final boolean match(Object obj) {
                                            boolean equals;
                                            equals = ((TaskUtil2.UpgradeObjInfo) obj).name.equals(SotaObjectBean.this.getAppId());
                                            return equals;
                                        }
                                    });
                                    final String str2 = upgradeObjInfo2 != null ? upgradeObjInfo2.updateSubmaster : "";
                                    SubmasterObjectList submasterObjectList2 = (SubmasterObjectList) CollectionUtil.findItem(arrayList, new CollectionUtil.Query() { // from class: com.changan.lh.api.-$$Lambda$TaskUtil2$FZs7dhNYK6zW_3eyWmf0Ne6VPfM
                                        @Override // com.incall.apps.softmanager.util.CollectionUtil.Query
                                        public final boolean match(Object obj) {
                                            boolean equals;
                                            equals = str2.equals(((TaskUtil2.SubmasterObjectList) obj).updateSubmaster);
                                            return equals;
                                        }
                                    });
                                    if (submasterObjectList2 == null) {
                                        submasterObjectList2 = new SubmasterObjectList();
                                        submasterObjectList2.updateSubmaster = str2;
                                        submasterObjectList2.objectInfoList = new ArrayList();
                                        arrayList.add(submasterObjectList2);
                                    }
                                    submasterObjectList2.objectInfoList.add(objectInfo2);
                                }
                            }
                        }
                    } else if (!CollectionUtil.isEmpty(targetUpgradeBean.getSotaList())) {
                        for (final SotaObjectBean sotaObjectBean2 : targetUpgradeBean.getSotaList()) {
                            if (!StringUtil.isEmpty(sotaObjectBean2.getAppId())) {
                                SubmasterInfo.ObjectInfo objectInfo3 = new SubmasterInfo.ObjectInfo();
                                objectInfo3.setName(sotaObjectBean2.getAppId());
                                objectInfo3.setOtaSortType(OtaType.SOTA.getValue());
                                objectInfo3.setRollbackVer(sotaObjectBean2.getCurrentVersion());
                                objectInfo3.setUpdateVer(sotaObjectBean2.getVersion());
                                objectInfo3.setSoftwareNumber(sotaObjectBean2.getSotaCode());
                                LogUtil.e(TAG, "SOTA-CODE-----> sotaCode = " + sotaObjectBean2.getSotaCode());
                                objectInfo3.setUpdateTimes(usbTaskBean.getMaxInstallTimes());
                                objectInfo3.setRollbackTimes(usbTaskBean.getMaxRollbackTimes());
                                UpgradeObjInfo upgradeObjInfo3 = (UpgradeObjInfo) CollectionUtil.findItem(otaSubmasterMap, new CollectionUtil.Query() { // from class: com.changan.lh.api.-$$Lambda$TaskUtil2$HMlJ6KCtGls2GMQmkult7b1iG6w
                                    @Override // com.incall.apps.softmanager.util.CollectionUtil.Query
                                    public final boolean match(Object obj) {
                                        boolean equals;
                                        equals = ((TaskUtil2.UpgradeObjInfo) obj).name.equals(SotaObjectBean.this.getAppId());
                                        return equals;
                                    }
                                });
                                final String str3 = upgradeObjInfo3 != null ? upgradeObjInfo3.updateSubmaster : "";
                                SubmasterObjectList submasterObjectList3 = (SubmasterObjectList) CollectionUtil.findItem(arrayList, new CollectionUtil.Query() { // from class: com.changan.lh.api.-$$Lambda$TaskUtil2$1BBcHN64Q0WSLva-pR_gfl2-ml8
                                    @Override // com.incall.apps.softmanager.util.CollectionUtil.Query
                                    public final boolean match(Object obj) {
                                        boolean equals;
                                        equals = str3.equals(((TaskUtil2.SubmasterObjectList) obj).updateSubmaster);
                                        return equals;
                                    }
                                });
                                if (submasterObjectList3 == null) {
                                    submasterObjectList3 = new SubmasterObjectList();
                                    submasterObjectList3.updateSubmaster = str3;
                                    submasterObjectList3.objectInfoList = new ArrayList();
                                    arrayList.add(submasterObjectList3);
                                }
                                submasterObjectList3.objectInfoList.add(objectInfo3);
                            }
                        }
                    }
                }
            }
        }
        return arrayList;
    }

    public static List<ArrayList<String>> createUpdateList(TargetUpgradeList targetUpgradeList) {
        ArrayList arrayList = new ArrayList();
        if (targetUpgradeList != null && !CollectionUtil.isEmpty(targetUpgradeList.getObjList())) {
            ArrayList arrayList2 = new ArrayList();
            ArrayList arrayList3 = new ArrayList();
            for (int i = 0; i < targetUpgradeList.getObjList().size(); i++) {
                TargetUpgradeBean targetUpgradeBean = targetUpgradeList.getObjList().get(i);
                if (targetUpgradeBean != null && !StringUtil.isEmpty(targetUpgradeBean.getUpgradeObj())) {
                    if (!StringUtil.isEmpty(targetUpgradeBean.getVersion())) {
                        arrayList2.add(targetUpgradeBean.getUpgradeObj());
                        if (!CollectionUtil.isEmpty(targetUpgradeBean.getSotaList())) {
                            for (SotaObjectBean sotaObjectBean : targetUpgradeBean.getSotaList()) {
                                if (!StringUtil.isEmpty(sotaObjectBean.getAppId())) {
                                    arrayList3.add(sotaObjectBean.getAppId());
                                }
                            }
                        }
                    } else if (!CollectionUtil.isEmpty(targetUpgradeBean.getSotaList())) {
                        for (SotaObjectBean sotaObjectBean2 : targetUpgradeBean.getSotaList()) {
                            if (!StringUtil.isEmpty(sotaObjectBean2.getAppId())) {
                                arrayList3.add(sotaObjectBean2.getAppId());
                            }
                        }
                    }
                }
            }
            if (arrayList2.size() > 0) {
                arrayList.add(arrayList2);
            }
            if (arrayList3.size() > 0) {
                arrayList.add(arrayList3);
            }
        }
        return arrayList;
    }

    public static void createUpdateTask(List<OTAConfig.UpdateSequenceBean> list, List<String> list2, List<List<String>> list3) {
        ArrayList arrayList = new ArrayList();
        for (int i = 0; i < list2.size(); i++) {
            if (!StringUtil.isEmpty(list2.get(i))) {
                if (!arrayList.contains(list2.get(i))) {
                    arrayList.add(list2.get(i));
                }
                if (!CollectionUtil.isEmpty(list)) {
                    for (int i2 = 0; i2 < list.size(); i2++) {
                        if (list.get(i2) != null) {
                            String firstUpdateObj = list.get(i2).getFirstUpdateObj();
                            String afterUpdateObj = list.get(i2).getAfterUpdateObj();
                            if (!StringUtil.isEmpty(firstUpdateObj) && !StringUtil.isEmpty(afterUpdateObj)) {
                                LogUtil.i(TAG, "first=" + firstUpdateObj + " after=" + afterUpdateObj);
                                LogUtil.i(TAG, "obj=" + list2.get(i));
                                boolean arrayContainStr = arrayContainStr(afterUpdateObj.split("\\*"), list2.get(i));
                                boolean listContainList = listContainList(list2, firstUpdateObj.split("\\*"));
                                LogUtil.i(TAG, "isAfter=" + arrayContainStr + " hasFirst=" + listContainList);
                                if (arrayContainStr && listContainList) {
                                    arrayList.remove(list2.get(i));
                                }
                            }
                        }
                    }
                }
            }
        }
        LogUtil.e(TAG, "createUpdateTask:" + arrayList);
        if (!CollectionUtil.isEmpty(arrayList)) {
            list3.add(arrayList);
            for (String str : arrayList) {
                if (list2.contains(str)) {
                    list2.remove(str);
                }
            }
        }
        if (CollectionUtil.isEmpty(list2)) {
            return;
        }
        createUpdateTask(list, list2, list3);
    }

    public static List<String> createSwitchTask(List<ConfigInfo.SubMasterConfig> list, List<String> list2) {
        ArrayList arrayList = new ArrayList();
        if (CollectionUtil.isEmpty(list) || CollectionUtil.isEmpty(list2)) {
            return arrayList;
        }
        for (int i = 0; i < list.size(); i++) {
            ConfigInfo.SubMasterConfig subMasterConfig = list.get(i);
            if (subMasterConfig != null && !CollectionUtil.isEmpty(subMasterConfig.getObjList())) {
                for (int i2 = 0; i2 < subMasterConfig.getObjList().size(); i2++) {
                    if (subMasterConfig.getObjList().get(i2) != null && !StringUtil.isEmpty(subMasterConfig.getObjList().get(i2).getObjName()) && !"USR".equalsIgnoreCase(subMasterConfig.getObjList().get(i2).getUpMethod()) && !arrayList.contains(subMasterConfig.getObjList().get(i2).getObjName())) {
                        arrayList.add(subMasterConfig.getObjList().get(i2).getObjName());
                    }
                }
            }
        }
        LogUtil.i(TAG, "createSwitchTask:" + arrayList);
        HashSet hashSet = new HashSet(arrayList);
        hashSet.retainAll(list2);
        LogUtil.e(TAG, "createSwitchTask result:" + hashSet);
        return new ArrayList(hashSet);
    }

    public static ArrayList<String> createRebootList(List<String> list, List<ConfigInfo.SubMasterConfig> list2) {
        Map<String, String> rebootObjMap = getRebootObjMap(list2);
        ArrayList<String> arrayList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            if (rebootObjMap.containsKey(list.get(i)) && !arrayList.contains(rebootObjMap.get(list.get(i)))) {
                arrayList.add(rebootObjMap.get(list.get(i)));
            }
        }
        return arrayList;
    }

    public static Map<String, String> getRebootObjMap(List<ConfigInfo.SubMasterConfig> list) {
        HashMap hashMap = new HashMap();
        if (CollectionUtil.isEmpty(list)) {
            return hashMap;
        }
        for (int i = 0; i < list.size(); i++) {
            if (list.get(i) != null && !CollectionUtil.isEmpty(list.get(i).getObjList())) {
                for (int i2 = 0; i2 < list.get(i).getObjList().size(); i2++) {
                    if (list.get(i).getObjList().get(i2) != null) {
                        if ((!"USR".equalsIgnoreCase(list.get(i).getObjList().get(i2).getUpMethod()) || (!StringUtil.isEmpty(list.get(i).getObjList().get(i2).getRebootObj()) && !list.get(i).getObjList().get(i2).getRebootObj().equals(list.get(i).getObjList().get(i2).getObjName()))) && !StringUtil.isEmpty(list.get(i).getObjList().get(i2).getRebootObj())) {
                            hashMap.put(list.get(i).getObjList().get(i2).getObjName(), list.get(i).getObjList().get(i2).getRebootObj());
                        }
                        if (!CollectionUtil.isEmpty(list.get(i).getObjList().get(i2).getSotaList())) {
                            for (int i3 = 0; i3 < list.get(i).getObjList().get(i2).getSotaList().size(); i3++) {
                                ConfigInfo.SotaConfig sotaConfig = list.get(i).getObjList().get(i2).getSotaList().get(i3);
                                if (sotaConfig != null && sotaConfig.isReboot()) {
                                    hashMap.put(sotaConfig.getAppId(), list.get(i).getObjList().get(i2).getRebootObj());
                                }
                            }
                        }
                    }
                }
            }
        }
        LogUtil.i(TAG, "getRebootObjMap:" + hashMap.size() + StringUtils.SPACE + GsonUtils.getInstance().toJson(hashMap));
        return hashMap;
    }

    public static void createRebootTask(List<OTAConfig.RebootSequenceBean> list, List<String> list2, List<List<String>> list3) {
        ArrayList arrayList = new ArrayList();
        for (int i = 0; i < list2.size(); i++) {
            if (!StringUtil.isEmpty(list2.get(i))) {
                if (!arrayList.contains(list2.get(i))) {
                    arrayList.add(list2.get(i));
                }
                if (!CollectionUtil.isEmpty(list)) {
                    for (int i2 = 0; i2 < list.size(); i2++) {
                        String firstRebootObj = list.get(i2).getFirstRebootObj();
                        String afterRebootObj = list.get(i2).getAfterRebootObj();
                        if (!StringUtil.isEmpty(firstRebootObj) && !StringUtil.isEmpty(afterRebootObj)) {
                            LogUtil.i(TAG, "first=" + firstRebootObj + " after=" + afterRebootObj);
                            boolean arrayContainStr = arrayContainStr(afterRebootObj.split("\\*"), list2.get(i));
                            LogUtil.i(TAG, "obj=" + list2.get(i));
                            boolean listContainList = listContainList(list2, firstRebootObj.split("\\*"));
                            LogUtil.i(TAG, "isAfter=" + arrayContainStr + " hasFirst=" + listContainList);
                            if (arrayContainStr && listContainList) {
                                arrayList.remove(list2.get(i));
                            }
                        }
                    }
                }
            }
        }
        LogUtil.e(TAG, "createRebootTask:" + arrayList);
        if (!CollectionUtil.isEmpty(arrayList)) {
            list3.add(arrayList);
            for (String str : arrayList) {
                if (list2.contains(str)) {
                    list2.remove(str);
                }
            }
        }
        if (CollectionUtil.isEmpty(list2)) {
            return;
        }
        createRebootTask(list, list2, list3);
    }

    public static boolean listContainList(List<String> list, String[] strArr) {
        for (String str : strArr) {
            if (list.contains(str)) {
                return true;
            }
        }
        return false;
    }

    public static boolean arrayContainStr(String[] strArr, String str) {
        if (strArr == null || StringUtil.isEmpty(str)) {
            return false;
        }
        return Arrays.asList(strArr).contains(str);
    }

    public static String createUpdateListByStr(String str, String str2, String str3, List<String> list, boolean z) {
        if (!checkParameter(str, str2, str3)) {
            return "";
        }
        TargetUpgradeList targetUpgradeList = (TargetUpgradeList) getObjectFromJson(str2, TargetUpgradeList.class);
        LogUtil.e(TAG, "SOTA-CODE-----> updateList = " + GsonUtils.getInstance().toJson(targetUpgradeList));
        OTAConfig oTAConfig = (OTAConfig) getObjectFromJson(str, OTAConfig.class);
        ConfigInfo configInfo = OtaMasterShareData.getInstance().getConfigInfo();
        if (configInfo != null) {
            LogUtil.i(TAG, "setSubmasterInfo:" + configInfo.getSubMasterList());
            oTAConfig.setSubmasterInfo(configInfo.getSubMasterList());
        }
        return createUpdateListByStr(oTAConfig, targetUpgradeList, (UsbTaskBean) getObjectFromJson(str3, UsbTaskBean.class), list, z);
    }

    public static String createRollbackListByStr(String str, String str2, String str3) {
        if (!checkParameter(str, str2, str3)) {
            return "";
        }
        TargetUpgradeList targetUpgradeList = (TargetUpgradeList) getObjectFromJson(str2, TargetUpgradeList.class);
        OTAConfig oTAConfig = (OTAConfig) getObjectFromJson(str, OTAConfig.class);
        ConfigInfo configInfo = OtaMasterShareData.getInstance().getConfigInfo();
        if (configInfo != null) {
            LogUtil.i(TAG, "setSubmasterInfo:" + configInfo.getSubMasterList());
            oTAConfig.setSubmasterInfo(configInfo.getSubMasterList());
        }
        return createRollbackListByStr(oTAConfig, targetUpgradeList, (UsbTaskBean) getObjectFromJson(str3, UsbTaskBean.class));
    }

    public static String createUpdateListByStr(OTAConfig oTAConfig, TargetUpgradeList targetUpgradeList, UsbTaskBean usbTaskBean, List<String> list, boolean z) {
        return createTask(ApiType.OTA_UPDATE, oTAConfig, targetUpgradeList, usbTaskBean, list, z);
    }

    public static String createRollbackListByStr(OTAConfig oTAConfig, TargetUpgradeList targetUpgradeList, UsbTaskBean usbTaskBean) {
        return createTask(ApiType.OTA_ROLLBACK, oTAConfig, targetUpgradeList, usbTaskBean, null, false);
    }

    public static String createTask(ApiType apiType, OTAConfig oTAConfig, TargetUpgradeList targetUpgradeList, UsbTaskBean usbTaskBean, List<String> list, boolean z) {
        List<ArrayList<String>> createUpdateList = createUpdateList(targetUpgradeList);
        LogUtil.e(TAG, "SOTA-CODE-----> fsList = " + GsonUtils.getInstance().toJson(createUpdateList));
        ArrayList arrayList = new ArrayList();
        if (createUpdateList.size() > 0 && !CollectionUtil.isEmpty(createUpdateList.get(0))) {
            ArrayList arrayList2 = new ArrayList();
            createUpdateTask(oTAConfig.getUpdateSequence(), clone(createUpdateList.get(0)), arrayList2);
            LogUtil.i(TAG, "firstUpdateGroup:" + GsonUtils.getInstance().toJson(arrayList2));
            doSubList(arrayList2, getActionValueByType(apiType), arrayList);
            LogUtil.i(TAG, "subList1:" + GsonUtils.getInstance().toJson(arrayList));
            if (apiType == ApiType.OTA_UPDATE) {
                List<String> createSwitchTask = createSwitchTask(oTAConfig.getSubmasterInfo(), clone(createUpdateList.get(0)));
                LogUtil.i(TAG, "switchList:" + GsonUtils.getInstance().toJson(createSwitchTask));
                doSubList(createSwitchTask, Action.SWITCH, arrayList);
                LogUtil.i(TAG, "subList2:" + GsonUtils.getInstance().toJson(arrayList));
            }
            ArrayList<String> createRebootList = createRebootList(clone(createUpdateList.get(0)), oTAConfig.getSubmasterInfo());
            LogUtil.i(TAG, "firstRebootList:" + GsonUtils.getInstance().toJson(createRebootList));
            ArrayList arrayList3 = new ArrayList();
            createRebootTask(oTAConfig.getRebootSequence(), clone(createRebootList), arrayList3);
            LogUtil.i(TAG, "firstRebootGroup:" + GsonUtils.getInstance().toJson(arrayList3));
            doSubList(arrayList3, Action.REBOOT.getValue(), arrayList);
            LogUtil.i(TAG, "subList3:" + GsonUtils.getInstance().toJson(arrayList));
        }
        if (createUpdateList.size() > 1 && !CollectionUtil.isEmpty(createUpdateList.get(1))) {
            doSubList(createUpdateList.get(1), getActionByType(apiType), arrayList);
            LogUtil.i(TAG, "subList4:" + GsonUtils.getInstance().toJson(arrayList));
            ArrayList<String> createRebootList2 = createRebootList(clone(createUpdateList.get(1)), oTAConfig.getSubmasterInfo());
            LogUtil.i(TAG, "secondRebootList:" + GsonUtils.getInstance().toJson(createRebootList2));
            ArrayList arrayList4 = new ArrayList();
            createRebootTask(oTAConfig.getRebootSequence(), clone(createRebootList2), arrayList4);
            LogUtil.i(TAG, "secondRebootGroup:" + GsonUtils.getInstance().toJson(arrayList4));
            doSubList(arrayList4, Action.REBOOT.getValue(), arrayList);
            LogUtil.i(TAG, "subList5:" + GsonUtils.getInstance().toJson(arrayList));
        }
        List<SubmasterObjectList> allList = getAllList(targetUpgradeList, usbTaskBean, oTAConfig.getSubmasterInfo(), list, z);
        LogUtil.i(TAG, "allList:" + GsonUtils.getInstance().toJson(allList));
        LogUtil.i(TAG, "---------------------------------");
        List<SubmasterInfo> groupBySubmaster = groupBySubmaster(allList);
        LogUtil.e(TAG, "submasterInfoList:" + GsonUtils.getInstance().toJson(groupBySubmaster));
        return GsonUtils.getInstance().toJson(new UpgradeTaskBean(usbTaskBean.getPreConditions(), usbTaskBean.getSoftwareRelations(), new InstallTask("", hasPreFlashTask(allList), arrayList, arrayList.size()), groupBySubmaster));
    }

    public static void doSubList(List<String> list, Action action, List<InstallTask.SubTask> list2) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        InstallTask.SubTask subTask = new InstallTask.SubTask();
        ArrayList arrayList = new ArrayList();
        Iterator<String> it = list.iterator();
        while (it.hasNext()) {
            arrayList.add(new InstallTask.ObjectAction(it.next(), action.getValue()));
        }
        subTask.setObjList(arrayList);
        subTask.setTaskSerialNum(list2.size());
        list2.add(subTask);
    }

    public static void doSubList(List<List<String>> list, String str, List<InstallTask.SubTask> list2) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        int size = list2.size();
        int i = 0;
        for (int i2 = 0; i2 < list.size(); i2++) {
            InstallTask.SubTask subTask = new InstallTask.SubTask();
            ArrayList arrayList = new ArrayList();
            if (!CollectionUtil.isEmpty(list.get(i2))) {
                Iterator<String> it = list.get(i2).iterator();
                while (it.hasNext()) {
                    arrayList.add(new InstallTask.ObjectAction(it.next(), str));
                }
                subTask.setObjList(arrayList);
                subTask.setTaskSerialNum(i + size);
                list2.add(subTask);
                i++;
            }
        }
    }

    public static ArrayList<String> clone(ArrayList<String> arrayList) {
        return (ArrayList) arrayList.clone();
    }

    public static String getActionValueByType(ApiType apiType) {
        return getActionByType(apiType).getValue();
    }

    public static Action getActionByType(ApiType apiType) {
        if (apiType == ApiType.OTA_ROLLBACK) {
            return Action.ROLLBACK;
        }
        return Action.UPDATE;
    }

    public static List<UpgradeObjInfo> getOtaSubmasterMap(List<ConfigInfo.SubMasterConfig> list) {
        ArrayList arrayList = new ArrayList();
        if (list == null) {
            return arrayList;
        }
        for (int i = 0; i < list.size(); i++) {
            if (list.get(i) != null && !CollectionUtil.isEmpty(list.get(i).getObjList())) {
                for (int i2 = 0; i2 < list.get(i).getObjList().size(); i2++) {
                    if (list.get(i).getObjList().get(i2) != null) {
                        UpgradeObjInfo upgradeObjInfo = new UpgradeObjInfo();
                        upgradeObjInfo.name = list.get(i).getObjList().get(i2).getObjName();
                        upgradeObjInfo.dualBank = list.get(i).getObjList().get(i2).getDualBank();
                        upgradeObjInfo.preFlashFlag = list.get(i).getObjList().get(i2).getPreUpdate() == 1;
                        upgradeObjInfo.updateSubmaster = list.get(i).getSubMasterName();
                        arrayList.add(upgradeObjInfo);
                        if (!CollectionUtil.isEmpty(list.get(i).getObjList().get(i2).getSotaList())) {
                            for (int i3 = 0; i3 < list.get(i).getObjList().get(i2).getSotaList().size(); i3++) {
                                if (list.get(i).getObjList().get(i2).getSotaList().get(i3) != null) {
                                    UpgradeObjInfo upgradeObjInfo2 = new UpgradeObjInfo();
                                    upgradeObjInfo2.name = list.get(i).getObjList().get(i2).getSotaList().get(i3).getAppId();
                                    upgradeObjInfo2.updateSubmaster = list.get(i).getObjList().get(i2).getSotaList().get(i3).getSubmaster();
                                    arrayList.add(upgradeObjInfo2);
                                }
                            }
                        }
                    }
                }
            }
        }
        return arrayList;
    }

    public static List<SubmasterInfo> groupBySubmaster(List<SubmasterObjectList> list) {
        ArrayList arrayList = new ArrayList();
        for (final SubmasterObjectList submasterObjectList : list) {
            SubmasterInfo submasterInfo = (SubmasterInfo) CollectionUtil.findItem(arrayList, new CollectionUtil.Query() { // from class: com.changan.lh.api.-$$Lambda$TaskUtil2$fXB9rGafOniLeimPTti0-DF6lgs
                @Override // com.incall.apps.softmanager.util.CollectionUtil.Query
                public final boolean match(Object obj) {
                    boolean equals;
                    equals = TaskUtil2.SubmasterObjectList.this.updateSubmaster.equals(((SubmasterInfo) obj).getName());
                    return equals;
                }
            });
            if (submasterInfo == null) {
                submasterInfo = new SubmasterInfo(submasterObjectList.updateSubmaster, 0, new ArrayList());
                arrayList.add(submasterInfo);
            }
            submasterInfo.getObjInfo().addAll(submasterObjectList.objectInfoList);
            submasterInfo.setObjNum(submasterInfo.getObjInfo().size());
        }
        return arrayList;
    }

    public static boolean hasPreFlashTask(List<SubmasterObjectList> list) {
        if (list != null && !list.isEmpty()) {
            Iterator<SubmasterObjectList> it = list.iterator();
            while (it.hasNext()) {
                Iterator<SubmasterInfo.ObjectInfo> it2 = it.next().objectInfoList.iterator();
                while (it2.hasNext()) {
                    if (it2.next().isPreFlashFlag()) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    public static <T> T getObjectFromJson(String str, Class<T> cls) {
        try {
            return (T) GsonUtils.getInstance().fromJson(str, (Class) cls);
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e(TAG, cls.getName() + " transfer json exception!");
            return null;
        }
    }

    static class UpgradeObjInfo {
        int dualBank;
        String name;
        boolean preFlashFlag;
        String updateSubmaster;

        UpgradeObjInfo() {
        }
    }

    static class SubmasterObjectList {
        List<SubmasterInfo.ObjectInfo> objectInfoList;
        String updateSubmaster;

        SubmasterObjectList() {
        }
    }
}
