<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    android:orientation="vertical"
    android:background="@drawable/fota_bg_dialog"
    android:paddingBottom="32dp"
    android:layout_width="1280dp"
    android:layout_height="wrap_content"
    android:maxHeight="880dp">
    <TextView
        android:textSize="44sp"
        android:textStyle="bold"
        android:textColor="@color/caui_config_text_color_primary"
        android:gravity="center"
        android:id="@+id/lv_version_title"
        android:layout_width="match_parent"
        android:layout_height="108dp"
        android:layout_marginTop="32dp"
        android:text="SOTA检查结果详情"/>
    <LinearLayout
        android:gravity="center"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="60dp">
        <TextView
            android:textSize="26sp"
            android:textColor="@color/caui_config_text_color_primary"
            android:gravity="center"
            android:id="@+id/tv_item_name"
            android:background="@color/caui_config_content_bg_color_primary"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:text="APPid"
            android:lines="2"
            android:layout_weight="1"
            android:paddingStart="16dp"
            android:paddingEnd="12dp"/>
        <TextView
            android:textSize="26sp"
            android:textColor="@color/caui_config_text_color_primary"
            android:gravity="center"
            android:id="@+id/tv_item_version_target"
            android:background="@color/caui_config_divider_color_secondary"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:text="目标软件版本"
            android:lines="2"
            android:layout_weight="1"
            android:paddingStart="16dp"
            android:paddingEnd="12dp"/>
        <TextView
            android:textSize="26sp"
            android:gravity="center"
            android:id="@+id/tv_item_version_local"
            android:background="@color/caui_config_content_bg_color_primary"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:text="本地软件版本"
            android:lines="2"
            android:layout_weight="1"
            android:paddingStart="16dp"
            android:paddingEnd="12dp"/>
        <TextView
            android:textSize="26sp"
            android:textColor="@color/caui_config_text_color_primary"
            android:gravity="center"
            android:id="@+id/tv_item_result"
            android:background="@color/caui_config_divider_color_secondary"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:text="检测结果"
            android:lines="2"
            android:layout_weight="1"
            android:paddingStart="16dp"
            android:paddingEnd="12dp"/>
    </LinearLayout>
    <View
        android:background="@color/caui_config_content_bg_color_primary"
        android:layout_width="match_parent"
        android:layout_height="1dp"/>
    <androidx.recyclerview.widget.RecyclerView
        android:orientation="vertical"
        android:id="@+id/rv_sota_result_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="740dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>
</LinearLayout>
