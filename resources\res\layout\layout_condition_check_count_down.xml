<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:tag="layout/layout_condition_check_count_down_0"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginTop="100dp"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">
    <TextView
        android:textSize="56sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/ready_install_tip"
        android:layout_width="wrap_content"
        android:layout_height="78dp"
        android:layout_marginTop="104dp"
        android:text="@string/condition_is_readying"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="40sp"
        android:textColor="@color/caui_config_text_color_secondary"
        android:id="@+id/condition_check_count_down_content"
        android:layout_width="wrap_content"
        android:layout_height="56dp"
        android:layout_marginTop="32dp"
        android:text="@string/condition_checking_tip3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ready_install_tip"/>
    <TextView
        android:textSize="120sp"
        android:textColor="@color/caui_config_theme_color_normal"
        android:gravity="center"
        android:id="@+id/count_down_tx"
        android:tag="binding_1"
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="230dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/condition_check_count_down_content"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:tag="binding_2"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="242dp"
        android:text="@string/condition_install_now"
        android:layout_marginStart="904dp"
        app:caui_round_btn_type="main"
        app:caui_round_radius="@dimen/caui_config_corner_radius_middle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/count_down_tx"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:tag="binding_3"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="242dp"
        android:text="@string/condition_cancel_install"
        android:layout_marginEnd="904dp"
        app:caui_round_btn_type="third"
        app:caui_round_radius="@dimen/caui_config_corner_radius_middle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/count_down_tx"/>
</androidx.constraintlayout.widget.ConstraintLayout>
