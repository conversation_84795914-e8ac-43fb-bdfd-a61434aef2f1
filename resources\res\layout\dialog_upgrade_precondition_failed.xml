<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/transparent"
    android:layout_width="880dp"
    android:layout_height="wrap_content">
    <ImageView
        android:id="@+id/common_failed_close_img"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="55dp"
        android:src="@drawable/caui_icon_dialog_close"
        android:layout_marginStart="64dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="40sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/tx_common_fail_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="48dp"
        android:layout_marginStart="267dp"
        android:layout_marginEnd="264dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="32sp"
        android:textColor="@color/caui_config_text_color_secondary"
        android:gravity="left"
        android:id="@+id/tx_common_fail_content"
        android:layout_width="752dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:layout_marginStart="64dp"
        android:layout_marginEnd="64dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tx_common_fail_title"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/btn_common_fail_ok"
        android:layout_width="400dp"
        android:layout_height="96dp"
        android:layout_marginTop="48dp"
        android:layout_marginBottom="56dp"
        android:text="@string/confirm_dialog_protocol_yes"
        android:layout_marginStart="240dp"
        android:layout_marginEnd="240dp"
        app:caui_round_btn_type="main"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tx_common_fail_content"/>
</androidx.constraintlayout.widget.ConstraintLayout>
