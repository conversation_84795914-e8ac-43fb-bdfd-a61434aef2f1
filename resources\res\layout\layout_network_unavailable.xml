<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:tag="layout/layout_network_unavailable_0"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">
    <ImageView
        android:id="@+id/img_checking_circular"
        android:layout_width="367dp"
        android:layout_height="220dp"
        android:layout_marginTop="240dp"
        android:src="@drawable/fota_net_error"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/Base.Widget.AppCompat.ProgressBar"/>
    <TextView
        android:textSize="36sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="520dp"
        android:text="@string/network_unavailable"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="32sp"
        android:textColor="@color/caui_config_text_color_secondary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="586dp"
        android:text="@string/check_network"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/btn_network_setting"
        android:tag="binding_1"
        android:layout_width="400dp"
        android:layout_height="88dp"
        android:layout_marginTop="711dp"
        android:text="@string/network_setting"
        android:layout_marginStart="840dp"
        app:caui_round_btn_type="main"
        app:caui_round_radius="@dimen/caui_config_corner_radius_x_small"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/btn_retry"
        android:tag="binding_2"
        android:layout_width="400dp"
        android:layout_height="88dp"
        android:layout_marginTop="711dp"
        android:text="@string/tx_retry"
        android:layout_marginEnd="840dp"
        app:caui_round_btn_type="secondary"
        app:caui_round_radius="@dimen/caui_config_corner_radius_x_small"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
