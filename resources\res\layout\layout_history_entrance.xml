<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:tag="layout/layout_history_entrance_0"
    android:layout_width="1260dp"
    android:layout_height="150dp">
    <com.incall.apps.caui.layout.CAUIConstraintLayout
        android:background="@color/caui_config_content_bg_color_primary"
        android:layout_width="match_parent"
        android:layout_height="128dp"
        app:caui_radius="@dimen/caui_config_corner_radius_middle">
        <View
            android:tag="binding_1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="[打开]历史版本"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <TextView
            android:textSize="32sp"
            android:textColor="@color/caui_config_text_color_primary"
            android:id="@+id/history_tx"
            android:tag="binding_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="42dp"
            android:text="@string/history_entrance_tip"
            android:contentDescription="[打开]历史版本"
            android:layout_marginEnd="426dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <ImageView
            android:textSize="36sp"
            android:textColor="@color/caui_config_text_color_secondary"
            android:id="@+id/history_back_img"
            android:tag="binding_3"
            android:layout_width="15dp"
            android:layout_height="26dp"
            android:src="@drawable/caui_icon_chevron"
            android:scaleType="center"
            android:contentDescription="[打开]历史版本"
            android:layout_marginEnd="11dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
    </com.incall.apps.caui.layout.CAUIConstraintLayout>
    <View
        android:background="@color/caui_config_divider_color_primary"
        android:layout_width="1260dp"
        android:layout_height="2dp"
        android:layout_marginTop="16dp"/>
</LinearLayout>
