<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_gravity="center"
        android:background="@color/transparent"
        android:layout_width="880dp"
        android:layout_height="304dp"
        android:minHeight="404dp">
        <ImageView
            android:id="@+id/test_vehicle_dialog_iv"
            android:background="@drawable/caui_icon_dialog_close"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginTop="55dp"
            android:layout_marginStart="64dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <TextView
            android:textSize="44sp"
            android:textStyle="bold"
            android:textColor="@color/caui_config_text_color_primary"
            android:id="@+id/wifi_download_dialog_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="48dp"
            android:text="是否判断安装条件"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
            android:textSize="32sp"
            android:gravity="center"
            android:id="@+id/confirm_condition_check"
            android:layout_width="344dp"
            android:layout_height="96dp"
            android:layout_marginBottom="56dp"
            android:text="是"
            android:layout_marginStart="64dp"
            app:caui_round_btn_type="main"
            app:caui_round_radius="@dimen/caui_config_corner_radius_small"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>
        <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
            android:textSize="32sp"
            android:gravity="center"
            android:id="@+id/reject_condition_check"
            android:layout_width="344dp"
            android:layout_height="96dp"
            android:layout_marginBottom="56dp"
            android:text="否"
            android:layout_marginEnd="64dp"
            app:caui_round_btn_type="negative"
            app:caui_round_radius="@dimen/caui_config_corner_radius_small"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
