<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="#000000"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ImageView
        android:id="@+id/installing_image_view"
        android:layout_width="320dp"
        android:layout_height="320dp"
        android:layout_marginTop="504dp"
        android:layout_marginBottom="776dp"
        android:src="@drawable/installing"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.incall.apps.caui.widget.progress.CAUIProgressBar
        android:gravity="center"
        android:id="@+id/progress_install"
        android:layout_width="640dp"
        android:layout_height="8dp"
        android:layout_marginTop="24dp"
        app:caui_progress_color="@color/caui_config_color_white"
        app:caui_progress_track_color="@color/progress_track_color"
        app:caui_progress_track_width="640dp"
        app:caui_progress_type="type_round_rect"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/installing_image_view"/>
    <TextView
        android:textSize="32sp"
        android:textColor="#ffffff"
        android:id="@+id/tx_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="6dp"
        android:layout_marginStart="24dp"
        app:layout_constraintBottom_toBottomOf="@+id/progress_install"
        app:layout_constraintStart_toEndOf="@+id/progress_install"
        app:layout_constraintTop_toTopOf="@+id/progress_install"/>
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="100dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/installing_image_view">
        <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
            android:textSize="36sp"
            android:textColor="@color/color_dialog_installing_tip"
            android:gravity="center"
            android:id="@+id/btn_exit"
            android:padding="10dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="20dp"
            android:text="退出PDI刷写"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"/>
        <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
            android:textSize="36sp"
            android:textColor="@color/color_dialog_installing_tip"
            android:gravity="center"
            android:id="@+id/btn_continue"
            android:padding="10dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="20dp"
            android:text="继续PDI刷写"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"/>
        <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
            android:textSize="36sp"
            android:textColor="@color/color_dialog_installing_tip"
            android:gravity="center"
            android:id="@+id/btn_cancel"
            android:padding="10dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="取消下载"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"/>
    </LinearLayout>
    <TextView
        android:textSize="36sp"
        android:textColor="@color/color_dialog_installing_tip"
        android:gravity="center"
        android:id="@+id/tx_install_time"
        android:layout_width="800dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="200dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/installing_image_view"/>
    <com.incall.apps.softmanager.base.view.FactoryView
        android:id="@+id/left_view"
        android:layout_width="400dp"
        android:layout_height="400dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>
    <com.incall.apps.softmanager.base.view.FactoryView
        android:id="@+id/right_view"
        android:layout_width="400dp"
        android:layout_height="400dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
