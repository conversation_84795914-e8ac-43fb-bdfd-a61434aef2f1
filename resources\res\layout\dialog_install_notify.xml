<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/transparent"
    android:layout_width="1280dp"
    android:layout_height="1024dp"
    android:minHeight="1024dp">
    <ImageView
        android:id="@+id/close_img_notify_install"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="55dp"
        android:src="@drawable/caui_icon_dialog_close"
        android:layout_marginStart="64dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="40sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/notify_title_tx"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="46dp"
        android:text="@string/notify_dialog_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <androidx.core.widget.NestedScrollView
        android:scrollbarThumbVertical="@drawable/fota_scroll_thumb"
        android:scrollbarStyle="outsideOverlay"
        android:id="@+id/nested_scroll_view"
        android:background="@color/transparent"
        android:scrollbars="vertical"
        android:layout_width="match_parent"
        android:layout_height="596dp"
        android:layout_marginTop="34dp"
        android:layout_marginBottom="32dp"
        app:layout_constraintBottom_toTopOf="@+id/tx_tip_auto_upgrade"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/notify_title_tx">
        <com.incall.apps.caui.layout.CAUIConstraintLayout
            android:background="@color/transparent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="64dp"
            android:layout_marginEnd="64dp">
            <TextView
                android:textSize="32sp"
                android:textColor="@color/caui_config_text_color_primary"
                android:id="@+id/tx_big_version"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <TextView
                android:textSize="32sp"
                android:textColor="@color/caui_config_text_color_primary"
                android:id="@+id/tx_stubborn_tip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tx_big_version"/>
            <TextView
                android:textSize="28sp"
                android:textColor="@color/caui_config_text_color_primary"
                android:id="@+id/tx_upgrade_detail"
                android:layout_width="1152dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tx_stubborn_tip"/>
            <androidx.recyclerview.widget.RecyclerView
                android:orientation="vertical"
                android:id="@+id/rv_notify"
                android:background="@color/transparent"
                android:layout_width="1152dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tx_upgrade_detail"/>
        </com.incall.apps.caui.layout.CAUIConstraintLayout>
    </androidx.core.widget.NestedScrollView>
    <TextView
        android:textSize="32sp"
        android:textColor="@color/caui_config_text_color_secondary"
        android:id="@+id/tx_tip_auto_upgrade"
        android:layout_width="1152dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32sp"
        android:layout_marginBottom="32dp"
        android:layout_marginStart="64dp"
        android:layout_marginEnd="64dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/nested_scroll_view"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/btn_positive"
        android:layout_width="341dp"
        android:layout_height="120dp"
        android:layout_marginTop="32dp"
        android:layout_marginBottom="64dp"
        android:layout_marginStart="64dp"
        app:caui_round_btn_type="main"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tx_tip_auto_upgrade"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/btn_negative"
        android:layout_width="342dp"
        android:layout_height="120dp"
        android:layout_marginTop="32dp"
        android:layout_marginBottom="64dp"
        android:layout_marginStart="469dp"
        app:caui_round_btn_type="secondary"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tx_tip_auto_upgrade"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:textColor="@color/caui_config_text_color_secondary"
        android:gravity="center"
        android:id="@+id/btn_later_upgrade"
        android:clickable="true"
        android:layout_width="341dp"
        android:layout_height="120dp"
        android:layout_marginTop="32dp"
        android:layout_marginBottom="64dp"
        android:text="@string/notify_dialog_button_ignore"
        android:contentDescription="暂不升级"
        android:layout_marginStart="875dp"
        app:caui_round_btn_type="secondary"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tx_tip_auto_upgrade"/>
</androidx.constraintlayout.widget.ConstraintLayout>
