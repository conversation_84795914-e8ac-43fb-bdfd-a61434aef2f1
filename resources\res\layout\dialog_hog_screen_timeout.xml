<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_gravity="center"
        android:background="@drawable/fota_bg_dialog"
        android:layout_width="880dp"
        android:layout_height="444dp">
        <TextView
            android:textSize="40sp"
            android:textColor="@color/caui_config_text_color_primary"
            android:id="@+id/tx_common_confirm_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="48dp"
            android:text="@string/dialog_viu_control_power_down"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <TextView
            android:textSize="32sp"
            android:textColor="@color/caui_config_text_color_primary"
            android:id="@+id/tx_common_confirm_content"
            android:layout_width="752dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="131dp"
            android:text="@string/dialog_viu_control_tip2"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
            android:textSize="36sp"
            android:gravity="center"
            android:id="@+id/btn_yes"
            android:layout_width="344dp"
            android:layout_height="96dp"
            android:layout_marginTop="287dp"
            android:text="@string/common_confirm_dialog_confirm"
            android:layout_marginStart="64dp"
            app:caui_round_btn_type="main"
            app:caui_round_radius="@dimen/caui_config_corner_radius_small"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
            android:textSize="36sp"
            android:gravity="center"
            android:id="@+id/btn_no"
            android:layout_width="344dp"
            android:layout_height="96dp"
            android:layout_marginTop="287dp"
            android:text="@string/common_confirm_dialog_no"
            android:layout_marginEnd="64dp"
            app:caui_round_btn_type="secondary"
            app:caui_round_radius="@dimen/caui_config_corner_radius_small"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
