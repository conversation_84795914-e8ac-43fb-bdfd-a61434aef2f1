package com.bumptech.glide.load.resource.bitmap;

import android.graphics.Bitmap;
import com.bumptech.glide.load.EncodeStrategy;
import com.bumptech.glide.load.Option;
import com.bumptech.glide.load.Options;
import com.bumptech.glide.load.ResourceEncoder;
import com.bumptech.glide.load.engine.bitmap_recycle.ArrayPool;

/* loaded from: classes.dex */
public class BitmapEncoder implements ResourceEncoder<Bitmap> {
    private static final String TAG = "BitmapEncoder";
    private final ArrayPool arrayPool;
    public static final Option<Integer> COMPRESSION_QUALITY = Option.memory("com.bumptech.glide.load.resource.bitmap.BitmapEncoder.CompressionQuality", 90);
    public static final Option<Bitmap.CompressFormat> COMPRESSION_FORMAT = Option.memory("com.bumptech.glide.load.resource.bitmap.BitmapEncoder.CompressionFormat");

    public BitmapEncoder(ArrayPool arrayPool) {
        this.arrayPool = arrayPool;
    }

    @Deprecated
    public BitmapEncoder() {
        this.arrayPool = null;
    }

    /* JADX WARN: Code restructure failed: missing block: B:35:0x0066, code lost:
    
        if (r6 != null) goto L44;
     */
    @Override // com.bumptech.glide.load.Encoder
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean encode(com.bumptech.glide.load.engine.Resource<android.graphics.Bitmap> r9, java.io.File r10, com.bumptech.glide.load.Options r11) {
        /*
            r8 = this;
            java.lang.String r0 = "BitmapEncoder"
            java.lang.Object r9 = r9.get()
            android.graphics.Bitmap r9 = (android.graphics.Bitmap) r9
            android.graphics.Bitmap$CompressFormat r1 = r8.getFormat(r9, r11)
            int r2 = r9.getWidth()
            java.lang.Integer r2 = java.lang.Integer.valueOf(r2)
            int r3 = r9.getHeight()
            java.lang.Integer r3 = java.lang.Integer.valueOf(r3)
            java.lang.String r4 = "encode: [%dx%d] %s"
            com.bumptech.glide.util.pool.GlideTrace.beginSectionFormat(r4, r2, r3, r1)
            long r2 = com.bumptech.glide.util.LogTime.getLogTime()     // Catch: java.lang.Throwable -> Lca
            com.bumptech.glide.load.Option<java.lang.Integer> r4 = com.bumptech.glide.load.resource.bitmap.BitmapEncoder.COMPRESSION_QUALITY     // Catch: java.lang.Throwable -> Lca
            java.lang.Object r4 = r11.get(r4)     // Catch: java.lang.Throwable -> Lca
            java.lang.Integer r4 = (java.lang.Integer) r4     // Catch: java.lang.Throwable -> Lca
            int r4 = r4.intValue()     // Catch: java.lang.Throwable -> Lca
            r5 = 0
            r6 = 0
            java.io.FileOutputStream r7 = new java.io.FileOutputStream     // Catch: java.lang.Throwable -> L57 java.io.IOException -> L59
            r7.<init>(r10)     // Catch: java.lang.Throwable -> L57 java.io.IOException -> L59
            com.bumptech.glide.load.engine.bitmap_recycle.ArrayPool r10 = r8.arrayPool     // Catch: java.lang.Throwable -> L51 java.io.IOException -> L54
            if (r10 == 0) goto L45
            com.bumptech.glide.load.data.BufferedOutputStream r10 = new com.bumptech.glide.load.data.BufferedOutputStream     // Catch: java.lang.Throwable -> L51 java.io.IOException -> L54
            com.bumptech.glide.load.engine.bitmap_recycle.ArrayPool r8 = r8.arrayPool     // Catch: java.lang.Throwable -> L51 java.io.IOException -> L54
            r10.<init>(r7, r8)     // Catch: java.lang.Throwable -> L51 java.io.IOException -> L54
            r6 = r10
            goto L46
        L45:
            r6 = r7
        L46:
            r9.compress(r1, r4, r6)     // Catch: java.lang.Throwable -> L57 java.io.IOException -> L59
            r6.close()     // Catch: java.lang.Throwable -> L57 java.io.IOException -> L59
            r5 = 1
        L4d:
            r6.close()     // Catch: java.io.IOException -> L69 java.lang.Throwable -> Lca
            goto L69
        L51:
            r8 = move-exception
            r6 = r7
            goto Lc4
        L54:
            r8 = move-exception
            r6 = r7
            goto L5a
        L57:
            r8 = move-exception
            goto Lc4
        L59:
            r8 = move-exception
        L5a:
            r10 = 3
            boolean r10 = android.util.Log.isLoggable(r0, r10)     // Catch: java.lang.Throwable -> L57
            if (r10 == 0) goto L66
            java.lang.String r10 = "Failed to encode Bitmap"
            android.util.Log.d(r0, r10, r8)     // Catch: java.lang.Throwable -> L57
        L66:
            if (r6 == 0) goto L69
            goto L4d
        L69:
            r8 = 2
            boolean r8 = android.util.Log.isLoggable(r0, r8)     // Catch: java.lang.Throwable -> Lca
            if (r8 == 0) goto Lc0
            java.lang.StringBuilder r8 = new java.lang.StringBuilder     // Catch: java.lang.Throwable -> Lca
            r8.<init>()     // Catch: java.lang.Throwable -> Lca
            java.lang.String r10 = "Compressed with type: "
            java.lang.StringBuilder r8 = r8.append(r10)     // Catch: java.lang.Throwable -> Lca
            java.lang.StringBuilder r8 = r8.append(r1)     // Catch: java.lang.Throwable -> Lca
            java.lang.String r10 = " of size "
            java.lang.StringBuilder r8 = r8.append(r10)     // Catch: java.lang.Throwable -> Lca
            int r10 = com.bumptech.glide.util.Util.getBitmapByteSize(r9)     // Catch: java.lang.Throwable -> Lca
            java.lang.StringBuilder r8 = r8.append(r10)     // Catch: java.lang.Throwable -> Lca
            java.lang.String r10 = " in "
            java.lang.StringBuilder r8 = r8.append(r10)     // Catch: java.lang.Throwable -> Lca
            double r1 = com.bumptech.glide.util.LogTime.getElapsedMillis(r2)     // Catch: java.lang.Throwable -> Lca
            java.lang.StringBuilder r8 = r8.append(r1)     // Catch: java.lang.Throwable -> Lca
            java.lang.String r10 = ", options format: "
            java.lang.StringBuilder r8 = r8.append(r10)     // Catch: java.lang.Throwable -> Lca
            com.bumptech.glide.load.Option<android.graphics.Bitmap$CompressFormat> r10 = com.bumptech.glide.load.resource.bitmap.BitmapEncoder.COMPRESSION_FORMAT     // Catch: java.lang.Throwable -> Lca
            java.lang.Object r10 = r11.get(r10)     // Catch: java.lang.Throwable -> Lca
            java.lang.StringBuilder r8 = r8.append(r10)     // Catch: java.lang.Throwable -> Lca
            java.lang.String r10 = ", hasAlpha: "
            java.lang.StringBuilder r8 = r8.append(r10)     // Catch: java.lang.Throwable -> Lca
            boolean r9 = r9.hasAlpha()     // Catch: java.lang.Throwable -> Lca
            java.lang.StringBuilder r8 = r8.append(r9)     // Catch: java.lang.Throwable -> Lca
            java.lang.String r8 = r8.toString()     // Catch: java.lang.Throwable -> Lca
            android.util.Log.v(r0, r8)     // Catch: java.lang.Throwable -> Lca
        Lc0:
            com.bumptech.glide.util.pool.GlideTrace.endSection()
            return r5
        Lc4:
            if (r6 == 0) goto Lc9
            r6.close()     // Catch: java.io.IOException -> Lc9 java.lang.Throwable -> Lca
        Lc9:
            throw r8     // Catch: java.lang.Throwable -> Lca
        Lca:
            r8 = move-exception
            com.bumptech.glide.util.pool.GlideTrace.endSection()
            throw r8
        */
        throw new UnsupportedOperationException("Method not decompiled: com.bumptech.glide.load.resource.bitmap.BitmapEncoder.encode(com.bumptech.glide.load.engine.Resource, java.io.File, com.bumptech.glide.load.Options):boolean");
    }

    private Bitmap.CompressFormat getFormat(Bitmap bitmap, Options options) {
        Bitmap.CompressFormat compressFormat = (Bitmap.CompressFormat) options.get(COMPRESSION_FORMAT);
        if (compressFormat != null) {
            return compressFormat;
        }
        if (bitmap.hasAlpha()) {
            return Bitmap.CompressFormat.PNG;
        }
        return Bitmap.CompressFormat.JPEG;
    }

    @Override // com.bumptech.glide.load.ResourceEncoder
    public EncodeStrategy getEncodeStrategy(Options options) {
        return EncodeStrategy.TRANSFORMED;
    }
}
