package com.changan.evs.ipc.proxy.base;

import java.util.function.ToIntFunction;

/* compiled from: lambda */
/* renamed from: com.changan.evs.ipc.proxy.base.-$$Lambda$EvsBaseProxy$uBsCIBAbAW1p84hPJ6ULJah0SPE, reason: invalid class name */
/* loaded from: classes.dex */
public final /* synthetic */ class $$Lambda$EvsBaseProxy$uBsCIBAbAW1p84hPJ6ULJah0SPE implements ToIntFunction {
    public static final /* synthetic */ $$Lambda$EvsBaseProxy$uBsCIBAbAW1p84hPJ6ULJah0SPE INSTANCE = new $$Lambda$EvsBaseProxy$uBsCIBAbAW1p84hPJ6ULJah0SPE();

    private /* synthetic */ $$Lambda$EvsBaseProxy$uBsCIBAbAW1p84hPJ6ULJah0SPE() {
    }

    @Override // java.util.function.ToIntFunction
    public final int applyAsInt(Object obj) {
        int intValue;
        intValue = ((Integer) obj).intValue();
        return intValue;
    }
}
