package android.hardware.automotive.cavehicle.V1_0;

import java.util.ArrayList;

/* loaded from: classes.dex */
public final class VehicleProperty {
    public static final int ABS_ACTIVE = 287310858;
    public static final int AP_POWER_BOOTUP_REASON = 289409538;
    public static final int AP_POWER_STATE_REPORT = 289475073;
    public static final int AP_POWER_STATE_REQ = 289475072;
    public static final int CABIN_LIGHTS_STATE = 289410817;
    public static final int CABIN_LIGHTS_SWITCH = 289410818;
    public static final int CREATE_USER = 299896585;
    public static final int CURRENT_GEAR = 289408001;
    public static final int DISABLED_OPTIONAL_FEATURES = 286265094;
    public static final int DISPLAY_BRIGHTNESS = 289409539;
    public static final int DISTANCE_DISPLAY_UNITS = 289408512;
    public static final int DOOR_LOCK = 371198722;
    public static final int DOOR_MOVE = 373295873;
    public static final int DOOR_POS = 373295872;
    public static final int ENGINE_COOLANT_TEMP = 291504897;
    public static final int ENGINE_OIL_LEVEL = 289407747;
    public static final int ENGINE_OIL_TEMP = 291504900;
    public static final int ENGINE_RPM = 291504901;
    public static final int ENV_OUTSIDE_TEMPERATURE = 291505923;
    public static final int EV_BATTERY_DISPLAY_UNITS = 289408515;
    public static final int EV_BATTERY_INSTANTANEOUS_CHARGE_RATE = 291504908;
    public static final int EV_BATTERY_LEVEL = 291504905;
    public static final int EV_CHARGE_PORT_CONNECTED = 287310603;
    public static final int EV_CHARGE_PORT_OPEN = 287310602;
    public static final int FOG_LIGHTS_STATE = 289410562;
    public static final int FOG_LIGHTS_SWITCH = 289410578;
    public static final int FUEL_CONSUMPTION_UNITS_DISTANCE_OVER_VOLUME = 287311364;
    public static final int FUEL_DOOR_OPEN = 287310600;
    public static final int FUEL_LEVEL = 291504903;
    public static final int FUEL_LEVEL_LOW = 287310853;
    public static final int FUEL_VOLUME_DISPLAY_UNITS = 289408513;
    public static final int GEAR_SELECTION = 289408000;
    public static final int HAZARD_LIGHTS_STATE = 289410563;
    public static final int HAZARD_LIGHTS_SWITCH = 289410579;
    public static final int HEADLIGHTS_STATE = 289410560;
    public static final int HEADLIGHTS_SWITCH = 289410576;
    public static final int HIGH_BEAM_LIGHTS_STATE = 289410561;
    public static final int HIGH_BEAM_LIGHTS_SWITCH = 289410577;
    public static final int HVAC_ACTUAL_FAN_SPEED_RPM = 356517135;
    public static final int HVAC_AC_ON = 354419973;
    public static final int HVAC_AUTO_ON = 354419978;
    public static final int HVAC_AUTO_RECIRC_ON = 354419986;
    public static final int HVAC_DEFROSTER = 320865540;
    public static final int HVAC_DUAL_ON = 354419977;
    public static final int HVAC_ELECTRIC_DEFROSTER_ON = 320865556;
    public static final int HVAC_FAN_DIRECTION = 356517121;
    public static final int HVAC_FAN_DIRECTION_AVAILABLE = 356582673;
    public static final int HVAC_FAN_SPEED = 356517120;
    public static final int HVAC_MAX_AC_ON = 354419974;
    public static final int HVAC_MAX_DEFROST_ON = 354419975;
    public static final int HVAC_POWER_ON = 354419984;
    public static final int HVAC_RECIRC_ON = 354419976;
    public static final int HVAC_SEAT_TEMPERATURE = 356517131;
    public static final int HVAC_SEAT_VENTILATION = 356517139;
    public static final int HVAC_SIDE_MIRROR_HEAT = 339739916;
    public static final int HVAC_STEERING_WHEEL_HEAT = 289408269;
    public static final int HVAC_TEMPERATURE_CURRENT = 358614274;
    public static final int HVAC_TEMPERATURE_DISPLAY_UNITS = 289408270;
    public static final int HVAC_TEMPERATURE_SET = 358614275;
    public static final int HW_KEY_INPUT = 289475088;
    public static final int HW_ROTARY_INPUT = 289475104;
    public static final int IGNITION_STATE = 289408009;
    public static final int INFO_DRIVER_SEAT = 356516106;
    public static final int INFO_EV_BATTERY_CAPACITY = 291504390;
    public static final int INFO_EV_CONNECTOR_TYPE = 289472775;
    public static final int INFO_EV_PORT_LOCATION = 289407241;
    public static final int INFO_EXTERIOR_DIMENSIONS = 289472779;
    public static final int INFO_FUEL_CAPACITY = 291504388;
    public static final int INFO_FUEL_DOOR_LOCATION = 289407240;
    public static final int INFO_FUEL_TYPE = 289472773;
    public static final int INFO_MAKE = 286261505;
    public static final int INFO_MODEL = 286261506;
    public static final int INFO_MODEL_YEAR = 289407235;
    public static final int INFO_MULTI_EV_PORT_LOCATIONS = 289472780;
    public static final int INFO_VIN = 286261504;
    public static final int INITIAL_USER_INFO = 299896583;
    public static final int INVALID = 0;
    public static final int MCU_DSP_LOUDSPEAKER_GAIN = 557925658;
    public static final int MCU_DSP_MEDIA_CHANNEL_SWITCH = 557860117;
    public static final int MCU_DSP_MIC_MUTE_CTR = 557860115;
    public static final int MCU_DSP_SOUND_EFFECTS_CTR = 557860113;
    public static final int MCU_DSP_SOUND_FIELD_SET = 557860116;
    public static final int MCU_DSP_SOUND_SOURCE = 557860118;
    public static final int MCU_DSP_SOUND_WAVE_MODE = 557860120;
    public static final int MCU_DSP_SUPER_BASS_MODE = 557860121;
    public static final int MCU_DSP_VOLUME_CTR = 557860112;
    public static final int MCU_DSP_VOLUME_MUTE_CTR = 557860114;
    public static final int MIRROR_FOLD = 287312709;
    public static final int MIRROR_LOCK = 287312708;
    public static final int MIRROR_Y_MOVE = 339741507;
    public static final int MIRROR_Y_POS = 339741506;
    public static final int MIRROR_Z_MOVE = 339741505;
    public static final int MIRROR_Z_POS = 339741504;
    public static final int NIGHT_MODE = 287310855;
    public static final int OBD2_FREEZE_FRAME = 299896065;
    public static final int OBD2_FREEZE_FRAME_CLEAR = 299896067;
    public static final int OBD2_FREEZE_FRAME_INFO = 299896066;
    public static final int OBD2_LIVE_FRAME = 299896064;
    public static final int PARKING_BRAKE_AUTO_APPLY = 287310851;
    public static final int PARKING_BRAKE_ON = 287310850;
    public static final int PERF_ODOMETER = 291504644;
    public static final int PERF_REAR_STEERING_ANGLE = 291504656;
    public static final int PERF_STEERING_ANGLE = 291504649;
    public static final int PERF_VEHICLE_SPEED = 291504647;
    public static final int PERF_VEHICLE_SPEED_DISPLAY = 291504648;
    public static final int RANGE_REMAINING = 291504904;
    public static final int READING_LIGHTS_STATE = 356519683;
    public static final int READING_LIGHTS_SWITCH = 356519684;
    public static final int REMOVE_USER = 299896586;
    public static final int SEAT_BACKREST_ANGLE_1_MOVE = 356518792;
    public static final int SEAT_BACKREST_ANGLE_1_POS = 356518791;
    public static final int SEAT_BACKREST_ANGLE_2_MOVE = 356518794;
    public static final int SEAT_BACKREST_ANGLE_2_POS = 356518793;
    public static final int SEAT_BELT_BUCKLED = 354421634;
    public static final int SEAT_BELT_HEIGHT_MOVE = 356518788;
    public static final int SEAT_BELT_HEIGHT_POS = 356518787;
    public static final int SEAT_DEPTH_MOVE = 356518798;
    public static final int SEAT_DEPTH_POS = 356518797;
    public static final int SEAT_FORE_AFT_MOVE = 356518790;
    public static final int SEAT_FORE_AFT_POS = 356518789;
    public static final int SEAT_HEADREST_ANGLE_MOVE = 356518808;
    public static final int SEAT_HEADREST_ANGLE_POS = 356518807;
    public static final int SEAT_HEADREST_FORE_AFT_MOVE = 356518810;
    public static final int SEAT_HEADREST_FORE_AFT_POS = 356518809;
    public static final int SEAT_HEADREST_HEIGHT_MOVE = 356518806;
    public static final int SEAT_HEADREST_HEIGHT_POS = 289409941;
    public static final int SEAT_HEIGHT_MOVE = 356518796;
    public static final int SEAT_HEIGHT_POS = 356518795;
    public static final int SEAT_LUMBAR_FORE_AFT_MOVE = 356518802;
    public static final int SEAT_LUMBAR_FORE_AFT_POS = 356518801;
    public static final int SEAT_LUMBAR_SIDE_SUPPORT_MOVE = 356518804;
    public static final int SEAT_LUMBAR_SIDE_SUPPORT_POS = 356518803;
    public static final int SEAT_MEMORY_SELECT = 356518784;
    public static final int SEAT_MEMORY_SET = 356518785;
    public static final int SEAT_OCCUPANCY = 356518832;
    public static final int SEAT_TILT_MOVE = 356518800;
    public static final int SEAT_TILT_POS = 356518799;
    public static final int SUPPORT_CUSTOMIZE_VENDOR_PERMISSION = 287313669;
    public static final int SWITCH_USER = 299896584;
    public static final int TIRE_PRESSURE = 392168201;
    public static final int TIRE_PRESSURE_DISPLAY_UNITS = 289408514;
    public static final int TRACTION_CONTROL_ACTIVE = 287310859;
    public static final int TURN_SIGNAL_STATE = 289408008;
    public static final int USER_IDENTIFICATION_ASSOCIATION = 299896587;
    public static final int VEHICLE_MAP_SERVICE = 299895808;
    public static final int VEHICLE_SPEED_DISPLAY_UNITS = 289408517;
    public static final int VENDOR_ACTIVE_NOISE_REDUCTION_STATUS = 557866516;
    public static final int VENDOR_ADAS_ACC_MODE = 557866008;
    public static final int VENDOR_ADAS_DOWLEFT_ALERT_STATUS = 557866006;
    public static final int VENDOR_ADAS_DOWRIGHT_ALERT_STATUS = 557866007;
    public static final int VENDOR_ADAS_LEFT_SEA_ALERT_STATUS = 557866004;
    public static final int VENDOR_ADAS_LNG_TAKEOVER_REQ_STATUS = 557866009;
    public static final int VENDOR_ADAS_RIGHT_SEA_ALERT_STATUS = 557866005;
    public static final int VENDOR_CAN_INFO_2MCU = 561005072;
    public static final int VENDOR_CERTID_DISTRIBUTE = 561006356;
    public static final int VENDOR_CERTIFICATE_ID = 561005594;
    public static final int VENDOR_CERTIFICATE_STATUS = 561005592;
    public static final int VENDOR_CHANG_CONFIG_INFO = 561013282;
    public static final int VENDOR_DATA_SIGN_REQUEST = 561006355;
    public static final int VENDOR_DTC_FAULT_CODE_FEEDBACK = 557866771;
    public static final int VENDOR_DTC_FAULT_CODE_REQUEST = 557866768;
    public static final int VENDOR_DTC_FAULT_CODE_SEND = 557932306;
    public static final int VENDOR_DTC_FAULT_CODE_UP_SET = 557932305;
    public static final int VENDOR_EDC_HW_VERSION = 554714129;
    public static final int VENDOR_EDC_PART_NUMBER = 554714130;
    public static final int VENDOR_EDC_SUPPLIER_CODE = 554714131;
    public static final int VENDOR_FACTORY_FEEDBACK = 561013271;
    public static final int VENDOR_FACTORY_PULL_HUAYANG_SERVICE = 557867544;
    public static final int VENDOR_FACTORY_TRANSMIT = 561013270;
    public static final int VENDOR_GET_CAN_CONFIG_INFO = 561004560;
    public static final int VENDOR_GET_CAN_INFO = 561013284;
    public static final int VENDOR_HEARTBEAT_FEEDBACK = 557929232;
    public static final int VENDOR_HEARTBEAT_INFO = 561009168;
    public static final int VENDOR_INTERACTIVE_LIGHT_FEEDBACK = 557867025;
    public static final int VENDOR_INTERACTIVE_LIGHT_SET = 557867024;
    public static final int VENDOR_MANAGER_DIAGNO_CONFLICT = 561013280;
    public static final int VENDOR_MCU_CHECK_RESULT = 561013274;
    public static final int VENDOR_MCU_LOG_UPLOAD = 554719760;
    public static final int VENDOR_MCU_PART_NUMBER = 554714132;
    public static final int VENDOR_MCU_TIME = 557930513;
    public static final int VENDOR_MCU_TIME_REQ = 557864976;
    public static final int VENDOR_MCU_TIME_REQ_STATUS = 557930768;
    public static final int VENDOR_MCU_TIME_STATUS = 557865233;
    public static final int VENDOR_MCU_VERSION = 554714128;
    public static final int VENDOR_MILEAGE_RESET = 557861137;
    public static final int VENDOR_OFFLINE_CONFIG_INFO_REQUEST = 557867529;
    public static final int VENDOR_OFFLINE_CONFIG_INFO_UP = 561013264;
    public static final int VENDOR_OFFLINE_CONFIG_VERSION = 561013265;
    public static final int VENDOR_OFF_LINE_SET = 561005329;
    public static final int VENDOR_OFF_LINE_STATUS = 561005591;
    public static final int VENDOR_PLUGGABLE_SOUND_EQUIPMENT_BATTERY_LEVEL = 557866002;
    public static final int VENDOR_PLUGGABLE_SOUND_EQUIPMENT_CHARGE_STATUS = 557866001;
    public static final int VENDOR_PLUGGABLE_SOUND_EQUIPMENT_FAULT_STATUS = 557866003;
    public static final int VENDOR_PLUGGABLE_SOUND_EQUIPMENT_STATUS = 557866000;
    public static final int VENDOR_POWER_AMPLIFIER_SWITCH = 557867550;
    public static final int VENDOR_POWER_MODE_FEEDBACK = 557928722;
    public static final int VENDOR_POWER_MODE_REPORT = 557863185;
    public static final int VENDOR_PRIVATE_CERTIFICATE_STATUS = 561005593;
    public static final int VENDOR_PRIVATE_KEY_CERTIFICATE_DISTRIBUTE = 561006354;
    public static final int VENDOR_PROJECTION_SET = 557867026;
    public static final int VENDOR_PUBLIC_KEY_CERTIFICATE_DISTRIBUTE = 561006353;
    public static final int VENDOR_REQUEST_MCU_HANDSHAKE = 557842944;
    public static final int VENDOR_SELF_DEFINE_SOUND_EFFECTS_CTR = 557860119;
    public static final int VENDOR_SOC_PART_NUMBER_FEEDBACK = 561012243;
    public static final int VENDOR_SOC_PART_NUMBER_REQUEST = 557866514;
    public static final int VENDOR_SOC_VERSION_FEEDBACK = 561012241;
    public static final int VENDOR_SOC_VERSION_REQUEST = 557866512;
    public static final int VENDOR_SUBTOTAL_MILEAGE_RESET = 557864464;
    public static final int VENDOR_TUID_DISTRIBUTE = 561006352;
    public static final int VENDOR_TUID_INFO = 561005589;
    public static final int VENDOR_VEHICLE_KEY_TASK = 557864208;
    public static final int VENDOR_VERSION_REQ = 557859600;
    public static final int VENDOR_VIN_CODE_SET = 561005334;
    public static final int VENDOR_VIN_INFO = 561005590;
    public static final int VENDOR_VOLUME_SET_BY_SPEED = 557867280;
    public static final int WHEEL_TICK = 290521862;
    public static final int WINDOW_LOCK = 320867268;
    public static final int WINDOW_MOVE = 322964417;
    public static final int WINDOW_POS = 322964416;

    public static final String toString(int i) {
        return i == 0 ? "INVALID" : i == 286261504 ? "INFO_VIN" : i == 286261505 ? "INFO_MAKE" : i == 286261506 ? "INFO_MODEL" : i == 289407235 ? "INFO_MODEL_YEAR" : i == 291504388 ? "INFO_FUEL_CAPACITY" : i == 289472773 ? "INFO_FUEL_TYPE" : i == 291504390 ? "INFO_EV_BATTERY_CAPACITY" : i == 289472775 ? "INFO_EV_CONNECTOR_TYPE" : i == 289407240 ? "INFO_FUEL_DOOR_LOCATION" : i == 289407241 ? "INFO_EV_PORT_LOCATION" : i == 356516106 ? "INFO_DRIVER_SEAT" : i == 289472779 ? "INFO_EXTERIOR_DIMENSIONS" : i == 289472780 ? "INFO_MULTI_EV_PORT_LOCATIONS" : i == 291504644 ? "PERF_ODOMETER" : i == 291504647 ? "PERF_VEHICLE_SPEED" : i == 291504648 ? "PERF_VEHICLE_SPEED_DISPLAY" : i == 291504649 ? "PERF_STEERING_ANGLE" : i == 291504656 ? "PERF_REAR_STEERING_ANGLE" : i == 291504897 ? "ENGINE_COOLANT_TEMP" : i == 289407747 ? "ENGINE_OIL_LEVEL" : i == 291504900 ? "ENGINE_OIL_TEMP" : i == 291504901 ? "ENGINE_RPM" : i == 290521862 ? "WHEEL_TICK" : i == 291504903 ? "FUEL_LEVEL" : i == 287310600 ? "FUEL_DOOR_OPEN" : i == 291504905 ? "EV_BATTERY_LEVEL" : i == 287310602 ? "EV_CHARGE_PORT_OPEN" : i == 287310603 ? "EV_CHARGE_PORT_CONNECTED" : i == 291504908 ? "EV_BATTERY_INSTANTANEOUS_CHARGE_RATE" : i == 291504904 ? "RANGE_REMAINING" : i == 392168201 ? "TIRE_PRESSURE" : i == 289408000 ? "GEAR_SELECTION" : i == 289408001 ? "CURRENT_GEAR" : i == 287310850 ? "PARKING_BRAKE_ON" : i == 287310851 ? "PARKING_BRAKE_AUTO_APPLY" : i == 287310853 ? "FUEL_LEVEL_LOW" : i == 287310855 ? "NIGHT_MODE" : i == 289408008 ? "TURN_SIGNAL_STATE" : i == 289408009 ? "IGNITION_STATE" : i == 287310858 ? "ABS_ACTIVE" : i == 287310859 ? "TRACTION_CONTROL_ACTIVE" : i == 356517120 ? "HVAC_FAN_SPEED" : i == 356517121 ? "HVAC_FAN_DIRECTION" : i == 358614274 ? "HVAC_TEMPERATURE_CURRENT" : i == 358614275 ? "HVAC_TEMPERATURE_SET" : i == 320865540 ? "HVAC_DEFROSTER" : i == 354419973 ? "HVAC_AC_ON" : i == 354419974 ? "HVAC_MAX_AC_ON" : i == 354419975 ? "HVAC_MAX_DEFROST_ON" : i == 354419976 ? "HVAC_RECIRC_ON" : i == 354419977 ? "HVAC_DUAL_ON" : i == 354419978 ? "HVAC_AUTO_ON" : i == 356517131 ? "HVAC_SEAT_TEMPERATURE" : i == 339739916 ? "HVAC_SIDE_MIRROR_HEAT" : i == 289408269 ? "HVAC_STEERING_WHEEL_HEAT" : i == 289408270 ? "HVAC_TEMPERATURE_DISPLAY_UNITS" : i == 356517135 ? "HVAC_ACTUAL_FAN_SPEED_RPM" : i == 354419984 ? "HVAC_POWER_ON" : i == 356582673 ? "HVAC_FAN_DIRECTION_AVAILABLE" : i == 354419986 ? "HVAC_AUTO_RECIRC_ON" : i == 356517139 ? "HVAC_SEAT_VENTILATION" : i == 320865556 ? "HVAC_ELECTRIC_DEFROSTER_ON" : i == 289408512 ? "DISTANCE_DISPLAY_UNITS" : i == 289408513 ? "FUEL_VOLUME_DISPLAY_UNITS" : i == 289408514 ? "TIRE_PRESSURE_DISPLAY_UNITS" : i == 289408515 ? "EV_BATTERY_DISPLAY_UNITS" : i == 287311364 ? "FUEL_CONSUMPTION_UNITS_DISTANCE_OVER_VOLUME" : i == 289408517 ? "VEHICLE_SPEED_DISPLAY_UNITS" : i == 291505923 ? "ENV_OUTSIDE_TEMPERATURE" : i == 289475072 ? "AP_POWER_STATE_REQ" : i == 289475073 ? "AP_POWER_STATE_REPORT" : i == 289409538 ? "AP_POWER_BOOTUP_REASON" : i == 289409539 ? "DISPLAY_BRIGHTNESS" : i == 289475088 ? "HW_KEY_INPUT" : i == 289475104 ? "HW_ROTARY_INPUT" : i == 373295872 ? "DOOR_POS" : i == 373295873 ? "DOOR_MOVE" : i == 371198722 ? "DOOR_LOCK" : i == 339741504 ? "MIRROR_Z_POS" : i == 339741505 ? "MIRROR_Z_MOVE" : i == 339741506 ? "MIRROR_Y_POS" : i == 339741507 ? "MIRROR_Y_MOVE" : i == 287312708 ? "MIRROR_LOCK" : i == 287312709 ? "MIRROR_FOLD" : i == 356518784 ? "SEAT_MEMORY_SELECT" : i == 356518785 ? "SEAT_MEMORY_SET" : i == 354421634 ? "SEAT_BELT_BUCKLED" : i == 356518787 ? "SEAT_BELT_HEIGHT_POS" : i == 356518788 ? "SEAT_BELT_HEIGHT_MOVE" : i == 356518789 ? "SEAT_FORE_AFT_POS" : i == 356518790 ? "SEAT_FORE_AFT_MOVE" : i == 356518791 ? "SEAT_BACKREST_ANGLE_1_POS" : i == 356518792 ? "SEAT_BACKREST_ANGLE_1_MOVE" : i == 356518793 ? "SEAT_BACKREST_ANGLE_2_POS" : i == 356518794 ? "SEAT_BACKREST_ANGLE_2_MOVE" : i == 356518795 ? "SEAT_HEIGHT_POS" : i == 356518796 ? "SEAT_HEIGHT_MOVE" : i == 356518797 ? "SEAT_DEPTH_POS" : i == 356518798 ? "SEAT_DEPTH_MOVE" : i == 356518799 ? "SEAT_TILT_POS" : i == 356518800 ? "SEAT_TILT_MOVE" : i == 356518801 ? "SEAT_LUMBAR_FORE_AFT_POS" : i == 356518802 ? "SEAT_LUMBAR_FORE_AFT_MOVE" : i == 356518803 ? "SEAT_LUMBAR_SIDE_SUPPORT_POS" : i == 356518804 ? "SEAT_LUMBAR_SIDE_SUPPORT_MOVE" : i == 289409941 ? "SEAT_HEADREST_HEIGHT_POS" : i == 356518806 ? "SEAT_HEADREST_HEIGHT_MOVE" : i == 356518807 ? "SEAT_HEADREST_ANGLE_POS" : i == 356518808 ? "SEAT_HEADREST_ANGLE_MOVE" : i == 356518809 ? "SEAT_HEADREST_FORE_AFT_POS" : i == 356518810 ? "SEAT_HEADREST_FORE_AFT_MOVE" : i == 356518832 ? "SEAT_OCCUPANCY" : i == 322964416 ? "WINDOW_POS" : i == 322964417 ? "WINDOW_MOVE" : i == 320867268 ? "WINDOW_LOCK" : i == 299895808 ? "VEHICLE_MAP_SERVICE" : i == 299896064 ? "OBD2_LIVE_FRAME" : i == 299896065 ? "OBD2_FREEZE_FRAME" : i == 299896066 ? "OBD2_FREEZE_FRAME_INFO" : i == 299896067 ? "OBD2_FREEZE_FRAME_CLEAR" : i == 289410560 ? "HEADLIGHTS_STATE" : i == 289410561 ? "HIGH_BEAM_LIGHTS_STATE" : i == 289410562 ? "FOG_LIGHTS_STATE" : i == 289410563 ? "HAZARD_LIGHTS_STATE" : i == 289410576 ? "HEADLIGHTS_SWITCH" : i == 289410577 ? "HIGH_BEAM_LIGHTS_SWITCH" : i == 289410578 ? "FOG_LIGHTS_SWITCH" : i == 289410579 ? "HAZARD_LIGHTS_SWITCH" : i == 289410817 ? "CABIN_LIGHTS_STATE" : i == 289410818 ? "CABIN_LIGHTS_SWITCH" : i == 356519683 ? "READING_LIGHTS_STATE" : i == 356519684 ? "READING_LIGHTS_SWITCH" : i == 287313669 ? "SUPPORT_CUSTOMIZE_VENDOR_PERMISSION" : i == 286265094 ? "DISABLED_OPTIONAL_FEATURES" : i == 299896583 ? "INITIAL_USER_INFO" : i == 299896584 ? "SWITCH_USER" : i == 299896585 ? "CREATE_USER" : i == 299896586 ? "REMOVE_USER" : i == 299896587 ? "USER_IDENTIFICATION_ASSOCIATION" : i == 557842944 ? "VENDOR_REQUEST_MCU_HANDSHAKE" : i == 561004560 ? "VENDOR_GET_CAN_CONFIG_INFO" : i == 561005072 ? "VENDOR_CAN_INFO_2MCU" : i == 557859600 ? "VENDOR_VERSION_REQ" : i == 561005329 ? "VENDOR_OFF_LINE_SET" : i == 561005334 ? "VENDOR_VIN_CODE_SET" : i == 554714128 ? "VENDOR_MCU_VERSION" : i == 554714129 ? "VENDOR_EDC_HW_VERSION" : i == 554714130 ? "VENDOR_EDC_PART_NUMBER" : i == 554714131 ? "VENDOR_EDC_SUPPLIER_CODE" : i == 554714132 ? "VENDOR_MCU_PART_NUMBER" : i == 561005589 ? "VENDOR_TUID_INFO" : i == 561005590 ? "VENDOR_VIN_INFO" : i == 561005591 ? "VENDOR_OFF_LINE_STATUS" : i == 561005592 ? "VENDOR_CERTIFICATE_STATUS" : i == 561005593 ? "VENDOR_PRIVATE_CERTIFICATE_STATUS" : i == 561005594 ? "VENDOR_CERTIFICATE_ID" : i == 557860112 ? "MCU_DSP_VOLUME_CTR" : i == 557860113 ? "MCU_DSP_SOUND_EFFECTS_CTR" : i == 557860114 ? "MCU_DSP_VOLUME_MUTE_CTR" : i == 557860115 ? "MCU_DSP_MIC_MUTE_CTR" : i == 557860116 ? "MCU_DSP_SOUND_FIELD_SET" : i == 557860117 ? "MCU_DSP_MEDIA_CHANNEL_SWITCH" : i == 557860118 ? "MCU_DSP_SOUND_SOURCE" : i == 557860119 ? "VENDOR_SELF_DEFINE_SOUND_EFFECTS_CTR" : i == 557860120 ? "MCU_DSP_SOUND_WAVE_MODE" : i == 557860121 ? "MCU_DSP_SUPER_BASS_MODE" : i == 557925658 ? "MCU_DSP_LOUDSPEAKER_GAIN" : i == 561006352 ? "VENDOR_TUID_DISTRIBUTE" : i == 561006353 ? "VENDOR_PUBLIC_KEY_CERTIFICATE_DISTRIBUTE" : i == 561006354 ? "VENDOR_PRIVATE_KEY_CERTIFICATE_DISTRIBUTE" : i == 561006355 ? "VENDOR_DATA_SIGN_REQUEST" : i == 561006356 ? "VENDOR_CERTID_DISTRIBUTE" : i == 557861137 ? "VENDOR_MILEAGE_RESET" : i == 557863185 ? "VENDOR_POWER_MODE_REPORT" : i == 557928722 ? "VENDOR_POWER_MODE_FEEDBACK" : i == 561009168 ? "VENDOR_HEARTBEAT_INFO" : i == 557929232 ? "VENDOR_HEARTBEAT_FEEDBACK" : i == 557864208 ? "VENDOR_VEHICLE_KEY_TASK" : i == 557864464 ? "VENDOR_SUBTOTAL_MILEAGE_RESET" : i == 557864976 ? "VENDOR_MCU_TIME_REQ" : i == 557930513 ? "VENDOR_MCU_TIME" : i == 557930768 ? "VENDOR_MCU_TIME_REQ_STATUS" : i == 557865233 ? "VENDOR_MCU_TIME_STATUS" : i == 554719760 ? "VENDOR_MCU_LOG_UPLOAD" : i == 557866000 ? "VENDOR_PLUGGABLE_SOUND_EQUIPMENT_STATUS" : i == 557866001 ? "VENDOR_PLUGGABLE_SOUND_EQUIPMENT_CHARGE_STATUS" : i == 557866002 ? "VENDOR_PLUGGABLE_SOUND_EQUIPMENT_BATTERY_LEVEL" : i == 557866003 ? "VENDOR_PLUGGABLE_SOUND_EQUIPMENT_FAULT_STATUS" : i == 557866004 ? "VENDOR_ADAS_LEFT_SEA_ALERT_STATUS" : i == 557866005 ? "VENDOR_ADAS_RIGHT_SEA_ALERT_STATUS" : i == 557866006 ? "VENDOR_ADAS_DOWLEFT_ALERT_STATUS" : i == 557866007 ? "VENDOR_ADAS_DOWRIGHT_ALERT_STATUS" : i == 557866008 ? "VENDOR_ADAS_ACC_MODE" : i == 557866009 ? "VENDOR_ADAS_LNG_TAKEOVER_REQ_STATUS" : i == 557866512 ? "VENDOR_SOC_VERSION_REQUEST" : i == 561012241 ? "VENDOR_SOC_VERSION_FEEDBACK" : i == 557866514 ? "VENDOR_SOC_PART_NUMBER_REQUEST" : i == 561012243 ? "VENDOR_SOC_PART_NUMBER_FEEDBACK" : i == 557866516 ? "VENDOR_ACTIVE_NOISE_REDUCTION_STATUS" : i == 557866768 ? "VENDOR_DTC_FAULT_CODE_REQUEST" : i == 557932305 ? "VENDOR_DTC_FAULT_CODE_UP_SET" : i == 557932306 ? "VENDOR_DTC_FAULT_CODE_SEND" : i == 557866771 ? "VENDOR_DTC_FAULT_CODE_FEEDBACK" : i == 557867024 ? "VENDOR_INTERACTIVE_LIGHT_SET" : i == 557867025 ? "VENDOR_INTERACTIVE_LIGHT_FEEDBACK" : i == 557867026 ? "VENDOR_PROJECTION_SET" : i == 557867280 ? "VENDOR_VOLUME_SET_BY_SPEED" : i == 557867529 ? "VENDOR_OFFLINE_CONFIG_INFO_REQUEST" : i == 561013264 ? "VENDOR_OFFLINE_CONFIG_INFO_UP" : i == 561013265 ? "VENDOR_OFFLINE_CONFIG_VERSION" : i == 561013270 ? "VENDOR_FACTORY_TRANSMIT" : i == 561013271 ? "VENDOR_FACTORY_FEEDBACK" : i == 557867544 ? "VENDOR_FACTORY_PULL_HUAYANG_SERVICE" : i == 561013274 ? "VENDOR_MCU_CHECK_RESULT" : i == 557867550 ? "VENDOR_POWER_AMPLIFIER_SWITCH" : i == 561013280 ? "VENDOR_MANAGER_DIAGNO_CONFLICT" : i == 561013282 ? "VENDOR_CHANG_CONFIG_INFO" : i == 561013284 ? "VENDOR_GET_CAN_INFO" : "0x" + Integer.toHexString(i);
    }

    public static final String dumpBitfield(int i) {
        ArrayList arrayList = new ArrayList();
        arrayList.add("INVALID");
        int i2 = 286261504;
        if ((i & 286261504) == 286261504) {
            arrayList.add("INFO_VIN");
        } else {
            i2 = 0;
        }
        if ((i & 286261505) == 286261505) {
            arrayList.add("INFO_MAKE");
            i2 |= 286261505;
        }
        if ((i & 286261506) == 286261506) {
            arrayList.add("INFO_MODEL");
            i2 |= 286261506;
        }
        if ((i & 289407235) == 289407235) {
            arrayList.add("INFO_MODEL_YEAR");
            i2 |= 289407235;
        }
        if ((i & 291504388) == 291504388) {
            arrayList.add("INFO_FUEL_CAPACITY");
            i2 |= 291504388;
        }
        if ((i & 289472773) == 289472773) {
            arrayList.add("INFO_FUEL_TYPE");
            i2 |= 289472773;
        }
        if ((i & 291504390) == 291504390) {
            arrayList.add("INFO_EV_BATTERY_CAPACITY");
            i2 |= 291504390;
        }
        if ((i & 289472775) == 289472775) {
            arrayList.add("INFO_EV_CONNECTOR_TYPE");
            i2 |= 289472775;
        }
        if ((i & 289407240) == 289407240) {
            arrayList.add("INFO_FUEL_DOOR_LOCATION");
            i2 |= 289407240;
        }
        if ((i & 289407241) == 289407241) {
            arrayList.add("INFO_EV_PORT_LOCATION");
            i2 |= 289407241;
        }
        if ((i & 356516106) == 356516106) {
            arrayList.add("INFO_DRIVER_SEAT");
            i2 |= 356516106;
        }
        if ((i & INFO_EXTERIOR_DIMENSIONS) == 289472779) {
            arrayList.add("INFO_EXTERIOR_DIMENSIONS");
            i2 |= INFO_EXTERIOR_DIMENSIONS;
        }
        if ((i & INFO_MULTI_EV_PORT_LOCATIONS) == 289472780) {
            arrayList.add("INFO_MULTI_EV_PORT_LOCATIONS");
            i2 |= INFO_MULTI_EV_PORT_LOCATIONS;
        }
        if ((i & 291504644) == 291504644) {
            arrayList.add("PERF_ODOMETER");
            i2 |= 291504644;
        }
        if ((i & 291504647) == 291504647) {
            arrayList.add("PERF_VEHICLE_SPEED");
            i2 |= 291504647;
        }
        if ((i & PERF_VEHICLE_SPEED_DISPLAY) == 291504648) {
            arrayList.add("PERF_VEHICLE_SPEED_DISPLAY");
            i2 |= PERF_VEHICLE_SPEED_DISPLAY;
        }
        if ((i & PERF_STEERING_ANGLE) == 291504649) {
            arrayList.add("PERF_STEERING_ANGLE");
            i2 |= PERF_STEERING_ANGLE;
        }
        if ((i & PERF_REAR_STEERING_ANGLE) == 291504656) {
            arrayList.add("PERF_REAR_STEERING_ANGLE");
            i2 |= PERF_REAR_STEERING_ANGLE;
        }
        if ((i & 291504897) == 291504897) {
            arrayList.add("ENGINE_COOLANT_TEMP");
            i2 |= 291504897;
        }
        if ((i & 289407747) == 289407747) {
            arrayList.add("ENGINE_OIL_LEVEL");
            i2 |= 289407747;
        }
        if ((i & 291504900) == 291504900) {
            arrayList.add("ENGINE_OIL_TEMP");
            i2 |= 291504900;
        }
        if ((i & 291504901) == 291504901) {
            arrayList.add("ENGINE_RPM");
            i2 |= 291504901;
        }
        if ((290521862 & i) == 290521862) {
            arrayList.add("WHEEL_TICK");
            i2 |= 290521862;
        }
        if ((291504903 & i) == 291504903) {
            arrayList.add("FUEL_LEVEL");
            i2 |= 291504903;
        }
        if ((287310600 & i) == 287310600) {
            arrayList.add("FUEL_DOOR_OPEN");
            i2 |= FUEL_DOOR_OPEN;
        }
        if ((291504905 & i) == 291504905) {
            arrayList.add("EV_BATTERY_LEVEL");
            i2 |= 291504905;
        }
        if ((287310602 & i) == 287310602) {
            arrayList.add("EV_CHARGE_PORT_OPEN");
            i2 |= 287310602;
        }
        if ((287310603 & i) == 287310603) {
            arrayList.add("EV_CHARGE_PORT_CONNECTED");
            i2 |= 287310603;
        }
        if ((291504908 & i) == 291504908) {
            arrayList.add("EV_BATTERY_INSTANTANEOUS_CHARGE_RATE");
            i2 |= 291504908;
        }
        if ((291504904 & i) == 291504904) {
            arrayList.add("RANGE_REMAINING");
            i2 |= 291504904;
        }
        if ((392168201 & i) == 392168201) {
            arrayList.add("TIRE_PRESSURE");
            i2 |= TIRE_PRESSURE;
        }
        if ((289408000 & i) == 289408000) {
            arrayList.add("GEAR_SELECTION");
            i2 |= 289408000;
        }
        if ((289408001 & i) == 289408001) {
            arrayList.add("CURRENT_GEAR");
            i2 |= 289408001;
        }
        if ((287310850 & i) == 287310850) {
            arrayList.add("PARKING_BRAKE_ON");
            i2 |= 287310850;
        }
        if ((287310851 & i) == 287310851) {
            arrayList.add("PARKING_BRAKE_AUTO_APPLY");
            i2 |= 287310851;
        }
        if ((287310853 & i) == 287310853) {
            arrayList.add("FUEL_LEVEL_LOW");
            i2 |= 287310853;
        }
        if ((287310855 & i) == 287310855) {
            arrayList.add("NIGHT_MODE");
            i2 |= 287310855;
        }
        if ((289408008 & i) == 289408008) {
            arrayList.add("TURN_SIGNAL_STATE");
            i2 |= 289408008;
        }
        if ((289408009 & i) == 289408009) {
            arrayList.add("IGNITION_STATE");
            i2 |= 289408009;
        }
        if ((287310858 & i) == 287310858) {
            arrayList.add("ABS_ACTIVE");
            i2 |= 287310858;
        }
        if ((287310859 & i) == 287310859) {
            arrayList.add("TRACTION_CONTROL_ACTIVE");
            i2 |= 287310859;
        }
        if ((356517120 & i) == 356517120) {
            arrayList.add("HVAC_FAN_SPEED");
            i2 |= 356517120;
        }
        if ((356517121 & i) == 356517121) {
            arrayList.add("HVAC_FAN_DIRECTION");
            i2 |= 356517121;
        }
        if ((358614274 & i) == 358614274) {
            arrayList.add("HVAC_TEMPERATURE_CURRENT");
            i2 |= 358614274;
        }
        if ((358614275 & i) == 358614275) {
            arrayList.add("HVAC_TEMPERATURE_SET");
            i2 |= 358614275;
        }
        if ((320865540 & i) == 320865540) {
            arrayList.add("HVAC_DEFROSTER");
            i2 |= HVAC_DEFROSTER;
        }
        if ((354419973 & i) == 354419973) {
            arrayList.add("HVAC_AC_ON");
            i2 |= HVAC_AC_ON;
        }
        if ((354419974 & i) == 354419974) {
            arrayList.add("HVAC_MAX_AC_ON");
            i2 |= HVAC_MAX_AC_ON;
        }
        if ((354419975 & i) == 354419975) {
            arrayList.add("HVAC_MAX_DEFROST_ON");
            i2 |= 354419975;
        }
        if ((354419976 & i) == 354419976) {
            arrayList.add("HVAC_RECIRC_ON");
            i2 |= HVAC_RECIRC_ON;
        }
        if ((354419977 & i) == 354419977) {
            arrayList.add("HVAC_DUAL_ON");
            i2 |= HVAC_DUAL_ON;
        }
        if ((354419978 & i) == 354419978) {
            arrayList.add("HVAC_AUTO_ON");
            i2 |= HVAC_AUTO_ON;
        }
        if ((356517131 & i) == 356517131) {
            arrayList.add("HVAC_SEAT_TEMPERATURE");
            i2 |= 356517131;
        }
        if ((339739916 & i) == 339739916) {
            arrayList.add("HVAC_SIDE_MIRROR_HEAT");
            i2 |= 339739916;
        }
        if ((289408269 & i) == 289408269) {
            arrayList.add("HVAC_STEERING_WHEEL_HEAT");
            i2 |= 289408269;
        }
        if ((289408270 & i) == 289408270) {
            arrayList.add("HVAC_TEMPERATURE_DISPLAY_UNITS");
            i2 |= 289408270;
        }
        if ((356517135 & i) == 356517135) {
            arrayList.add("HVAC_ACTUAL_FAN_SPEED_RPM");
            i2 |= 356517135;
        }
        if ((354419984 & i) == 354419984) {
            arrayList.add("HVAC_POWER_ON");
            i2 |= HVAC_POWER_ON;
        }
        if ((356582673 & i) == 356582673) {
            arrayList.add("HVAC_FAN_DIRECTION_AVAILABLE");
            i2 |= 356582673;
        }
        if ((354419986 & i) == 354419986) {
            arrayList.add("HVAC_AUTO_RECIRC_ON");
            i2 |= 354419986;
        }
        if ((356517139 & i) == 356517139) {
            arrayList.add("HVAC_SEAT_VENTILATION");
            i2 |= 356517139;
        }
        if ((320865556 & i) == 320865556) {
            arrayList.add("HVAC_ELECTRIC_DEFROSTER_ON");
            i2 |= HVAC_ELECTRIC_DEFROSTER_ON;
        }
        if ((289408512 & i) == 289408512) {
            arrayList.add("DISTANCE_DISPLAY_UNITS");
            i2 |= DISTANCE_DISPLAY_UNITS;
        }
        if ((289408513 & i) == 289408513) {
            arrayList.add("FUEL_VOLUME_DISPLAY_UNITS");
            i2 |= FUEL_VOLUME_DISPLAY_UNITS;
        }
        if ((289408514 & i) == 289408514) {
            arrayList.add("TIRE_PRESSURE_DISPLAY_UNITS");
            i2 |= TIRE_PRESSURE_DISPLAY_UNITS;
        }
        if ((289408515 & i) == 289408515) {
            arrayList.add("EV_BATTERY_DISPLAY_UNITS");
            i2 |= EV_BATTERY_DISPLAY_UNITS;
        }
        if ((287311364 & i) == 287311364) {
            arrayList.add("FUEL_CONSUMPTION_UNITS_DISTANCE_OVER_VOLUME");
            i2 |= FUEL_CONSUMPTION_UNITS_DISTANCE_OVER_VOLUME;
        }
        if ((289408517 & i) == 289408517) {
            arrayList.add("VEHICLE_SPEED_DISPLAY_UNITS");
            i2 |= VEHICLE_SPEED_DISPLAY_UNITS;
        }
        if ((291505923 & i) == 291505923) {
            arrayList.add("ENV_OUTSIDE_TEMPERATURE");
            i2 |= 291505923;
        }
        if ((289475072 & i) == 289475072) {
            arrayList.add("AP_POWER_STATE_REQ");
            i2 |= 289475072;
        }
        if ((289475073 & i) == 289475073) {
            arrayList.add("AP_POWER_STATE_REPORT");
            i2 |= 289475073;
        }
        if ((289409538 & i) == 289409538) {
            arrayList.add("AP_POWER_BOOTUP_REASON");
            i2 |= 289409538;
        }
        if ((289409539 & i) == 289409539) {
            arrayList.add("DISPLAY_BRIGHTNESS");
            i2 |= 289409539;
        }
        if ((289475088 & i) == 289475088) {
            arrayList.add("HW_KEY_INPUT");
            i2 |= 289475088;
        }
        if ((289475104 & i) == 289475104) {
            arrayList.add("HW_ROTARY_INPUT");
            i2 |= HW_ROTARY_INPUT;
        }
        if ((373295872 & i) == 373295872) {
            arrayList.add("DOOR_POS");
            i2 |= 373295872;
        }
        if ((373295873 & i) == 373295873) {
            arrayList.add("DOOR_MOVE");
            i2 |= 373295873;
        }
        if ((371198722 & i) == 371198722) {
            arrayList.add("DOOR_LOCK");
            i2 |= 371198722;
        }
        if ((339741504 & i) == 339741504) {
            arrayList.add("MIRROR_Z_POS");
            i2 |= 339741504;
        }
        if ((339741505 & i) == 339741505) {
            arrayList.add("MIRROR_Z_MOVE");
            i2 |= 339741505;
        }
        if ((339741506 & i) == 339741506) {
            arrayList.add("MIRROR_Y_POS");
            i2 |= 339741506;
        }
        if ((339741507 & i) == 339741507) {
            arrayList.add("MIRROR_Y_MOVE");
            i2 |= 339741507;
        }
        if ((287312708 & i) == 287312708) {
            arrayList.add("MIRROR_LOCK");
            i2 |= 287312708;
        }
        if ((287312709 & i) == 287312709) {
            arrayList.add("MIRROR_FOLD");
            i2 |= MIRROR_FOLD;
        }
        if ((356518784 & i) == 356518784) {
            arrayList.add("SEAT_MEMORY_SELECT");
            i2 |= 356518784;
        }
        if ((356518785 & i) == 356518785) {
            arrayList.add("SEAT_MEMORY_SET");
            i2 |= 356518785;
        }
        if ((354421634 & i) == 354421634) {
            arrayList.add("SEAT_BELT_BUCKLED");
            i2 |= 354421634;
        }
        if ((356518787 & i) == 356518787) {
            arrayList.add("SEAT_BELT_HEIGHT_POS");
            i2 |= 356518787;
        }
        if ((356518788 & i) == 356518788) {
            arrayList.add("SEAT_BELT_HEIGHT_MOVE");
            i2 |= 356518788;
        }
        if ((356518789 & i) == 356518789) {
            arrayList.add("SEAT_FORE_AFT_POS");
            i2 |= 356518789;
        }
        if ((356518790 & i) == 356518790) {
            arrayList.add("SEAT_FORE_AFT_MOVE");
            i2 |= 356518790;
        }
        if ((356518791 & i) == 356518791) {
            arrayList.add("SEAT_BACKREST_ANGLE_1_POS");
            i2 |= 356518791;
        }
        if ((356518792 & i) == 356518792) {
            arrayList.add("SEAT_BACKREST_ANGLE_1_MOVE");
            i2 |= 356518792;
        }
        if ((356518793 & i) == 356518793) {
            arrayList.add("SEAT_BACKREST_ANGLE_2_POS");
            i2 |= 356518793;
        }
        if ((356518794 & i) == 356518794) {
            arrayList.add("SEAT_BACKREST_ANGLE_2_MOVE");
            i2 |= 356518794;
        }
        if ((356518795 & i) == 356518795) {
            arrayList.add("SEAT_HEIGHT_POS");
            i2 |= 356518795;
        }
        if ((356518796 & i) == 356518796) {
            arrayList.add("SEAT_HEIGHT_MOVE");
            i2 |= 356518796;
        }
        if ((356518797 & i) == 356518797) {
            arrayList.add("SEAT_DEPTH_POS");
            i2 |= 356518797;
        }
        if ((356518798 & i) == 356518798) {
            arrayList.add("SEAT_DEPTH_MOVE");
            i2 |= 356518798;
        }
        if ((356518799 & i) == 356518799) {
            arrayList.add("SEAT_TILT_POS");
            i2 |= 356518799;
        }
        if ((356518800 & i) == 356518800) {
            arrayList.add("SEAT_TILT_MOVE");
            i2 |= 356518800;
        }
        if ((356518801 & i) == 356518801) {
            arrayList.add("SEAT_LUMBAR_FORE_AFT_POS");
            i2 |= 356518801;
        }
        if ((356518802 & i) == 356518802) {
            arrayList.add("SEAT_LUMBAR_FORE_AFT_MOVE");
            i2 |= 356518802;
        }
        if ((356518803 & i) == 356518803) {
            arrayList.add("SEAT_LUMBAR_SIDE_SUPPORT_POS");
            i2 |= 356518803;
        }
        if ((356518804 & i) == 356518804) {
            arrayList.add("SEAT_LUMBAR_SIDE_SUPPORT_MOVE");
            i2 |= 356518804;
        }
        if ((289409941 & i) == 289409941) {
            arrayList.add("SEAT_HEADREST_HEIGHT_POS");
            i2 |= 289409941;
        }
        if ((356518806 & i) == 356518806) {
            arrayList.add("SEAT_HEADREST_HEIGHT_MOVE");
            i2 |= 356518806;
        }
        if ((356518807 & i) == 356518807) {
            arrayList.add("SEAT_HEADREST_ANGLE_POS");
            i2 |= 356518807;
        }
        if ((356518808 & i) == 356518808) {
            arrayList.add("SEAT_HEADREST_ANGLE_MOVE");
            i2 |= 356518808;
        }
        if ((356518809 & i) == 356518809) {
            arrayList.add("SEAT_HEADREST_FORE_AFT_POS");
            i2 |= 356518809;
        }
        if ((356518810 & i) == 356518810) {
            arrayList.add("SEAT_HEADREST_FORE_AFT_MOVE");
            i2 |= 356518810;
        }
        if ((356518832 & i) == 356518832) {
            arrayList.add("SEAT_OCCUPANCY");
            i2 |= SEAT_OCCUPANCY;
        }
        if ((322964416 & i) == 322964416) {
            arrayList.add("WINDOW_POS");
            i2 |= 322964416;
        }
        if ((322964417 & i) == 322964417) {
            arrayList.add("WINDOW_MOVE");
            i2 |= 322964417;
        }
        if ((320867268 & i) == 320867268) {
            arrayList.add("WINDOW_LOCK");
            i2 |= 320867268;
        }
        if ((299895808 & i) == 299895808) {
            arrayList.add("VEHICLE_MAP_SERVICE");
            i2 |= 299895808;
        }
        if ((299896064 & i) == 299896064) {
            arrayList.add("OBD2_LIVE_FRAME");
            i2 |= 299896064;
        }
        if ((299896065 & i) == 299896065) {
            arrayList.add("OBD2_FREEZE_FRAME");
            i2 |= 299896065;
        }
        if ((299896066 & i) == 299896066) {
            arrayList.add("OBD2_FREEZE_FRAME_INFO");
            i2 |= 299896066;
        }
        if ((299896067 & i) == 299896067) {
            arrayList.add("OBD2_FREEZE_FRAME_CLEAR");
            i2 |= 299896067;
        }
        if ((289410560 & i) == 289410560) {
            arrayList.add("HEADLIGHTS_STATE");
            i2 |= 289410560;
        }
        if ((289410561 & i) == 289410561) {
            arrayList.add("HIGH_BEAM_LIGHTS_STATE");
            i2 |= 289410561;
        }
        if ((289410562 & i) == 289410562) {
            arrayList.add("FOG_LIGHTS_STATE");
            i2 |= FOG_LIGHTS_STATE;
        }
        if ((289410563 & i) == 289410563) {
            arrayList.add("HAZARD_LIGHTS_STATE");
            i2 |= 289410563;
        }
        if ((289410576 & i) == 289410576) {
            arrayList.add("HEADLIGHTS_SWITCH");
            i2 |= 289410576;
        }
        if ((289410577 & i) == 289410577) {
            arrayList.add("HIGH_BEAM_LIGHTS_SWITCH");
            i2 |= 289410577;
        }
        if ((289410578 & i) == 289410578) {
            arrayList.add("FOG_LIGHTS_SWITCH");
            i2 |= 289410578;
        }
        if ((289410579 & i) == 289410579) {
            arrayList.add("HAZARD_LIGHTS_SWITCH");
            i2 |= 289410579;
        }
        if ((289410817 & i) == 289410817) {
            arrayList.add("CABIN_LIGHTS_STATE");
            i2 |= CABIN_LIGHTS_STATE;
        }
        if ((289410818 & i) == 289410818) {
            arrayList.add("CABIN_LIGHTS_SWITCH");
            i2 |= CABIN_LIGHTS_SWITCH;
        }
        if ((356519683 & i) == 356519683) {
            arrayList.add("READING_LIGHTS_STATE");
            i2 |= READING_LIGHTS_STATE;
        }
        if ((356519684 & i) == 356519684) {
            arrayList.add("READING_LIGHTS_SWITCH");
            i2 |= READING_LIGHTS_SWITCH;
        }
        if ((287313669 & i) == 287313669) {
            arrayList.add("SUPPORT_CUSTOMIZE_VENDOR_PERMISSION");
            i2 |= SUPPORT_CUSTOMIZE_VENDOR_PERMISSION;
        }
        if ((286265094 & i) == 286265094) {
            arrayList.add("DISABLED_OPTIONAL_FEATURES");
            i2 |= DISABLED_OPTIONAL_FEATURES;
        }
        if ((299896583 & i) == 299896583) {
            arrayList.add("INITIAL_USER_INFO");
            i2 |= INITIAL_USER_INFO;
        }
        if ((299896584 & i) == 299896584) {
            arrayList.add("SWITCH_USER");
            i2 |= SWITCH_USER;
        }
        if ((299896585 & i) == 299896585) {
            arrayList.add("CREATE_USER");
            i2 |= CREATE_USER;
        }
        if ((299896586 & i) == 299896586) {
            arrayList.add("REMOVE_USER");
            i2 |= REMOVE_USER;
        }
        if ((299896587 & i) == 299896587) {
            arrayList.add("USER_IDENTIFICATION_ASSOCIATION");
            i2 |= USER_IDENTIFICATION_ASSOCIATION;
        }
        if ((557842944 & i) == 557842944) {
            arrayList.add("VENDOR_REQUEST_MCU_HANDSHAKE");
            i2 |= VENDOR_REQUEST_MCU_HANDSHAKE;
        }
        if ((561004560 & i) == 561004560) {
            arrayList.add("VENDOR_GET_CAN_CONFIG_INFO");
            i2 |= VENDOR_GET_CAN_CONFIG_INFO;
        }
        if ((561005072 & i) == 561005072) {
            arrayList.add("VENDOR_CAN_INFO_2MCU");
            i2 |= VENDOR_CAN_INFO_2MCU;
        }
        if ((557859600 & i) == 557859600) {
            arrayList.add("VENDOR_VERSION_REQ");
            i2 |= VENDOR_VERSION_REQ;
        }
        if ((561005329 & i) == 561005329) {
            arrayList.add("VENDOR_OFF_LINE_SET");
            i2 |= VENDOR_OFF_LINE_SET;
        }
        if ((561005334 & i) == 561005334) {
            arrayList.add("VENDOR_VIN_CODE_SET");
            i2 |= VENDOR_VIN_CODE_SET;
        }
        if ((554714128 & i) == 554714128) {
            arrayList.add("VENDOR_MCU_VERSION");
            i2 |= VENDOR_MCU_VERSION;
        }
        if ((554714129 & i) == 554714129) {
            arrayList.add("VENDOR_EDC_HW_VERSION");
            i2 |= VENDOR_EDC_HW_VERSION;
        }
        if ((554714130 & i) == 554714130) {
            arrayList.add("VENDOR_EDC_PART_NUMBER");
            i2 |= VENDOR_EDC_PART_NUMBER;
        }
        if ((554714131 & i) == 554714131) {
            arrayList.add("VENDOR_EDC_SUPPLIER_CODE");
            i2 |= VENDOR_EDC_SUPPLIER_CODE;
        }
        if ((554714132 & i) == 554714132) {
            arrayList.add("VENDOR_MCU_PART_NUMBER");
            i2 |= VENDOR_MCU_PART_NUMBER;
        }
        if ((561005589 & i) == 561005589) {
            arrayList.add("VENDOR_TUID_INFO");
            i2 |= VENDOR_TUID_INFO;
        }
        if ((561005590 & i) == 561005590) {
            arrayList.add("VENDOR_VIN_INFO");
            i2 |= VENDOR_VIN_INFO;
        }
        if ((561005591 & i) == 561005591) {
            arrayList.add("VENDOR_OFF_LINE_STATUS");
            i2 |= VENDOR_OFF_LINE_STATUS;
        }
        if ((561005592 & i) == 561005592) {
            arrayList.add("VENDOR_CERTIFICATE_STATUS");
            i2 |= VENDOR_CERTIFICATE_STATUS;
        }
        if ((561005593 & i) == 561005593) {
            arrayList.add("VENDOR_PRIVATE_CERTIFICATE_STATUS");
            i2 |= VENDOR_PRIVATE_CERTIFICATE_STATUS;
        }
        if ((561005594 & i) == 561005594) {
            arrayList.add("VENDOR_CERTIFICATE_ID");
            i2 |= VENDOR_CERTIFICATE_ID;
        }
        if ((557860112 & i) == 557860112) {
            arrayList.add("MCU_DSP_VOLUME_CTR");
            i2 |= MCU_DSP_VOLUME_CTR;
        }
        if ((557860113 & i) == 557860113) {
            arrayList.add("MCU_DSP_SOUND_EFFECTS_CTR");
            i2 |= MCU_DSP_SOUND_EFFECTS_CTR;
        }
        if ((557860114 & i) == 557860114) {
            arrayList.add("MCU_DSP_VOLUME_MUTE_CTR");
            i2 |= MCU_DSP_VOLUME_MUTE_CTR;
        }
        if ((557860115 & i) == 557860115) {
            arrayList.add("MCU_DSP_MIC_MUTE_CTR");
            i2 |= MCU_DSP_MIC_MUTE_CTR;
        }
        if ((557860116 & i) == 557860116) {
            arrayList.add("MCU_DSP_SOUND_FIELD_SET");
            i2 |= MCU_DSP_SOUND_FIELD_SET;
        }
        if ((557860117 & i) == 557860117) {
            arrayList.add("MCU_DSP_MEDIA_CHANNEL_SWITCH");
            i2 |= MCU_DSP_MEDIA_CHANNEL_SWITCH;
        }
        if ((557860118 & i) == 557860118) {
            arrayList.add("MCU_DSP_SOUND_SOURCE");
            i2 |= MCU_DSP_SOUND_SOURCE;
        }
        if ((557860119 & i) == 557860119) {
            arrayList.add("VENDOR_SELF_DEFINE_SOUND_EFFECTS_CTR");
            i2 |= VENDOR_SELF_DEFINE_SOUND_EFFECTS_CTR;
        }
        if ((557860120 & i) == 557860120) {
            arrayList.add("MCU_DSP_SOUND_WAVE_MODE");
            i2 |= MCU_DSP_SOUND_WAVE_MODE;
        }
        if ((557860121 & i) == 557860121) {
            arrayList.add("MCU_DSP_SUPER_BASS_MODE");
            i2 |= MCU_DSP_SUPER_BASS_MODE;
        }
        if ((557925658 & i) == 557925658) {
            arrayList.add("MCU_DSP_LOUDSPEAKER_GAIN");
            i2 |= MCU_DSP_LOUDSPEAKER_GAIN;
        }
        if ((561006352 & i) == 561006352) {
            arrayList.add("VENDOR_TUID_DISTRIBUTE");
            i2 |= VENDOR_TUID_DISTRIBUTE;
        }
        if ((561006353 & i) == 561006353) {
            arrayList.add("VENDOR_PUBLIC_KEY_CERTIFICATE_DISTRIBUTE");
            i2 |= VENDOR_PUBLIC_KEY_CERTIFICATE_DISTRIBUTE;
        }
        if ((561006354 & i) == 561006354) {
            arrayList.add("VENDOR_PRIVATE_KEY_CERTIFICATE_DISTRIBUTE");
            i2 |= VENDOR_PRIVATE_KEY_CERTIFICATE_DISTRIBUTE;
        }
        if ((561006355 & i) == 561006355) {
            arrayList.add("VENDOR_DATA_SIGN_REQUEST");
            i2 |= VENDOR_DATA_SIGN_REQUEST;
        }
        if ((561006356 & i) == 561006356) {
            arrayList.add("VENDOR_CERTID_DISTRIBUTE");
            i2 |= VENDOR_CERTID_DISTRIBUTE;
        }
        if ((557861137 & i) == 557861137) {
            arrayList.add("VENDOR_MILEAGE_RESET");
            i2 |= VENDOR_MILEAGE_RESET;
        }
        if ((557863185 & i) == 557863185) {
            arrayList.add("VENDOR_POWER_MODE_REPORT");
            i2 |= VENDOR_POWER_MODE_REPORT;
        }
        if ((557928722 & i) == 557928722) {
            arrayList.add("VENDOR_POWER_MODE_FEEDBACK");
            i2 |= VENDOR_POWER_MODE_FEEDBACK;
        }
        if ((561009168 & i) == 561009168) {
            arrayList.add("VENDOR_HEARTBEAT_INFO");
            i2 |= VENDOR_HEARTBEAT_INFO;
        }
        if ((557929232 & i) == 557929232) {
            arrayList.add("VENDOR_HEARTBEAT_FEEDBACK");
            i2 |= VENDOR_HEARTBEAT_FEEDBACK;
        }
        if ((557864208 & i) == 557864208) {
            arrayList.add("VENDOR_VEHICLE_KEY_TASK");
            i2 |= VENDOR_VEHICLE_KEY_TASK;
        }
        if ((557864464 & i) == 557864464) {
            arrayList.add("VENDOR_SUBTOTAL_MILEAGE_RESET");
            i2 |= VENDOR_SUBTOTAL_MILEAGE_RESET;
        }
        if ((557864976 & i) == 557864976) {
            arrayList.add("VENDOR_MCU_TIME_REQ");
            i2 |= VENDOR_MCU_TIME_REQ;
        }
        if ((557930513 & i) == 557930513) {
            arrayList.add("VENDOR_MCU_TIME");
            i2 |= VENDOR_MCU_TIME;
        }
        if ((557930768 & i) == 557930768) {
            arrayList.add("VENDOR_MCU_TIME_REQ_STATUS");
            i2 |= VENDOR_MCU_TIME_REQ_STATUS;
        }
        if ((557865233 & i) == 557865233) {
            arrayList.add("VENDOR_MCU_TIME_STATUS");
            i2 |= VENDOR_MCU_TIME_STATUS;
        }
        if ((554719760 & i) == 554719760) {
            arrayList.add("VENDOR_MCU_LOG_UPLOAD");
            i2 |= VENDOR_MCU_LOG_UPLOAD;
        }
        if ((557866000 & i) == 557866000) {
            arrayList.add("VENDOR_PLUGGABLE_SOUND_EQUIPMENT_STATUS");
            i2 |= VENDOR_PLUGGABLE_SOUND_EQUIPMENT_STATUS;
        }
        if ((557866001 & i) == 557866001) {
            arrayList.add("VENDOR_PLUGGABLE_SOUND_EQUIPMENT_CHARGE_STATUS");
            i2 |= VENDOR_PLUGGABLE_SOUND_EQUIPMENT_CHARGE_STATUS;
        }
        if ((557866002 & i) == 557866002) {
            arrayList.add("VENDOR_PLUGGABLE_SOUND_EQUIPMENT_BATTERY_LEVEL");
            i2 |= VENDOR_PLUGGABLE_SOUND_EQUIPMENT_BATTERY_LEVEL;
        }
        if ((557866003 & i) == 557866003) {
            arrayList.add("VENDOR_PLUGGABLE_SOUND_EQUIPMENT_FAULT_STATUS");
            i2 |= VENDOR_PLUGGABLE_SOUND_EQUIPMENT_FAULT_STATUS;
        }
        if ((557866004 & i) == 557866004) {
            arrayList.add("VENDOR_ADAS_LEFT_SEA_ALERT_STATUS");
            i2 |= VENDOR_ADAS_LEFT_SEA_ALERT_STATUS;
        }
        if ((557866005 & i) == 557866005) {
            arrayList.add("VENDOR_ADAS_RIGHT_SEA_ALERT_STATUS");
            i2 |= VENDOR_ADAS_RIGHT_SEA_ALERT_STATUS;
        }
        if ((557866006 & i) == 557866006) {
            arrayList.add("VENDOR_ADAS_DOWLEFT_ALERT_STATUS");
            i2 |= VENDOR_ADAS_DOWLEFT_ALERT_STATUS;
        }
        if ((557866007 & i) == 557866007) {
            arrayList.add("VENDOR_ADAS_DOWRIGHT_ALERT_STATUS");
            i2 |= VENDOR_ADAS_DOWRIGHT_ALERT_STATUS;
        }
        if ((557866008 & i) == 557866008) {
            arrayList.add("VENDOR_ADAS_ACC_MODE");
            i2 |= VENDOR_ADAS_ACC_MODE;
        }
        if ((557866009 & i) == 557866009) {
            arrayList.add("VENDOR_ADAS_LNG_TAKEOVER_REQ_STATUS");
            i2 |= VENDOR_ADAS_LNG_TAKEOVER_REQ_STATUS;
        }
        if ((557866512 & i) == 557866512) {
            arrayList.add("VENDOR_SOC_VERSION_REQUEST");
            i2 |= VENDOR_SOC_VERSION_REQUEST;
        }
        if ((561012241 & i) == 561012241) {
            arrayList.add("VENDOR_SOC_VERSION_FEEDBACK");
            i2 |= VENDOR_SOC_VERSION_FEEDBACK;
        }
        if ((557866514 & i) == 557866514) {
            arrayList.add("VENDOR_SOC_PART_NUMBER_REQUEST");
            i2 |= VENDOR_SOC_PART_NUMBER_REQUEST;
        }
        if ((561012243 & i) == 561012243) {
            arrayList.add("VENDOR_SOC_PART_NUMBER_FEEDBACK");
            i2 |= VENDOR_SOC_PART_NUMBER_FEEDBACK;
        }
        if ((557866516 & i) == 557866516) {
            arrayList.add("VENDOR_ACTIVE_NOISE_REDUCTION_STATUS");
            i2 |= VENDOR_ACTIVE_NOISE_REDUCTION_STATUS;
        }
        if ((557866768 & i) == 557866768) {
            arrayList.add("VENDOR_DTC_FAULT_CODE_REQUEST");
            i2 |= VENDOR_DTC_FAULT_CODE_REQUEST;
        }
        if ((557932305 & i) == 557932305) {
            arrayList.add("VENDOR_DTC_FAULT_CODE_UP_SET");
            i2 |= VENDOR_DTC_FAULT_CODE_UP_SET;
        }
        if ((557932306 & i) == 557932306) {
            arrayList.add("VENDOR_DTC_FAULT_CODE_SEND");
            i2 |= VENDOR_DTC_FAULT_CODE_SEND;
        }
        if ((557866771 & i) == 557866771) {
            arrayList.add("VENDOR_DTC_FAULT_CODE_FEEDBACK");
            i2 |= VENDOR_DTC_FAULT_CODE_FEEDBACK;
        }
        if ((557867024 & i) == 557867024) {
            arrayList.add("VENDOR_INTERACTIVE_LIGHT_SET");
            i2 |= VENDOR_INTERACTIVE_LIGHT_SET;
        }
        if ((557867025 & i) == 557867025) {
            arrayList.add("VENDOR_INTERACTIVE_LIGHT_FEEDBACK");
            i2 |= VENDOR_INTERACTIVE_LIGHT_FEEDBACK;
        }
        if ((557867026 & i) == 557867026) {
            arrayList.add("VENDOR_PROJECTION_SET");
            i2 |= VENDOR_PROJECTION_SET;
        }
        if ((557867280 & i) == 557867280) {
            arrayList.add("VENDOR_VOLUME_SET_BY_SPEED");
            i2 |= VENDOR_VOLUME_SET_BY_SPEED;
        }
        if ((557867529 & i) == 557867529) {
            arrayList.add("VENDOR_OFFLINE_CONFIG_INFO_REQUEST");
            i2 |= VENDOR_OFFLINE_CONFIG_INFO_REQUEST;
        }
        if ((561013264 & i) == 561013264) {
            arrayList.add("VENDOR_OFFLINE_CONFIG_INFO_UP");
            i2 |= VENDOR_OFFLINE_CONFIG_INFO_UP;
        }
        if ((561013265 & i) == 561013265) {
            arrayList.add("VENDOR_OFFLINE_CONFIG_VERSION");
            i2 |= VENDOR_OFFLINE_CONFIG_VERSION;
        }
        if ((561013270 & i) == 561013270) {
            arrayList.add("VENDOR_FACTORY_TRANSMIT");
            i2 |= VENDOR_FACTORY_TRANSMIT;
        }
        if ((561013271 & i) == 561013271) {
            arrayList.add("VENDOR_FACTORY_FEEDBACK");
            i2 |= VENDOR_FACTORY_FEEDBACK;
        }
        if ((557867544 & i) == 557867544) {
            arrayList.add("VENDOR_FACTORY_PULL_HUAYANG_SERVICE");
            i2 |= VENDOR_FACTORY_PULL_HUAYANG_SERVICE;
        }
        if ((561013274 & i) == 561013274) {
            arrayList.add("VENDOR_MCU_CHECK_RESULT");
            i2 |= VENDOR_MCU_CHECK_RESULT;
        }
        if ((557867550 & i) == 557867550) {
            arrayList.add("VENDOR_POWER_AMPLIFIER_SWITCH");
            i2 |= VENDOR_POWER_AMPLIFIER_SWITCH;
        }
        if ((561013280 & i) == 561013280) {
            arrayList.add("VENDOR_MANAGER_DIAGNO_CONFLICT");
            i2 |= VENDOR_MANAGER_DIAGNO_CONFLICT;
        }
        if ((561013282 & i) == 561013282) {
            arrayList.add("VENDOR_CHANG_CONFIG_INFO");
            i2 |= VENDOR_CHANG_CONFIG_INFO;
        }
        if ((561013284 & i) == 561013284) {
            arrayList.add("VENDOR_GET_CAN_INFO");
            i2 |= VENDOR_GET_CAN_INFO;
        }
        if (i != i2) {
            arrayList.add("0x" + Integer.toHexString(i & (~i2)));
        }
        return String.join(" | ", arrayList);
    }
}
