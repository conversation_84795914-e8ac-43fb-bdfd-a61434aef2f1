package androidx.appcompat.resources;

/* loaded from: classes.dex */
public final class R {

    public static final class drawable {
        public static final int abc_vector_test = 0x7f080056;

        private drawable() {
        }
    }

    public static final class styleable {
        public static final int AnimatedStateListDrawableCompat_android_constantSize = 0x00000003;
        public static final int AnimatedStateListDrawableCompat_android_dither = 0x00000000;
        public static final int AnimatedStateListDrawableCompat_android_enterFadeDuration = 0x00000004;
        public static final int AnimatedStateListDrawableCompat_android_exitFadeDuration = 0x00000005;
        public static final int AnimatedStateListDrawableCompat_android_variablePadding = 0x00000002;
        public static final int AnimatedStateListDrawableCompat_android_visible = 0x00000001;
        public static final int AnimatedStateListDrawableItem_android_drawable = 0x00000001;
        public static final int AnimatedStateListDrawableItem_android_id = 0x00000000;
        public static final int AnimatedStateListDrawableTransition_android_drawable = 0x00000000;
        public static final int AnimatedStateListDrawableTransition_android_fromId = 0x00000002;
        public static final int AnimatedStateListDrawableTransition_android_reversible = 0x00000003;
        public static final int AnimatedStateListDrawableTransition_android_toId = 0x00000001;
        public static final int StateListDrawableItem_android_drawable = 0x00000000;
        public static final int StateListDrawable_android_constantSize = 0x00000003;
        public static final int StateListDrawable_android_dither = 0x00000000;
        public static final int StateListDrawable_android_enterFadeDuration = 0x00000004;
        public static final int StateListDrawable_android_exitFadeDuration = 0x00000005;
        public static final int StateListDrawable_android_variablePadding = 0x00000002;
        public static final int StateListDrawable_android_visible = 0x00000001;
        public static final int[] AnimatedStateListDrawableCompat = {android.R.attr.dither, android.R.attr.visible, android.R.attr.variablePadding, android.R.attr.constantSize, android.R.attr.enterFadeDuration, android.R.attr.exitFadeDuration};
        public static final int[] AnimatedStateListDrawableItem = {android.R.attr.id, android.R.attr.drawable};
        public static final int[] AnimatedStateListDrawableTransition = {android.R.attr.drawable, android.R.attr.toId, android.R.attr.fromId, android.R.attr.reversible};
        public static final int[] StateListDrawable = {android.R.attr.dither, android.R.attr.visible, android.R.attr.variablePadding, android.R.attr.constantSize, android.R.attr.enterFadeDuration, android.R.attr.exitFadeDuration};
        public static final int[] StateListDrawableItem = {android.R.attr.drawable};

        private styleable() {
        }
    }

    private R() {
    }
}
