package com.ca.car.proxy.mcu;

import android.os.SystemProperties;
import android.text.TextUtils;
import com.ca.car.proxy.utils.CaCarLogUtils;

/* loaded from: classes.dex */
public class CaMcuManager {
    private static CaMcuManager mCaMcuManager;
    private volatile boolean bIsParseError;
    private byte[] mEOLbufA;
    private byte[] mEOLbufB;
    private byte[] mVsolbuf;
    private final String TAG = "CaMcuManager";
    public final String EOLCONFIG_A = "persist.vehicle.FucCfgA";
    public final String EOLCONFIG_B = "persist.vehicle.FucCfgB";
    public final String VEHICLE_VSOL = "persist.vehicle.vsol";
    private final int INT_BUF_LENGHT = 32;

    private int getIntFromByteWithBit(byte b, int i, int i2) {
        return ((b & 255) >> i) & ((1 << i2) - 1);
    }

    public static synchronized CaMcuManager getInstance() {
        CaMcuManager caMcuManager;
        synchronized (CaMcuManager.class) {
            if (mCaMcuManager == null) {
                mCaMcuManager = new CaMcuManager();
            }
            caMcuManager = mCaMcuManager;
        }
        return caMcuManager;
    }

    private CaMcuManager() {
        this.mEOLbufA = new byte[32];
        this.mEOLbufB = new byte[32];
        this.mVsolbuf = new byte[32];
        this.bIsParseError = false;
        try {
            this.mEOLbufA = toByteArray(getEolConfig("persist.vehicle.FucCfgA"));
            this.mEOLbufB = toByteArray(getEolConfig("persist.vehicle.FucCfgB"));
            this.mVsolbuf = toByteArray(getEolConfig("persist.vehicle.vsol"));
        } catch (Exception e) {
            e.printStackTrace();
            this.bIsParseError = true;
        }
    }

    public static String getEolConfig(String str) {
        return SystemProperties.get(str);
    }

    private byte[] toByteArray(String str) {
        if (TextUtils.isEmpty(str)) {
            throw new IllegalArgumentException("cfg is empty");
        }
        CaCarLogUtils.i("CaMcuManager", "toByteArray(), cfg: " + str);
        byte[] bArr = new byte[str.length() / 2];
        int i = 0;
        for (int i2 = 0; i2 < 32; i2++) {
            bArr[i2] = (byte) ((((byte) (Character.digit(str.charAt(i), 16) & 255)) << 4) | ((byte) (Character.digit(str.charAt(i + 1), 16) & 255)));
            i += 2;
        }
        return bArr;
    }

    public int getIntByIndexWithBit(int i, int i2, int i3) {
        if (i < 0 || i > 63 || i2 < 0 || i2 > 7 || i3 < 1 || i3 > 8) {
            throw new IllegalArgumentException("param is invilib");
        }
        if (this.bIsParseError) {
            return -1;
        }
        int i4 = i / 32;
        if (i4 == 0) {
            return getIntFromByteWithBit(this.mEOLbufA[i], i2, i3);
        }
        if (i4 == 1) {
            return getIntFromByteWithBit(this.mEOLbufB[i % 32], i2, i3);
        }
        return -1;
    }

    public int getIntByIndexWithBitExt(int i, int i2, int i3, int i4) {
        if (i2 < 0 || ((1 == i && i2 > 16) || ((2 == i && i2 > 31) || i3 < 0 || i3 > 7 || i4 < 1 || i4 > 8))) {
            throw new IllegalArgumentException("param is invilib");
        }
        if (this.bIsParseError) {
            return -1;
        }
        if (1 == i) {
            return getIntFromByteWithBit(this.mEOLbufA[i2], i3, i4);
        }
        if (2 == i) {
            return getIntFromByteWithBit(this.mVsolbuf[i2], i3, i4);
        }
        return -1;
    }
}
