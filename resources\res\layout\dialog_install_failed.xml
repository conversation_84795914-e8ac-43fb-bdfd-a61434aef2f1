<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="880dp"
    android:layout_height="wrap_content"
    android:minHeight="444dp">
    <ImageView
        android:id="@+id/install_failed_iv"
        android:background="@drawable/caui_icon_dialog_close"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="55dp"
        android:layout_marginStart="64dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="40sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/install_failed_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="48dp"
        android:text="@string/dialog_install_failed"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="32sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:gravity="left"
        android:id="@+id/tx_install_failed"
        android:clickable="true"
        android:layout_width="752dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:text="@string/dialog_install_failed_tip_contact"
        android:contentDescription="拨打[电话|客服电话]"
        android:layout_marginStart="64dp"
        android:layout_marginEnd="64dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/install_failed_title"/>
    <TextView
        android:id="@+id/background_dial"
        android:background="@color/transparent"
        android:clickable="true"
        android:layout_width="270dp"
        android:layout_height="50dp"
        android:layout_marginBottom="60dp"
        app:layout_constraintBottom_toTopOf="@+id/btn_install_fail"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/btn_install_fail"
        android:layout_width="400dp"
        android:layout_height="96dp"
        android:layout_marginTop="48dp"
        android:layout_marginBottom="56dp"
        android:text="@string/dialog_install_failed_confirm"
        android:contentDescription="好的"
        android:layout_marginStart="240dp"
        android:layout_marginEnd="240dp"
        app:caui_round_btn_type="main"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tx_install_failed"/>
</androidx.constraintlayout.widget.ConstraintLayout>
