<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:gravity="center"
    android:orientation="vertical"
    android:tag="layout/dialog_history_0"
    android:clipChildren="false"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <com.incall.apps.caui.shape.CAUIShapeConstraintLayout
        android:gravity="center"
        android:tag="binding_1"
        android:layout_width="1280dp"
        android:layout_height="1024dp"
        android:layout_margin="@dimen/caui_config_shadow_elevation_large"
        app:caui_shape_borderColor="@color/caui_config_pop_up_stroke_color"
        app:caui_shape_borderWidth="1dp"
        app:caui_shape_color="@color/caui_config_pop_up_bg_color"
        app:caui_shape_radius="@dimen/caui_config_corner_radius_x_large"
        app:caui_shape_shadowColor="@color/caui_config_shadow_color_large"
        app:caui_shape_shadowElevation="@dimen/caui_config_shadow_elevation_large">
        <ImageView
            android:id="@+id/history_close_img"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginTop="55dp"
            android:src="@drawable/caui_icon_dialog_close"
            android:layout_marginStart="64dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <TextView
            android:textSize="44sp"
            android:textColor="@color/caui_config_text_color_primary"
            android:gravity="center"
            android:id="@+id/history_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="48dp"
            android:text="@string/history_dialog_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <ImageView
            android:id="@+id/iv_null_history_version"
            android:tag="binding_2"
            android:layout_width="432dp"
            android:layout_height="252dp"
            android:layout_marginTop="360dp"
            android:src="@drawable/fota_null_history_version"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <TextView
            android:textSize="32sp"
            android:textColor="@color/caui_config_text_color_secondary"
            android:gravity="center_horizontal"
            android:id="@+id/tv_null_history_version"
            android:tag="binding_3"
            android:layout_width="512dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/null_history_version_tip"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_null_history_version"/>
        <androidx.recyclerview.widget.RecyclerView
            android:scrollbarThumbVertical="@drawable/fota_scroll_thumb"
            android:scrollbarStyle="outsideOverlay"
            android:orientation="vertical"
            android:id="@+id/history_recyclerview"
            android:tag="binding_4"
            android:scrollbars="vertical"
            android:layout_width="1280dp"
            android:layout_height="819dp"
            android:layout_marginTop="150dp"
            android:layout_marginBottom="55dp"
            android:minWidth="1280dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
    </com.incall.apps.caui.shape.CAUIShapeConstraintLayout>
</LinearLayout>
