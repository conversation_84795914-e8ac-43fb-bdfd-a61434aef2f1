package com.changan.evs.ipc;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface AppMessageType extends IInterface {
    public static final int APP_APA_GET_SWITCH_STATE = 1802;
    public static final int APP_APA_PARK_SWITCH = 1801;
    public static final int APP_APA_SPORG_TRAIN_START_UP = 21801;
    public static final int APP_APA_SPROG_TRAIN_BREAK = 1804;
    public static final int APP_APA_SPROG_TRAIN_END = 1805;
    public static final int APP_APA_VOICE_CONTROL_COMMAND = 1803;
    public static final int APP_AVM_CLOSE = 1402;
    public static final int APP_AVM_MASK_WINDOW_UP = 21402;
    public static final int APP_AVM_OPEN = 1401;
    public static final int APP_AVM_START_ACTIVITY_UP = 21401;
    public static final int APP_AVM_SWITCH_STATE_UPDATE_UP = 21403;
    public static final int APP_CAMP_ALARM_INFO_UPDATE_UP = 21304;
    public static final int APP_CAMP_ALARM_METHOD_SET = 1303;
    public static final int APP_CAMP_ALARM_METHOD_SET_SINGLE = 1315;
    public static final int APP_CAMP_ALARM_METHOD_SET_SINGLE_UP = 21314;
    public static final int APP_CAMP_DISTANCE_ALARM_INFO_GET = 1311;
    public static final int APP_CAMP_DISTANCE_ALARM_INFO_SET = 1310;
    public static final int APP_CAMP_DISTANCE_ALARM_INFO_UPDATE_UP = 21309;
    public static final int APP_CAMP_DISTANCE_SET = 1302;
    public static final int APP_CAMP_DISTANCE_UPDATE_UP = 21303;
    public static final int APP_CAMP_EXIT_SCREENSHOW_EVENT_UP = 21308;
    public static final int APP_CAMP_FUNCTION_OPEN_CLOSE_EVENT_UP = 21306;
    public static final int APP_CAMP_FUNCTION_TRIG_OUT_EVENT_UP = 21307;
    public static final int APP_CAMP_GET_ALARM_INFO = 1307;
    public static final int APP_CAMP_GET_DISTANCE = 1306;
    public static final int APP_CAMP_GET_STATUS_BAR_STATE = 1313;
    public static final int APP_CAMP_GET_SWITCH_STATE = 1304;
    public static final int APP_CAMP_GET_TRIG_TIME_INFO = 1309;
    public static final int APP_CAMP_GET_VIDEO_RECORD_STATE = 1314;
    public static final int APP_CAMP_ITEM_POSITION_UPDATE_UP = 21311;
    public static final int APP_CAMP_SET_USER_NOTIFY_CONFIG_INFO = 1308;
    public static final int APP_CAMP_STATUS_BAR_STATE_UPDATE_UP = 21310;
    public static final int APP_CAMP_SWITCH_SET = 1301;
    public static final int APP_CAMP_SWITCH_STATE_UPDATE_UP = 21301;
    public static final int APP_CAMP_USER_NOTIFY_CONFIRM_EVENT_UP = 21305;
    public static final int APP_DVR_ALBUM_UPDATE_UP = 21004;
    public static final int APP_DVR_CAMERA_SIGNAL_STATE_GET = 1029;
    public static final int APP_DVR_CAMERA_SIGNAL_STATE_UPDATE_UP = 21018;
    public static final int APP_DVR_CAMERA_STATE_ERROR_UP = 21010;
    public static final int APP_DVR_CAMERA_STREAM_STATE_UPDATE_UP = 21011;
    public static final int APP_DVR_CHANGE_VIEW = 1006;
    public static final int APP_DVR_CLOSE_APP_UP = 21001;
    public static final int APP_DVR_CLOSE_VOICE_RECORD = 1020;
    public static final int APP_DVR_CLOSE_WATER_SHOW = 1025;
    public static final int APP_DVR_CYCLE_STATE_UPDATE_UP = 21008;
    public static final int APP_DVR_DEL_ALBUM_DATA = 1012;
    public static final int APP_DVR_FORMAT_SD = 1005;
    public static final int APP_DVR_GET_ALBUM_DATA = 1011;
    public static final int APP_DVR_GET_CYCLE_STATUS = 1010;
    public static final int APP_DVR_GET_RECORD_DURATION = 1015;
    public static final int APP_DVR_GET_RESOLUTION = 1023;
    public static final int APP_DVR_GET_SD_FREE_SPACE = 1016;
    public static final int APP_DVR_GET_SD_MOUNT_STATUS = 1009;
    public static final int APP_DVR_GET_SD_SPACE_BY_FILETYPE = 1018;
    public static final int APP_DVR_GET_SD_TOTAL_SPACE = 1017;
    public static final int APP_DVR_GET_VOICE_RECORD_STATUS = 1021;
    public static final int APP_DVR_GET_WATER_SHOW_STATUS = 1026;
    public static final int APP_DVR_OPEN_VOICE_RECORD = 1019;
    public static final int APP_DVR_OPEN_WATER_SHOW = 1024;
    public static final int APP_DVR_RECORD_DISK_ERROR_DAMAGE_UP = 21016;
    public static final int APP_DVR_RECORD_DISK_ERROR_READ_LOW_UP = 21015;
    public static final int APP_DVR_RECORD_ERROR_STATE_GET = 1028;
    public static final int APP_DVR_RECORD_ERROR_STATE_UP = 21017;
    public static final int APP_DVR_RECORD_PATH_GET = 1030;
    public static final int APP_DVR_RECORD_UNKNOWN_ERROR_UP = 21014;
    public static final int APP_DVR_RECORD_VOICE_STATE_UPDATE_UP = 21013;
    public static final int APP_DVR_RECORD_WATER_STATE_UPDATE_UP = 21012;
    public static final int APP_DVR_RECORD__PROGRESS_UPDATE_UP = 21009;
    public static final int APP_DVR_SD_MOUNT_CHANGE_UP = 21006;
    public static final int APP_DVR_SD_SPACE_LOW_UP = 21007;
    public static final int APP_DVR_SET_RECORD_DURATION = 1014;
    public static final int APP_DVR_SET_RESOLUTION = 1022;
    public static final int APP_DVR_START_PREVIEW = 1007;
    public static final int APP_DVR_START_RECORD = 1001;
    public static final int APP_DVR_START_URGENCY = 1004;
    public static final int APP_DVR_STOP_PREVIEW = 1008;
    public static final int APP_DVR_STOP_RECORD = 1002;
    public static final int APP_DVR_STOP_URGENCY = 1027;
    public static final int APP_DVR_TAKE_PICTURE = 1003;
    public static final int APP_DVR_TRANSIENT_ALBUM = 1013;
    public static final int APP_DVR_TRANSIENT_END_UP = 21005;
    public static final int APP_DVR_VIDEO_PAUSE_UP = 21002;
    public static final int APP_DVR_VIDEO_RESUME_UP = 21003;
    public static final int APP_RMA_PHONE_TOUCH_POINT = 2001;
    public static final int APP_RMA_SCREEN_TIP_UP = 22001;
    public static final int APP_RMA_SWITCH_STATE = 2002;
    public static final int APP_SENTRY_ACTIVE_MODE_SWITCH_ELECFEN_INFO_GET = 1211;
    public static final int APP_SENTRY_ACTIVE_MODE_SWITCH_ELECFEN_INFO_SET = 1210;
    public static final int APP_SENTRY_ACTIVE_MODE_SWITCH_SET = 1201;
    public static final int APP_SENTRY_CAMP_FIRST_START_UP = 21315;
    public static final int APP_SENTRY_CAMP_SD_ERROR_UP = 21313;
    public static final int APP_SENTRY_CAMP_TRIG_EVENT_UP = 21312;
    public static final int APP_SENTRY_CAMP_USER_NOTIFY_CONFIRM_EVENT_UP = 21205;
    public static final int APP_SENTRY_DEL_TRIG_TIME_INFO = 1218;
    public static final int APP_SENTRY_ELECFEN_INFO_SET = 1203;
    public static final int APP_SENTRY_ELECFEN_INFO_UPDATE_UP = 21204;
    public static final int APP_SENTRY_EXIT_SCREENSHOW_EVENT_UP = 21208;
    public static final int APP_SENTRY_FUNCTION_OPEN_CLOSE_EVENT_UP = 21206;
    public static final int APP_SENTRY_FUNCTION_OPEN_STATE_UPDATE_UP = 21202;
    public static final int APP_SENTRY_FUNCTION_TRIG_OUT_EVENT_UP = 21207;
    public static final int APP_SENTRY_GET_ACTIVE_MODE_ELECFEN_INFO = 1207;
    public static final int APP_SENTRY_GET_ACTIVE_MODE_FUNCTION_OPEN_STATE = 1205;
    public static final int APP_SENTRY_GET_ACTIVE_MODE_OPEN_METHOD = 1206;
    public static final int APP_SENTRY_GET_ACTIVE_MODE_STATE = 1204;
    public static final int APP_SENTRY_GET_NOTIFY_METHOD = 1216;
    public static final int APP_SENTRY_GET_STATUS_BAR_STATE = 1213;
    public static final int APP_SENTRY_GET_TRIG_TIME_INFO = 1209;
    public static final int APP_SENTRY_GET_VIDEO_RECORD_STATE = 1214;
    public static final int APP_SENTRY_NOTIFY_FUNCTION_OPEN_STATE = 1212;
    public static final int APP_SENTRY_NOTIFY_METHOD_UPDATE_UP = 21211;
    public static final int APP_SENTRY_OPEN_METHOD_ELECFEN_INFO_UPDATE_UP = 21209;
    public static final int APP_SENTRY_OPEN_METHOD_SET = 1202;
    public static final int APP_SENTRY_OPEN_METHOD_UPDATE_UP = 21203;
    public static final int APP_SENTRY_SET_NOTIFY_METHOD = 1215;
    public static final int APP_SENTRY_SET_NOTIFY_METHOD_SINGLE = 1217;
    public static final int APP_SENTRY_SET_NOTIFY_METHOD_SINGLE_UP = 21212;
    public static final int APP_SENTRY_SET_USER_NOTIFY_CONFIG_INFO = 1208;
    public static final int APP_SENTRY_STATUS_BAR_STATE_UPDATE_UP = 21210;
    public static final int APP_SENTRY_SWITCH_STATE_UPDATE_UP = 21201;
    public static final int CODE_CAMP_BATTERY_NOT_SUPPORT = 404;
    public static final int CODE_CAMP_GEAR_IS_NOT_P = 403;
    public static final int CODE_CAMP_OPEN_NO_USER_CONFIRM = 402;
    public static final int CODE_CAMP_SDCARD_NOT_SUPPORT = 405;
    public static final int CODE_COMMON_CON_NO = 101;
    public static final int CODE_COMMON_CON_OK = 100;
    public static final int CODE_COMMON_MSG_NO = 201;
    public static final int CODE_COMMON_MSG_OK = 200;
    public static final int CODE_COMMON_MSG_TIMEOUT = 203;
    public static final int CODE_COMMON_MSG_UNHANDLED = 202;
    public static final int CODE_DVR_ASSIST_RECORDING = 302;
    public static final int CODE_DVR_CYCLE_RECORDING = 306;
    public static final int CODE_DVR_DETECTION_NO_U = 308;
    public static final int CODE_DVR_SD_FORMATING = 305;
    public static final int CODE_DVR_SD_FORMAT_NOT_SUPPORT = 300;
    public static final int CODE_DVR_SD_SPACE_LOW = 304;
    public static final int CODE_DVR_SD_UNMOUNTED = 301;
    public static final int CODE_DVR_TAKING_PICTURE = 310;
    public static final int CODE_DVR_URGENCY_RECORDING = 303;
    public static final int CODE_DVR_U_SPACE_LOW = 309;
    public static final int CODE_SENTRY_OPEN_FAIL_NO = 451;
    public static final int CODE_SENTRY_OPEN_FAIL_NO_ELEC = 452;
    public static final int CODE_SENTRY_OPEN_FAIL_NO_SDCARD = 453;
    public static final int CODE_SENTRY_OPEN_FAIL_POWER_ON = 456;
    public static final int CODE_SENTRY_OPEN_FAIL_SDCARD_FORMAT_ERROR = 454;
    public static final int CODE_SENTRY_OPEN_FAIL_SDCARD_LOW_SPACE = 455;
    public static final int CODE_SENTRY_OPEN_FAIL_SWITCH_CLOSE = 457;
    public static final int CODE_SENTRY_OPEN_FAIL_UNKNOW = 458;
    public static final int CODE_SENTRY_OPEN_NO_USER_CONFIRM = 401;
    public static final int PARAMETER_APA_PARK_SWITCH_APPLIST = 3;
    public static final int PARAMETER_APA_PARK_SWITCH_LAUNCHER = 0;
    public static final int PARAMETER_APA_PARK_SWITCH_STEERING_WHEEL = 2;
    public static final int PARAMETER_APA_PARK_SWITCH_VOICE = 1;
    public static final int PARAMETER_APA_VOICE_CONTROL_0x1 = 1;
    public static final int PARAMETER_APA_VOICE_CONTROL_0x2 = 2;
    public static final int PARAMETER_APA_VOICE_CONTROL_0x3 = 3;
    public static final int PARAMETER_APA_VOICE_CONTROL_0x4 = 4;
    public static final int PARAMETER_APA_VOICE_CONTROL_0x5 = 5;
    public static final int PARAMETER_APA_VOICE_CONTROL_0x6 = 6;
    public static final int PARAMETER_APA_VOICE_CONTROL_0x7 = 7;
    public static final int PARAMETER_APA_VOICE_CONTROL_0x8 = 8;
    public static final int PARAMETER_APA_VOICE_CONTROL_0x9 = 9;
    public static final int PARAMETER_APA_VOICE_CONTROL_0xA = 10;
    public static final int PARAMETER_APA_VOICE_CONTROL_0xB = 11;
    public static final int PARAMETER_APA_VOICE_CONTROL_0xC = 12;
    public static final int PARAMETER_AVM_FULL_SCREEN = 1;
    public static final int PARAMETER_AVM_NO_SHOW = 0;
    public static final int PARAMETER_AVM_SAFE_VIEW_BIG = 2;
    public static final int PARAMETER_AVM_SAFE_VIEW_SMALL = 3;
    public static final int PARAMETER_AVM_START_SOFTKEY = 1;
    public static final int PARAMETER_AVM_START_VOICE = 2;
    public static final int PARAMETER_CAMERA_FRONT = 1;
    public static final int PARAMETER_CAMERA_LEFT = 4;
    public static final int PARAMETER_CAMERA_PERISCOPIC_TYPE_FROUNT = 5;
    public static final int PARAMETER_CAMERA_REAR = 2;
    public static final int PARAMETER_CAMERA_RIGHT = 8;
    public static final int PARAMETER_CAMERA_TYPE_FRONT = 0;
    public static final int PARAMETER_CAMERA_TYPE_GROUP = 4;
    public static final int PARAMETER_CAMERA_TYPE_LEFT = 2;
    public static final int PARAMETER_CAMERA_TYPE_REAR = 1;
    public static final int PARAMETER_CAMERA_TYPE_RIGHT = 3;
    public static final int PARAMETER_CAMERA_VISION_FRONT = 1;
    public static final int PARAMETER_CAMERA_VISION_GROUP = 16;
    public static final int PARAMETER_CAMERA_VISION_LEFT = 4;
    public static final int PARAMETER_CAMERA_VISION_PERISCOPIC = 32;
    public static final int PARAMETER_CAMERA_VISION_REAR = 2;
    public static final int PARAMETER_CAMERA_VISION_RIGHT = 8;
    public static final int PARAMETER_CAMP_CIRCLE_TYPE_INNER = 0;
    public static final int PARAMETER_CAMP_CIRCLE_TYPE_MIDDLE = 1;
    public static final int PARAMETER_CAMP_CIRCLE_TYPE_OUTTER = 2;
    public static final int PARAMETER_CAMP_NOTIFY_METHOD_LIGHT = 4;
    public static final int PARAMETER_CAMP_NOTIFY_METHOD_NO = 0;
    public static final int PARAMETER_CAMP_NOTIFY_METHOD_PHONE = 1;
    public static final int PARAMETER_CAMP_NOTIFY_METHOD_VOICE = 2;
    public static final int PARAMETER_COMMON_0 = 0;
    public static final int PARAMETER_COMMON_1 = 1;
    public static final int PARAMETER_CYCLE_RECORD_120_TIME = 120;
    public static final int PARAMETER_CYCLE_RECORD_300_TIME = 300;
    public static final int PARAMETER_CYCLE_RECORD_60_TIME = 60;
    public static final int PARAMETER_DISK_DAMAGE = 2;
    public static final int PARAMETER_DISK_WRITE_READ_LOW_SPEED = 1;
    public static final int PARAMETER_LANGUAGE_CN = 2;
    public static final int PARAMETER_LANGUAGE_EN = 1;
    public static final int PARAMETER_MEDIA_TYPE_ALL = 0;
    public static final int PARAMETER_MEDIA_TYPE_ASSIST = 3;
    public static final int PARAMETER_MEDIA_TYPE_CYCLE = 1;
    public static final int PARAMETER_MEDIA_TYPE_PHOTO = 4;
    public static final int PARAMETER_MEDIA_TYPE_URGENCY = 2;
    public static final int PARAMETER_MEDIA_TYPE_URGENCY_AEB = 2;
    public static final int PARAMETER_MEDIA_TYPE_URGENCY_CRASH = 1;
    public static final int PARAMETER_MEDIA_TYPE_URGENCY_MANUAL = 0;
    public static final int PARAMETER_MEDIA_TYPE_URGENCY_SRS = 3;
    public static final int PARAMETER_RESOLUTION_HEIGHT_1080 = 1080;
    public static final int PARAMETER_RESOLUTION_HEIGHT_720 = 720;
    public static final int PARAMETER_RESOLUTION_WIDTH_1080 = 1080;
    public static final int PARAMETER_RESOLUTION_WIDTH_1280 = 1280;
    public static final int PARAMETER_RESOLUTION_WIDTH_1920 = 1920;
    public static final int PARAMETER_SENTRY_BURGLAR = 3;
    public static final int PARAMETER_SENTRY_CRASH = 1;
    public static final int PARAMETER_SENTRY_ELECFEN_COMPANY = 2;
    public static final int PARAMETER_SENTRY_ELECFEN_HOME = 1;
    public static final int PARAMETER_SENTRY_ELECFEN_HOME_COMPANY = 3;
    public static final int PARAMETER_SENTRY_ELECFEN_NO = 0;
    public static final int PARAMETER_SENTRY_NOTIFY_METHOD_LIGHT = 4;
    public static final int PARAMETER_SENTRY_NOTIFY_METHOD_NO = 0;
    public static final int PARAMETER_SENTRY_NOTIFY_METHOD_PHONE = 1;
    public static final int PARAMETER_SENTRY_NOTIFY_METHOD_VOICE = 2;
    public static final int PARAMETER_SENTRY_TRIG_ALARM = 0;
    public static final int PARAMETER_TRANSIENT_TYPE_U = 1;
    public static final int PARAMETER_TRANSIENT_TYPE_URGENCY = 0;
    public static final int PARAMETER_UPDATE_TYPE_ADD = 0;
    public static final int PARAMETER_UPDATE_TYPE_CLEAR = 2;
    public static final int PARAMETER_UPDATE_TYPE_DEL = 1;
    public static final int PARAMETER_WATER_PREVIEW = 1;
    public static final int PARAMETER_WATER_RECORD = 2;
    public static final int PARAMETER_WATER_TAKE_PICTURE = 4;

    public static class Default implements AppMessageType {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }
    }

    public static abstract class Stub extends Binder implements AppMessageType {
        private static final String DESCRIPTOR = "com.changan.evs.ipc.AppMessageType";

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static AppMessageType asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof AppMessageType)) {
                return (AppMessageType) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i == 1598968902) {
                parcel2.writeString(DESCRIPTOR);
                return true;
            }
            return super.onTransact(i, parcel, parcel2, i2);
        }

        private static class Proxy implements AppMessageType {
            public static AppMessageType sDefaultImpl;
            private IBinder mRemote;

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }
        }

        public static boolean setDefaultImpl(AppMessageType appMessageType) {
            if (Proxy.sDefaultImpl != null) {
                throw new IllegalStateException("setDefaultImpl() called twice");
            }
            if (appMessageType == null) {
                return false;
            }
            Proxy.sDefaultImpl = appMessageType;
            return true;
        }

        public static AppMessageType getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }
    }
}
