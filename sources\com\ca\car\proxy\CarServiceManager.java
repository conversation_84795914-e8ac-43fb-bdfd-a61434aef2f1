package com.ca.car.proxy;

import android.content.Context;
import com.ca.car.proxy.CarServiceStatusCallbacks;

/* loaded from: classes.dex */
public class CarServiceManager {
    private static CarServiceManager mCarServiceManager;
    private String TAG = "CarServiceManager";

    private CarServiceManager() {
    }

    public static synchronized CarServiceManager getInstance() {
        CarServiceManager carServiceManager;
        synchronized (CarServiceManager.class) {
            if (mCarServiceManager == null) {
                mCarServiceManager = new CarServiceManager();
            }
            carServiceManager = mCarServiceManager;
        }
        return carServiceManager;
    }

    public void registerServiceStatusListener(Context context, String str, CarServiceStatusCallbacks.ICarServiceStatusLisener iCarServiceStatusLisener) {
        CarServiceStatusCallbackManager.getInstance().registerServiceStatusListener(context, str, iCarServiceStatusLisener);
    }

    public void unRegisterServiceStatusListener(String str) {
        CarServiceStatusCallbackManager.getInstance().unRegisterServiceStatusListener(str);
    }
}
