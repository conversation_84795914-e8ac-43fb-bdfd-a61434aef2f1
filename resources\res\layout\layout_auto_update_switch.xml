<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@color/caui_config_content_bg_color_primary"
    android:layout_width="1260dp"
    android:layout_height="wrap_content"
    android:minWidth="1260dp"
    app:caui_radius="@dimen/caui_config_corner_radius_middle">
    <com.incall.apps.caui.layout.CAUIConstraintLayout
        android:id="@+id/auto_update_switch_layout"
        android:background="@color/caui_config_content_bg_color_primary"
        android:layout_width="match_parent"
        android:layout_height="154dp"
        android:minWidth="1260dp"
        app:caui_radius="@dimen/caui_config_corner_radius_middle">
        <TextView
            android:textSize="32sp"
            android:textColor="@color/caui_config_text_color_primary"
            android:id="@+id/auto_upgrade_tx"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:text="@string/switch_bar_auto_update"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <TextView
            android:textSize="28sp"
            android:textColor="@color/caui_config_text_color_third"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/switch_bar_auto_update_tip2"
            android:textFontWeight="400"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/auto_upgrade_tx"/>
        <com.incall.apps.caui.widget.button.switchbutton.SwitchButton
            android:id="@+id/auto_update_switch"
            android:layout_width="120dp"
            android:layout_height="64dp"
            android:layout_marginTop="52dp"
            android:layout_marginStart="48dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:swb_thumbRadius="@dimen/caui_config_corner_radius_small"
            style="@style/SwitchButtonDefault"/>
        <Button
            android:id="@+id/btn_mask"
            android:background="@android:color/transparent"
            android:layout_width="120dp"
            android:layout_height="64dp"
            android:layout_marginTop="52dp"
            android:layout_marginStart="48dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
    </com.incall.apps.caui.layout.CAUIConstraintLayout>
    <View
        android:id="@+id/auto_upgrade_switch_divider"
        android:background="@color/caui_config_divider_color_primary"
        android:layout_width="1260dp"
        android:layout_height="2dp"
        android:layout_marginTop="16dp"/>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_time"
        android:layout_width="1260dp"
        android:layout_height="128dp"
        android:layout_marginTop="16dp">
        <TextView
            android:textSize="32sp"
            android:textColor="@color/caui_config_text_color_primary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="42dp"
            android:text="@string/switch_bar_auto_update_time"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <ImageView
            android:layout_width="15dp"
            android:layout_height="26dp"
            android:src="@drawable/caui_icon_chevron"
            android:scaleType="center"
            android:contentDescription="[打开]自动升级时间[设置]"
            android:layout_marginEnd="11dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <TextView
            android:textSize="28sp"
            android:textColor="@color/caui_config_text_color_disabled"
            android:id="@+id/tx_auto_upgrade_time"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:text="3:00"
            android:contentDescription="[打开]自动升级时间[设置]"
            android:layout_marginEnd="52dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>
