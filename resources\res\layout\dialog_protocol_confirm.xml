<?xml version="1.0" encoding="utf-8"?>
<com.incall.apps.caui.shape.CAUIShapeConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    android:background="@color/transparent"
    android:layout_width="880dp"
    android:layout_height="wrap_content">
    <ImageView
        android:id="@+id/protocol_confirm_iv"
        android:background="@drawable/caui_icon_dialog_close"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="55dp"
        android:layout_marginStart="64dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.incall.apps.caui.layout.CAUITextView
        android:textSize="40sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/confirm_protocol_title"
        android:layout_width="wrap_content"
        android:layout_height="62dp"
        android:layout_marginTop="48dp"
        android:layout_marginBottom="348dp"
        android:text="@string/install_confirm_title"
        android:layout_marginStart="396dp"
        android:layout_marginEnd="396dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.incall.apps.caui.layout.CAUITextView
        android:textSize="32sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:gravity="left"
        android:id="@+id/confirm_protocol_content"
        android:layout_width="752dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:layout_marginBottom="200dp"
        android:text="@string/install_confirm_tip"
        android:layout_marginStart="64dp"
        android:layout_marginEnd="64dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/protocol_confirm_iv"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/confirm_protocol_btn"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="48dp"
        android:layout_marginBottom="56dp"
        android:text="@string/agree_install_btn_tx"
        android:layout_marginStart="64dp"
        android:layout_marginEnd="472dp"
        app:caui_round_btn_type="main"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/confirm_protocol_content"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/reject_protocol_btn"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="48dp"
        android:layout_marginBottom="56dp"
        android:text="@string/later_install_btn_tx"
        android:layout_marginStart="472dp"
        android:layout_marginEnd="64dp"
        app:caui_round_btn_type="negative"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/confirm_protocol_content"/>
</com.incall.apps.caui.shape.CAUIShapeConstraintLayout>
