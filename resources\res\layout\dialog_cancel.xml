<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.incall.apps.caui.layout.CAUIConstraintLayout
        android:layout_gravity="center"
        android:layout_width="880dp"
        android:layout_height="wrap_content"
        android:minHeight="380dp">
        <ImageView
            android:id="@+id/cancel_dialog_iv"
            android:background="@drawable/caui_icon_dialog_close"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginTop="55dp"
            android:layout_marginStart="64dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <com.incall.apps.caui.layout.CAUITextView
            android:textSize="40sp"
            android:textColor="@color/caui_config_text_color_primary"
            android:id="@+id/cancel_dialog_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="48dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <com.incall.apps.caui.layout.CAUITextView
            android:textSize="32sp"
            android:textColor="@color/caui_config_text_color_primary"
            android:id="@+id/cancel_dialog_content"
            android:layout_width="752dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:layout_marginStart="64dp"
            android:layout_marginEnd="64dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cancel_dialog_title"/>
        <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
            android:textSize="36sp"
            android:gravity="center"
            android:id="@+id/confirm_close_btn"
            android:layout_width="344dp"
            android:layout_height="96dp"
            android:layout_marginTop="48dp"
            android:layout_marginBottom="56dp"
            android:text="@string/confirm_cancel"
            android:contentDescription="确认关闭"
            android:layout_marginStart="64dp"
            app:caui_round_btn_type="delete"
            app:caui_round_radius="@dimen/caui_config_corner_radius_small"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cancel_dialog_content"/>
        <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
            android:textSize="36sp"
            android:gravity="center"
            android:id="@+id/cancel_close_btn"
            android:layout_width="344dp"
            android:layout_height="96dp"
            android:layout_marginTop="48dp"
            android:layout_marginBottom="56dp"
            android:text="@string/btn_cancel_protocol"
            android:contentDescription="取消"
            android:layout_marginEnd="64dp"
            app:caui_round_btn_type="negative"
            app:caui_round_radius="@dimen/caui_config_corner_radius_small"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cancel_dialog_content"/>
    </com.incall.apps.caui.layout.CAUIConstraintLayout>
</FrameLayout>
