<?xml version="1.0" encoding="utf-8"?>
<com.incall.apps.caui.layout.CAUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    android:background="#0000"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minWidth="880dp"
    android:minHeight="832dp">
    <ImageView
        android:id="@+id/order_time_close_iv"
        android:background="@drawable/caui_icon_dialog_close"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="55dp"
        android:layout_marginStart="64dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="40sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="56dp"
        android:layout_marginTop="46dp"
        android:text="修改升级时间"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="32sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/content"
        android:layout_width="752dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="34dp"
        android:text="@string/order_upgrade_tip"
        android:layout_marginStart="64dp"
        android:layout_marginEnd="64dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title"/>
    <com.incall.apps.caui.layout.CAUIConstraintLayout
        android:background="@color/caui_config_pressed_bg_color"
        android:layout_width="752dp"
        android:layout_height="128dp"
        android:layout_marginTop="152dp"
        app:caui_radius="@dimen/caui_config_corner_radius_middle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/content"/>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:gravity="center"
        android:orientation="horizontal"
        android:id="@+id/ll_picker"
        android:layout_width="752dp"
        android:layout_height="376dp"
        android:layout_marginTop="24dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/content">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <com.incall.apps.softmanager.base.view.CycleWheelView
                android:layout_gravity="center"
                android:id="@+id/day_picker"
                android:layout_width="0dp"
                android:layout_height="384dp"
                android:layout_weight="1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/NumberPicker"/>
            <com.incall.apps.softmanager.base.view.CycleWheelView
                android:layout_gravity="center"
                android:id="@+id/hour_picker"
                android:layout_width="0dp"
                android:layout_height="384dp"
                android:layout_weight="1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/NumberPicker"/>
            <com.incall.apps.softmanager.base.view.CycleWheelView
                android:layout_gravity="center"
                android:id="@+id/minute_picker"
                android:layout_width="0dp"
                android:layout_height="384dp"
                android:layout_weight="1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/NumberPicker"/>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/btn_dialog_yes"
        android:visibility="visible"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="48dp"
        android:layout_marginBottom="56dp"
        android:text="@string/install_confirm_dialog_btn"
        android:contentDescription="确定"
        android:layout_marginStart="64dp"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_picker"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/btn_dialog_cancel"
        android:visibility="visible"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="48dp"
        android:layout_marginBottom="56dp"
        android:text="@string/install_cancel_dialog_btn"
        android:contentDescription="取消"
        android:layout_marginEnd="64dp"
        app:caui_round_btn_type="negative"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_picker"/>
</com.incall.apps.caui.layout.CAUIConstraintLayout>
