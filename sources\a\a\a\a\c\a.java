package a.a.a.a.c;

import android.text.TextUtils;
import android.util.Log;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.nio.charset.StandardCharsets;
import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.crypto.CipherOutputStream;
import javax.crypto.spec.SecretKeySpec;
import kotlin.UByte;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes.dex */
public class a {
    public static String a(String str, String str2) {
        try {
            if (str2 == null) {
                Log.i("AESUtil", "Key为空null");
                return null;
            }
            if (str2.length() != 16) {
                Log.i("AESUtil", "Key长度不是16位");
                return null;
            }
            SecretKeySpec secretKeySpec = new SecretKeySpec(str2.getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/NoPadding");
            cipher.init(2, secretKeySpec);
            return new String(cipher.doFinal(b.a(str)), StandardCharsets.UTF_8);
        } catch (Exception e) {
            Log.i("AESUtil", StringUtils.SPACE + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    public static boolean b(byte[] bArr) {
        return bArr == null || bArr.length == 0;
    }

    public static byte[] a(byte[] bArr, byte[] bArr2, byte[] bArr3) {
        if (b(bArr) || b(bArr2)) {
            return null;
        }
        byte[] bArr4 = new byte[bArr.length + bArr2.length + bArr3.length];
        int i = 0;
        for (byte b : bArr) {
            bArr4[i] = b;
            i++;
        }
        for (byte b2 : bArr2) {
            bArr4[i] = b2;
            i++;
        }
        for (byte b3 : bArr3) {
            bArr4[i] = b3;
            i++;
        }
        return bArr4;
    }

    public static void a(String str, byte[] bArr, String str2, String str3) {
        if (!TextUtils.isEmpty(str) && !TextUtils.isEmpty(str2) && !TextUtils.isEmpty(str3)) {
            File file = new File(str2);
            File file2 = new File(str3);
            if (!file.exists() || !file.isFile()) {
                return;
            }
            if (!file2.getParentFile().exists()) {
                file2.getParentFile().mkdirs();
            }
            file2.createNewFile();
            FileInputStream fileInputStream = new FileInputStream(file);
            FileOutputStream fileOutputStream = new FileOutputStream(file2);
            SecretKeySpec secretKeySpec = new SecretKeySpec(str.getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(1, secretKeySpec);
            CipherInputStream cipherInputStream = new CipherInputStream(fileInputStream, cipher);
            if (bArr != null && bArr.length != 0) {
                fileOutputStream.write(bArr);
            }
            byte[] bArr2 = new byte[1024];
            while (true) {
                int read = cipherInputStream.read(bArr2);
                if (read != -1) {
                    fileOutputStream.write(bArr2, 0, read);
                    fileOutputStream.flush();
                } else {
                    fileOutputStream.close();
                    cipherInputStream.close();
                    fileInputStream.close();
                    return;
                }
            }
        } else {
            Log.i("AESUtil", "Key or sourceFilePath or destFilePath 为空");
        }
    }

    public static String a(byte[] bArr) {
        String str = "";
        if (bArr.length == 0) {
            return "";
        }
        for (int i = 0; i < bArr.length; i++) {
            String hexString = Integer.toHexString(bArr[i] & UByte.MAX_VALUE);
            if (hexString.length() == 1) {
                hexString = '0' + hexString;
            }
            if (i == bArr.length - 1) {
                str = str + hexString.toUpperCase();
            } else {
                str = str + hexString.toUpperCase() + ",";
            }
        }
        return str;
    }

    public static void a(String str, int i, String str2, String str3) {
        Cipher cipher;
        if (!TextUtils.isEmpty(str) && !TextUtils.isEmpty(str2) && !TextUtils.isEmpty(str3)) {
            File file = new File(str2);
            File file2 = new File(str3);
            if (!file.exists() || !file.isFile()) {
                return;
            }
            if (file2.exists() && !file2.delete()) {
                throw new Exception("删除原始文件失败");
            }
            if ((file2.getParentFile() != null && file2.getParentFile().exists()) || file2.getParentFile().mkdirs()) {
                if (file2.createNewFile()) {
                    try {
                        FileInputStream fileInputStream = new FileInputStream(file);
                        try {
                            FileOutputStream fileOutputStream = new FileOutputStream(file2);
                            try {
                                SecretKeySpec secretKeySpec = new SecretKeySpec(str.getBytes(StandardCharsets.UTF_8), "AES");
                                try {
                                    cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
                                    cipher.init(2, secretKeySpec);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    cipher = null;
                                }
                                CipherOutputStream cipherOutputStream = new CipherOutputStream(fileOutputStream, cipher);
                                try {
                                    byte[] bArr = new byte[1024];
                                    long j = i;
                                    if (fileInputStream.skip(j) == j) {
                                        while (true) {
                                            int read = fileInputStream.read(bArr);
                                            if (read == -1) {
                                                break;
                                            }
                                            cipherOutputStream.write(bArr, 0, read);
                                            cipherOutputStream.flush();
                                        }
                                    }
                                    cipherOutputStream.close();
                                    fileOutputStream.close();
                                    fileInputStream.close();
                                } finally {
                                }
                            } finally {
                            }
                        } finally {
                        }
                    } catch (Exception e2) {
                        Log.e("AESUtil", "decrypt error:" + e2);
                        throw e2;
                    }
                } else {
                    throw new Exception("创建解密文件失败");
                }
            } else {
                throw new Exception("创建父文件夹失败");
            }
        } else {
            Log.i("AESUtil", "Key or sourceFilePath or destFilePath 为空");
        }
    }

    public static synchronized byte[] a(Cipher cipher, byte[] bArr, long j, long j2, long j3) {
        synchronized (a.class) {
            int i = j2 > j ? 0 : (int) (j - j2);
            if (i > 0) {
                try {
                    int length = bArr.length - i;
                    if (!b(bArr) && length >= 0 && i >= 0 && i + length <= bArr.length) {
                        byte[] bArr2 = new byte[length];
                        System.arraycopy(bArr, i, bArr2, 0, length);
                        bArr = bArr2;
                    }
                    bArr = null;
                } catch (Throwable th) {
                    throw th;
                }
            }
            if (bArr == null) {
                Log.e("DataDecryptHelper", "finalData is null");
                return null;
            }
            Log.i("AESUtil", "getDecryptedData data:" + bArr.length + ", skipLength:" + i + ", offset:" + j2 + ", totalLength:" + j3);
            if (j2 + bArr.length + i >= j3) {
                return cipher.doFinal(bArr);
            }
            return cipher.update(bArr);
        }
    }
}
