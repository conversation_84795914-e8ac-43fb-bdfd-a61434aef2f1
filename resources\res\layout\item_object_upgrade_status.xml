<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:tag="layout/item_object_upgrade_status_0"
    android:background="@color/caui_config_content_bg_color_secondary"
    android:clipToPadding="false"
    android:layout_width="800dp"
    android:layout_height="100dp"
    android:layout_marginBottom="24dp"
    android:layout_marginEnd="64dp"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">
    <TextView
        android:textSize="44sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:gravity="center_vertical"
        android:id="@+id/tv_object_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="800dp"
        android:text="EDC_MCU"
        android:layout_marginStart="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.incall.apps.caui.widget.progress.CAUIProgressBar
        android:id="@+id/progress_object_install_status"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:layout_marginTop="24dp"
        android:layout_marginBottom="8dp"
        app:caui_progress_color="@color/caui_config_color_white"
        app:caui_progress_track_color="@color/caui_config_divider_color_secondary"
        app:caui_progress_track_width="640dp"
        app:caui_progress_type="type_rect"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>
    <TextView
        android:textSize="44sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:gravity="center_vertical"
        android:id="@+id/tv_install_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="800dp"
        android:text="安装状态"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="44sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:gravity="center_vertical"
        android:id="@+id/tv_install_times"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="800dp"
        android:text="安装次数"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
