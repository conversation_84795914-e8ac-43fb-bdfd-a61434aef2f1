{"submasterInfo": [{"smName": "SMA_EDC_A_QNX", "smIp": "************", "smPort": 13400, "smLogicalAddr": "0x0E03", "osName": "EDC_A_OS", "objlist": [{"objName": "EDC_A_OS", "ecuTypeCode": "EDC", "deploymentLocation": "EDC.A_OS", "installTime": 400, "smName": "SMA_EDC_A_QNX", "sota": 1, "ota": 1, "proto": "DDS", "dualBank": 1, "preUpdate": 1, "flashPower": "HIGH", "channel": "EDC_A_OS", "doipGatewayLogicalAddr": "/", "reqId": "/", "resId": "/", "smIp": "************", "ecuIp": "/", "logicalAddr": "/", "securityKey": "/", "didList": "/", "updateMethod": "U", "packageSuffix": "tgz", "smPort": 13400, "rebootObj": "EDC_MCU", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "f131", "softwareNumDid": "f130", "sotaList": [{"appId": "com.incall.apps.softmanager", "submaster": "SMA_EDC_A_ANDROID", "reboot": false, "installTime": "10", "channel": "EDC_A_OS"}, {"appId": "submaster", "submaster": "SMA_EDC_A_QNX", "reboot": true, "installTime": "10", "channel": "EDC_A_OS"}, {"appId": "EDC_RESOURCES", "submaster": "SMA_EDC_A_ANDROID", "reboot": true, "installTime": "400", "channel": "EDC_A_OS"}, {"appId": "com.incall.apps.intelligenthmi", "submaster": "SMA_EDC_A_ANDROID", "reboot": false, "installTime": "10", "channel": "EDC_A_OS"}, {"appId": "com.incall.apps.navi", "submaster": "SMA_EDC_A_ANDROID", "reboot": false, "installTime": "10", "channel": "EDC_A_OS"}, {"appId": "com.incall.apps.speechassistant", "submaster": "SMA_EDC_A_ANDROID", "reboot": false, "installTime": "10", "channel": "EDC_A_OS"}, {"appId": "com.changan.sound.data", "submaster": "SMA_EDC_A_ANDROID", "reboot": false, "installTime": "10", "channel": "EDC_A_OS"}, {"appId": "com.changan.so.lightlanguage", "submaster": "SMA_EDC_A_ANDROID", "reboot": false, "installTime": "10", "channel": "EDC_A_OS"}, {"appId": "com.incall.apps.fastCoPilot", "submaster": "SMA_EDC_A_ANDROID", "reboot": false, "installTime": "10", "channel": "EDC_A_OS"}, {"appId": "com.incall.apps.bluetooth", "submaster": "SMA_EDC_A_ANDROID", "reboot": false, "installTime": "10", "channel": "EDC_A_OS"}, {"appId": "com.incall.apps.sdamedia.onlinemusic", "submaster": "SMA_EDC_A_ANDROID", "reboot": false, "installTime": "10", "channel": "EDC_A_OS"}, {"appId": "com.incall.apps.sdamedia.bluetoothmusic", "submaster": "SMA_EDC_A_ANDROID", "reboot": false, "installTime": "10", "channel": "EDC_A_OS"}, {"appId": "com.incall.apps.sdamedia.usbmusic", "submaster": "SMA_EDC_A_ANDROID", "reboot": false, "installTime": "10", "channel": "EDC_A_OS"}, {"appId": "com.incall.apps.microworld", "submaster": "SMA_EDC_A_ANDROID", "reboot": false, "installTime": "10", "channel": "EDC_A_OS"}, {"appId": "com.incall.apps.album", "submaster": "SMA_EDC_A_ANDROID", "reboot": false, "installTime": "10", "channel": "EDC_A_OS"}, {"appId": "com.incall.apps.dvr", "submaster": "SMA_EDC_A_ANDROID", "reboot": false, "installTime": "10", "channel": "EDC_A_OS"}, {"appId": "com.incall.apps.sentry", "submaster": "SMA_EDC_A_ANDROID", "reboot": false, "installTime": "10", "channel": "EDC_A_OS"}, {"appId": "com.incall.apps.notification", "submaster": "SMA_EDC_A_ANDROID", "reboot": false, "installTime": "10", "channel": "EDC_A_OS"}, {"appId": "com.incall.apps.weather", "submaster": "SMA_EDC_A_ANDROID", "reboot": false, "installTime": "10", "channel": "EDC_A_OS"}, {"appId": "com.incall.apps.energy", "submaster": "SMA_EDC_A_ANDROID", "reboot": false, "installTime": "10", "channel": "EDC_A_OS"}, {"appId": "com.incall.apps.scenebrain", "submaster": "SMA_EDC_A_ANDROID", "reboot": false, "installTime": "10", "channel": "EDC_A_OS"}, {"appId": "com.incall.apps.speechassistant", "submaster": "SMA_EDC_A_ANDROID", "reboot": false, "installTime": "10", "channel": "EDC_A_OS"}, {"appId": "com.incall.apps.speechadapter", "submaster": "SMA_EDC_A_ANDROID", "reboot": false, "installTime": "10", "channel": "EDC_A_OS"}], "subModuleList": [], "linMessage": {}}, {"objName": "EDC_MCU", "ecuTypeCode": "EDC", "deploymentLocation": "EDC.MCU", "installTime": 90, "smName": "SMA_EDC_A_QNX", "sota": 0, "ota": 1, "proto": "DOIP", "dualBank": 1, "preUpdate": 1, "flashPower": "HIGH", "channel": "ETH_EDC_MCU", "doipGatewayLogicalAddr": "/", "reqId": "0x713", "resId": "0x71B", "smIp": "************", "ecuIp": "************", "logicalAddr": "0x1713", "securityKey": "0x71F96C1D", "didList": "f189/f188/f187/f089/f170", "updateMethod": "U", "packageSuffix": "sdb", "smPort": 13400, "rebootObj": "EDC_MCU", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [], "linMessage": {}}, {"objName": "EDC_SWITCH", "ecuTypeCode": "EDC", "deploymentLocation": "EDC.SWITCH", "installTime": 40, "smName": "SMA_EDC_A_QNX", "sota": 0, "ota": 1, "proto": "DEV", "dualBank": 0, "preUpdate": 0, "flashPower": "HIGH", "channel": "EDC_SWITCH", "doipGatewayLogicalAddr": "/", "reqId": "/", "resId": "/", "smIp": "************", "ecuIp": "/", "logicalAddr": "/", "securityKey": "/", "didList": "/", "updateMethod": "USR", "packageSuffix": "img", "smPort": 13400, "rebootObj": "EDC_SWITCH", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "f133", "softwareNumDid": "f132", "sotaList": [], "subModuleList": [], "linMessage": {}}, {"objName": "AMP", "ecuTypeCode": "AMP", "deploymentLocation": "AMP", "installTime": 25, "smName": "SMA_EDC_A_QNX", "sota": 0, "ota": 1, "proto": "CAN", "dualBank": 0, "preUpdate": 0, "flashPower": "HIGH", "channel": "EDC_CANFD", "doipGatewayLogicalAddr": "0x1713", "reqId": "0x735", "resId": "0x73D", "smIp": "************", "ecuIp": "************", "logicalAddr": "0x1735", "securityKey": "0xC4658A3E", "didList": "f189/f188/f187/f089/f170", "updateMethod": "USR", "packageSuffix": "sdb", "smPort": 13400, "rebootObj": "AMP", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [], "linMessage": {}}, {"objName": "VIU_FL", "ecuTypeCode": "VIU_FL", "deploymentLocation": "VIU_FL", "installTime": 95, "smName": "SMA_EDC_A_QNX", "sota": 0, "ota": 1, "proto": "DOIP", "dualBank": 0, "preUpdate": 0, "flashPower": "HIGH", "channel": "ETH_VIU_FL", "doipGatewayLogicalAddr": "/", "reqId": "0x700", "resId": "0x708", "smIp": "************", "ecuIp": "************", "logicalAddr": "0x1700", "securityKey": "0x7FE47516", "didList": "f189/f188/f187/f089/f170", "updateMethod": "USR", "packageSuffix": "sdb", "smPort": 13400, "rebootObj": "VIU_FL", "versionConnect": "N", "isSplice": 0, "isImportantObj": 1, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [{"subModuleName": "VIU_FL_SWITCH", "softwareVersionDid": "B11B"}, {"subModuleName": "VIU_FL_BLUE", "softwareVersionDid": "0E23"}, {"subModuleName": "VIU_FL_SEC", "softwareVersionDid": "0E28"}], "linMessage": {}}, {"objName": "VIU_FR", "ecuTypeCode": "VIU_FR", "deploymentLocation": "VIU_FR", "installTime": 95, "smName": "SMA_EDC_A_QNX", "sota": 0, "ota": 1, "proto": "DOIP", "dualBank": 0, "preUpdate": 0, "flashPower": "HIGH", "channel": "ETH_VIU_FR", "doipGatewayLogicalAddr": "/", "reqId": "0x720", "resId": "0x728", "smIp": "************", "ecuIp": "************", "logicalAddr": "0x1720", "securityKey": "0x5E6D3E5F", "didList": "f189/f188/f187/f089/f170", "updateMethod": "USR", "packageSuffix": "sdb", "smPort": 13400, "rebootObj": "VIU_FR", "versionConnect": "N", "isSplice": 0, "isImportantObj": 1, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [{"subModuleName": "VIU_FR_SWITCH", "softwareVersionDid": "B11B"}, {"subModuleName": "VIU_FR_BLUE", "softwareVersionDid": "0E23"}, {"subModuleName": "VIU_FR_SEC", "softwareVersionDid": "0E28"}], "linMessage": {}}, {"objName": "BLE1", "ecuTypeCode": "BLE", "deploymentLocation": "BLE1", "installTime": 300, "smName": "SMA_EDC_A_QNX", "sota": 0, "ota": 1, "proto": "LIN", "dualBank": 0, "preUpdate": 0, "flashPower": "HIGH", "channel": "VIUFL_LIN6", "doipGatewayLogicalAddr": "0x1700", "reqId": "0x793", "resId": "0x79B", "smIp": "************", "ecuIp": "************", "logicalAddr": "0x1793", "securityKey": "0x18E68B3A", "didList": "0E37/0E39/0E36/0E38", "updateMethod": "USR", "packageSuffix": "sdb", "smPort": 13400, "rebootObj": "BLE1", "versionConnect": "N", "isSplice": 1, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [], "linMessage": {"linMaster": "VIU_FL", "masterAddr": "0x1700", "masterKey": "0x7FE47516"}}, {"objName": "BLE2", "ecuTypeCode": "BLE", "deploymentLocation": "BLE2", "installTime": 300, "smName": "SMA_EDC_A_QNX", "sota": 0, "ota": 1, "proto": "LIN", "dualBank": 0, "preUpdate": 0, "flashPower": "HIGH", "channel": "VIUFL_LIN6", "doipGatewayLogicalAddr": "0x1700", "reqId": "0x794", "resId": "0x79C", "smIp": "************", "ecuIp": "************", "logicalAddr": "0x1794", "securityKey": "0x18E68B3B", "didList": "0E3B/0E3D/0E3A/0E3C", "updateMethod": "USR", "packageSuffix": "sdb", "smPort": 13400, "rebootObj": "BLE2", "versionConnect": "N", "isSplice": 1, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [], "linMessage": {"linMaster": "VIU_FL", "masterAddr": "0x1700", "masterKey": "0x7FE47516"}}, {"objName": "BLE3", "ecuTypeCode": "BLE", "deploymentLocation": "BLE3", "installTime": 300, "smName": "SMA_EDC_A_QNX", "sota": 0, "ota": 1, "proto": "LIN", "dualBank": 0, "preUpdate": 0, "flashPower": "HIGH", "channel": "VIUFL_LIN6", "doipGatewayLogicalAddr": "0x1700", "reqId": "0x795", "resId": "0x79D", "smIp": "************", "ecuIp": "************", "logicalAddr": "0x1795", "securityKey": "0x18E68B3C", "didList": "0E3F/0E41/0E3E/0E40", "updateMethod": "USR", "packageSuffix": "sdb", "smPort": 13400, "rebootObj": "BLE3", "versionConnect": "N", "isSplice": 1, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [], "linMessage": {"linMaster": "VIU_FL", "masterAddr": "0x1700", "masterKey": "0x7FE47516"}}, {"objName": "BLE4", "ecuTypeCode": "BLE", "deploymentLocation": "BLE4", "installTime": 300, "smName": "SMA_EDC_A_QNX", "sota": 0, "ota": 1, "proto": "LIN", "dualBank": 0, "preUpdate": 0, "flashPower": "HIGH", "channel": "VIUFL_LIN6", "doipGatewayLogicalAddr": "0x1700", "reqId": "0x796", "resId": "0x79E", "smIp": "************", "ecuIp": "************", "logicalAddr": "0x1796", "securityKey": "0x18E68B3D", "didList": "0E43/0E45/0E42/0E44", "updateMethod": "USR", "packageSuffix": "sdb", "smPort": 13400, "rebootObj": "BLE4", "versionConnect": "N", "isSplice": 1, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [], "linMessage": {"linMaster": "VIU_FL", "masterAddr": "0x1700", "masterKey": "0x7FE47516"}}, {"objName": "WLCM", "ecuTypeCode": "WLCM", "deploymentLocation": "WLCM", "installTime": 44, "smName": "SMA_EDC_A_QNX", "sota": 0, "ota": 1, "proto": "CAN", "dualBank": 0, "preUpdate": 0, "flashPower": "HIGH", "channel": "VIUF2_CANFD", "doipGatewayLogicalAddr": "0x1700", "reqId": "0x732", "resId": "0x73A", "smIp": "************", "ecuIp": "************", "logicalAddr": "0x1732", "securityKey": "0x8A6B374A", "didList": "f189/f188/f187/f089/f170", "updateMethod": "USR", "packageSuffix": "sdb", "smPort": 13400, "rebootObj": "WLCM", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [], "linMessage": {}}, {"objName": "AR-HUD", "ecuTypeCode": "AR_HUD", "deploymentLocation": "AR-HUD", "installTime": 110, "smName": "SMA_EDC_A_QNX", "sota": 0, "ota": 1, "proto": "DEV", "dualBank": 0, "preUpdate": 0, "flashPower": "HIGH", "channel": "AR-HUD", "doipGatewayLogicalAddr": "/", "reqId": "/", "resId": "/", "smIp": "************", "ecuIp": "/", "logicalAddr": "/", "securityKey": "/", "didList": "/", "updateMethod": "USR", "packageSuffix": "bin", "smPort": 13400, "rebootObj": "AR-HUD", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "f139", "softwareNumDid": "f138", "sotaList": [], "subModuleList": [], "linMessage": {}}, {"objName": "BEIDOU_IVI", "ecuTypeCode": "IVI", "deploymentLocation": "BEIDOU_IVI", "installTime": 50, "smName": "SMA_EDC_A_QNX", "sota": 0, "ota": 1, "proto": "DEV", "dualBank": 2, "preUpdate": 0, "flashPower": "HIGH", "channel": "BEIDOU_IVI", "doipGatewayLogicalAddr": "/", "reqId": "/", "resId": "/", "smIp": "************", "ecuIp": "/", "logicalAddr": "/", "securityKey": "/", "didList": "/", "updateMethod": "USR", "packageSuffix": "zip", "smPort": 13400, "rebootObj": "BEIDOU_IVI", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "f137", "softwareNumDid": "f136", "sotaList": [], "subModuleList": [], "linMessage": {}}, {"objName": "EPS", "ecuTypeCode": "EPS", "deploymentLocation": "EPS", "installTime": 60, "smName": "SMA_EDC_A_QNX", "sota": 0, "ota": 1, "proto": "CAN", "dualBank": 0, "preUpdate": 0, "flashPower": "HIGH", "channel": "CHS_CANFD", "doipGatewayLogicalAddr": "0x1700", "reqId": "0x785", "resId": "0x78D", "smIp": "************", "ecuIp": "************", "logicalAddr": "0x1785", "securityKey": "0x70F77302", "didList": "f189/f188/f187/f089/f170", "updateMethod": "USR", "packageSuffix": "sdb", "smPort": 13400, "rebootObj": "EPS", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [], "linMessage": {}}, {"objName": "VMC", "ecuTypeCode": "VMC", "deploymentLocation": "VMC", "installTime": 40, "smName": "SMA_EDC_A_QNX", "sota": 0, "ota": 1, "proto": "CAN", "dualBank": 1, "preUpdate": 0, "flashPower": "HIGH", "channel": "VIUF2_CANFD", "doipGatewayLogicalAddr": "0x1700", "reqId": "0x723", "resId": "0X72B", "smIp": "************", "ecuIp": "************", "logicalAddr": "0x1723", "securityKey": "0xCFA0D2EF", "didList": "f189/f188/f187/f089/f170", "updateMethod": "USR", "packageSuffix": "sdb", "smPort": 13400, "rebootObj": "VMC", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [], "linMessage": {}}, {"objName": "IBCU", "ecuTypeCode": "IBCU", "deploymentLocation": "IBCU", "installTime": 120, "smName": "SMA_EDC_A_QNX", "sota": 0, "ota": 1, "proto": "CAN", "dualBank": 0, "preUpdate": 0, "flashPower": "HIGH", "channel": "R_CHCANFD", "doipGatewayLogicalAddr": "0x1700", "reqId": "0x771", "resId": "0x779", "smIp": "************", "ecuIp": "************", "logicalAddr": "0x1771", "securityKey": "0xC92B84EA", "didList": "f189/f188/f187/f089/f170", "updateMethod": "USR", "packageSuffix": "sdb", "smPort": 13400, "rebootObj": "IBCU", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [], "linMessage": {}}, {"objName": "OBC", "ecuTypeCode": "OBC", "deploymentLocation": "OBC", "installTime": 100, "smName": "SMA_EDC_A_QNX", "sota": 0, "ota": 1, "proto": "CAN", "dualBank": 0, "preUpdate": 0, "flashPower": "LOW", "channel": "PT_CANFD", "doipGatewayLogicalAddr": "0x1720", "reqId": "0x7E7", "resId": "0x7EF", "smIp": "************", "ecuIp": "************", "logicalAddr": "0x17E7", "securityKey": "0x3644FE76", "didList": "f189/f188/f187/f089/f170", "updateMethod": "USR", "packageSuffix": "sdb", "smPort": 13400, "rebootObj": "OBC", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [], "linMessage": {}}, {"objName": "DPEU", "ecuTypeCode": "DPEU", "deploymentLocation": "DPEU", "installTime": 120, "smName": "SMA_EDC_A_QNX", "sota": 0, "ota": 1, "proto": "CAN", "dualBank": 0, "preUpdate": 0, "flashPower": "HIGH", "channel": "PT_CANFD", "doipGatewayLogicalAddr": "0x1720", "reqId": "0x7E3", "resId": "0x7EB", "smIp": "************", "ecuIp": "************", "logicalAddr": "0x17E3", "securityKey": "0x3B8EFD6A", "didList": "f189/f188/f187/f089/f170", "updateMethod": "USR", "packageSuffix": "sdb", "smPort": 13400, "rebootObj": "DPEU", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [], "linMessage": {}}, {"objName": "EMS", "ecuTypeCode": "EMS", "deploymentLocation": "EMS", "installTime": 120, "smName": "SMA_EDC_A_QNX", "sota": 0, "ota": 1, "proto": "CAN", "dualBank": 0, "preUpdate": 0, "flashPower": "HIGH", "channel": "PT_CANFD", "doipGatewayLogicalAddr": "0x1720", "reqId": "0x7E0", "resId": "0x7E8", "smIp": "************", "ecuIp": "************", "logicalAddr": "0x17E0", "securityKey": "0x70F27304", "didList": "f189/f188/f187/f089/f170", "updateMethod": "USR", "packageSuffix": "sdb", "smPort": 13400, "rebootObj": "EMS", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [], "linMessage": {}}, {"objName": "BMS", "ecuTypeCode": "BMS", "deploymentLocation": "BMS", "installTime": 50, "smName": "SMA_EDC_A_QNX", "sota": 0, "ota": 1, "proto": "CAN", "dualBank": 0, "preUpdate": 0, "flashPower": "LOW", "channel": "PT_CANFD", "doipGatewayLogicalAddr": "0x1720", "reqId": "0x7A1", "resId": "0x7A9", "smIp": "************", "ecuIp": "************", "logicalAddr": "0x17A1", "securityKey": "0x70F27304", "didList": "f189/f188/f187/f089/f170", "updateMethod": "USR", "packageSuffix": "sdb", "smPort": 13400, "rebootObj": "BMS", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [], "linMessage": {}}, {"objName": "NFC", "ecuTypeCode": "NFC", "deploymentLocation": "NFC", "installTime": 25, "smName": "SMA_EDC_A_QNX", "sota": 0, "ota": 1, "proto": "CAN", "dualBank": 0, "preUpdate": 0, "flashPower": "HIGH", "channel": "VIUF2_CANFD", "doipGatewayLogicalAddr": "0x1700", "reqId": "0x712", "resId": "0x71A", "smIp": "************", "ecuIp": "************", "logicalAddr": "0x1712", "securityKey": "0x38FCB60E", "didList": "f189/f188/f187/f089/f170", "updateMethod": "USR", "packageSuffix": "sdb", "smPort": 13400, "rebootObj": "NFC", "versionConnect": "N", "isSplice": 0, "isImportantObj": 1, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [], "linMessage": {}}]}, {"smName": "SMA_TBOX", "smIp": "/", "smPort": 13400, "smLogicalAddr": "0x0E06", "osName": "TBOX_4G_OS", "objlist": [{"objName": "TBOX_4G_OS", "ecuTypeCode": "EDC", "deploymentLocation": "EDC.TBOX_4G_OS", "installTime": 75, "smName": "SMA_TBOX", "sota": 1, "ota": 1, "proto": "DDS", "dualBank": 1, "preUpdate": 1, "flashPower": "HIGH", "channel": "TBOX_4G_OS", "doipGatewayLogicalAddr": "/", "reqId": "/", "resId": "/", "smIp": "/", "ecuIp": "/", "logicalAddr": "/", "securityKey": "/", "didList": "/", "updateMethod": "U", "packageSuffix": "zip", "smPort": 13400, "rebootObj": "EDC_MCU", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "f135", "softwareNumDid": "f134", "sotaList": [{"appId": "tbox-submaster", "submaster": "SMA_TBOX", "reboot": true, "installTime": "10", "channel": "TBOX_4G_OS"}], "subModuleList": [], "linMessage": {}}]}, {"smName": "SMA_EDC_A_ANDROID", "smIp": "***********", "smPort": 13400, "smLogicalAddr": "0x0E07", "osName": "EDC_A_OS", "objlist": [{"objName": "EDC_ANDROID", "ecuTypeCode": "EDC", "deploymentLocation": "EDC.ANDROID", "installTime": 0, "smName": "SMA_EDC_A_ANDROID", "sota": 0, "ota": 1, "proto": "DDS", "dualBank": 1, "preUpdate": 1, "flashPower": "HIGH", "channel": "EDC_ANDROID", "doipGatewayLogicalAddr": "/", "reqId": "/", "resId": "/", "smIp": "***********", "ecuIp": "/", "logicalAddr": "/", "securityKey": "/", "didList": "/", "updateMethod": "U", "packageSuffix": "apk", "smPort": 13400, "rebootObj": "EDC_ANDROID", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "f189", "softwareNumDid": "f188", "sotaList": [], "subModuleList": [], "linMessage": {}}]}, {"smName": "SMA_M_ADC", "smIp": "************", "smPort": 13400, "smLogicalAddr": "0x0E08", "osName": "M_ADC_OS", "objlist": [{"objName": "M_ADC_OS", "ecuTypeCode": "M_ADC", "deploymentLocation": "M_ADC.OS", "installTime": 190, "smName": "SMA_M_ADC", "sota": 1, "ota": 1, "proto": "DDS", "dualBank": 1, "preUpdate": 0, "flashPower": "HIGH", "channel": "M_ADC_OS", "doipGatewayLogicalAddr": "/", "reqId": "/", "resId": "/", "smIp": "************", "ecuIp": "/", "logicalAddr": "/", "securityKey": "/", "didList": "f189/f188/f187/f089/f170", "updateMethod": "U", "packageSuffix": "zip", "smPort": 13400, "rebootObj": "M_ADC_MCU", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [{"appId": "submaster", "submaster": "SMA_M_ADC", "reboot": false, "installTime": "10", "channel": "M_ADC_OS"}], "subModuleList": [], "linMessage": {}}, {"objName": "M_ADC_MCU", "ecuTypeCode": "M_ADC", "deploymentLocation": "M_ADC.MCU", "installTime": 380, "smName": "SMA_M_ADC", "sota": 0, "ota": 1, "proto": "DEV", "dualBank": 1, "preUpdate": 0, "flashPower": "HIGH", "channel": "M_ADC_MCU", "doipGatewayLogicalAddr": "/", "reqId": "/", "resId": "/", "smIp": "************", "ecuIp": "/", "logicalAddr": "/", "securityKey": "/", "didList": "f189/f188/f187/f089/f170", "updateMethod": "U", "packageSuffix": "s19", "smPort": 13400, "rebootObj": "M_ADC_MCU", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [], "linMessage": {}}, {"objName": "M_ADC_camera", "ecuTypeCode": "M_ADC", "deploymentLocation": "M_ADC.CAMERA", "installTime": 0, "smName": "SMA_M_ADC", "sota": 0, "ota": 1, "proto": "DEV", "dualBank": 0, "preUpdate": 0, "flashPower": "HIGH", "channel": "M_ADC_CAMERA", "doipGatewayLogicalAddr": "/", "reqId": "/", "resId": "/", "smIp": "************", "ecuIp": "/", "logicalAddr": "/", "securityKey": "/", "didList": "/", "updateMethod": "USR", "packageSuffix": "bin", "smPort": 13400, "rebootObj": "M_ADC_MCU", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [], "linMessage": {}}, {"objName": "RLR_L", "ecuTypeCode": "RADAR", "deploymentLocation": "RLR_L", "installTime": 40, "smName": "SMA_M_ADC", "sota": 0, "ota": 1, "proto": "SCAN", "dualBank": 2, "preUpdate": 0, "flashPower": "HIGH", "channel": "ADCC_CANFD", "doipGatewayLogicalAddr": "0x17D2", "reqId": "0x7B1", "resId": "0x7B9", "smIp": "************", "ecuIp": "/", "logicalAddr": "0x17B1", "securityKey": "0xAA230B5B", "didList": "f189/f188/f187/f089/f170", "updateMethod": "USR", "packageSuffix": "zip", "smPort": 13400, "rebootObj": "RLR_L", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [], "linMessage": {}}, {"objName": "RRR_L", "ecuTypeCode": "RADAR", "deploymentLocation": "RRR_L", "installTime": 40, "smName": "SMA_M_ADC", "sota": 0, "ota": 1, "proto": "SCAN", "dualBank": 2, "preUpdate": 0, "flashPower": "HIGH", "channel": "ADCC_CANFD", "doipGatewayLogicalAddr": "0x17D2", "reqId": "0x7B2", "resId": "0x7BA", "smIp": "************", "ecuIp": "/", "logicalAddr": "0x17B2", "securityKey": "0xAA230B5B", "didList": "f189/f188/f187/f089/f170", "updateMethod": "USR", "packageSuffix": "zip", "smPort": 13400, "rebootObj": "RRR_L", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [], "linMessage": {}}, {"objName": "FLR_L", "ecuTypeCode": "RADAR", "deploymentLocation": "FLR_L", "installTime": 40, "smName": "SMA_M_ADC", "sota": 0, "ota": 1, "proto": "SCAN", "dualBank": 2, "preUpdate": 0, "flashPower": "HIGH", "channel": "ADCB_CANFD", "doipGatewayLogicalAddr": "0x17D2", "reqId": "0x7D0", "resId": "0x7D8", "smIp": "************", "ecuIp": "/", "logicalAddr": "0x17D0", "securityKey": "0xAA230B5B", "didList": "f189/f188/f187/f089/f170", "updateMethod": "USR", "packageSuffix": "zip", "smPort": 13400, "rebootObj": "FLR_L", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [], "linMessage": {}}, {"objName": "FRR_L", "ecuTypeCode": "RADAR", "deploymentLocation": "FRR_L", "installTime": 40, "smName": "SMA_M_ADC", "sota": 0, "ota": 1, "proto": "SCAN", "dualBank": 2, "preUpdate": 0, "flashPower": "HIGH", "channel": "ADCB_CANFD", "doipGatewayLogicalAddr": "0x17D2", "reqId": "0x7D1", "resId": "0x7D9", "smIp": "************", "ecuIp": "/", "logicalAddr": "0x17D1", "securityKey": "0xAA230B5B", "didList": "f189/f188/f187/f089/f170", "updateMethod": "USR", "packageSuffix": "zip", "smPort": 13400, "rebootObj": "FRR_L", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [], "linMessage": {}}, {"objName": "FR", "ecuTypeCode": "FR", "deploymentLocation": "FR", "installTime": 30, "smName": "SMA_M_ADC", "sota": 0, "ota": 1, "proto": "SCAN", "dualBank": 1, "preUpdate": 0, "flashPower": "HIGH", "channel": "ADCA_CANFD", "doipGatewayLogicalAddr": "0x17D2", "reqId": "0x765", "resId": "0x76D", "smIp": "************", "ecuIp": "/", "logicalAddr": "0x1765", "securityKey": "0x52712B6F", "didList": "f189/f188/f187/f089/f170", "updateMethod": "USR", "packageSuffix": "sdb", "smPort": 13400, "rebootObj": "FR", "versionConnect": "N", "isSplice": 0, "isImportantObj": 0, "objAttribute": "**********", "softwareVerDid": "F189", "softwareNumDid": "F188", "sotaList": [], "subModuleList": [], "linMessage": {}}]}], "updateSequence": [{"groupNum": 1, "firstUpdateObj": "VIU_FL", "afterUpdateObj": "VIU_FR*EDC_A_OS*EDC_MCU*TBOX_4G_OS*AMP*BLE1*BLE2*BLE3*BLE4*NFC*WLCM*BMS*EPS*IBCU*AR-HUD*FR*EDC_SWITCH*M_ADC_OS*M_ADC_MCU*M_ADC_camera*RLR_L*RRR_L*FLR_L*FRR_L*EMS*DPEU*OBC*BEIDOU_IVI*VMC"}, {"groupNum": 2, "firstUpdateObj": "VIU_FR", "afterUpdateObj": "EDC_A_OS*EDC_MCU*TBOX_4G_OS*AMP*BLE1*BLE2*BLE3*BLE4*NFC*WLCM*BMS*EPS*IBCU*AR-HUD*FR*EDC_SWITCH*M_ADC_OS*M_ADC_MCU*M_ADC_camera*RLR_L*RRR_L*FLR_L*FRR_L*EMS*DPEU*OBC*BEIDOU_IVI*VMC"}, {"groupNum": 3, "firstUpdateObj": "EDC_A_OS*EDC_MCU*TBOX_4G_OS", "afterUpdateObj": "AMP*BLE1*BLE2*BLE3*BLE4*NFC*WLCM*BMS*EPS*IBCU*AR-HUD*FR*EDC_SWITCH*M_ADC_OS*M_ADC_MCU*M_ADC_camera*RLR_L*RRR_L*FLR_L*FRR_L*EMS*DPEU*OBC*VMC"}, {"groupNum": 4, "firstUpdateObj": "EDC_A_OS*EDC_MCU*TBOX_4G_OS*AMP*BLE1*BLE2*BLE3*BLE4*NFC*WLCM*BMS*EPS*IBCU*AR-HUD*FR*EDC_SWITCH*M_ADC_OS*M_ADC_MCU*M_ADC_camera*RLR_L*RRR_L*FLR_L*FRR_L*EMS*DPEU*OBC*VMC", "afterUpdateObj": "BEIDOU_IVI"}, {"groupNum": 5, "firstUpdateObj": "EDC_A_OS*EDC_MCU*TBOX_4G_OS*AMP*BLE1*BLE2*BLE3*BLE4*NFC*WLCM*BMS*EPS*IBCU*AR-HUD*FR*M_ADC_OS*M_ADC_MCU*M_ADC_camera*RLR_L*RRR_L*FLR_L*FRR_L*EMS*DPEU*OBC*VMC", "afterUpdateObj": "EDC_SWITCH"}, {"groupNum": 6, "firstUpdateObj": "EDC_A_OS*EDC_MCU*TBOX_4G_OS*AMP*BLE1*BLE2*BLE3*BLE4*NFC*WLCM*EPS*IBCU*AR-HUD*FR*M_ADC_OS*M_ADC_MCU*M_ADC_camera*RLR_L*RRR_L*FLR_L*FRR_L*EMS*DPEU*VMC", "afterUpdateObj": "OBC*BMS"}, {"groupNum": 7, "firstUpdateObj": "EDC_MCU", "afterUpdateObj": "AMP"}, {"groupNum": 8, "firstUpdateObj": "M_ADC_MCU", "afterUpdateObj": "RLR_L*RRR_L*FLR_L*FRR_L"}], "rebootSequence": [{"groupNum": 1, "firstRebootObj": "M_ADC_OS*M_ADC_MCU", "afterRebootObj": "EDC_A_OS*EDC_MCU"}, {"groupNum": 2, "firstRebootObj": "EDC_A_OS", "afterRebootObj": "EDC_MCU"}], "userPerception": 1}