package com.ca.car.proxy;

import android.content.Context;
import android.database.ContentObserver;
import android.os.Message;
import android.provider.Settings;
import com.ca.car.proxy.CarServiceStatusCallbacks;
import com.ca.car.proxy.utils.CarCallbackHelper;
import com.ca.car.proxy.utils.CarConstants;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/* loaded from: classes.dex */
final class CarServiceStatusCallbackManager extends CarCallbackHelper {
    private static CarServiceStatusCallbackManager mCarServiceStatusCallbackManager;
    private String TAG = "CarServiceStatusCallbackManager";
    private CarCallbackHelper.CarObserver mCarServiceStatusObserver = null;
    private ConcurrentHashMap<String, CarServiceStatusCallbacks.ICarServiceStatusLisener> mMap = new ConcurrentHashMap<>();

    private CarServiceStatusCallbackManager() {
    }

    public static synchronized CarServiceStatusCallbackManager getInstance() {
        CarServiceStatusCallbackManager carServiceStatusCallbackManager;
        synchronized (CarServiceStatusCallbackManager.class) {
            if (mCarServiceStatusCallbackManager == null) {
                mCarServiceStatusCallbackManager = new CarServiceStatusCallbackManager();
            }
            CarServiceStatusCallbackManager carServiceStatusCallbackManager2 = mCarServiceStatusCallbackManager;
            if (carServiceStatusCallbackManager2.mCarServiceStatusObserver == null) {
                carServiceStatusCallbackManager2.initCallback();
            }
            carServiceStatusCallbackManager = mCarServiceStatusCallbackManager;
        }
        return carServiceStatusCallbackManager;
    }

    @Override // com.ca.car.proxy.utils.CarCallbackHelper
    protected void initCallback() {
        this.mCarServiceStatusObserver = new CarCallbackHelper.CarObserver(getTaskHandler(this.TAG), 101);
    }

    @Override // com.ca.car.proxy.utils.CarCallbackHelper
    protected void parseMsg(Message message) {
        if (message.what == 101) {
            synchronized (this.mMap) {
                for (final Map.Entry<String, CarServiceStatusCallbacks.ICarServiceStatusLisener> entry : this.mMap.entrySet()) {
                    getMainHandler().post(new Runnable() { // from class: com.ca.car.proxy.CarServiceStatusCallbackManager.1
                        @Override // java.lang.Runnable
                        public void run() {
                            ((CarServiceStatusCallbacks.ICarServiceStatusLisener) entry.getValue()).report(true);
                        }
                    });
                }
            }
        }
    }

    @Override // com.ca.car.proxy.utils.CarCallbackHelper
    protected void registerObserver(Context context, String str, ContentObserver contentObserver) {
        if (this.mContext == null) {
            this.mContext = context.getApplicationContext();
        }
        this.mContext.getContentResolver().registerContentObserver(Settings.System.getUriFor(str), false, contentObserver);
    }

    @Override // com.ca.car.proxy.utils.CarCallbackHelper
    protected void unregisterObserver(ContentObserver contentObserver) {
        if (this.mContext != null) {
            this.mContext.getContentResolver().unregisterContentObserver(contentObserver);
        }
    }

    public void registerServiceStatusListener(Context context, String str, CarServiceStatusCallbacks.ICarServiceStatusLisener iCarServiceStatusLisener) {
        synchronized (this.mMap) {
            if (this.mMap.containsKey(str)) {
                this.mMap.replace(str, iCarServiceStatusLisener);
            } else {
                this.mMap.put(str, iCarServiceStatusLisener);
                if (this.mMap.size() == 1) {
                    registerObserver(context, CarConstants.CAR_URI_SERVICE_STATUS, this.mCarServiceStatusObserver);
                }
            }
        }
    }

    public void unRegisterServiceStatusListener(String str) {
        synchronized (this.mMap) {
            if (this.mMap.containsKey(str)) {
                this.mMap.remove(str);
            }
            if (this.mMap.size() == 0) {
                unregisterObserver(this.mCarServiceStatusObserver);
            }
        }
    }
}
