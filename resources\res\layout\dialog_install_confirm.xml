<?xml version="1.0" encoding="utf-8"?>
<com.incall.apps.caui.shape.CAUIShapeConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:tag="layout/dialog_install_confirm_0"
    android:background="@color/transparent"
    android:layout_width="1280dp"
    android:layout_height="1024dp"
    android:minWidth="1280dp"
    android:minHeight="1024dp">
    <ImageView
        android:id="@+id/install_confirm_iv"
        android:background="@drawable/caui_icon_dialog_close"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="55dp"
        android:layout_marginStart="64dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.incall.apps.caui.layout.CAUITextView
        android:textSize="44sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/dialog_install_confirm_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="48dp"
        android:text="@string/confirm_dialog_title"
        android:layout_marginStart="13dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.incall.apps.caui.layout.CAUITextView
        android:textSize="32sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/tx_note"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:text="@string/confirm_dialog_tip"
        android:layout_marginStart="64dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/dialog_install_confirm_title"/>
    <androidx.recyclerview.widget.RecyclerView
        android:scrollbarThumbVertical="@drawable/fota_scroll_thumb"
        android:scrollbarStyle="outsideOverlay"
        android:id="@+id/confirm_upgrade_tip_recyclerview"
        android:scrollbars="vertical"
        android:layout_width="match_parent"
        android:layout_height="528dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="34dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toTopOf="@+id/check_box_tip"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tx_note"/>
    <CheckBox
        android:id="@+id/protocol_checkbox"
        android:background="@drawable/caui_checkbox_button_with_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:button="@null"
        android:layout_marginStart="61dp"
        android:layout_marginEnd="1168dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/confirm_upgrade_tip_recyclerview"/>
    <com.incall.apps.caui.layout.CAUITextView
        android:textSize="32sp"
        android:textColor="@color/caui_config_text_color_secondary"
        android:id="@+id/check_box_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="34dp"
        android:text="@string/confirm_dialog_read_and_agree"
        android:layout_marginStart="24dp"
        app:layout_constraintStart_toEndOf="@+id/protocol_checkbox"
        app:layout_constraintTop_toBottomOf="@+id/confirm_upgrade_tip_recyclerview"/>
    <com.incall.apps.caui.layout.CAUITextView
        android:textSize="32sp"
        android:textColor="@color/caui_config_text_theme_color"
        android:gravity="left"
        android:id="@+id/tx_protocol"
        android:clickable="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="34dp"
        android:text="@string/confirm_dialog_protocol"
        android:contentDescription="[查看]车辆升级服务协议"
        android:layout_marginStart="6dp"
        app:layout_constraintStart_toEndOf="@+id/check_box_tip"
        app:layout_constraintTop_toBottomOf="@+id/confirm_upgrade_tip_recyclerview"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/btn_confirm"
        android:layout_width="544dp"
        android:layout_height="96dp"
        android:layout_marginTop="34dp"
        android:layout_marginBottom="56dp"
        android:text="@string/install_confirm_dialog_btn"
        android:contentDescription="确定"
        android:layout_marginStart="63dp"
        android:layout_marginEnd="671dp"
        app:caui_round_btn_type="main"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/check_box_tip"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/install_dialog_btn_cancel"
        android:layout_width="544dp"
        android:layout_height="96dp"
        android:layout_marginTop="34dp"
        android:layout_marginBottom="56dp"
        android:text="@string/install_cancel_dialog_btn"
        android:contentDescription="取消"
        android:layout_marginStart="671dp"
        android:layout_marginEnd="63dp"
        app:caui_round_btn_type="negative"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/check_box_tip"/>
</com.incall.apps.caui.shape.CAUIShapeConstraintLayout>
