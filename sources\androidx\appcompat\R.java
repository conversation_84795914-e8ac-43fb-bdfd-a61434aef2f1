package androidx.appcompat;

/* loaded from: classes.dex */
public final class R {

    public static final class anim {
        public static final int abc_fade_in = 0x7f010000;
        public static final int abc_fade_out = 0x7f010001;
        public static final int abc_grow_fade_in_from_bottom = 0x7f010002;
        public static final int abc_popup_enter = 0x7f010003;
        public static final int abc_popup_exit = 0x7f010004;
        public static final int abc_shrink_fade_out_from_bottom = 0x7f010005;
        public static final int abc_slide_in_bottom = 0x7f010006;
        public static final int abc_slide_in_top = 0x7f010007;
        public static final int abc_slide_out_bottom = 0x7f010008;
        public static final int abc_slide_out_top = 0x7f010009;
        public static final int abc_tooltip_enter = 0x7f01000a;
        public static final int abc_tooltip_exit = 0x7f01000b;
        public static final int btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c;
        public static final int btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d;
        public static final int btn_checkbox_to_checked_icon_null_animation = 0x7f01000e;
        public static final int btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f;
        public static final int btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010;
        public static final int btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011;
        public static final int btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012;
        public static final int btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013;
        public static final int btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014;
        public static final int btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015;
        public static final int btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016;
        public static final int btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017;

        private anim() {
        }
    }

    public static final class attr {
        public static final int actionBarDivider = 0x7f040011;
        public static final int actionBarItemBackground = 0x7f040012;
        public static final int actionBarPopupTheme = 0x7f040013;
        public static final int actionBarSize = 0x7f040014;
        public static final int actionBarSplitStyle = 0x7f040015;
        public static final int actionBarStyle = 0x7f040016;
        public static final int actionBarTabBarStyle = 0x7f040017;
        public static final int actionBarTabStyle = 0x7f040018;
        public static final int actionBarTabTextStyle = 0x7f040019;
        public static final int actionBarTheme = 0x7f04001a;
        public static final int actionBarWidgetTheme = 0x7f04001b;
        public static final int actionButtonStyle = 0x7f04001c;
        public static final int actionDropDownStyle = 0x7f04001d;
        public static final int actionLayout = 0x7f04001e;
        public static final int actionMenuTextAppearance = 0x7f04001f;
        public static final int actionMenuTextColor = 0x7f040020;
        public static final int actionModeBackground = 0x7f040021;
        public static final int actionModeCloseButtonStyle = 0x7f040022;
        public static final int actionModeCloseContentDescription = 0x7f040023;
        public static final int actionModeCloseDrawable = 0x7f040024;
        public static final int actionModeCopyDrawable = 0x7f040025;
        public static final int actionModeCutDrawable = 0x7f040026;
        public static final int actionModeFindDrawable = 0x7f040027;
        public static final int actionModePasteDrawable = 0x7f040028;
        public static final int actionModePopupWindowStyle = 0x7f040029;
        public static final int actionModeSelectAllDrawable = 0x7f04002a;
        public static final int actionModeShareDrawable = 0x7f04002b;
        public static final int actionModeSplitBackground = 0x7f04002c;
        public static final int actionModeStyle = 0x7f04002d;
        public static final int actionModeTheme = 0x7f04002e;
        public static final int actionModeWebSearchDrawable = 0x7f04002f;
        public static final int actionOverflowButtonStyle = 0x7f040030;
        public static final int actionOverflowMenuStyle = 0x7f040031;
        public static final int actionProviderClass = 0x7f040032;
        public static final int actionViewClass = 0x7f040034;
        public static final int activityChooserViewStyle = 0x7f040035;
        public static final int alertDialogButtonGroupStyle = 0x7f040036;
        public static final int alertDialogCenterButtons = 0x7f040037;
        public static final int alertDialogStyle = 0x7f040038;
        public static final int alertDialogTheme = 0x7f040039;
        public static final int allowStacking = 0x7f04003a;
        public static final int alphabeticModifiers = 0x7f04003c;
        public static final int arrowHeadLength = 0x7f040044;
        public static final int arrowShaftLength = 0x7f040045;
        public static final int autoCompleteTextViewStyle = 0x7f040048;
        public static final int autoSizeMaxTextSize = 0x7f040049;
        public static final int autoSizeMinTextSize = 0x7f04004a;
        public static final int autoSizePresetSizes = 0x7f04004b;
        public static final int autoSizeStepGranularity = 0x7f04004c;
        public static final int autoSizeTextType = 0x7f04004d;
        public static final int background = 0x7f04004f;
        public static final int backgroundSplit = 0x7f040056;
        public static final int backgroundStacked = 0x7f040057;
        public static final int backgroundTint = 0x7f040058;
        public static final int backgroundTintMode = 0x7f040059;
        public static final int barLength = 0x7f040073;
        public static final int borderlessButtonStyle = 0x7f040092;
        public static final int buttonBarButtonStyle = 0x7f0400ad;
        public static final int buttonBarNegativeButtonStyle = 0x7f0400ae;
        public static final int buttonBarNeutralButtonStyle = 0x7f0400af;
        public static final int buttonBarPositiveButtonStyle = 0x7f0400b0;
        public static final int buttonBarStyle = 0x7f0400b1;
        public static final int buttonCompat = 0x7f0400b2;
        public static final int buttonGravity = 0x7f0400b3;
        public static final int buttonIconDimen = 0x7f0400b4;
        public static final int buttonPanelSideLayout = 0x7f0400b5;
        public static final int buttonStyle = 0x7f0400b6;
        public static final int buttonStyleSmall = 0x7f0400b7;
        public static final int buttonTint = 0x7f0400b8;
        public static final int buttonTintMode = 0x7f0400b9;
        public static final int checkMarkCompat = 0x7f040202;
        public static final int checkMarkTint = 0x7f040203;
        public static final int checkMarkTintMode = 0x7f040204;
        public static final int checkboxStyle = 0x7f040205;
        public static final int checkedTextViewStyle = 0x7f04020f;
        public static final int closeIcon = 0x7f040231;
        public static final int closeItemLayout = 0x7f040238;
        public static final int collapseContentDescription = 0x7f040239;
        public static final int collapseIcon = 0x7f04023a;
        public static final int color = 0x7f040244;
        public static final int colorAccent = 0x7f040245;
        public static final int colorBackgroundFloating = 0x7f040246;
        public static final int colorButtonNormal = 0x7f040247;
        public static final int colorControlActivated = 0x7f040249;
        public static final int colorControlHighlight = 0x7f04024a;
        public static final int colorControlNormal = 0x7f04024b;
        public static final int colorError = 0x7f04024c;
        public static final int colorPrimary = 0x7f04025d;
        public static final int colorPrimaryDark = 0x7f04025f;
        public static final int colorSwitchThumbNormal = 0x7f040274;
        public static final int commitIcon = 0x7f040277;
        public static final int contentDescription = 0x7f040280;
        public static final int contentInsetEnd = 0x7f040281;
        public static final int contentInsetEndWithActions = 0x7f040282;
        public static final int contentInsetLeft = 0x7f040283;
        public static final int contentInsetRight = 0x7f040284;
        public static final int contentInsetStart = 0x7f040285;
        public static final int contentInsetStartWithNavigation = 0x7f040286;
        public static final int controlBackground = 0x7f040290;
        public static final int customNavigationLayout = 0x7f0402b2;
        public static final int defaultQueryHint = 0x7f0402bb;
        public static final int dialogCornerRadius = 0x7f0402c0;
        public static final int dialogPreferredPadding = 0x7f0402c1;
        public static final int dialogTheme = 0x7f0402c2;
        public static final int displayOptions = 0x7f0402c3;
        public static final int divider = 0x7f0402c4;
        public static final int dividerHorizontal = 0x7f0402c6;
        public static final int dividerPadding = 0x7f0402c9;
        public static final int dividerVertical = 0x7f0402cb;
        public static final int drawableBottomCompat = 0x7f0402d0;
        public static final int drawableEndCompat = 0x7f0402d1;
        public static final int drawableLeftCompat = 0x7f0402d2;
        public static final int drawableRightCompat = 0x7f0402d3;
        public static final int drawableSize = 0x7f0402d4;
        public static final int drawableStartCompat = 0x7f0402d5;
        public static final int drawableTint = 0x7f0402d6;
        public static final int drawableTintMode = 0x7f0402d7;
        public static final int drawableTopCompat = 0x7f0402d8;
        public static final int drawerArrowStyle = 0x7f0402d9;
        public static final int dropDownListViewStyle = 0x7f0402dc;
        public static final int dropdownListPreferredItemHeight = 0x7f0402dd;
        public static final int editTextBackground = 0x7f0402e9;
        public static final int editTextColor = 0x7f0402ea;
        public static final int editTextStyle = 0x7f0402eb;
        public static final int elevation = 0x7f0402ec;
        public static final int emojiCompatEnabled = 0x7f0402f0;
        public static final int expandActivityOverflowButtonDrawable = 0x7f040302;
        public static final int firstBaselineToTopHeight = 0x7f040320;
        public static final int fontFamily = 0x7f04033f;
        public static final int fontVariationSettings = 0x7f040348;
        public static final int gapBetweenBars = 0x7f04034d;
        public static final int goIcon = 0x7f04034f;
        public static final int height = 0x7f040354;
        public static final int hideOnContentScroll = 0x7f04035b;
        public static final int homeAsUpIndicator = 0x7f040361;
        public static final int homeLayout = 0x7f040362;
        public static final int icon = 0x7f040366;
        public static final int iconTint = 0x7f04036c;
        public static final int iconTintMode = 0x7f04036d;
        public static final int iconifiedByDefault = 0x7f04036e;
        public static final int imageButtonStyle = 0x7f040371;
        public static final int indeterminateProgressStyle = 0x7f040377;
        public static final int initialActivityCount = 0x7f040387;
        public static final int isLightTheme = 0x7f040389;
        public static final int itemPadding = 0x7f040396;
        public static final int lastBaselineToBottomHeight = 0x7f0403b0;
        public static final int layout = 0x7f0403b2;
        public static final int lineHeight = 0x7f040400;
        public static final int listChoiceBackgroundIndicator = 0x7f040403;
        public static final int listChoiceIndicatorMultipleAnimated = 0x7f040404;
        public static final int listChoiceIndicatorSingleAnimated = 0x7f040405;
        public static final int listDividerAlertDialog = 0x7f040406;
        public static final int listItemLayout = 0x7f040407;
        public static final int listLayout = 0x7f040408;
        public static final int listMenuViewStyle = 0x7f040409;
        public static final int listPopupWindowStyle = 0x7f04040a;
        public static final int listPreferredItemHeight = 0x7f04040b;
        public static final int listPreferredItemHeightLarge = 0x7f04040c;
        public static final int listPreferredItemHeightSmall = 0x7f04040d;
        public static final int listPreferredItemPaddingEnd = 0x7f04040e;
        public static final int listPreferredItemPaddingLeft = 0x7f04040f;
        public static final int listPreferredItemPaddingRight = 0x7f040410;
        public static final int listPreferredItemPaddingStart = 0x7f040411;
        public static final int logo = 0x7f040412;
        public static final int logoDescription = 0x7f040414;
        public static final int maxButtonHeight = 0x7f040453;
        public static final int measureWithLargestChild = 0x7f04045a;
        public static final int menu = 0x7f04045b;
        public static final int multiChoiceItemLayout = 0x7f040490;
        public static final int navigationContentDescription = 0x7f040491;
        public static final int navigationIcon = 0x7f040492;
        public static final int navigationMode = 0x7f040494;
        public static final int numericModifiers = 0x7f04049b;
        public static final int overlapAnchor = 0x7f0404a3;
        public static final int paddingBottomNoButtons = 0x7f0404a5;
        public static final int paddingEnd = 0x7f0404a7;
        public static final int paddingStart = 0x7f0404aa;
        public static final int paddingTopNoTitle = 0x7f0404ab;
        public static final int panelBackground = 0x7f0404ad;
        public static final int panelMenuListTheme = 0x7f0404ae;
        public static final int panelMenuListWidth = 0x7f0404af;
        public static final int popupMenuStyle = 0x7f0404c3;
        public static final int popupTheme = 0x7f0404c4;
        public static final int popupWindowStyle = 0x7f0404c5;
        public static final int preserveIconSpacing = 0x7f0404c9;
        public static final int progressBarPadding = 0x7f0404cb;
        public static final int progressBarStyle = 0x7f0404cc;
        public static final int queryBackground = 0x7f0404d0;
        public static final int queryHint = 0x7f0404d1;
        public static final int radioButtonStyle = 0x7f0404d3;
        public static final int ratingBarStyle = 0x7f0404d5;
        public static final int ratingBarStyleIndicator = 0x7f0404d6;
        public static final int ratingBarStyleSmall = 0x7f0404d7;
        public static final int searchHintIcon = 0x7f040519;
        public static final int searchIcon = 0x7f04051a;
        public static final int searchViewStyle = 0x7f04051b;
        public static final int seekBarStyle = 0x7f04051c;
        public static final int selectableItemBackground = 0x7f04051d;
        public static final int selectableItemBackgroundBorderless = 0x7f04051e;
        public static final int showAsAction = 0x7f040529;
        public static final int showDividers = 0x7f04052b;
        public static final int showText = 0x7f04052e;
        public static final int showTitle = 0x7f04052f;
        public static final int singleChoiceItemLayout = 0x7f040533;
        public static final int spinBars = 0x7f04055a;
        public static final int spinnerDropDownItemStyle = 0x7f04055b;
        public static final int spinnerStyle = 0x7f04055c;
        public static final int splitTrack = 0x7f04055d;
        public static final int srcCompat = 0x7f040563;
        public static final int state_above_anchor = 0x7f04059d;
        public static final int subMenuArrow = 0x7f0405a8;
        public static final int submitBackground = 0x7f0405ad;
        public static final int subtitle = 0x7f0405ae;
        public static final int subtitleTextAppearance = 0x7f0405b0;
        public static final int subtitleTextColor = 0x7f0405b1;
        public static final int subtitleTextStyle = 0x7f0405b2;
        public static final int suggestionRowLayout = 0x7f0405b6;
        public static final int switchMinWidth = 0x7f0405d0;
        public static final int switchPadding = 0x7f0405d1;
        public static final int switchStyle = 0x7f0405d2;
        public static final int switchTextAppearance = 0x7f0405d3;
        public static final int textAllCaps = 0x7f040604;
        public static final int textAppearanceLargePopupMenu = 0x7f04061b;
        public static final int textAppearanceListItem = 0x7f04061d;
        public static final int textAppearanceListItemSecondary = 0x7f04061e;
        public static final int textAppearanceListItemSmall = 0x7f04061f;
        public static final int textAppearancePopupMenuHeader = 0x7f040621;
        public static final int textAppearanceSearchResultSubtitle = 0x7f040622;
        public static final int textAppearanceSearchResultTitle = 0x7f040623;
        public static final int textAppearanceSmallPopupMenu = 0x7f040624;
        public static final int textColorAlertDialogListItem = 0x7f04062f;
        public static final int textColorSearchUrl = 0x7f040630;
        public static final int textLocale = 0x7f04063b;
        public static final int theme = 0x7f040646;
        public static final int thickness = 0x7f040648;
        public static final int thumbTextPadding = 0x7f04064e;
        public static final int thumbTint = 0x7f04064f;
        public static final int thumbTintMode = 0x7f040650;
        public static final int tickMark = 0x7f040654;
        public static final int tickMarkTint = 0x7f040655;
        public static final int tickMarkTintMode = 0x7f040656;
        public static final int tint = 0x7f040658;
        public static final int tintMode = 0x7f040659;
        public static final int title = 0x7f04066f;
        public static final int titleMargin = 0x7f040673;
        public static final int titleMarginBottom = 0x7f040674;
        public static final int titleMarginEnd = 0x7f040675;
        public static final int titleMarginStart = 0x7f040676;
        public static final int titleMarginTop = 0x7f040677;
        public static final int titleMargins = 0x7f040678;
        public static final int titleTextAppearance = 0x7f04067a;
        public static final int titleTextColor = 0x7f04067b;
        public static final int titleTextStyle = 0x7f04067c;
        public static final int toolbarNavigationButtonStyle = 0x7f04067e;
        public static final int toolbarStyle = 0x7f04067f;
        public static final int tooltipForegroundColor = 0x7f040681;
        public static final int tooltipFrameBackground = 0x7f040682;
        public static final int tooltipText = 0x7f040684;
        public static final int track = 0x7f040689;
        public static final int trackTint = 0x7f040690;
        public static final int trackTintMode = 0x7f040691;
        public static final int viewInflaterClass = 0x7f0406ae;
        public static final int voiceIcon = 0x7f0406b4;
        public static final int windowActionBar = 0x7f0406bf;
        public static final int windowActionBarOverlay = 0x7f0406c0;
        public static final int windowActionModeOverlay = 0x7f0406c1;
        public static final int windowFixedHeightMajor = 0x7f0406c2;
        public static final int windowFixedHeightMinor = 0x7f0406c3;
        public static final int windowFixedWidthMajor = 0x7f0406c4;
        public static final int windowFixedWidthMinor = 0x7f0406c5;
        public static final int windowMinWidthMajor = 0x7f0406c6;
        public static final int windowMinWidthMinor = 0x7f0406c7;
        public static final int windowNoTitle = 0x7f0406c8;

        private attr() {
        }
    }

    public static final class bool {
        public static final int abc_action_bar_embed_tabs = 0x7f050000;
        public static final int abc_config_actionMenuItemAllCaps = 0x7f050001;

        private bool() {
        }
    }

    public static final class color {
        public static final int abc_background_cache_hint_selector_material_dark = 0x7f060000;
        public static final int abc_background_cache_hint_selector_material_light = 0x7f060001;
        public static final int abc_btn_colored_borderless_text_material = 0x7f060002;
        public static final int abc_btn_colored_text_material = 0x7f060003;
        public static final int abc_color_highlight_material = 0x7f060004;
        public static final int abc_decor_view_status_guard = 0x7f060005;
        public static final int abc_decor_view_status_guard_light = 0x7f060006;
        public static final int abc_hint_foreground_material_dark = 0x7f060007;
        public static final int abc_hint_foreground_material_light = 0x7f060008;
        public static final int abc_primary_text_disable_only_material_dark = 0x7f060009;
        public static final int abc_primary_text_disable_only_material_light = 0x7f06000a;
        public static final int abc_primary_text_material_dark = 0x7f06000b;
        public static final int abc_primary_text_material_light = 0x7f06000c;
        public static final int abc_search_url_text = 0x7f06000d;
        public static final int abc_search_url_text_normal = 0x7f06000e;
        public static final int abc_search_url_text_pressed = 0x7f06000f;
        public static final int abc_search_url_text_selected = 0x7f060010;
        public static final int abc_secondary_text_material_dark = 0x7f060011;
        public static final int abc_secondary_text_material_light = 0x7f060012;
        public static final int abc_tint_btn_checkable = 0x7f060013;
        public static final int abc_tint_default = 0x7f060014;
        public static final int abc_tint_edittext = 0x7f060015;
        public static final int abc_tint_seek_thumb = 0x7f060016;
        public static final int abc_tint_spinner = 0x7f060017;
        public static final int abc_tint_switch_track = 0x7f060018;
        public static final int accent_material_dark = 0x7f060019;
        public static final int accent_material_light = 0x7f06001a;
        public static final int background_floating_material_dark = 0x7f06001d;
        public static final int background_floating_material_light = 0x7f06001e;
        public static final int background_material_dark = 0x7f06001f;
        public static final int background_material_light = 0x7f060020;
        public static final int bright_foreground_disabled_material_dark = 0x7f060027;
        public static final int bright_foreground_disabled_material_light = 0x7f060028;
        public static final int bright_foreground_inverse_material_dark = 0x7f060029;
        public static final int bright_foreground_inverse_material_light = 0x7f06002a;
        public static final int bright_foreground_material_dark = 0x7f06002b;
        public static final int bright_foreground_material_light = 0x7f06002c;
        public static final int button_material_dark = 0x7f06002d;
        public static final int button_material_light = 0x7f06002e;
        public static final int dim_foreground_disabled_material_dark = 0x7f060132;
        public static final int dim_foreground_disabled_material_light = 0x7f060133;
        public static final int dim_foreground_material_dark = 0x7f060134;
        public static final int dim_foreground_material_light = 0x7f060135;
        public static final int error_color_material_dark = 0x7f060136;
        public static final int error_color_material_light = 0x7f060137;
        public static final int foreground_material_dark = 0x7f060138;
        public static final int foreground_material_light = 0x7f060139;
        public static final int highlighted_text_material_dark = 0x7f060151;
        public static final int highlighted_text_material_light = 0x7f060152;
        public static final int material_blue_grey_800 = 0x7f060294;
        public static final int material_blue_grey_900 = 0x7f060295;
        public static final int material_blue_grey_950 = 0x7f060296;
        public static final int material_deep_teal_200 = 0x7f060298;
        public static final int material_deep_teal_500 = 0x7f060299;
        public static final int material_grey_100 = 0x7f0602dc;
        public static final int material_grey_300 = 0x7f0602dd;
        public static final int material_grey_50 = 0x7f0602de;
        public static final int material_grey_600 = 0x7f0602df;
        public static final int material_grey_800 = 0x7f0602e0;
        public static final int material_grey_850 = 0x7f0602e1;
        public static final int material_grey_900 = 0x7f0602e2;
        public static final int primary_dark_material_dark = 0x7f060334;
        public static final int primary_dark_material_light = 0x7f060335;
        public static final int primary_material_dark = 0x7f060336;
        public static final int primary_material_light = 0x7f060337;
        public static final int primary_text_default_material_dark = 0x7f060338;
        public static final int primary_text_default_material_light = 0x7f060339;
        public static final int primary_text_disabled_material_dark = 0x7f06033a;
        public static final int primary_text_disabled_material_light = 0x7f06033b;
        public static final int ripple_material_dark = 0x7f060342;
        public static final int ripple_material_light = 0x7f060343;
        public static final int secondary_text_default_material_dark = 0x7f060344;
        public static final int secondary_text_default_material_light = 0x7f060345;
        public static final int secondary_text_disabled_material_dark = 0x7f060346;
        public static final int secondary_text_disabled_material_light = 0x7f060347;
        public static final int switch_thumb_disabled_material_dark = 0x7f060348;
        public static final int switch_thumb_disabled_material_light = 0x7f060349;
        public static final int switch_thumb_material_dark = 0x7f06034a;
        public static final int switch_thumb_material_light = 0x7f06034b;
        public static final int switch_thumb_normal_material_dark = 0x7f06034c;
        public static final int switch_thumb_normal_material_light = 0x7f06034d;
        public static final int tooltip_background_dark = 0x7f060354;
        public static final int tooltip_background_light = 0x7f060355;

        private color() {
        }
    }

    public static final class dimen {
        public static final int abc_action_bar_content_inset_material = 0x7f070000;
        public static final int abc_action_bar_content_inset_with_nav = 0x7f070001;
        public static final int abc_action_bar_default_height_material = 0x7f070002;
        public static final int abc_action_bar_default_padding_end_material = 0x7f070003;
        public static final int abc_action_bar_default_padding_start_material = 0x7f070004;
        public static final int abc_action_bar_elevation_material = 0x7f070005;
        public static final int abc_action_bar_icon_vertical_padding_material = 0x7f070006;
        public static final int abc_action_bar_overflow_padding_end_material = 0x7f070007;
        public static final int abc_action_bar_overflow_padding_start_material = 0x7f070008;
        public static final int abc_action_bar_stacked_max_height = 0x7f070009;
        public static final int abc_action_bar_stacked_tab_max_width = 0x7f07000a;
        public static final int abc_action_bar_subtitle_bottom_margin_material = 0x7f07000b;
        public static final int abc_action_bar_subtitle_top_margin_material = 0x7f07000c;
        public static final int abc_action_button_min_height_material = 0x7f07000d;
        public static final int abc_action_button_min_width_material = 0x7f07000e;
        public static final int abc_action_button_min_width_overflow_material = 0x7f07000f;
        public static final int abc_alert_dialog_button_bar_height = 0x7f070010;
        public static final int abc_alert_dialog_button_dimen = 0x7f070011;
        public static final int abc_button_inset_horizontal_material = 0x7f070012;
        public static final int abc_button_inset_vertical_material = 0x7f070013;
        public static final int abc_button_padding_horizontal_material = 0x7f070014;
        public static final int abc_button_padding_vertical_material = 0x7f070015;
        public static final int abc_cascading_menus_min_smallest_width = 0x7f070016;
        public static final int abc_config_prefDialogWidth = 0x7f070017;
        public static final int abc_control_corner_material = 0x7f070018;
        public static final int abc_control_inset_material = 0x7f070019;
        public static final int abc_control_padding_material = 0x7f07001a;
        public static final int abc_dialog_corner_radius_material = 0x7f07001b;
        public static final int abc_dialog_fixed_height_major = 0x7f07001c;
        public static final int abc_dialog_fixed_height_minor = 0x7f07001d;
        public static final int abc_dialog_fixed_width_major = 0x7f07001e;
        public static final int abc_dialog_fixed_width_minor = 0x7f07001f;
        public static final int abc_dialog_list_padding_bottom_no_buttons = 0x7f070020;
        public static final int abc_dialog_list_padding_top_no_title = 0x7f070021;
        public static final int abc_dialog_min_width_major = 0x7f070022;
        public static final int abc_dialog_min_width_minor = 0x7f070023;
        public static final int abc_dialog_padding_material = 0x7f070024;
        public static final int abc_dialog_padding_top_material = 0x7f070025;
        public static final int abc_dialog_title_divider_material = 0x7f070026;
        public static final int abc_disabled_alpha_material_dark = 0x7f070027;
        public static final int abc_disabled_alpha_material_light = 0x7f070028;
        public static final int abc_dropdownitem_icon_width = 0x7f070029;
        public static final int abc_dropdownitem_text_padding_left = 0x7f07002a;
        public static final int abc_dropdownitem_text_padding_right = 0x7f07002b;
        public static final int abc_edit_text_inset_bottom_material = 0x7f07002c;
        public static final int abc_edit_text_inset_horizontal_material = 0x7f07002d;
        public static final int abc_edit_text_inset_top_material = 0x7f07002e;
        public static final int abc_floating_window_z = 0x7f07002f;
        public static final int abc_list_item_height_large_material = 0x7f070030;
        public static final int abc_list_item_height_material = 0x7f070031;
        public static final int abc_list_item_height_small_material = 0x7f070032;
        public static final int abc_list_item_padding_horizontal_material = 0x7f070033;
        public static final int abc_panel_menu_list_width = 0x7f070034;
        public static final int abc_progress_bar_height_material = 0x7f070035;
        public static final int abc_search_view_preferred_height = 0x7f070036;
        public static final int abc_search_view_preferred_width = 0x7f070037;
        public static final int abc_seekbar_track_background_height_material = 0x7f070038;
        public static final int abc_seekbar_track_progress_height_material = 0x7f070039;
        public static final int abc_select_dialog_padding_start_material = 0x7f07003a;
        public static final int abc_star_big = 0x7f07003b;
        public static final int abc_star_medium = 0x7f07003c;
        public static final int abc_star_small = 0x7f07003d;
        public static final int abc_switch_padding = 0x7f07003e;
        public static final int abc_text_size_body_1_material = 0x7f07003f;
        public static final int abc_text_size_body_2_material = 0x7f070040;
        public static final int abc_text_size_button_material = 0x7f070041;
        public static final int abc_text_size_caption_material = 0x7f070042;
        public static final int abc_text_size_display_1_material = 0x7f070043;
        public static final int abc_text_size_display_2_material = 0x7f070044;
        public static final int abc_text_size_display_3_material = 0x7f070045;
        public static final int abc_text_size_display_4_material = 0x7f070046;
        public static final int abc_text_size_headline_material = 0x7f070047;
        public static final int abc_text_size_large_material = 0x7f070048;
        public static final int abc_text_size_medium_material = 0x7f070049;
        public static final int abc_text_size_menu_header_material = 0x7f07004a;
        public static final int abc_text_size_menu_material = 0x7f07004b;
        public static final int abc_text_size_small_material = 0x7f07004c;
        public static final int abc_text_size_subhead_material = 0x7f07004d;
        public static final int abc_text_size_subtitle_material_toolbar = 0x7f07004e;
        public static final int abc_text_size_title_material = 0x7f07004f;
        public static final int abc_text_size_title_material_toolbar = 0x7f070050;
        public static final int disabled_alpha_material_dark = 0x7f0700b8;
        public static final int disabled_alpha_material_light = 0x7f0700b9;
        public static final int highlight_alpha_material_colored = 0x7f0700bd;
        public static final int highlight_alpha_material_dark = 0x7f0700be;
        public static final int highlight_alpha_material_light = 0x7f0700bf;
        public static final int hint_alpha_material_dark = 0x7f0700c0;
        public static final int hint_alpha_material_light = 0x7f0700c1;
        public static final int hint_pressed_alpha_material_dark = 0x7f0700c2;
        public static final int hint_pressed_alpha_material_light = 0x7f0700c3;
        public static final int tooltip_corner_radius = 0x7f07026d;
        public static final int tooltip_horizontal_padding = 0x7f07026e;
        public static final int tooltip_margin = 0x7f07026f;
        public static final int tooltip_precise_anchor_extra_offset = 0x7f070270;
        public static final int tooltip_precise_anchor_threshold = 0x7f070271;
        public static final int tooltip_vertical_padding = 0x7f070272;
        public static final int tooltip_y_offset_non_touch = 0x7f070273;
        public static final int tooltip_y_offset_touch = 0x7f070274;

        private dimen() {
        }
    }

    public static final class drawable {
        public static final int abc_ab_share_pack_mtrl_alpha = 0x7f080008;
        public static final int abc_action_bar_item_background_material = 0x7f080009;
        public static final int abc_btn_borderless_material = 0x7f08000a;
        public static final int abc_btn_check_material = 0x7f08000b;
        public static final int abc_btn_check_material_anim = 0x7f08000c;
        public static final int abc_btn_check_to_on_mtrl_000 = 0x7f08000d;
        public static final int abc_btn_check_to_on_mtrl_015 = 0x7f08000e;
        public static final int abc_btn_colored_material = 0x7f08000f;
        public static final int abc_btn_default_mtrl_shape = 0x7f080010;
        public static final int abc_btn_radio_material = 0x7f080011;
        public static final int abc_btn_radio_material_anim = 0x7f080012;
        public static final int abc_btn_radio_to_on_mtrl_000 = 0x7f080013;
        public static final int abc_btn_radio_to_on_mtrl_015 = 0x7f080014;
        public static final int abc_btn_switch_to_on_mtrl_00001 = 0x7f080015;
        public static final int abc_btn_switch_to_on_mtrl_00012 = 0x7f080016;
        public static final int abc_cab_background_internal_bg = 0x7f080017;
        public static final int abc_cab_background_top_material = 0x7f080018;
        public static final int abc_cab_background_top_mtrl_alpha = 0x7f080019;
        public static final int abc_control_background_material = 0x7f08001a;
        public static final int abc_dialog_material_background = 0x7f08001b;
        public static final int abc_edit_text_material = 0x7f08001c;
        public static final int abc_ic_ab_back_material = 0x7f08001d;
        public static final int abc_ic_arrow_drop_right_black_24dp = 0x7f08001e;
        public static final int abc_ic_clear_material = 0x7f08001f;
        public static final int abc_ic_commit_search_api_mtrl_alpha = 0x7f080020;
        public static final int abc_ic_go_search_api_material = 0x7f080021;
        public static final int abc_ic_menu_copy_mtrl_am_alpha = 0x7f080022;
        public static final int abc_ic_menu_cut_mtrl_alpha = 0x7f080023;
        public static final int abc_ic_menu_overflow_material = 0x7f080024;
        public static final int abc_ic_menu_paste_mtrl_am_alpha = 0x7f080025;
        public static final int abc_ic_menu_selectall_mtrl_alpha = 0x7f080026;
        public static final int abc_ic_menu_share_mtrl_alpha = 0x7f080027;
        public static final int abc_ic_search_api_material = 0x7f080028;
        public static final int abc_ic_voice_search_api_material = 0x7f080029;
        public static final int abc_item_background_holo_dark = 0x7f08002a;
        public static final int abc_item_background_holo_light = 0x7f08002b;
        public static final int abc_list_divider_material = 0x7f08002c;
        public static final int abc_list_divider_mtrl_alpha = 0x7f08002d;
        public static final int abc_list_focused_holo = 0x7f08002e;
        public static final int abc_list_longpressed_holo = 0x7f08002f;
        public static final int abc_list_pressed_holo_dark = 0x7f080030;
        public static final int abc_list_pressed_holo_light = 0x7f080031;
        public static final int abc_list_selector_background_transition_holo_dark = 0x7f080032;
        public static final int abc_list_selector_background_transition_holo_light = 0x7f080033;
        public static final int abc_list_selector_disabled_holo_dark = 0x7f080034;
        public static final int abc_list_selector_disabled_holo_light = 0x7f080035;
        public static final int abc_list_selector_holo_dark = 0x7f080036;
        public static final int abc_list_selector_holo_light = 0x7f080037;
        public static final int abc_menu_hardkey_panel_mtrl_mult = 0x7f080038;
        public static final int abc_popup_background_mtrl_mult = 0x7f080039;
        public static final int abc_ratingbar_indicator_material = 0x7f08003a;
        public static final int abc_ratingbar_material = 0x7f08003b;
        public static final int abc_ratingbar_small_material = 0x7f08003c;
        public static final int abc_scrubber_control_off_mtrl_alpha = 0x7f08003d;
        public static final int abc_scrubber_control_to_pressed_mtrl_000 = 0x7f08003e;
        public static final int abc_scrubber_control_to_pressed_mtrl_005 = 0x7f08003f;
        public static final int abc_scrubber_primary_mtrl_alpha = 0x7f080040;
        public static final int abc_scrubber_track_mtrl_alpha = 0x7f080041;
        public static final int abc_seekbar_thumb_material = 0x7f080042;
        public static final int abc_seekbar_tick_mark_material = 0x7f080043;
        public static final int abc_seekbar_track_material = 0x7f080044;
        public static final int abc_spinner_mtrl_am_alpha = 0x7f080045;
        public static final int abc_spinner_textfield_background_material = 0x7f080046;
        public static final int abc_star_black_48dp = 0x7f080047;
        public static final int abc_star_half_black_48dp = 0x7f080048;
        public static final int abc_switch_thumb_material = 0x7f080049;
        public static final int abc_switch_track_mtrl_alpha = 0x7f08004a;
        public static final int abc_tab_indicator_material = 0x7f08004b;
        public static final int abc_tab_indicator_mtrl_alpha = 0x7f08004c;
        public static final int abc_text_cursor_material = 0x7f08004d;
        public static final int abc_text_select_handle_left_mtrl = 0x7f08004e;
        public static final int abc_text_select_handle_middle_mtrl = 0x7f08004f;
        public static final int abc_text_select_handle_right_mtrl = 0x7f080050;
        public static final int abc_textfield_activated_mtrl_alpha = 0x7f080051;
        public static final int abc_textfield_default_mtrl_alpha = 0x7f080052;
        public static final int abc_textfield_search_activated_mtrl_alpha = 0x7f080053;
        public static final int abc_textfield_search_default_mtrl_alpha = 0x7f080054;
        public static final int abc_textfield_search_material = 0x7f080055;
        public static final int btn_checkbox_checked_mtrl = 0x7f080068;
        public static final int btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f080069;
        public static final int btn_checkbox_unchecked_mtrl = 0x7f08006a;
        public static final int btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f08006b;
        public static final int btn_radio_off_mtrl = 0x7f08006d;
        public static final int btn_radio_off_to_on_mtrl_animation = 0x7f08006e;
        public static final int btn_radio_on_mtrl = 0x7f08006f;
        public static final int btn_radio_on_to_off_mtrl_animation = 0x7f080070;
        public static final int test_level_drawable = 0x7f080126;
        public static final int tooltip_frame_dark = 0x7f080127;
        public static final int tooltip_frame_light = 0x7f080128;

        private drawable() {
        }
    }

    public static final class id {
        public static final int action_bar = 0x7f0a0040;
        public static final int action_bar_activity_content = 0x7f0a0041;
        public static final int action_bar_container = 0x7f0a0042;
        public static final int action_bar_root = 0x7f0a0043;
        public static final int action_bar_spinner = 0x7f0a0044;
        public static final int action_bar_subtitle = 0x7f0a0045;
        public static final int action_bar_title = 0x7f0a0046;
        public static final int action_context_bar = 0x7f0a0048;
        public static final int action_menu_divider = 0x7f0a004b;
        public static final int action_menu_presenter = 0x7f0a004c;
        public static final int action_mode_bar = 0x7f0a004d;
        public static final int action_mode_bar_stub = 0x7f0a004e;
        public static final int action_mode_close_button = 0x7f0a004f;
        public static final int activity_chooser_view_content = 0x7f0a0052;
        public static final int add = 0x7f0a0053;
        public static final int alertTitle = 0x7f0a0054;
        public static final int buttonPanel = 0x7f0a00c0;
        public static final int checkbox = 0x7f0a00ee;
        public static final int checked = 0x7f0a00ef;
        public static final int content = 0x7f0a0123;
        public static final int contentPanel = 0x7f0a0124;
        public static final int custom = 0x7f0a0133;
        public static final int customPanel = 0x7f0a0134;
        public static final int decor_content_parent = 0x7f0a013d;
        public static final int default_activity_button = 0x7f0a013e;
        public static final int edit_query = 0x7f0a016e;
        public static final int expand_activities_button = 0x7f0a017c;
        public static final int expanded_menu = 0x7f0a017d;
        public static final int group_divider = 0x7f0a019e;
        public static final int home = 0x7f0a01ab;
        public static final int icon = 0x7f0a01b5;
        public static final int image = 0x7f0a01ba;
        public static final int listMode = 0x7f0a01f4;
        public static final int list_item = 0x7f0a01f5;
        public static final int message = 0x7f0a0221;
        public static final int multiply = 0x7f0a024e;
        public static final int none = 0x7f0a0262;
        public static final int normal = 0x7f0a0263;
        public static final int off = 0x7f0a026a;
        public static final int on = 0x7f0a026b;
        public static final int parentPanel = 0x7f0a027b;
        public static final int progress_circular = 0x7f0a028e;
        public static final int progress_horizontal = 0x7f0a028f;
        public static final int radio = 0x7f0a029a;
        public static final int screen = 0x7f0a02bf;
        public static final int scrollIndicatorDown = 0x7f0a02c1;
        public static final int scrollIndicatorUp = 0x7f0a02c2;
        public static final int scrollView = 0x7f0a02c3;
        public static final int search_badge = 0x7f0a02c5;
        public static final int search_bar = 0x7f0a02c6;
        public static final int search_button = 0x7f0a02c7;
        public static final int search_close_btn = 0x7f0a02c8;
        public static final int search_edit_frame = 0x7f0a02c9;
        public static final int search_go_btn = 0x7f0a02ca;
        public static final int search_mag_icon = 0x7f0a02cb;
        public static final int search_plate = 0x7f0a02cc;
        public static final int search_src_text = 0x7f0a02cd;
        public static final int search_voice_btn = 0x7f0a02ce;
        public static final int select_dialog_listview = 0x7f0a02d1;
        public static final int shortcut = 0x7f0a02d7;
        public static final int spacer = 0x7f0a02ee;
        public static final int split_action_bar = 0x7f0a02f1;
        public static final int src_atop = 0x7f0a02f6;
        public static final int src_in = 0x7f0a02f7;
        public static final int src_over = 0x7f0a02f8;
        public static final int submenuarrow = 0x7f0a0307;
        public static final int submit_area = 0x7f0a0308;
        public static final int tabMode = 0x7f0a030c;
        public static final int textSpacerNoButtons = 0x7f0a0329;
        public static final int textSpacerNoTitle = 0x7f0a032a;
        public static final int title = 0x7f0a0345;
        public static final int titleDividerNoCustom = 0x7f0a0346;
        public static final int title_template = 0x7f0a0347;
        public static final int topPanel = 0x7f0a034b;
        public static final int unchecked = 0x7f0a039d;
        public static final int uniform = 0x7f0a039e;
        public static final int up = 0x7f0a03a0;
        public static final int wrap_content = 0x7f0a03d5;

        private id() {
        }
    }

    public static final class integer {
        public static final int abc_config_activityDefaultDur = 0x7f0b0000;
        public static final int abc_config_activityShortDur = 0x7f0b0001;
        public static final int cancel_button_image_alpha = 0x7f0b0004;
        public static final int config_tooltipAnimTime = 0x7f0b0005;

        private integer() {
        }
    }

    public static final class interpolator {
        public static final int btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0c0000;
        public static final int btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0c0001;
        public static final int btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0c0002;
        public static final int btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0c0003;
        public static final int btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0c0004;
        public static final int btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0c0005;
        public static final int fast_out_slow_in = 0x7f0c0006;

        private interpolator() {
        }
    }

    public static final class layout {
        public static final int abc_action_bar_title_item = 0x7f0d0000;
        public static final int abc_action_bar_up_container = 0x7f0d0001;
        public static final int abc_action_menu_item_layout = 0x7f0d0002;
        public static final int abc_action_menu_layout = 0x7f0d0003;
        public static final int abc_action_mode_bar = 0x7f0d0004;
        public static final int abc_action_mode_close_item_material = 0x7f0d0005;
        public static final int abc_activity_chooser_view = 0x7f0d0006;
        public static final int abc_activity_chooser_view_list_item = 0x7f0d0007;
        public static final int abc_alert_dialog_button_bar_material = 0x7f0d0008;
        public static final int abc_alert_dialog_material = 0x7f0d0009;
        public static final int abc_alert_dialog_title_material = 0x7f0d000a;
        public static final int abc_cascading_menu_item_layout = 0x7f0d000b;
        public static final int abc_dialog_title_material = 0x7f0d000c;
        public static final int abc_expanded_menu_layout = 0x7f0d000d;
        public static final int abc_list_menu_item_checkbox = 0x7f0d000e;
        public static final int abc_list_menu_item_icon = 0x7f0d000f;
        public static final int abc_list_menu_item_layout = 0x7f0d0010;
        public static final int abc_list_menu_item_radio = 0x7f0d0011;
        public static final int abc_popup_menu_header_item_layout = 0x7f0d0012;
        public static final int abc_popup_menu_item_layout = 0x7f0d0013;
        public static final int abc_screen_content_include = 0x7f0d0014;
        public static final int abc_screen_simple = 0x7f0d0015;
        public static final int abc_screen_simple_overlay_action_mode = 0x7f0d0016;
        public static final int abc_screen_toolbar = 0x7f0d0017;
        public static final int abc_search_dropdown_item_icons_2line = 0x7f0d0018;
        public static final int abc_search_view = 0x7f0d0019;
        public static final int abc_select_dialog_material = 0x7f0d001a;
        public static final int abc_tooltip = 0x7f0d001b;
        public static final int select_dialog_item_material = 0x7f0d00d6;
        public static final int select_dialog_multichoice_material = 0x7f0d00d7;
        public static final int select_dialog_singlechoice_material = 0x7f0d00d8;
        public static final int support_simple_spinner_dropdown_item = 0x7f0d00d9;

        private layout() {
        }
    }

    public static final class string {
        public static final int abc_action_bar_home_description = 0x7f110000;
        public static final int abc_action_bar_up_description = 0x7f110001;
        public static final int abc_action_menu_overflow_description = 0x7f110002;
        public static final int abc_action_mode_done = 0x7f110003;
        public static final int abc_activity_chooser_view_see_all = 0x7f110004;
        public static final int abc_activitychooserview_choose_application = 0x7f110005;
        public static final int abc_capital_off = 0x7f110006;
        public static final int abc_capital_on = 0x7f110007;
        public static final int abc_menu_alt_shortcut_label = 0x7f110008;
        public static final int abc_menu_ctrl_shortcut_label = 0x7f110009;
        public static final int abc_menu_delete_shortcut_label = 0x7f11000a;
        public static final int abc_menu_enter_shortcut_label = 0x7f11000b;
        public static final int abc_menu_function_shortcut_label = 0x7f11000c;
        public static final int abc_menu_meta_shortcut_label = 0x7f11000d;
        public static final int abc_menu_shift_shortcut_label = 0x7f11000e;
        public static final int abc_menu_space_shortcut_label = 0x7f11000f;
        public static final int abc_menu_sym_shortcut_label = 0x7f110010;
        public static final int abc_prepend_shortcut_label = 0x7f110011;
        public static final int abc_search_hint = 0x7f110012;
        public static final int abc_searchview_description_clear = 0x7f110013;
        public static final int abc_searchview_description_query = 0x7f110014;
        public static final int abc_searchview_description_search = 0x7f110015;
        public static final int abc_searchview_description_submit = 0x7f110016;
        public static final int abc_searchview_description_voice = 0x7f110017;
        public static final int abc_shareactionprovider_share_with = 0x7f110018;
        public static final int abc_shareactionprovider_share_with_application = 0x7f110019;
        public static final int abc_toolbar_collapse_description = 0x7f11001a;
        public static final int search_menu_title = 0x7f1101cc;

        private string() {
        }
    }

    public static final class style {
        public static final int AlertDialog_AppCompat = 0x7f120000;
        public static final int AlertDialog_AppCompat_Light = 0x7f120001;
        public static final int Animation_AppCompat_Dialog = 0x7f120003;
        public static final int Animation_AppCompat_DropDownUp = 0x7f120004;
        public static final int Animation_AppCompat_Tooltip = 0x7f120005;
        public static final int Base_AlertDialog_AppCompat = 0x7f12000c;
        public static final int Base_AlertDialog_AppCompat_Light = 0x7f12000d;
        public static final int Base_Animation_AppCompat_Dialog = 0x7f12000e;
        public static final int Base_Animation_AppCompat_DropDownUp = 0x7f12000f;
        public static final int Base_Animation_AppCompat_Tooltip = 0x7f120010;
        public static final int Base_DialogWindowTitleBackground_AppCompat = 0x7f120013;
        public static final int Base_DialogWindowTitle_AppCompat = 0x7f120012;
        public static final int Base_TextAppearance_AppCompat = 0x7f120017;
        public static final int Base_TextAppearance_AppCompat_Body1 = 0x7f120018;
        public static final int Base_TextAppearance_AppCompat_Body2 = 0x7f120019;
        public static final int Base_TextAppearance_AppCompat_Button = 0x7f12001a;
        public static final int Base_TextAppearance_AppCompat_Caption = 0x7f12001b;
        public static final int Base_TextAppearance_AppCompat_Display1 = 0x7f12001c;
        public static final int Base_TextAppearance_AppCompat_Display2 = 0x7f12001d;
        public static final int Base_TextAppearance_AppCompat_Display3 = 0x7f12001e;
        public static final int Base_TextAppearance_AppCompat_Display4 = 0x7f12001f;
        public static final int Base_TextAppearance_AppCompat_Headline = 0x7f120020;
        public static final int Base_TextAppearance_AppCompat_Inverse = 0x7f120021;
        public static final int Base_TextAppearance_AppCompat_Large = 0x7f120022;
        public static final int Base_TextAppearance_AppCompat_Large_Inverse = 0x7f120023;
        public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f120024;
        public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f120025;
        public static final int Base_TextAppearance_AppCompat_Medium = 0x7f120026;
        public static final int Base_TextAppearance_AppCompat_Medium_Inverse = 0x7f120027;
        public static final int Base_TextAppearance_AppCompat_Menu = 0x7f120028;
        public static final int Base_TextAppearance_AppCompat_SearchResult = 0x7f120029;
        public static final int Base_TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f12002a;
        public static final int Base_TextAppearance_AppCompat_SearchResult_Title = 0x7f12002b;
        public static final int Base_TextAppearance_AppCompat_Small = 0x7f12002c;
        public static final int Base_TextAppearance_AppCompat_Small_Inverse = 0x7f12002d;
        public static final int Base_TextAppearance_AppCompat_Subhead = 0x7f12002e;
        public static final int Base_TextAppearance_AppCompat_Subhead_Inverse = 0x7f12002f;
        public static final int Base_TextAppearance_AppCompat_Title = 0x7f120030;
        public static final int Base_TextAppearance_AppCompat_Title_Inverse = 0x7f120031;
        public static final int Base_TextAppearance_AppCompat_Tooltip = 0x7f120032;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f120033;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f120034;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f120035;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f120036;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f120037;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f120038;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f120039;
        public static final int Base_TextAppearance_AppCompat_Widget_Button = 0x7f12003a;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f12003b;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Colored = 0x7f12003c;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f12003d;
        public static final int Base_TextAppearance_AppCompat_Widget_DropDownItem = 0x7f12003e;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f12003f;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f120040;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f120041;
        public static final int Base_TextAppearance_AppCompat_Widget_Switch = 0x7f120042;
        public static final int Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f120043;
        public static final int Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f120048;
        public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f120049;
        public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f12004a;
        public static final int Base_ThemeOverlay_AppCompat = 0x7f120072;
        public static final int Base_ThemeOverlay_AppCompat_ActionBar = 0x7f120073;
        public static final int Base_ThemeOverlay_AppCompat_Dark = 0x7f120074;
        public static final int Base_ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f120075;
        public static final int Base_ThemeOverlay_AppCompat_Dialog = 0x7f120076;
        public static final int Base_ThemeOverlay_AppCompat_Dialog_Alert = 0x7f120077;
        public static final int Base_ThemeOverlay_AppCompat_Light = 0x7f120078;
        public static final int Base_Theme_AppCompat = 0x7f12004b;
        public static final int Base_Theme_AppCompat_CompactMenu = 0x7f12004c;
        public static final int Base_Theme_AppCompat_Dialog = 0x7f12004d;
        public static final int Base_Theme_AppCompat_DialogWhenLarge = 0x7f120051;
        public static final int Base_Theme_AppCompat_Dialog_Alert = 0x7f12004e;
        public static final int Base_Theme_AppCompat_Dialog_FixedSize = 0x7f12004f;
        public static final int Base_Theme_AppCompat_Dialog_MinWidth = 0x7f120050;
        public static final int Base_Theme_AppCompat_Light = 0x7f120052;
        public static final int Base_Theme_AppCompat_Light_DarkActionBar = 0x7f120053;
        public static final int Base_Theme_AppCompat_Light_Dialog = 0x7f120054;
        public static final int Base_Theme_AppCompat_Light_DialogWhenLarge = 0x7f120058;
        public static final int Base_Theme_AppCompat_Light_Dialog_Alert = 0x7f120055;
        public static final int Base_Theme_AppCompat_Light_Dialog_FixedSize = 0x7f120056;
        public static final int Base_Theme_AppCompat_Light_Dialog_MinWidth = 0x7f120057;
        public static final int Base_V21_ThemeOverlay_AppCompat_Dialog = 0x7f12009e;
        public static final int Base_V21_Theme_AppCompat = 0x7f120096;
        public static final int Base_V21_Theme_AppCompat_Dialog = 0x7f120097;
        public static final int Base_V21_Theme_AppCompat_Light = 0x7f120098;
        public static final int Base_V21_Theme_AppCompat_Light_Dialog = 0x7f120099;
        public static final int Base_V22_Theme_AppCompat = 0x7f1200a1;
        public static final int Base_V22_Theme_AppCompat_Light = 0x7f1200a2;
        public static final int Base_V23_Theme_AppCompat = 0x7f1200a3;
        public static final int Base_V23_Theme_AppCompat_Light = 0x7f1200a4;
        public static final int Base_V26_Theme_AppCompat = 0x7f1200a9;
        public static final int Base_V26_Theme_AppCompat_Light = 0x7f1200aa;
        public static final int Base_V26_Widget_AppCompat_Toolbar = 0x7f1200ab;
        public static final int Base_V28_Theme_AppCompat = 0x7f1200ac;
        public static final int Base_V28_Theme_AppCompat_Light = 0x7f1200ad;
        public static final int Base_V7_ThemeOverlay_AppCompat_Dialog = 0x7f1200b2;
        public static final int Base_V7_Theme_AppCompat = 0x7f1200ae;
        public static final int Base_V7_Theme_AppCompat_Dialog = 0x7f1200af;
        public static final int Base_V7_Theme_AppCompat_Light = 0x7f1200b0;
        public static final int Base_V7_Theme_AppCompat_Light_Dialog = 0x7f1200b1;
        public static final int Base_V7_Widget_AppCompat_AutoCompleteTextView = 0x7f1200b3;
        public static final int Base_V7_Widget_AppCompat_EditText = 0x7f1200b4;
        public static final int Base_V7_Widget_AppCompat_Toolbar = 0x7f1200b5;
        public static final int Base_Widget_AppCompat_ActionBar = 0x7f1200b6;
        public static final int Base_Widget_AppCompat_ActionBar_Solid = 0x7f1200b7;
        public static final int Base_Widget_AppCompat_ActionBar_TabBar = 0x7f1200b8;
        public static final int Base_Widget_AppCompat_ActionBar_TabText = 0x7f1200b9;
        public static final int Base_Widget_AppCompat_ActionBar_TabView = 0x7f1200ba;
        public static final int Base_Widget_AppCompat_ActionButton = 0x7f1200bb;
        public static final int Base_Widget_AppCompat_ActionButton_CloseMode = 0x7f1200bc;
        public static final int Base_Widget_AppCompat_ActionButton_Overflow = 0x7f1200bd;
        public static final int Base_Widget_AppCompat_ActionMode = 0x7f1200be;
        public static final int Base_Widget_AppCompat_ActivityChooserView = 0x7f1200bf;
        public static final int Base_Widget_AppCompat_AutoCompleteTextView = 0x7f1200c0;
        public static final int Base_Widget_AppCompat_Button = 0x7f1200c1;
        public static final int Base_Widget_AppCompat_ButtonBar = 0x7f1200c7;
        public static final int Base_Widget_AppCompat_ButtonBar_AlertDialog = 0x7f1200c8;
        public static final int Base_Widget_AppCompat_Button_Borderless = 0x7f1200c2;
        public static final int Base_Widget_AppCompat_Button_Borderless_Colored = 0x7f1200c3;
        public static final int Base_Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f1200c4;
        public static final int Base_Widget_AppCompat_Button_Colored = 0x7f1200c5;
        public static final int Base_Widget_AppCompat_Button_Small = 0x7f1200c6;
        public static final int Base_Widget_AppCompat_CompoundButton_CheckBox = 0x7f1200c9;
        public static final int Base_Widget_AppCompat_CompoundButton_RadioButton = 0x7f1200ca;
        public static final int Base_Widget_AppCompat_CompoundButton_Switch = 0x7f1200cb;
        public static final int Base_Widget_AppCompat_DrawerArrowToggle = 0x7f1200cc;
        public static final int Base_Widget_AppCompat_DrawerArrowToggle_Common = 0x7f1200cd;
        public static final int Base_Widget_AppCompat_DropDownItem_Spinner = 0x7f1200ce;
        public static final int Base_Widget_AppCompat_EditText = 0x7f1200cf;
        public static final int Base_Widget_AppCompat_ImageButton = 0x7f1200d0;
        public static final int Base_Widget_AppCompat_Light_ActionBar = 0x7f1200d1;
        public static final int Base_Widget_AppCompat_Light_ActionBar_Solid = 0x7f1200d2;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabBar = 0x7f1200d3;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabText = 0x7f1200d4;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f1200d5;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabView = 0x7f1200d6;
        public static final int Base_Widget_AppCompat_Light_PopupMenu = 0x7f1200d7;
        public static final int Base_Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f1200d8;
        public static final int Base_Widget_AppCompat_ListMenuView = 0x7f1200d9;
        public static final int Base_Widget_AppCompat_ListPopupWindow = 0x7f1200da;
        public static final int Base_Widget_AppCompat_ListView = 0x7f1200db;
        public static final int Base_Widget_AppCompat_ListView_DropDown = 0x7f1200dc;
        public static final int Base_Widget_AppCompat_ListView_Menu = 0x7f1200dd;
        public static final int Base_Widget_AppCompat_PopupMenu = 0x7f1200de;
        public static final int Base_Widget_AppCompat_PopupMenu_Overflow = 0x7f1200df;
        public static final int Base_Widget_AppCompat_PopupWindow = 0x7f1200e0;
        public static final int Base_Widget_AppCompat_ProgressBar = 0x7f1200e1;
        public static final int Base_Widget_AppCompat_ProgressBar_Horizontal = 0x7f1200e2;
        public static final int Base_Widget_AppCompat_RatingBar = 0x7f1200e3;
        public static final int Base_Widget_AppCompat_RatingBar_Indicator = 0x7f1200e4;
        public static final int Base_Widget_AppCompat_RatingBar_Small = 0x7f1200e5;
        public static final int Base_Widget_AppCompat_SearchView = 0x7f1200e6;
        public static final int Base_Widget_AppCompat_SearchView_ActionBar = 0x7f1200e7;
        public static final int Base_Widget_AppCompat_SeekBar = 0x7f1200e8;
        public static final int Base_Widget_AppCompat_SeekBar_Discrete = 0x7f1200e9;
        public static final int Base_Widget_AppCompat_Spinner = 0x7f1200ea;
        public static final int Base_Widget_AppCompat_Spinner_Underlined = 0x7f1200eb;
        public static final int Base_Widget_AppCompat_TextView = 0x7f1200ec;
        public static final int Base_Widget_AppCompat_TextView_SpinnerItem = 0x7f1200ed;
        public static final int Base_Widget_AppCompat_Toolbar = 0x7f1200ee;
        public static final int Base_Widget_AppCompat_Toolbar_Button_Navigation = 0x7f1200ef;
        public static final int Platform_AppCompat = 0x7f120164;
        public static final int Platform_AppCompat_Light = 0x7f120165;
        public static final int Platform_ThemeOverlay_AppCompat = 0x7f12016a;
        public static final int Platform_ThemeOverlay_AppCompat_Dark = 0x7f12016b;
        public static final int Platform_ThemeOverlay_AppCompat_Light = 0x7f12016c;
        public static final int Platform_V21_AppCompat = 0x7f12016d;
        public static final int Platform_V21_AppCompat_Light = 0x7f12016e;
        public static final int Platform_V25_AppCompat = 0x7f12016f;
        public static final int Platform_V25_AppCompat_Light = 0x7f120170;
        public static final int Platform_Widget_AppCompat_Spinner = 0x7f120171;
        public static final int RtlOverlay_DialogWindowTitle_AppCompat = 0x7f120173;
        public static final int RtlOverlay_Widget_AppCompat_ActionBar_TitleItem = 0x7f120174;
        public static final int RtlOverlay_Widget_AppCompat_DialogTitle_Icon = 0x7f120175;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem = 0x7f120176;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup = 0x7f120177;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut = 0x7f120178;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow = 0x7f120179;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Text = 0x7f12017a;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Title = 0x7f12017b;
        public static final int RtlOverlay_Widget_AppCompat_SearchView_MagIcon = 0x7f120181;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown = 0x7f12017c;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 = 0x7f12017d;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 = 0x7f12017e;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Query = 0x7f12017f;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Text = 0x7f120180;
        public static final int RtlUnderlay_Widget_AppCompat_ActionButton = 0x7f120182;
        public static final int RtlUnderlay_Widget_AppCompat_ActionButton_Overflow = 0x7f120183;
        public static final int TextAppearance_AppCompat = 0x7f1201c9;
        public static final int TextAppearance_AppCompat_Body1 = 0x7f1201ca;
        public static final int TextAppearance_AppCompat_Body2 = 0x7f1201cb;
        public static final int TextAppearance_AppCompat_Button = 0x7f1201cc;
        public static final int TextAppearance_AppCompat_Caption = 0x7f1201cd;
        public static final int TextAppearance_AppCompat_Display1 = 0x7f1201ce;
        public static final int TextAppearance_AppCompat_Display2 = 0x7f1201cf;
        public static final int TextAppearance_AppCompat_Display3 = 0x7f1201d0;
        public static final int TextAppearance_AppCompat_Display4 = 0x7f1201d1;
        public static final int TextAppearance_AppCompat_Headline = 0x7f1201d2;
        public static final int TextAppearance_AppCompat_Inverse = 0x7f1201d3;
        public static final int TextAppearance_AppCompat_Large = 0x7f1201d4;
        public static final int TextAppearance_AppCompat_Large_Inverse = 0x7f1201d5;
        public static final int TextAppearance_AppCompat_Light_SearchResult_Subtitle = 0x7f1201d6;
        public static final int TextAppearance_AppCompat_Light_SearchResult_Title = 0x7f1201d7;
        public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f1201d8;
        public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f1201d9;
        public static final int TextAppearance_AppCompat_Medium = 0x7f1201da;
        public static final int TextAppearance_AppCompat_Medium_Inverse = 0x7f1201db;
        public static final int TextAppearance_AppCompat_Menu = 0x7f1201dc;
        public static final int TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f1201dd;
        public static final int TextAppearance_AppCompat_SearchResult_Title = 0x7f1201de;
        public static final int TextAppearance_AppCompat_Small = 0x7f1201df;
        public static final int TextAppearance_AppCompat_Small_Inverse = 0x7f1201e0;
        public static final int TextAppearance_AppCompat_Subhead = 0x7f1201e1;
        public static final int TextAppearance_AppCompat_Subhead_Inverse = 0x7f1201e2;
        public static final int TextAppearance_AppCompat_Title = 0x7f1201e3;
        public static final int TextAppearance_AppCompat_Title_Inverse = 0x7f1201e4;
        public static final int TextAppearance_AppCompat_Tooltip = 0x7f1201e5;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f1201e6;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f1201e7;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f1201e8;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f1201e9;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f1201ea;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f1201eb;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse = 0x7f1201ec;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f1201ed;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse = 0x7f1201ee;
        public static final int TextAppearance_AppCompat_Widget_Button = 0x7f1201ef;
        public static final int TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f1201f0;
        public static final int TextAppearance_AppCompat_Widget_Button_Colored = 0x7f1201f1;
        public static final int TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f1201f2;
        public static final int TextAppearance_AppCompat_Widget_DropDownItem = 0x7f1201f3;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f1201f4;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f1201f5;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f1201f6;
        public static final int TextAppearance_AppCompat_Widget_Switch = 0x7f1201f7;
        public static final int TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f1201f8;
        public static final int TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f12023e;
        public static final int TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f12023f;
        public static final int TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f120240;
        public static final int ThemeOverlay_AppCompat = 0x7f1202a9;
        public static final int ThemeOverlay_AppCompat_ActionBar = 0x7f1202aa;
        public static final int ThemeOverlay_AppCompat_Dark = 0x7f1202ab;
        public static final int ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f1202ac;
        public static final int ThemeOverlay_AppCompat_DayNight = 0x7f1202ad;
        public static final int ThemeOverlay_AppCompat_DayNight_ActionBar = 0x7f1202ae;
        public static final int ThemeOverlay_AppCompat_Dialog = 0x7f1202af;
        public static final int ThemeOverlay_AppCompat_Dialog_Alert = 0x7f1202b0;
        public static final int ThemeOverlay_AppCompat_Light = 0x7f1202b1;
        public static final int Theme_AppCompat = 0x7f120241;
        public static final int Theme_AppCompat_CompactMenu = 0x7f120242;
        public static final int Theme_AppCompat_DayNight = 0x7f120243;
        public static final int Theme_AppCompat_DayNight_DarkActionBar = 0x7f120244;
        public static final int Theme_AppCompat_DayNight_Dialog = 0x7f120245;
        public static final int Theme_AppCompat_DayNight_DialogWhenLarge = 0x7f120248;
        public static final int Theme_AppCompat_DayNight_Dialog_Alert = 0x7f120246;
        public static final int Theme_AppCompat_DayNight_Dialog_MinWidth = 0x7f120247;
        public static final int Theme_AppCompat_DayNight_NoActionBar = 0x7f120249;
        public static final int Theme_AppCompat_Dialog = 0x7f12024a;
        public static final int Theme_AppCompat_DialogWhenLarge = 0x7f12024d;
        public static final int Theme_AppCompat_Dialog_Alert = 0x7f12024b;
        public static final int Theme_AppCompat_Dialog_MinWidth = 0x7f12024c;
        public static final int Theme_AppCompat_Empty = 0x7f12024e;
        public static final int Theme_AppCompat_Light = 0x7f12024f;
        public static final int Theme_AppCompat_Light_DarkActionBar = 0x7f120250;
        public static final int Theme_AppCompat_Light_Dialog = 0x7f120251;
        public static final int Theme_AppCompat_Light_DialogWhenLarge = 0x7f120254;
        public static final int Theme_AppCompat_Light_Dialog_Alert = 0x7f120252;
        public static final int Theme_AppCompat_Light_Dialog_MinWidth = 0x7f120253;
        public static final int Theme_AppCompat_Light_NoActionBar = 0x7f120255;
        public static final int Theme_AppCompat_NoActionBar = 0x7f120256;
        public static final int Widget_AppCompat_ActionBar = 0x7f120316;
        public static final int Widget_AppCompat_ActionBar_Solid = 0x7f120317;
        public static final int Widget_AppCompat_ActionBar_TabBar = 0x7f120318;
        public static final int Widget_AppCompat_ActionBar_TabText = 0x7f120319;
        public static final int Widget_AppCompat_ActionBar_TabView = 0x7f12031a;
        public static final int Widget_AppCompat_ActionButton = 0x7f12031b;
        public static final int Widget_AppCompat_ActionButton_CloseMode = 0x7f12031c;
        public static final int Widget_AppCompat_ActionButton_Overflow = 0x7f12031d;
        public static final int Widget_AppCompat_ActionMode = 0x7f12031e;
        public static final int Widget_AppCompat_ActivityChooserView = 0x7f12031f;
        public static final int Widget_AppCompat_AutoCompleteTextView = 0x7f120320;
        public static final int Widget_AppCompat_Button = 0x7f120321;
        public static final int Widget_AppCompat_ButtonBar = 0x7f120327;
        public static final int Widget_AppCompat_ButtonBar_AlertDialog = 0x7f120328;
        public static final int Widget_AppCompat_Button_Borderless = 0x7f120322;
        public static final int Widget_AppCompat_Button_Borderless_Colored = 0x7f120323;
        public static final int Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f120324;
        public static final int Widget_AppCompat_Button_Colored = 0x7f120325;
        public static final int Widget_AppCompat_Button_Small = 0x7f120326;
        public static final int Widget_AppCompat_CompoundButton_CheckBox = 0x7f120329;
        public static final int Widget_AppCompat_CompoundButton_RadioButton = 0x7f12032a;
        public static final int Widget_AppCompat_CompoundButton_Switch = 0x7f12032b;
        public static final int Widget_AppCompat_DrawerArrowToggle = 0x7f12032c;
        public static final int Widget_AppCompat_DropDownItem_Spinner = 0x7f12032d;
        public static final int Widget_AppCompat_EditText = 0x7f12032e;
        public static final int Widget_AppCompat_ImageButton = 0x7f12032f;
        public static final int Widget_AppCompat_Light_ActionBar = 0x7f120330;
        public static final int Widget_AppCompat_Light_ActionBar_Solid = 0x7f120331;
        public static final int Widget_AppCompat_Light_ActionBar_Solid_Inverse = 0x7f120332;
        public static final int Widget_AppCompat_Light_ActionBar_TabBar = 0x7f120333;
        public static final int Widget_AppCompat_Light_ActionBar_TabBar_Inverse = 0x7f120334;
        public static final int Widget_AppCompat_Light_ActionBar_TabText = 0x7f120335;
        public static final int Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f120336;
        public static final int Widget_AppCompat_Light_ActionBar_TabView = 0x7f120337;
        public static final int Widget_AppCompat_Light_ActionBar_TabView_Inverse = 0x7f120338;
        public static final int Widget_AppCompat_Light_ActionButton = 0x7f120339;
        public static final int Widget_AppCompat_Light_ActionButton_CloseMode = 0x7f12033a;
        public static final int Widget_AppCompat_Light_ActionButton_Overflow = 0x7f12033b;
        public static final int Widget_AppCompat_Light_ActionMode_Inverse = 0x7f12033c;
        public static final int Widget_AppCompat_Light_ActivityChooserView = 0x7f12033d;
        public static final int Widget_AppCompat_Light_AutoCompleteTextView = 0x7f12033e;
        public static final int Widget_AppCompat_Light_DropDownItem_Spinner = 0x7f12033f;
        public static final int Widget_AppCompat_Light_ListPopupWindow = 0x7f120340;
        public static final int Widget_AppCompat_Light_ListView_DropDown = 0x7f120341;
        public static final int Widget_AppCompat_Light_PopupMenu = 0x7f120342;
        public static final int Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f120343;
        public static final int Widget_AppCompat_Light_SearchView = 0x7f120344;
        public static final int Widget_AppCompat_Light_Spinner_DropDown_ActionBar = 0x7f120345;
        public static final int Widget_AppCompat_ListMenuView = 0x7f120346;
        public static final int Widget_AppCompat_ListPopupWindow = 0x7f120347;
        public static final int Widget_AppCompat_ListView = 0x7f120348;
        public static final int Widget_AppCompat_ListView_DropDown = 0x7f120349;
        public static final int Widget_AppCompat_ListView_Menu = 0x7f12034a;
        public static final int Widget_AppCompat_PopupMenu = 0x7f12034b;
        public static final int Widget_AppCompat_PopupMenu_Overflow = 0x7f12034c;
        public static final int Widget_AppCompat_PopupWindow = 0x7f12034d;
        public static final int Widget_AppCompat_ProgressBar = 0x7f12034e;
        public static final int Widget_AppCompat_ProgressBar_Horizontal = 0x7f12034f;
        public static final int Widget_AppCompat_RatingBar = 0x7f120350;
        public static final int Widget_AppCompat_RatingBar_Indicator = 0x7f120351;
        public static final int Widget_AppCompat_RatingBar_Small = 0x7f120352;
        public static final int Widget_AppCompat_SearchView = 0x7f120353;
        public static final int Widget_AppCompat_SearchView_ActionBar = 0x7f120354;
        public static final int Widget_AppCompat_SeekBar = 0x7f120355;
        public static final int Widget_AppCompat_SeekBar_Discrete = 0x7f120356;
        public static final int Widget_AppCompat_Spinner = 0x7f120357;
        public static final int Widget_AppCompat_Spinner_DropDown = 0x7f120358;
        public static final int Widget_AppCompat_Spinner_DropDown_ActionBar = 0x7f120359;
        public static final int Widget_AppCompat_Spinner_Underlined = 0x7f12035a;
        public static final int Widget_AppCompat_TextView = 0x7f12035b;
        public static final int Widget_AppCompat_TextView_SpinnerItem = 0x7f12035c;
        public static final int Widget_AppCompat_Toolbar = 0x7f12035d;
        public static final int Widget_AppCompat_Toolbar_Button_Navigation = 0x7f12035e;

        private style() {
        }
    }

    public static final class styleable {
        public static final int ActionBarLayout_android_layout_gravity = 0x00000000;
        public static final int ActionBar_background = 0x00000000;
        public static final int ActionBar_backgroundSplit = 0x00000001;
        public static final int ActionBar_backgroundStacked = 0x00000002;
        public static final int ActionBar_contentInsetEnd = 0x00000003;
        public static final int ActionBar_contentInsetEndWithActions = 0x00000004;
        public static final int ActionBar_contentInsetLeft = 0x00000005;
        public static final int ActionBar_contentInsetRight = 0x00000006;
        public static final int ActionBar_contentInsetStart = 0x00000007;
        public static final int ActionBar_contentInsetStartWithNavigation = 0x00000008;
        public static final int ActionBar_customNavigationLayout = 0x00000009;
        public static final int ActionBar_displayOptions = 0x0000000a;
        public static final int ActionBar_divider = 0x0000000b;
        public static final int ActionBar_elevation = 0x0000000c;
        public static final int ActionBar_height = 0x0000000d;
        public static final int ActionBar_hideOnContentScroll = 0x0000000e;
        public static final int ActionBar_homeAsUpIndicator = 0x0000000f;
        public static final int ActionBar_homeLayout = 0x00000010;
        public static final int ActionBar_icon = 0x00000011;
        public static final int ActionBar_indeterminateProgressStyle = 0x00000012;
        public static final int ActionBar_itemPadding = 0x00000013;
        public static final int ActionBar_logo = 0x00000014;
        public static final int ActionBar_navigationMode = 0x00000015;
        public static final int ActionBar_popupTheme = 0x00000016;
        public static final int ActionBar_progressBarPadding = 0x00000017;
        public static final int ActionBar_progressBarStyle = 0x00000018;
        public static final int ActionBar_subtitle = 0x00000019;
        public static final int ActionBar_subtitleTextStyle = 0x0000001a;
        public static final int ActionBar_title = 0x0000001b;
        public static final int ActionBar_titleTextStyle = 0x0000001c;
        public static final int ActionMenuItemView_android_minWidth = 0x00000000;
        public static final int ActionMode_background = 0x00000000;
        public static final int ActionMode_backgroundSplit = 0x00000001;
        public static final int ActionMode_closeItemLayout = 0x00000002;
        public static final int ActionMode_height = 0x00000003;
        public static final int ActionMode_subtitleTextStyle = 0x00000004;
        public static final int ActionMode_titleTextStyle = 0x00000005;
        public static final int ActivityChooserView_expandActivityOverflowButtonDrawable = 0x00000000;
        public static final int ActivityChooserView_initialActivityCount = 0x00000001;
        public static final int AlertDialog_android_layout = 0x00000000;
        public static final int AlertDialog_buttonIconDimen = 0x00000001;
        public static final int AlertDialog_buttonPanelSideLayout = 0x00000002;
        public static final int AlertDialog_listItemLayout = 0x00000003;
        public static final int AlertDialog_listLayout = 0x00000004;
        public static final int AlertDialog_multiChoiceItemLayout = 0x00000005;
        public static final int AlertDialog_showTitle = 0x00000006;
        public static final int AlertDialog_singleChoiceItemLayout = 0x00000007;
        public static final int AppCompatImageView_android_src = 0x00000000;
        public static final int AppCompatImageView_srcCompat = 0x00000001;
        public static final int AppCompatImageView_tint = 0x00000002;
        public static final int AppCompatImageView_tintMode = 0x00000003;
        public static final int AppCompatSeekBar_android_thumb = 0x00000000;
        public static final int AppCompatSeekBar_tickMark = 0x00000001;
        public static final int AppCompatSeekBar_tickMarkTint = 0x00000002;
        public static final int AppCompatSeekBar_tickMarkTintMode = 0x00000003;
        public static final int AppCompatTextHelper_android_drawableBottom = 0x00000002;
        public static final int AppCompatTextHelper_android_drawableEnd = 0x00000006;
        public static final int AppCompatTextHelper_android_drawableLeft = 0x00000003;
        public static final int AppCompatTextHelper_android_drawableRight = 0x00000004;
        public static final int AppCompatTextHelper_android_drawableStart = 0x00000005;
        public static final int AppCompatTextHelper_android_drawableTop = 0x00000001;
        public static final int AppCompatTextHelper_android_textAppearance = 0x00000000;
        public static final int AppCompatTextView_android_textAppearance = 0x00000000;
        public static final int AppCompatTextView_autoSizeMaxTextSize = 0x00000001;
        public static final int AppCompatTextView_autoSizeMinTextSize = 0x00000002;
        public static final int AppCompatTextView_autoSizePresetSizes = 0x00000003;
        public static final int AppCompatTextView_autoSizeStepGranularity = 0x00000004;
        public static final int AppCompatTextView_autoSizeTextType = 0x00000005;
        public static final int AppCompatTextView_drawableBottomCompat = 0x00000006;
        public static final int AppCompatTextView_drawableEndCompat = 0x00000007;
        public static final int AppCompatTextView_drawableLeftCompat = 0x00000008;
        public static final int AppCompatTextView_drawableRightCompat = 0x00000009;
        public static final int AppCompatTextView_drawableStartCompat = 0x0000000a;
        public static final int AppCompatTextView_drawableTint = 0x0000000b;
        public static final int AppCompatTextView_drawableTintMode = 0x0000000c;
        public static final int AppCompatTextView_drawableTopCompat = 0x0000000d;
        public static final int AppCompatTextView_emojiCompatEnabled = 0x0000000e;
        public static final int AppCompatTextView_firstBaselineToTopHeight = 0x0000000f;
        public static final int AppCompatTextView_fontFamily = 0x00000010;
        public static final int AppCompatTextView_fontVariationSettings = 0x00000011;
        public static final int AppCompatTextView_lastBaselineToBottomHeight = 0x00000012;
        public static final int AppCompatTextView_lineHeight = 0x00000013;
        public static final int AppCompatTextView_textAllCaps = 0x00000014;
        public static final int AppCompatTextView_textLocale = 0x00000015;
        public static final int AppCompatTheme_actionBarDivider = 0x00000002;
        public static final int AppCompatTheme_actionBarItemBackground = 0x00000003;
        public static final int AppCompatTheme_actionBarPopupTheme = 0x00000004;
        public static final int AppCompatTheme_actionBarSize = 0x00000005;
        public static final int AppCompatTheme_actionBarSplitStyle = 0x00000006;
        public static final int AppCompatTheme_actionBarStyle = 0x00000007;
        public static final int AppCompatTheme_actionBarTabBarStyle = 0x00000008;
        public static final int AppCompatTheme_actionBarTabStyle = 0x00000009;
        public static final int AppCompatTheme_actionBarTabTextStyle = 0x0000000a;
        public static final int AppCompatTheme_actionBarTheme = 0x0000000b;
        public static final int AppCompatTheme_actionBarWidgetTheme = 0x0000000c;
        public static final int AppCompatTheme_actionButtonStyle = 0x0000000d;
        public static final int AppCompatTheme_actionDropDownStyle = 0x0000000e;
        public static final int AppCompatTheme_actionMenuTextAppearance = 0x0000000f;
        public static final int AppCompatTheme_actionMenuTextColor = 0x00000010;
        public static final int AppCompatTheme_actionModeBackground = 0x00000011;
        public static final int AppCompatTheme_actionModeCloseButtonStyle = 0x00000012;
        public static final int AppCompatTheme_actionModeCloseContentDescription = 0x00000013;
        public static final int AppCompatTheme_actionModeCloseDrawable = 0x00000014;
        public static final int AppCompatTheme_actionModeCopyDrawable = 0x00000015;
        public static final int AppCompatTheme_actionModeCutDrawable = 0x00000016;
        public static final int AppCompatTheme_actionModeFindDrawable = 0x00000017;
        public static final int AppCompatTheme_actionModePasteDrawable = 0x00000018;
        public static final int AppCompatTheme_actionModePopupWindowStyle = 0x00000019;
        public static final int AppCompatTheme_actionModeSelectAllDrawable = 0x0000001a;
        public static final int AppCompatTheme_actionModeShareDrawable = 0x0000001b;
        public static final int AppCompatTheme_actionModeSplitBackground = 0x0000001c;
        public static final int AppCompatTheme_actionModeStyle = 0x0000001d;
        public static final int AppCompatTheme_actionModeTheme = 0x0000001e;
        public static final int AppCompatTheme_actionModeWebSearchDrawable = 0x0000001f;
        public static final int AppCompatTheme_actionOverflowButtonStyle = 0x00000020;
        public static final int AppCompatTheme_actionOverflowMenuStyle = 0x00000021;
        public static final int AppCompatTheme_activityChooserViewStyle = 0x00000022;
        public static final int AppCompatTheme_alertDialogButtonGroupStyle = 0x00000023;
        public static final int AppCompatTheme_alertDialogCenterButtons = 0x00000024;
        public static final int AppCompatTheme_alertDialogStyle = 0x00000025;
        public static final int AppCompatTheme_alertDialogTheme = 0x00000026;
        public static final int AppCompatTheme_android_windowAnimationStyle = 0x00000001;
        public static final int AppCompatTheme_android_windowIsFloating = 0x00000000;
        public static final int AppCompatTheme_autoCompleteTextViewStyle = 0x00000027;
        public static final int AppCompatTheme_borderlessButtonStyle = 0x00000028;
        public static final int AppCompatTheme_buttonBarButtonStyle = 0x00000029;
        public static final int AppCompatTheme_buttonBarNegativeButtonStyle = 0x0000002a;
        public static final int AppCompatTheme_buttonBarNeutralButtonStyle = 0x0000002b;
        public static final int AppCompatTheme_buttonBarPositiveButtonStyle = 0x0000002c;
        public static final int AppCompatTheme_buttonBarStyle = 0x0000002d;
        public static final int AppCompatTheme_buttonStyle = 0x0000002e;
        public static final int AppCompatTheme_buttonStyleSmall = 0x0000002f;
        public static final int AppCompatTheme_checkboxStyle = 0x00000030;
        public static final int AppCompatTheme_checkedTextViewStyle = 0x00000031;
        public static final int AppCompatTheme_colorAccent = 0x00000032;
        public static final int AppCompatTheme_colorBackgroundFloating = 0x00000033;
        public static final int AppCompatTheme_colorButtonNormal = 0x00000034;
        public static final int AppCompatTheme_colorControlActivated = 0x00000035;
        public static final int AppCompatTheme_colorControlHighlight = 0x00000036;
        public static final int AppCompatTheme_colorControlNormal = 0x00000037;
        public static final int AppCompatTheme_colorError = 0x00000038;
        public static final int AppCompatTheme_colorPrimary = 0x00000039;
        public static final int AppCompatTheme_colorPrimaryDark = 0x0000003a;
        public static final int AppCompatTheme_colorSwitchThumbNormal = 0x0000003b;
        public static final int AppCompatTheme_controlBackground = 0x0000003c;
        public static final int AppCompatTheme_dialogCornerRadius = 0x0000003d;
        public static final int AppCompatTheme_dialogPreferredPadding = 0x0000003e;
        public static final int AppCompatTheme_dialogTheme = 0x0000003f;
        public static final int AppCompatTheme_dividerHorizontal = 0x00000040;
        public static final int AppCompatTheme_dividerVertical = 0x00000041;
        public static final int AppCompatTheme_dropDownListViewStyle = 0x00000042;
        public static final int AppCompatTheme_dropdownListPreferredItemHeight = 0x00000043;
        public static final int AppCompatTheme_editTextBackground = 0x00000044;
        public static final int AppCompatTheme_editTextColor = 0x00000045;
        public static final int AppCompatTheme_editTextStyle = 0x00000046;
        public static final int AppCompatTheme_homeAsUpIndicator = 0x00000047;
        public static final int AppCompatTheme_imageButtonStyle = 0x00000048;
        public static final int AppCompatTheme_listChoiceBackgroundIndicator = 0x00000049;
        public static final int AppCompatTheme_listChoiceIndicatorMultipleAnimated = 0x0000004a;
        public static final int AppCompatTheme_listChoiceIndicatorSingleAnimated = 0x0000004b;
        public static final int AppCompatTheme_listDividerAlertDialog = 0x0000004c;
        public static final int AppCompatTheme_listMenuViewStyle = 0x0000004d;
        public static final int AppCompatTheme_listPopupWindowStyle = 0x0000004e;
        public static final int AppCompatTheme_listPreferredItemHeight = 0x0000004f;
        public static final int AppCompatTheme_listPreferredItemHeightLarge = 0x00000050;
        public static final int AppCompatTheme_listPreferredItemHeightSmall = 0x00000051;
        public static final int AppCompatTheme_listPreferredItemPaddingEnd = 0x00000052;
        public static final int AppCompatTheme_listPreferredItemPaddingLeft = 0x00000053;
        public static final int AppCompatTheme_listPreferredItemPaddingRight = 0x00000054;
        public static final int AppCompatTheme_listPreferredItemPaddingStart = 0x00000055;
        public static final int AppCompatTheme_panelBackground = 0x00000056;
        public static final int AppCompatTheme_panelMenuListTheme = 0x00000057;
        public static final int AppCompatTheme_panelMenuListWidth = 0x00000058;
        public static final int AppCompatTheme_popupMenuStyle = 0x00000059;
        public static final int AppCompatTheme_popupWindowStyle = 0x0000005a;
        public static final int AppCompatTheme_radioButtonStyle = 0x0000005b;
        public static final int AppCompatTheme_ratingBarStyle = 0x0000005c;
        public static final int AppCompatTheme_ratingBarStyleIndicator = 0x0000005d;
        public static final int AppCompatTheme_ratingBarStyleSmall = 0x0000005e;
        public static final int AppCompatTheme_searchViewStyle = 0x0000005f;
        public static final int AppCompatTheme_seekBarStyle = 0x00000060;
        public static final int AppCompatTheme_selectableItemBackground = 0x00000061;
        public static final int AppCompatTheme_selectableItemBackgroundBorderless = 0x00000062;
        public static final int AppCompatTheme_spinnerDropDownItemStyle = 0x00000063;
        public static final int AppCompatTheme_spinnerStyle = 0x00000064;
        public static final int AppCompatTheme_switchStyle = 0x00000065;
        public static final int AppCompatTheme_textAppearanceLargePopupMenu = 0x00000066;
        public static final int AppCompatTheme_textAppearanceListItem = 0x00000067;
        public static final int AppCompatTheme_textAppearanceListItemSecondary = 0x00000068;
        public static final int AppCompatTheme_textAppearanceListItemSmall = 0x00000069;
        public static final int AppCompatTheme_textAppearancePopupMenuHeader = 0x0000006a;
        public static final int AppCompatTheme_textAppearanceSearchResultSubtitle = 0x0000006b;
        public static final int AppCompatTheme_textAppearanceSearchResultTitle = 0x0000006c;
        public static final int AppCompatTheme_textAppearanceSmallPopupMenu = 0x0000006d;
        public static final int AppCompatTheme_textColorAlertDialogListItem = 0x0000006e;
        public static final int AppCompatTheme_textColorSearchUrl = 0x0000006f;
        public static final int AppCompatTheme_toolbarNavigationButtonStyle = 0x00000070;
        public static final int AppCompatTheme_toolbarStyle = 0x00000071;
        public static final int AppCompatTheme_tooltipForegroundColor = 0x00000072;
        public static final int AppCompatTheme_tooltipFrameBackground = 0x00000073;
        public static final int AppCompatTheme_viewInflaterClass = 0x00000074;
        public static final int AppCompatTheme_windowActionBar = 0x00000075;
        public static final int AppCompatTheme_windowActionBarOverlay = 0x00000076;
        public static final int AppCompatTheme_windowActionModeOverlay = 0x00000077;
        public static final int AppCompatTheme_windowFixedHeightMajor = 0x00000078;
        public static final int AppCompatTheme_windowFixedHeightMinor = 0x00000079;
        public static final int AppCompatTheme_windowFixedWidthMajor = 0x0000007a;
        public static final int AppCompatTheme_windowFixedWidthMinor = 0x0000007b;
        public static final int AppCompatTheme_windowMinWidthMajor = 0x0000007c;
        public static final int AppCompatTheme_windowMinWidthMinor = 0x0000007d;
        public static final int AppCompatTheme_windowNoTitle = 0x0000007e;
        public static final int ButtonBarLayout_allowStacking = 0x00000000;
        public static final int CheckedTextView_android_checkMark = 0x00000000;
        public static final int CheckedTextView_checkMarkCompat = 0x00000001;
        public static final int CheckedTextView_checkMarkTint = 0x00000002;
        public static final int CheckedTextView_checkMarkTintMode = 0x00000003;
        public static final int CompoundButton_android_button = 0x00000000;
        public static final int CompoundButton_buttonCompat = 0x00000001;
        public static final int CompoundButton_buttonTint = 0x00000002;
        public static final int CompoundButton_buttonTintMode = 0x00000003;
        public static final int DrawerArrowToggle_arrowHeadLength = 0x00000000;
        public static final int DrawerArrowToggle_arrowShaftLength = 0x00000001;
        public static final int DrawerArrowToggle_barLength = 0x00000002;
        public static final int DrawerArrowToggle_color = 0x00000003;
        public static final int DrawerArrowToggle_drawableSize = 0x00000004;
        public static final int DrawerArrowToggle_gapBetweenBars = 0x00000005;
        public static final int DrawerArrowToggle_spinBars = 0x00000006;
        public static final int DrawerArrowToggle_thickness = 0x00000007;
        public static final int LinearLayoutCompat_Layout_android_layout_gravity = 0x00000000;
        public static final int LinearLayoutCompat_Layout_android_layout_height = 0x00000002;
        public static final int LinearLayoutCompat_Layout_android_layout_weight = 0x00000003;
        public static final int LinearLayoutCompat_Layout_android_layout_width = 0x00000001;
        public static final int LinearLayoutCompat_android_baselineAligned = 0x00000002;
        public static final int LinearLayoutCompat_android_baselineAlignedChildIndex = 0x00000003;
        public static final int LinearLayoutCompat_android_gravity = 0x00000000;
        public static final int LinearLayoutCompat_android_orientation = 0x00000001;
        public static final int LinearLayoutCompat_android_weightSum = 0x00000004;
        public static final int LinearLayoutCompat_divider = 0x00000005;
        public static final int LinearLayoutCompat_dividerPadding = 0x00000006;
        public static final int LinearLayoutCompat_measureWithLargestChild = 0x00000007;
        public static final int LinearLayoutCompat_showDividers = 0x00000008;
        public static final int ListPopupWindow_android_dropDownHorizontalOffset = 0x00000000;
        public static final int ListPopupWindow_android_dropDownVerticalOffset = 0x00000001;
        public static final int MenuGroup_android_checkableBehavior = 0x00000005;
        public static final int MenuGroup_android_enabled = 0x00000000;
        public static final int MenuGroup_android_id = 0x00000001;
        public static final int MenuGroup_android_menuCategory = 0x00000003;
        public static final int MenuGroup_android_orderInCategory = 0x00000004;
        public static final int MenuGroup_android_visible = 0x00000002;
        public static final int MenuItem_actionLayout = 0x0000000d;
        public static final int MenuItem_actionProviderClass = 0x0000000e;
        public static final int MenuItem_actionViewClass = 0x0000000f;
        public static final int MenuItem_alphabeticModifiers = 0x00000010;
        public static final int MenuItem_android_alphabeticShortcut = 0x00000009;
        public static final int MenuItem_android_checkable = 0x0000000b;
        public static final int MenuItem_android_checked = 0x00000003;
        public static final int MenuItem_android_enabled = 0x00000001;
        public static final int MenuItem_android_icon = 0x00000000;
        public static final int MenuItem_android_id = 0x00000002;
        public static final int MenuItem_android_menuCategory = 0x00000005;
        public static final int MenuItem_android_numericShortcut = 0x0000000a;
        public static final int MenuItem_android_onClick = 0x0000000c;
        public static final int MenuItem_android_orderInCategory = 0x00000006;
        public static final int MenuItem_android_title = 0x00000007;
        public static final int MenuItem_android_titleCondensed = 0x00000008;
        public static final int MenuItem_android_visible = 0x00000004;
        public static final int MenuItem_contentDescription = 0x00000011;
        public static final int MenuItem_iconTint = 0x00000012;
        public static final int MenuItem_iconTintMode = 0x00000013;
        public static final int MenuItem_numericModifiers = 0x00000014;
        public static final int MenuItem_showAsAction = 0x00000015;
        public static final int MenuItem_tooltipText = 0x00000016;
        public static final int MenuView_android_headerBackground = 0x00000004;
        public static final int MenuView_android_horizontalDivider = 0x00000002;
        public static final int MenuView_android_itemBackground = 0x00000005;
        public static final int MenuView_android_itemIconDisabledAlpha = 0x00000006;
        public static final int MenuView_android_itemTextAppearance = 0x00000001;
        public static final int MenuView_android_verticalDivider = 0x00000003;
        public static final int MenuView_android_windowAnimationStyle = 0x00000000;
        public static final int MenuView_preserveIconSpacing = 0x00000007;
        public static final int MenuView_subMenuArrow = 0x00000008;
        public static final int PopupWindowBackgroundState_state_above_anchor = 0x00000000;
        public static final int PopupWindow_android_popupAnimationStyle = 0x00000001;
        public static final int PopupWindow_android_popupBackground = 0x00000000;
        public static final int PopupWindow_overlapAnchor = 0x00000002;
        public static final int RecycleListView_paddingBottomNoButtons = 0x00000000;
        public static final int RecycleListView_paddingTopNoTitle = 0x00000001;
        public static final int SearchView_android_focusable = 0x00000000;
        public static final int SearchView_android_imeOptions = 0x00000003;
        public static final int SearchView_android_inputType = 0x00000002;
        public static final int SearchView_android_maxWidth = 0x00000001;
        public static final int SearchView_closeIcon = 0x00000004;
        public static final int SearchView_commitIcon = 0x00000005;
        public static final int SearchView_defaultQueryHint = 0x00000006;
        public static final int SearchView_goIcon = 0x00000007;
        public static final int SearchView_iconifiedByDefault = 0x00000008;
        public static final int SearchView_layout = 0x00000009;
        public static final int SearchView_queryBackground = 0x0000000a;
        public static final int SearchView_queryHint = 0x0000000b;
        public static final int SearchView_searchHintIcon = 0x0000000c;
        public static final int SearchView_searchIcon = 0x0000000d;
        public static final int SearchView_submitBackground = 0x0000000e;
        public static final int SearchView_suggestionRowLayout = 0x0000000f;
        public static final int SearchView_voiceIcon = 0x00000010;
        public static final int Spinner_android_dropDownWidth = 0x00000003;
        public static final int Spinner_android_entries = 0x00000000;
        public static final int Spinner_android_popupBackground = 0x00000001;
        public static final int Spinner_android_prompt = 0x00000002;
        public static final int Spinner_popupTheme = 0x00000004;
        public static final int SwitchCompat_android_textOff = 0x00000001;
        public static final int SwitchCompat_android_textOn = 0x00000000;
        public static final int SwitchCompat_android_thumb = 0x00000002;
        public static final int SwitchCompat_showText = 0x00000003;
        public static final int SwitchCompat_splitTrack = 0x00000004;
        public static final int SwitchCompat_switchMinWidth = 0x00000005;
        public static final int SwitchCompat_switchPadding = 0x00000006;
        public static final int SwitchCompat_switchTextAppearance = 0x00000007;
        public static final int SwitchCompat_thumbTextPadding = 0x00000008;
        public static final int SwitchCompat_thumbTint = 0x00000009;
        public static final int SwitchCompat_thumbTintMode = 0x0000000a;
        public static final int SwitchCompat_track = 0x0000000b;
        public static final int SwitchCompat_trackTint = 0x0000000c;
        public static final int SwitchCompat_trackTintMode = 0x0000000d;
        public static final int TextAppearance_android_fontFamily = 0x0000000a;
        public static final int TextAppearance_android_shadowColor = 0x00000006;
        public static final int TextAppearance_android_shadowDx = 0x00000007;
        public static final int TextAppearance_android_shadowDy = 0x00000008;
        public static final int TextAppearance_android_shadowRadius = 0x00000009;
        public static final int TextAppearance_android_textColor = 0x00000003;
        public static final int TextAppearance_android_textColorHint = 0x00000004;
        public static final int TextAppearance_android_textColorLink = 0x00000005;
        public static final int TextAppearance_android_textFontWeight = 0x0000000b;
        public static final int TextAppearance_android_textSize = 0x00000000;
        public static final int TextAppearance_android_textStyle = 0x00000002;
        public static final int TextAppearance_android_typeface = 0x00000001;
        public static final int TextAppearance_fontFamily = 0x0000000c;
        public static final int TextAppearance_fontVariationSettings = 0x0000000d;
        public static final int TextAppearance_textAllCaps = 0x0000000e;
        public static final int TextAppearance_textLocale = 0x0000000f;
        public static final int Toolbar_android_gravity = 0x00000000;
        public static final int Toolbar_android_minHeight = 0x00000001;
        public static final int Toolbar_buttonGravity = 0x00000002;
        public static final int Toolbar_collapseContentDescription = 0x00000003;
        public static final int Toolbar_collapseIcon = 0x00000004;
        public static final int Toolbar_contentInsetEnd = 0x00000005;
        public static final int Toolbar_contentInsetEndWithActions = 0x00000006;
        public static final int Toolbar_contentInsetLeft = 0x00000007;
        public static final int Toolbar_contentInsetRight = 0x00000008;
        public static final int Toolbar_contentInsetStart = 0x00000009;
        public static final int Toolbar_contentInsetStartWithNavigation = 0x0000000a;
        public static final int Toolbar_logo = 0x0000000b;
        public static final int Toolbar_logoDescription = 0x0000000c;
        public static final int Toolbar_maxButtonHeight = 0x0000000d;
        public static final int Toolbar_menu = 0x0000000e;
        public static final int Toolbar_navigationContentDescription = 0x0000000f;
        public static final int Toolbar_navigationIcon = 0x00000010;
        public static final int Toolbar_popupTheme = 0x00000011;
        public static final int Toolbar_subtitle = 0x00000012;
        public static final int Toolbar_subtitleTextAppearance = 0x00000013;
        public static final int Toolbar_subtitleTextColor = 0x00000014;
        public static final int Toolbar_title = 0x00000015;
        public static final int Toolbar_titleMargin = 0x00000016;
        public static final int Toolbar_titleMarginBottom = 0x00000017;
        public static final int Toolbar_titleMarginEnd = 0x00000018;
        public static final int Toolbar_titleMarginStart = 0x00000019;
        public static final int Toolbar_titleMarginTop = 0x0000001a;
        public static final int Toolbar_titleMargins = 0x0000001b;
        public static final int Toolbar_titleTextAppearance = 0x0000001c;
        public static final int Toolbar_titleTextColor = 0x0000001d;
        public static final int ViewBackgroundHelper_android_background = 0x00000000;
        public static final int ViewBackgroundHelper_backgroundTint = 0x00000001;
        public static final int ViewBackgroundHelper_backgroundTintMode = 0x00000002;
        public static final int ViewStubCompat_android_id = 0x00000000;
        public static final int ViewStubCompat_android_inflatedId = 0x00000002;
        public static final int ViewStubCompat_android_layout = 0x00000001;
        public static final int View_android_focusable = 0x00000001;
        public static final int View_android_theme = 0x00000000;
        public static final int View_paddingEnd = 0x00000002;
        public static final int View_paddingStart = 0x00000003;
        public static final int View_theme = 0x00000004;
        public static final int[] ActionBar = {com.incall.apps.softmanager.R.attr.background, com.incall.apps.softmanager.R.attr.backgroundSplit, com.incall.apps.softmanager.R.attr.backgroundStacked, com.incall.apps.softmanager.R.attr.contentInsetEnd, com.incall.apps.softmanager.R.attr.contentInsetEndWithActions, com.incall.apps.softmanager.R.attr.contentInsetLeft, com.incall.apps.softmanager.R.attr.contentInsetRight, com.incall.apps.softmanager.R.attr.contentInsetStart, com.incall.apps.softmanager.R.attr.contentInsetStartWithNavigation, com.incall.apps.softmanager.R.attr.customNavigationLayout, com.incall.apps.softmanager.R.attr.displayOptions, com.incall.apps.softmanager.R.attr.divider, com.incall.apps.softmanager.R.attr.elevation, com.incall.apps.softmanager.R.attr.height, com.incall.apps.softmanager.R.attr.hideOnContentScroll, com.incall.apps.softmanager.R.attr.homeAsUpIndicator, com.incall.apps.softmanager.R.attr.homeLayout, com.incall.apps.softmanager.R.attr.icon, com.incall.apps.softmanager.R.attr.indeterminateProgressStyle, com.incall.apps.softmanager.R.attr.itemPadding, com.incall.apps.softmanager.R.attr.logo, com.incall.apps.softmanager.R.attr.navigationMode, com.incall.apps.softmanager.R.attr.popupTheme, com.incall.apps.softmanager.R.attr.progressBarPadding, com.incall.apps.softmanager.R.attr.progressBarStyle, com.incall.apps.softmanager.R.attr.subtitle, com.incall.apps.softmanager.R.attr.subtitleTextStyle, com.incall.apps.softmanager.R.attr.title, com.incall.apps.softmanager.R.attr.titleTextStyle};
        public static final int[] ActionBarLayout = {android.R.attr.layout_gravity};
        public static final int[] ActionMenuItemView = {android.R.attr.minWidth};
        public static final int[] ActionMenuView = new int[0];
        public static final int[] ActionMode = {com.incall.apps.softmanager.R.attr.background, com.incall.apps.softmanager.R.attr.backgroundSplit, com.incall.apps.softmanager.R.attr.closeItemLayout, com.incall.apps.softmanager.R.attr.height, com.incall.apps.softmanager.R.attr.subtitleTextStyle, com.incall.apps.softmanager.R.attr.titleTextStyle};
        public static final int[] ActivityChooserView = {com.incall.apps.softmanager.R.attr.expandActivityOverflowButtonDrawable, com.incall.apps.softmanager.R.attr.initialActivityCount};
        public static final int[] AlertDialog = {android.R.attr.layout, com.incall.apps.softmanager.R.attr.buttonIconDimen, com.incall.apps.softmanager.R.attr.buttonPanelSideLayout, com.incall.apps.softmanager.R.attr.listItemLayout, com.incall.apps.softmanager.R.attr.listLayout, com.incall.apps.softmanager.R.attr.multiChoiceItemLayout, com.incall.apps.softmanager.R.attr.showTitle, com.incall.apps.softmanager.R.attr.singleChoiceItemLayout};
        public static final int[] AppCompatEmojiHelper = new int[0];
        public static final int[] AppCompatImageView = {android.R.attr.src, com.incall.apps.softmanager.R.attr.srcCompat, com.incall.apps.softmanager.R.attr.tint, com.incall.apps.softmanager.R.attr.tintMode};
        public static final int[] AppCompatSeekBar = {android.R.attr.thumb, com.incall.apps.softmanager.R.attr.tickMark, com.incall.apps.softmanager.R.attr.tickMarkTint, com.incall.apps.softmanager.R.attr.tickMarkTintMode};
        public static final int[] AppCompatTextHelper = {android.R.attr.textAppearance, android.R.attr.drawableTop, android.R.attr.drawableBottom, android.R.attr.drawableLeft, android.R.attr.drawableRight, android.R.attr.drawableStart, android.R.attr.drawableEnd};
        public static final int[] AppCompatTextView = {android.R.attr.textAppearance, com.incall.apps.softmanager.R.attr.autoSizeMaxTextSize, com.incall.apps.softmanager.R.attr.autoSizeMinTextSize, com.incall.apps.softmanager.R.attr.autoSizePresetSizes, com.incall.apps.softmanager.R.attr.autoSizeStepGranularity, com.incall.apps.softmanager.R.attr.autoSizeTextType, com.incall.apps.softmanager.R.attr.drawableBottomCompat, com.incall.apps.softmanager.R.attr.drawableEndCompat, com.incall.apps.softmanager.R.attr.drawableLeftCompat, com.incall.apps.softmanager.R.attr.drawableRightCompat, com.incall.apps.softmanager.R.attr.drawableStartCompat, com.incall.apps.softmanager.R.attr.drawableTint, com.incall.apps.softmanager.R.attr.drawableTintMode, com.incall.apps.softmanager.R.attr.drawableTopCompat, com.incall.apps.softmanager.R.attr.emojiCompatEnabled, com.incall.apps.softmanager.R.attr.firstBaselineToTopHeight, com.incall.apps.softmanager.R.attr.fontFamily, com.incall.apps.softmanager.R.attr.fontVariationSettings, com.incall.apps.softmanager.R.attr.lastBaselineToBottomHeight, com.incall.apps.softmanager.R.attr.lineHeight, com.incall.apps.softmanager.R.attr.textAllCaps, com.incall.apps.softmanager.R.attr.textLocale};
        public static final int[] AppCompatTheme = {android.R.attr.windowIsFloating, android.R.attr.windowAnimationStyle, com.incall.apps.softmanager.R.attr.actionBarDivider, com.incall.apps.softmanager.R.attr.actionBarItemBackground, com.incall.apps.softmanager.R.attr.actionBarPopupTheme, com.incall.apps.softmanager.R.attr.actionBarSize, com.incall.apps.softmanager.R.attr.actionBarSplitStyle, com.incall.apps.softmanager.R.attr.actionBarStyle, com.incall.apps.softmanager.R.attr.actionBarTabBarStyle, com.incall.apps.softmanager.R.attr.actionBarTabStyle, com.incall.apps.softmanager.R.attr.actionBarTabTextStyle, com.incall.apps.softmanager.R.attr.actionBarTheme, com.incall.apps.softmanager.R.attr.actionBarWidgetTheme, com.incall.apps.softmanager.R.attr.actionButtonStyle, com.incall.apps.softmanager.R.attr.actionDropDownStyle, com.incall.apps.softmanager.R.attr.actionMenuTextAppearance, com.incall.apps.softmanager.R.attr.actionMenuTextColor, com.incall.apps.softmanager.R.attr.actionModeBackground, com.incall.apps.softmanager.R.attr.actionModeCloseButtonStyle, com.incall.apps.softmanager.R.attr.actionModeCloseContentDescription, com.incall.apps.softmanager.R.attr.actionModeCloseDrawable, com.incall.apps.softmanager.R.attr.actionModeCopyDrawable, com.incall.apps.softmanager.R.attr.actionModeCutDrawable, com.incall.apps.softmanager.R.attr.actionModeFindDrawable, com.incall.apps.softmanager.R.attr.actionModePasteDrawable, com.incall.apps.softmanager.R.attr.actionModePopupWindowStyle, com.incall.apps.softmanager.R.attr.actionModeSelectAllDrawable, com.incall.apps.softmanager.R.attr.actionModeShareDrawable, com.incall.apps.softmanager.R.attr.actionModeSplitBackground, com.incall.apps.softmanager.R.attr.actionModeStyle, com.incall.apps.softmanager.R.attr.actionModeTheme, com.incall.apps.softmanager.R.attr.actionModeWebSearchDrawable, com.incall.apps.softmanager.R.attr.actionOverflowButtonStyle, com.incall.apps.softmanager.R.attr.actionOverflowMenuStyle, com.incall.apps.softmanager.R.attr.activityChooserViewStyle, com.incall.apps.softmanager.R.attr.alertDialogButtonGroupStyle, com.incall.apps.softmanager.R.attr.alertDialogCenterButtons, com.incall.apps.softmanager.R.attr.alertDialogStyle, com.incall.apps.softmanager.R.attr.alertDialogTheme, com.incall.apps.softmanager.R.attr.autoCompleteTextViewStyle, com.incall.apps.softmanager.R.attr.borderlessButtonStyle, com.incall.apps.softmanager.R.attr.buttonBarButtonStyle, com.incall.apps.softmanager.R.attr.buttonBarNegativeButtonStyle, com.incall.apps.softmanager.R.attr.buttonBarNeutralButtonStyle, com.incall.apps.softmanager.R.attr.buttonBarPositiveButtonStyle, com.incall.apps.softmanager.R.attr.buttonBarStyle, com.incall.apps.softmanager.R.attr.buttonStyle, com.incall.apps.softmanager.R.attr.buttonStyleSmall, com.incall.apps.softmanager.R.attr.checkboxStyle, com.incall.apps.softmanager.R.attr.checkedTextViewStyle, com.incall.apps.softmanager.R.attr.colorAccent, com.incall.apps.softmanager.R.attr.colorBackgroundFloating, com.incall.apps.softmanager.R.attr.colorButtonNormal, com.incall.apps.softmanager.R.attr.colorControlActivated, com.incall.apps.softmanager.R.attr.colorControlHighlight, com.incall.apps.softmanager.R.attr.colorControlNormal, com.incall.apps.softmanager.R.attr.colorError, com.incall.apps.softmanager.R.attr.colorPrimary, com.incall.apps.softmanager.R.attr.colorPrimaryDark, com.incall.apps.softmanager.R.attr.colorSwitchThumbNormal, com.incall.apps.softmanager.R.attr.controlBackground, com.incall.apps.softmanager.R.attr.dialogCornerRadius, com.incall.apps.softmanager.R.attr.dialogPreferredPadding, com.incall.apps.softmanager.R.attr.dialogTheme, com.incall.apps.softmanager.R.attr.dividerHorizontal, com.incall.apps.softmanager.R.attr.dividerVertical, com.incall.apps.softmanager.R.attr.dropDownListViewStyle, com.incall.apps.softmanager.R.attr.dropdownListPreferredItemHeight, com.incall.apps.softmanager.R.attr.editTextBackground, com.incall.apps.softmanager.R.attr.editTextColor, com.incall.apps.softmanager.R.attr.editTextStyle, com.incall.apps.softmanager.R.attr.homeAsUpIndicator, com.incall.apps.softmanager.R.attr.imageButtonStyle, com.incall.apps.softmanager.R.attr.listChoiceBackgroundIndicator, com.incall.apps.softmanager.R.attr.listChoiceIndicatorMultipleAnimated, com.incall.apps.softmanager.R.attr.listChoiceIndicatorSingleAnimated, com.incall.apps.softmanager.R.attr.listDividerAlertDialog, com.incall.apps.softmanager.R.attr.listMenuViewStyle, com.incall.apps.softmanager.R.attr.listPopupWindowStyle, com.incall.apps.softmanager.R.attr.listPreferredItemHeight, com.incall.apps.softmanager.R.attr.listPreferredItemHeightLarge, com.incall.apps.softmanager.R.attr.listPreferredItemHeightSmall, com.incall.apps.softmanager.R.attr.listPreferredItemPaddingEnd, com.incall.apps.softmanager.R.attr.listPreferredItemPaddingLeft, com.incall.apps.softmanager.R.attr.listPreferredItemPaddingRight, com.incall.apps.softmanager.R.attr.listPreferredItemPaddingStart, com.incall.apps.softmanager.R.attr.panelBackground, com.incall.apps.softmanager.R.attr.panelMenuListTheme, com.incall.apps.softmanager.R.attr.panelMenuListWidth, com.incall.apps.softmanager.R.attr.popupMenuStyle, com.incall.apps.softmanager.R.attr.popupWindowStyle, com.incall.apps.softmanager.R.attr.radioButtonStyle, com.incall.apps.softmanager.R.attr.ratingBarStyle, com.incall.apps.softmanager.R.attr.ratingBarStyleIndicator, com.incall.apps.softmanager.R.attr.ratingBarStyleSmall, com.incall.apps.softmanager.R.attr.searchViewStyle, com.incall.apps.softmanager.R.attr.seekBarStyle, com.incall.apps.softmanager.R.attr.selectableItemBackground, com.incall.apps.softmanager.R.attr.selectableItemBackgroundBorderless, com.incall.apps.softmanager.R.attr.spinnerDropDownItemStyle, com.incall.apps.softmanager.R.attr.spinnerStyle, com.incall.apps.softmanager.R.attr.switchStyle, com.incall.apps.softmanager.R.attr.textAppearanceLargePopupMenu, com.incall.apps.softmanager.R.attr.textAppearanceListItem, com.incall.apps.softmanager.R.attr.textAppearanceListItemSecondary, com.incall.apps.softmanager.R.attr.textAppearanceListItemSmall, com.incall.apps.softmanager.R.attr.textAppearancePopupMenuHeader, com.incall.apps.softmanager.R.attr.textAppearanceSearchResultSubtitle, com.incall.apps.softmanager.R.attr.textAppearanceSearchResultTitle, com.incall.apps.softmanager.R.attr.textAppearanceSmallPopupMenu, com.incall.apps.softmanager.R.attr.textColorAlertDialogListItem, com.incall.apps.softmanager.R.attr.textColorSearchUrl, com.incall.apps.softmanager.R.attr.toolbarNavigationButtonStyle, com.incall.apps.softmanager.R.attr.toolbarStyle, com.incall.apps.softmanager.R.attr.tooltipForegroundColor, com.incall.apps.softmanager.R.attr.tooltipFrameBackground, com.incall.apps.softmanager.R.attr.viewInflaterClass, com.incall.apps.softmanager.R.attr.windowActionBar, com.incall.apps.softmanager.R.attr.windowActionBarOverlay, com.incall.apps.softmanager.R.attr.windowActionModeOverlay, com.incall.apps.softmanager.R.attr.windowFixedHeightMajor, com.incall.apps.softmanager.R.attr.windowFixedHeightMinor, com.incall.apps.softmanager.R.attr.windowFixedWidthMajor, com.incall.apps.softmanager.R.attr.windowFixedWidthMinor, com.incall.apps.softmanager.R.attr.windowMinWidthMajor, com.incall.apps.softmanager.R.attr.windowMinWidthMinor, com.incall.apps.softmanager.R.attr.windowNoTitle};
        public static final int[] ButtonBarLayout = {com.incall.apps.softmanager.R.attr.allowStacking};
        public static final int[] CheckedTextView = {android.R.attr.checkMark, com.incall.apps.softmanager.R.attr.checkMarkCompat, com.incall.apps.softmanager.R.attr.checkMarkTint, com.incall.apps.softmanager.R.attr.checkMarkTintMode};
        public static final int[] CompoundButton = {android.R.attr.button, com.incall.apps.softmanager.R.attr.buttonCompat, com.incall.apps.softmanager.R.attr.buttonTint, com.incall.apps.softmanager.R.attr.buttonTintMode};
        public static final int[] DrawerArrowToggle = {com.incall.apps.softmanager.R.attr.arrowHeadLength, com.incall.apps.softmanager.R.attr.arrowShaftLength, com.incall.apps.softmanager.R.attr.barLength, com.incall.apps.softmanager.R.attr.color, com.incall.apps.softmanager.R.attr.drawableSize, com.incall.apps.softmanager.R.attr.gapBetweenBars, com.incall.apps.softmanager.R.attr.spinBars, com.incall.apps.softmanager.R.attr.thickness};
        public static final int[] LinearLayoutCompat = {android.R.attr.gravity, android.R.attr.orientation, android.R.attr.baselineAligned, android.R.attr.baselineAlignedChildIndex, android.R.attr.weightSum, com.incall.apps.softmanager.R.attr.divider, com.incall.apps.softmanager.R.attr.dividerPadding, com.incall.apps.softmanager.R.attr.measureWithLargestChild, com.incall.apps.softmanager.R.attr.showDividers};
        public static final int[] LinearLayoutCompat_Layout = {android.R.attr.layout_gravity, android.R.attr.layout_width, android.R.attr.layout_height, android.R.attr.layout_weight};
        public static final int[] ListPopupWindow = {android.R.attr.dropDownHorizontalOffset, android.R.attr.dropDownVerticalOffset};
        public static final int[] MenuGroup = {android.R.attr.enabled, android.R.attr.id, android.R.attr.visible, android.R.attr.menuCategory, android.R.attr.orderInCategory, android.R.attr.checkableBehavior};
        public static final int[] MenuItem = {android.R.attr.icon, android.R.attr.enabled, android.R.attr.id, android.R.attr.checked, android.R.attr.visible, android.R.attr.menuCategory, android.R.attr.orderInCategory, android.R.attr.title, android.R.attr.titleCondensed, android.R.attr.alphabeticShortcut, android.R.attr.numericShortcut, android.R.attr.checkable, android.R.attr.onClick, com.incall.apps.softmanager.R.attr.actionLayout, com.incall.apps.softmanager.R.attr.actionProviderClass, com.incall.apps.softmanager.R.attr.actionViewClass, com.incall.apps.softmanager.R.attr.alphabeticModifiers, com.incall.apps.softmanager.R.attr.contentDescription, com.incall.apps.softmanager.R.attr.iconTint, com.incall.apps.softmanager.R.attr.iconTintMode, com.incall.apps.softmanager.R.attr.numericModifiers, com.incall.apps.softmanager.R.attr.showAsAction, com.incall.apps.softmanager.R.attr.tooltipText};
        public static final int[] MenuView = {android.R.attr.windowAnimationStyle, android.R.attr.itemTextAppearance, android.R.attr.horizontalDivider, android.R.attr.verticalDivider, android.R.attr.headerBackground, android.R.attr.itemBackground, android.R.attr.itemIconDisabledAlpha, com.incall.apps.softmanager.R.attr.preserveIconSpacing, com.incall.apps.softmanager.R.attr.subMenuArrow};
        public static final int[] PopupWindow = {android.R.attr.popupBackground, android.R.attr.popupAnimationStyle, com.incall.apps.softmanager.R.attr.overlapAnchor};
        public static final int[] PopupWindowBackgroundState = {com.incall.apps.softmanager.R.attr.state_above_anchor};
        public static final int[] RecycleListView = {com.incall.apps.softmanager.R.attr.paddingBottomNoButtons, com.incall.apps.softmanager.R.attr.paddingTopNoTitle};
        public static final int[] SearchView = {android.R.attr.focusable, android.R.attr.maxWidth, android.R.attr.inputType, android.R.attr.imeOptions, com.incall.apps.softmanager.R.attr.closeIcon, com.incall.apps.softmanager.R.attr.commitIcon, com.incall.apps.softmanager.R.attr.defaultQueryHint, com.incall.apps.softmanager.R.attr.goIcon, com.incall.apps.softmanager.R.attr.iconifiedByDefault, com.incall.apps.softmanager.R.attr.layout, com.incall.apps.softmanager.R.attr.queryBackground, com.incall.apps.softmanager.R.attr.queryHint, com.incall.apps.softmanager.R.attr.searchHintIcon, com.incall.apps.softmanager.R.attr.searchIcon, com.incall.apps.softmanager.R.attr.submitBackground, com.incall.apps.softmanager.R.attr.suggestionRowLayout, com.incall.apps.softmanager.R.attr.voiceIcon};
        public static final int[] Spinner = {android.R.attr.entries, android.R.attr.popupBackground, android.R.attr.prompt, android.R.attr.dropDownWidth, com.incall.apps.softmanager.R.attr.popupTheme};
        public static final int[] SwitchCompat = {android.R.attr.textOn, android.R.attr.textOff, android.R.attr.thumb, com.incall.apps.softmanager.R.attr.showText, com.incall.apps.softmanager.R.attr.splitTrack, com.incall.apps.softmanager.R.attr.switchMinWidth, com.incall.apps.softmanager.R.attr.switchPadding, com.incall.apps.softmanager.R.attr.switchTextAppearance, com.incall.apps.softmanager.R.attr.thumbTextPadding, com.incall.apps.softmanager.R.attr.thumbTint, com.incall.apps.softmanager.R.attr.thumbTintMode, com.incall.apps.softmanager.R.attr.track, com.incall.apps.softmanager.R.attr.trackTint, com.incall.apps.softmanager.R.attr.trackTintMode};
        public static final int[] TextAppearance = {android.R.attr.textSize, android.R.attr.typeface, android.R.attr.textStyle, android.R.attr.textColor, android.R.attr.textColorHint, android.R.attr.textColorLink, android.R.attr.shadowColor, android.R.attr.shadowDx, android.R.attr.shadowDy, android.R.attr.shadowRadius, android.R.attr.fontFamily, android.R.attr.textFontWeight, com.incall.apps.softmanager.R.attr.fontFamily, com.incall.apps.softmanager.R.attr.fontVariationSettings, com.incall.apps.softmanager.R.attr.textAllCaps, com.incall.apps.softmanager.R.attr.textLocale};
        public static final int[] Toolbar = {android.R.attr.gravity, android.R.attr.minHeight, com.incall.apps.softmanager.R.attr.buttonGravity, com.incall.apps.softmanager.R.attr.collapseContentDescription, com.incall.apps.softmanager.R.attr.collapseIcon, com.incall.apps.softmanager.R.attr.contentInsetEnd, com.incall.apps.softmanager.R.attr.contentInsetEndWithActions, com.incall.apps.softmanager.R.attr.contentInsetLeft, com.incall.apps.softmanager.R.attr.contentInsetRight, com.incall.apps.softmanager.R.attr.contentInsetStart, com.incall.apps.softmanager.R.attr.contentInsetStartWithNavigation, com.incall.apps.softmanager.R.attr.logo, com.incall.apps.softmanager.R.attr.logoDescription, com.incall.apps.softmanager.R.attr.maxButtonHeight, com.incall.apps.softmanager.R.attr.menu, com.incall.apps.softmanager.R.attr.navigationContentDescription, com.incall.apps.softmanager.R.attr.navigationIcon, com.incall.apps.softmanager.R.attr.popupTheme, com.incall.apps.softmanager.R.attr.subtitle, com.incall.apps.softmanager.R.attr.subtitleTextAppearance, com.incall.apps.softmanager.R.attr.subtitleTextColor, com.incall.apps.softmanager.R.attr.title, com.incall.apps.softmanager.R.attr.titleMargin, com.incall.apps.softmanager.R.attr.titleMarginBottom, com.incall.apps.softmanager.R.attr.titleMarginEnd, com.incall.apps.softmanager.R.attr.titleMarginStart, com.incall.apps.softmanager.R.attr.titleMarginTop, com.incall.apps.softmanager.R.attr.titleMargins, com.incall.apps.softmanager.R.attr.titleTextAppearance, com.incall.apps.softmanager.R.attr.titleTextColor};
        public static final int[] View = {android.R.attr.theme, android.R.attr.focusable, com.incall.apps.softmanager.R.attr.paddingEnd, com.incall.apps.softmanager.R.attr.paddingStart, com.incall.apps.softmanager.R.attr.theme};
        public static final int[] ViewBackgroundHelper = {android.R.attr.background, com.incall.apps.softmanager.R.attr.backgroundTint, com.incall.apps.softmanager.R.attr.backgroundTintMode};
        public static final int[] ViewStubCompat = {android.R.attr.id, android.R.attr.layout, android.R.attr.inflatedId};

        private styleable() {
        }
    }

    private R() {
    }
}
