package com.changan.evs.ipc.proxy.util;

import android.os.IBinder;
import android.os.IInterface;
import android.os.RemoteException;

/* loaded from: classes.dex */
public abstract class ProxyUtil<T extends IInterface> {
    private final String TAG = "ProxyUtil";
    protected volatile T mService;

    public abstract boolean assertService();

    protected abstract void dealServiceDied();

    protected abstract T linkToService(IBinder iBinder);

    protected final T getService(String str) {
        IBinder iBinder;
        Object obj = new Object();
        try {
            LogUtil.getInstance().dL("ProxyUtil", "getService: " + str);
            iBinder = (IBinder) Class.forName("android.os.ServiceManager").getMethod("getService", String.class).invoke(obj, str);
        } catch (Exception e) {
            e.printStackTrace();
            iBinder = null;
        }
        if (iBinder == null) {
            return null;
        }
        try {
            iBinder.linkToDeath(new IBinder.DeathRecipient() { // from class: com.changan.evs.ipc.proxy.util.-$$Lambda$GxZ2efZLtuAHKuY3ty5Lfure-v8
                @Override // android.os.IBinder.DeathRecipient
                public final void binderDied() {
                    ProxyUtil.this.dealServiceDied();
                }
            }, 0);
            return linkToService(iBinder);
        } catch (RemoteException e2) {
            e2.printStackTrace();
            dealServiceDied();
            return null;
        }
    }
}
