<?xml version="1.0" encoding="utf-8"?>
<com.incall.apps.caui.shape.CAUIShapeConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/transparent"
    android:layout_width="match_parent"
    android:layout_height="1024dp"
    android:minWidth="1280dp"
    android:minHeight="1024dp">
    <ImageView
        android:id="@+id/protocol_back_iv"
        android:background="@drawable/caui_icon_dialog_close"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="55dp"
        android:layout_marginStart="64dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.incall.apps.caui.layout.CAUITextView
        android:textSize="40sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:gravity="center_horizontal"
        android:id="@+id/protocol_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="48dp"
        android:text="@string/dialog_protocol_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <ScrollView
        android:scrollbarThumbVertical="@drawable/fota_scroll_thumb"
        android:scrollbarStyle="outsideOverlay"
        android:id="@+id/protocol_dialog_scroll_view"
        android:scrollbars="vertical"
        android:layout_width="match_parent"
        android:layout_height="674dp"
        android:layout_marginTop="32dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/protocol_title">
        <com.incall.apps.caui.layout.CAUITextView
            android:textSize="32sp"
            android:textColor="@color/caui_config_text_color_primary"
            android:id="@+id/tx_protocol_content"
            android:layout_width="match_parent"
            android:layout_height="674dp"
            android:layout_marginStart="64dp"
            android:layout_marginEnd="64dp"/>
    </ScrollView>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:id="@+id/btn_know_protocol"
        android:layout_width="544dp"
        android:layout_height="96dp"
        android:layout_marginTop="48dp"
        android:layout_marginBottom="56dp"
        android:text="@string/confirm_dialog_protocol_yes"
        android:contentDescription="知道了"
        app:caui_round_btn_type="main"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/protocol_dialog_scroll_view"/>
</com.incall.apps.caui.shape.CAUIShapeConstraintLayout>
