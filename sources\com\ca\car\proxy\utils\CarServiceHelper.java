package com.ca.car.proxy.utils;

import android.os.Handler;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;

/* loaded from: classes.dex */
public abstract class CarServiceHelper<T extends IInterface> {
    protected volatile T mService;
    private final String TAG = "CarServiceHelper";
    private final int MSG_GET_SERVICE_BINDER = 1;
    private Handler mMainHandler = new Handler(Looper.getMainLooper()) { // from class: com.ca.car.proxy.utils.CarServiceHelper.1
        @Override // android.os.Handler
        public void handleMessage(Message message) {
            super.handleMessage(message);
            if (message.what == 1) {
                CarServiceHelper.this.getService(CarConstants.CAR_SERVICE_HAL_PROPERTY);
            }
        }
    };
    private IBinder.DeathRecipient mDeathRecipient = new IBinder.DeathRecipient() { // from class: com.ca.car.proxy.utils.CarServiceHelper.2
        @Override // android.os.IBinder.DeathRecipient
        public void binderDied() {
            CaCarLogUtils.i("CarServiceHelper", "mDeathRecipient, binderDied(), name: " + Thread.currentThread().getName());
            if (CarServiceHelper.this.mService != null) {
                CarServiceHelper.this.mService.asBinder().unlinkToDeath(CarServiceHelper.this.mDeathRecipient, 0);
                CarServiceHelper.this.mService = null;
            }
            CarServiceHelper.this.mMainHandler.sendEmptyMessage(1);
        }
    };

    public abstract boolean assertService();

    protected abstract void dealServiceDied();

    protected abstract T linkToService(IBinder iBinder);

    protected final T getService(String str) {
        IBinder iBinder;
        Object obj = new Object();
        T t = null;
        try {
            CaCarLogUtils.i("CarServiceHelper", "getService(), serviceName: " + str);
            iBinder = (IBinder) Class.forName("android.os.ServiceManager").getMethod("getService", String.class).invoke(obj, str);
        } catch (Exception e) {
            e.printStackTrace();
            iBinder = null;
        }
        if (iBinder == null) {
            this.mMainHandler.sendEmptyMessageDelayed(1, 1000L);
            CaCarLogUtils.e("CarServiceHelper", "getService(), binder is null");
        } else {
            t = linkToService(iBinder);
            if (this.mService == null) {
                this.mMainHandler.sendEmptyMessageDelayed(1, 1000L);
                CaCarLogUtils.e("CarServiceHelper", "getService(), service is null");
            } else {
                CaCarLogUtils.e("CarServiceHelper", "getService(), get service success");
                try {
                    iBinder.linkToDeath(new IBinder.DeathRecipient() { // from class: com.ca.car.proxy.utils.CarServiceHelper.3
                        @Override // android.os.IBinder.DeathRecipient
                        public void binderDied() {
                            CaCarLogUtils.i("CarServiceHelper", "getService(), binderDied(), name: " + Thread.currentThread().getName());
                            if (CarServiceHelper.this.mService != null) {
                                CarServiceHelper.this.mService.asBinder().unlinkToDeath(this, 0);
                                CarServiceHelper.this.mService = null;
                            }
                            CarServiceHelper.this.mMainHandler.sendEmptyMessage(1);
                        }
                    }, 0);
                } catch (RemoteException e2) {
                    e2.printStackTrace();
                    dealServiceDied();
                }
            }
        }
        return t;
    }
}
