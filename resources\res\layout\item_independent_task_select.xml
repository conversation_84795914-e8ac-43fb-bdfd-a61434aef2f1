<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <CheckBox
            android:id="@+id/cb_independent_task_title"
            android:background="@drawable/fota_bg_checkbox"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:button="@null"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"/>
        <TextView
            android:textSize="32sp"
            android:textColor="@color/caui_config_text_color_primary"
            android:id="@+id/tv_independent_task_title"
            android:paddingTop="24dp"
            android:paddingBottom="24dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="2022年06月30日   版本：12.3.0"/>
    </LinearLayout>
    <TextView
        android:textSize="24sp"
        android:textColor="@color/caui_config_text_color_secondary"
        android:id="@+id/tv_independent_task_content"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="2022年06月30日   版本：12.3.0"
        android:paddingStart="90dp"/>
</LinearLayout>
