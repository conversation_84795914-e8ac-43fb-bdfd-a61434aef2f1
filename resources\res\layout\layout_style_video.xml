<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:background="@android:color/black"
    android:descendantFocusability="afterDescendants"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <FrameLayout
        android:id="@+id/surface_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <ImageView
        android:id="@+id/fullscreen"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_marginTop="40dp"
        android:src="@drawable/vp_enlarge"
        android:scaleType="fitXY"
        android:layout_marginStart="40dp"/>
    <ImageView
        android:id="@+id/poster"
        android:background="#000000"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitCenter"
        android:adjustViewBounds="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentBottom="true"
        android:layout_alignParentStart="true"/>
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:id="@+id/layout_bottom_small"
        android:background="@drawable/vp_bottom_bg"
        android:visibility="invisible"
        android:layout_width="match_parent"
        android:layout_height="144dp"
        android:layout_alignParentBottom="true">
        <TextView
            android:textSize="36sp"
            android:textColor="#ffffff"
            android:id="@+id/current_progress_small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="40dp"
            android:text="00:00"/>
        <SeekBar
            android:layout_gravity="center_vertical"
            android:id="@+id/bottom_seek_progress_small"
            android:background="@null"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:maxHeight="12dp"
            android:max="100"
            android:progressDrawable="@drawable/vp_bottom_seek_progress"
            android:minHeight="12dp"
            android:thumb="@drawable/vp_bottom_seek_poster"
            android:thumbOffset="0dp"
            android:layout_weight="1"
            android:layout_marginStart="18dp"
            android:layout_marginEnd="18dp"/>
        <TextView
            android:textSize="36sp"
            android:textColor="#ffffff"
            android:id="@+id/total_progress_small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="00:00"
            android:layout_marginEnd="40dp"/>
    </LinearLayout>
    <RelativeLayout
        android:id="@+id/layout_control_full"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:id="@+id/layout_bottom_full"
            android:background="@drawable/vp_bottom_bg"
            android:paddingTop="12dp"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="156dp"
            android:layout_marginBottom="160dp"
            android:layout_alignParentBottom="true">
            <ImageView
                android:id="@+id/iv_rewind_full"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:src="@drawable/vp_full_rewind"
                android:scaleType="fitXY"
                android:layout_marginStart="72dp"/>
            <ImageView
                android:id="@+id/iv_play_pause_full"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:src="@drawable/vp_full_pause"
                android:scaleType="fitXY"
                android:layout_marginStart="80dp"/>
            <ImageView
                android:id="@+id/iv_forward_full"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:src="@drawable/vp_full_forward"
                android:scaleType="fitXY"
                android:layout_marginStart="80dp"/>
            <TextView
                android:textSize="36sp"
                android:textColor="#ffffff"
                android:id="@+id/current"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="84dp"
                android:text="00:00"/>
            <TextView
                android:textSize="36sp"
                android:textColor="#ffffff"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="/"/>
            <TextView
                android:textSize="36sp"
                android:textColor="#ffffff"
                android:id="@+id/total"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="00:00"/>
        </LinearLayout>
        <ImageView
            android:id="@+id/shrink_full"
            android:visibility="visible"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginTop="180dp"
            android:src="@drawable/fota_full_back"
            android:scaleType="fitXY"
            android:layout_alignParentTop="true"
            android:layout_marginStart="43dp"
            android:layout_alignParentStart="true"/>
        <SeekBar
            android:layout_gravity="center_vertical"
            android:id="@+id/bottom_seek_progress_full"
            android:background="@null"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxHeight="12dp"
            android:max="100"
            android:progressDrawable="@drawable/vp_bottom_seek_progress"
            android:minHeight="12dp"
            android:thumb="@drawable/vp_bottom_seek_poster"
            android:thumbOffset="0dp"
            android:layout_alignTop="@+id/layout_bottom_full"
            android:layout_marginStart="18dp"
            android:layout_marginEnd="18dp"/>
    </RelativeLayout>
    <ProgressBar
        android:id="@+id/bottom_progress"
        android:layout_width="match_parent"
        android:layout_height="1.5dp"
        android:max="100"
        android:progressDrawable="@drawable/vp_bottom_progress"
        android:layout_alignParentBottom="true"
        style="?android:attr/progressBarStyleHorizontal"/>
    <ProgressBar
        android:id="@+id/loading"
        android:visibility="invisible"
        android:layout_width="98dp"
        android:layout_height="98dp"
        android:indeterminateDrawable="@drawable/vp_loading"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"/>
    <LinearLayout
        android:layout_gravity="center_vertical"
        android:id="@+id/start_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true">
        <ImageView
            android:id="@+id/start"
            android:layout_width="98dp"
            android:layout_height="98dp"
            android:src="@drawable/vp_click_play_selector"
            android:scaleType="fitXY"/>
    </LinearLayout>
    <LinearLayout
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:id="@+id/retry_layout"
        android:visibility="invisible"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true">
        <TextView
            android:textSize="14sp"
            android:textColor="@android:color/white"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Video loading failed"/>
        <TextView
            android:textSize="32sp"
            android:textColor="@android:color/white"
            android:id="@+id/retry_btn"
            android:background="@drawable/vp_retry"
            android:paddingLeft="9dp"
            android:paddingTop="4dp"
            android:paddingRight="9dp"
            android:paddingBottom="4dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:text="Click to try again"/>
    </LinearLayout>
</RelativeLayout>
