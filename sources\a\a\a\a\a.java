package a.a.a.a;

import com.incall.apps.casecurity.CaSecurity;
import com.incall.apps.casecurity.bean.KeyGroupBean;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/* loaded from: classes.dex */
public class a {
    public static a b;

    /* renamed from: a, reason: collision with root package name */
    public List<a.a.a.a.b.a> f4a = new ArrayList();

    public static synchronized a a() {
        a aVar;
        synchronized (a.class) {
            if (b == null) {
                b = new a();
            }
            aVar = b;
        }
        return aVar;
    }

    public String a(KeyGroupBean keyGroupBean) {
        a.a.a.a.b.a aVar;
        Iterator<a.a.a.a.b.a> it = this.f4a.iterator();
        while (true) {
            if (!it.hasNext()) {
                aVar = null;
                break;
            }
            aVar = it.next();
            String str = aVar.f5a;
            if (str != null && str.equals(keyGroupBean.getKeyGroupID())) {
                break;
            }
        }
        if (aVar == null) {
            aVar = new a.a.a.a.b.a(keyGroupBean.getKeyGroupID(), a.a.a.a.c.a.a(keyGroupBean.getKey(), a.a.a.a.c.a.a(keyGroupBean.getKey1(), CaSecurity.decodeAesKey(keyGroupBean.getKey2()))));
            this.f4a.add(aVar);
        }
        return aVar.b;
    }
}
