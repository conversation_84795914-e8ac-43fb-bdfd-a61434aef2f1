<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:gravity="center"
    android:orientation="horizontal"
    android:tag="layout/layout_item_test_result_0"
    android:layout_width="match_parent"
    android:layout_height="80dp">
    <TextView
        android:textSize="30sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:gravity="center"
        android:id="@+id/tv_item_name"
        android:tag="binding_1"
        android:background="@color/caui_config_divider_color_primary"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:lines="2"
        android:layout_weight="1"
        android:paddingStart="16dp"
        android:paddingEnd="12dp"/>
    <TextView
        android:textSize="30sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:gravity="center"
        android:id="@+id/tv_item_num"
        android:tag="binding_2"
        android:background="@color/caui_config_divider_color_secondary"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:lines="2"
        android:layout_weight="1"
        android:paddingStart="16dp"
        android:paddingEnd="12dp"/>
    <TextView
        android:textSize="30sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:gravity="center"
        android:id="@+id/tv_item_version_target"
        android:tag="binding_3"
        android:background="@color/caui_config_divider_color_primary"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:lines="2"
        android:layout_weight="1"
        android:paddingStart="16dp"
        android:paddingEnd="12dp"/>
    <TextView
        android:textSize="30sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:gravity="center"
        android:id="@+id/tv_item_version_local"
        android:tag="binding_4"
        android:background="@color/caui_config_divider_color_secondary"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:lines="2"
        android:layout_weight="1"
        android:paddingStart="16dp"
        android:paddingEnd="12dp"/>
    <TextView
        android:textSize="30sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:gravity="center"
        android:id="@+id/tv_item_result"
        android:tag="binding_5"
        android:background="@color/caui_config_divider_color_primary"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:lines="2"
        android:layout_weight="1"
        android:paddingStart="16dp"
        android:paddingEnd="12dp"/>
    <TextView
        android:textSize="30sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:gravity="center"
        android:id="@+id/tv_item_sota"
        android:tag="binding_6"
        android:background="@color/caui_config_divider_color_secondary"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:lines="2"
        android:layout_weight="1"
        android:paddingStart="16dp"
        android:paddingEnd="12dp"/>
    <CheckBox
        android:textSize="30sp"
        android:gravity="center"
        android:id="@+id/check_box_update"
        android:background="@color/caui_config_divider_color_primary"
        android:visibility="invisible"
        android:layout_width="150dp"
        android:layout_height="match_parent"/>
</LinearLayout>
