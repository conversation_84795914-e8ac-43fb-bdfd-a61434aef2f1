<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cv_notify_rv"
    android:background="@drawable/fota_image_corner"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp">
    <com.incall.apps.softmanager.base.view.StyleVideoPlayer
        android:id="@+id/video_player"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <com.incall.apps.softmanager.base.view.FullScreenImageView
        android:id="@+id/iv_notify_viewpager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"/>
</androidx.cardview.widget.CardView>
