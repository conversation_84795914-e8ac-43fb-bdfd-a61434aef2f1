<?xml version="1.0" encoding="utf-8"?>
<com.incall.apps.caui.layout.CAUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:tag="layout/activity_main_0"
    android:background="@color/caui_config_content_bg_color_primary"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <FrameLayout
        android:id="@+id/fragment_demo"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.incall.apps.softmanager.base.view.FactoryView
        android:tag="binding_1"
        android:layout_width="400dp"
        android:layout_height="400dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>
    <com.incall.apps.softmanager.base.view.FactoryView
        android:tag="binding_2"
        android:layout_width="400dp"
        android:layout_height="400dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>
</com.incall.apps.caui.layout.CAUIConstraintLayout>
