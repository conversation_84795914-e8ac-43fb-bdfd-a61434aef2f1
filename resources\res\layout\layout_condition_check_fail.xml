<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:tag="layout/layout_condition_check_fail_0"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">
    <TextView
        android:textSize="56sp"
        android:textColor="@color/caui_config_text_color_primary"
        android:id="@+id/condition_not_meet_tx"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="104dp"
        android:text="@string/condition_is_checked_failed"
        android:layout_marginStart="32dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <androidx.recyclerview.widget.RecyclerView
        android:scrollbarThumbVertical="@drawable/fota_scroll_thumb"
        android:scrollbarStyle="outsideOverlay"
        android:id="@+id/rv_conditions"
        android:tag="binding_1"
        android:scrollbars="vertical"
        android:layout_width="1242dp"
        android:layout_height="300dp"
        android:layout_marginTop="96dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/condition_not_meet_tx"/>
    <TextView
        android:textSize="40sp"
        android:textColor="@color/caui_config_theme_color_normal"
        android:gravity="center"
        android:id="@+id/condition_check_fail_tip"
        android:tag="binding_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="110dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rv_conditions"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:tag="binding_3"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="103dp"
        android:contentDescription="(重试|重新校验)"
        android:layout_marginEnd="500dp"
        app:caui_round_btn_type="main"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/condition_check_fail_tip"/>
    <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
        android:textSize="36sp"
        android:gravity="center"
        android:tag="binding_4"
        android:layout_width="344dp"
        android:layout_height="96dp"
        android:layout_marginTop="103dp"
        android:contentDescription="(返回)"
        android:layout_marginStart="500dp"
        app:caui_round_btn_type="secondary"
        app:caui_round_radius="@dimen/caui_config_corner_radius_small"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/condition_check_fail_tip"/>
</androidx.constraintlayout.widget.ConstraintLayout>
