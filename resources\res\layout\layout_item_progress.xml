<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:tag="layout/layout_item_progress_0"
    android:layout_width="match_parent"
    android:layout_height="30dp">
    <TextView
        android:textSize="20sp"
        android:textColor="@color/caui_config_text_color_third"
        android:id="@+id/tv_object_name"
        android:tag="binding_1"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <ProgressBar
        android:textColor="@color/caui_config_color_white"
        android:id="@+id/pr_item_version_content"
        android:tag="binding_2"
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:progressDrawable="@drawable/fota_progress_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/tv_object_name"
        app:layout_constraintTop_toTopOf="parent"
        style="?android:attr/progressBarStyleHorizontal"/>
    <TextView
        android:textSize="20sp"
        android:textColor="@color/caui_config_text_color_third"
        android:tag="binding_3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/pr_item_version_content"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
