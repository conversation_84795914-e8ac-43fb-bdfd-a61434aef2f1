package com.carled.jni;

import com.incall.apps.softmanager.thusubmaster.common.utils.LogUtil;

/* loaded from: classes.dex */
public class CarLedControl {
    public native int cancelInstallCarLed();

    public native int exitCarLed();

    public native int getCarLedLVDSSwitchState();

    public native int getCarLedLocationSkinMode();

    public native int getCarLedMasterSwitchState();

    public native int getCarLedPlayState();

    public native int getCarLedPlayerState();

    public native int getCarLedScreenBrightness();

    public native CarLedVersionBean getCarLedVersionBean();

    public native String getCarLedWelcomeVideoInfo();

    public native int initCarLed();

    public native void installCarLed(String str, InstallListener installListener);

    public native void installCarLedWelcomeVideo(String str, InstallListener installListener);

    public native int queryPartitionCarLed();

    public native int rebootCarLed();

    public native int registerPlayStatusListener(PlayStatusListener playStatusListener);

    public native int setCarLedLVDSSwitchState(int i);

    public native int setCarLedLocationSkinMode(int i);

    public native int setCarLedMasterSwitchState(int i);

    public native int setCarLedPlayerState(int i);

    public native int setCarLedScreenBrightness(int i);

    public native int switchCarLed();

    public native int updateCarLedSkin(String str);

    static {
        LogUtil.i("CarLedControl", "loadLibrary");
        System.loadLibrary("carled");
        LogUtil.i("CarLedControl", "loadLibrary end");
    }
}
