<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_gravity="center"
        android:orientation="vertical"
        android:background="@color/caui_config_pop_up_bg_color"
        android:layout_width="2000dp"
        android:layout_height="1200dp"
        android:layout_margin="50dp">
        <TextView
            android:textSize="44sp"
            android:textStyle="bold"
            android:textColor="@color/caui_config_text_color_primary"
            android:gravity="center"
            android:id="@+id/lv_version_title"
            android:layout_width="match_parent"
            android:layout_height="108dp"
            android:layout_marginTop="32dp"
            android:text="试验检查结果详情"
            app:layout_constraintTop_toTopOf="parent"/>
        <TextView
            android:textSize="26sp"
            android:textStyle="bold"
            android:textColor="@color/caui_config_text_color_secondary"
            android:gravity="center"
            android:id="@+id/lv_tips"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="(1.橙色信息代表版本信息异常 2.其中包含零件信息缺失、待升级软件 3.点击升级到目标版本按钮可以将待升级软件升级到目标版本)"
            app:layout_constraintTop_toBottomOf="@+id/lv_version_title"/>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:id="@+id/lv_title"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            app:layout_constraintTop_toBottomOf="@+id/lv_tips">
            <TextView
                android:textSize="30sp"
                android:textColor="@color/caui_config_text_color_primary"
                android:gravity="center"
                android:id="@+id/tv_item_name"
                android:background="@color/caui_config_content_bg_color_secondary"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:text="升级对象名称"
                android:lines="2"
                android:layout_weight="1"
                android:paddingStart="16dp"
                android:paddingEnd="12dp"/>
            <TextView
                android:textSize="30sp"
                android:textColor="@color/caui_config_text_color_primary"
                android:gravity="center"
                android:id="@+id/tv_item_num"
                android:background="@color/caui_config_divider_color_primary"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:text="目标软件件号"
                android:lines="2"
                android:layout_weight="1"
                android:paddingStart="16dp"
                android:paddingEnd="12dp"/>
            <TextView
                android:textSize="30sp"
                android:textColor="@color/caui_config_text_color_primary"
                android:gravity="center"
                android:id="@+id/tv_item_version_target"
                android:background="@color/caui_config_content_bg_color_secondary"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:text="目标软件版本"
                android:lines="2"
                android:layout_weight="1"
                android:paddingStart="16dp"
                android:paddingEnd="12dp"/>
            <TextView
                android:textSize="30sp"
                android:textColor="@color/caui_config_text_color_primary"
                android:gravity="center"
                android:id="@+id/tv_item_version_local"
                android:background="@color/caui_config_divider_color_primary"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:text="本地软件版本"
                android:lines="2"
                android:layout_weight="1"
                android:paddingStart="16dp"
                android:paddingEnd="12dp"/>
            <TextView
                android:textSize="30sp"
                android:textColor="@color/caui_config_text_color_primary"
                android:gravity="center"
                android:id="@+id/tv_item_result"
                android:background="@color/caui_config_content_bg_color_secondary"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:text="FOTA检测结果"
                android:lines="2"
                android:layout_weight="1"
                android:paddingStart="16dp"
                android:paddingEnd="12dp"/>
            <TextView
                android:textSize="30sp"
                android:textColor="@color/caui_config_text_color_primary"
                android:gravity="center"
                android:id="@+id/tv_item_sota"
                android:background="@color/caui_config_divider_color_primary"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:text="SOTA检测结果"
                android:lines="2"
                android:layout_weight="1"
                android:paddingStart="16dp"
                android:paddingEnd="12dp"/>
            <CheckBox
                android:textSize="30sp"
                android:textColor="@color/caui_config_text_color_primary"
                android:gravity="center"
                android:id="@+id/tv_item_check_box"
                android:background="@color/caui_config_content_bg_color_secondary"
                android:layout_width="150dp"
                android:layout_height="match_parent"
                android:text="全选"
                android:lines="2"
                android:paddingStart="16dp"
                android:paddingEnd="12dp"/>
        </LinearLayout>
        <View
            android:background="@color/caui_config_divider_color_secondary"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            app:layout_constraintTop_toBottomOf="@+id/lv_title"/>
        <androidx.recyclerview.widget.RecyclerView
            android:orientation="vertical"
            android:id="@+id/rv_result_list"
            android:layout_width="match_parent"
            android:layout_height="800dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@+id/divider_test_vehicle_dialog"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/lv_title"/>
        <TextView
            android:textSize="44sp"
            android:textColor="@color/caui_config_text_color_primary"
            android:gravity="center"
            android:id="@+id/tx_no_test_vehicle_task"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="未检测到试验车任务"
            app:layout_constraintBottom_toTopOf="@+id/btn_dialog_no"
            app:layout_constraintTop_toTopOf="@+id/rv_result_list"/>
        <View
            android:id="@+id/divider_test_vehicle_dialog"
            android:background="@color/transparent"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginBottom="24dp"
            app:layout_constraintBottom_toTopOf="@+id/btn_dialog_no"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>
        <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
            android:textSize="36sp"
            android:gravity="center"
            android:id="@+id/btn_dialog_no"
            android:layout_width="344dp"
            android:layout_height="96dp"
            android:layout_marginBottom="50dp"
            android:text="退出"
            android:height="56dp"
            android:width="186dp"
            android:layout_marginEnd="100dp"
            app:caui_round_btn_type="negative"
            app:caui_round_radius="@dimen/caui_config_corner_radius_small"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>
        <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
            android:textSize="36sp"
            android:gravity="center"
            android:id="@+id/btn_dialog_yes"
            android:layout_width="344dp"
            android:layout_height="96dp"
            android:layout_marginBottom="50dp"
            android:text="升级到目标版本"
            android:height="56dp"
            android:width="186dp"
            android:layout_marginStart="100dp"
            app:caui_round_btn_type="main"
            app:caui_round_radius="@dimen/caui_config_corner_radius_small"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
