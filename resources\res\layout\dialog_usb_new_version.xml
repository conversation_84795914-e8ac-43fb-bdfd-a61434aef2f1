<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/caui_config_content_bg_color_primary"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_gravity="center"
        android:background="@drawable/fota_bg_dialog"
        android:layout_width="880dp"
        android:layout_height="880dp">
        <TextView
            android:textSize="44sp"
            android:textColor="@color/caui_config_text_color_primary"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="108dp"
            android:layout_marginTop="32dp"
            android:text="@string/wifi_download_dialog_title"
            app:layout_constraintTop_toTopOf="parent"/>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="752dp"
            android:layout_height="532dp"
            android:layout_marginTop="140dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <TextView
                android:textSize="36sp"
                android:textColor="@color/caui_config_text_color_secondary"
                android:id="@+id/tx_usb_dialog_version"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/wifi_download_dialog_new_version"
                app:layout_constraintTop_toTopOf="parent"/>
            <TextView
                android:textSize="36sp"
                android:textColor="@color/caui_config_text_color_secondary"
                android:id="@+id/tx_usb_dialog_update_tips"
                android:layout_width="match_parent"
                android:layout_height="400dp"
                app:layout_constraintBottom_toBottomOf="parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
            android:textSize="36sp"
            android:gravity="center"
            android:id="@+id/btn_dialog_usb_upgrade"
            android:layout_width="344dp"
            android:layout_height="80dp"
            android:layout_marginBottom="64dp"
            android:text="@string/dialog_usb_new_version_confirm"
            android:layout_marginStart="64dp"
            app:caui_round_btn_type="main"
            app:caui_round_radius="@dimen/caui_config_corner_radius_small"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>
        <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
            android:textSize="36sp"
            android:gravity="center"
            android:id="@+id/btn_dialog_usb_cancel"
            android:layout_width="344dp"
            android:layout_height="80dp"
            android:layout_marginBottom="64dp"
            android:text="@string/dialog_usb_new_version_cancel"
            android:layout_marginEnd="64dp"
            app:caui_round_btn_type="secondary"
            app:caui_round_radius="@dimen/caui_config_corner_radius_small"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
