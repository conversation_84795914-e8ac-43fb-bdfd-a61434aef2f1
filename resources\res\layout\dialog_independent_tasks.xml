<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/color_dialog_bg"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_gravity="center"
        android:background="@drawable/fota_bg_dialog"
        android:layout_width="880dp"
        android:layout_height="880dp">
        <TextView
            android:textSize="44sp"
            android:textColor="@color/caui_config_text_color_primary"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:text="@string/select_independent_install_tasks_title"
            app:layout_constraintTop_toTopOf="parent"/>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="752dp"
            android:layout_height="532dp"
            android:layout_marginTop="140dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_independent_task_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
            android:textSize="36sp"
            android:gravity="center"
            android:id="@+id/btn_independent_task_confirm"
            android:layout_width="344dp"
            android:layout_height="120dp"
            android:layout_marginBottom="64dp"
            android:text="@string/dialog_independent_new_version_confirm"
            android:layout_marginStart="64dp"
            app:caui_round_btn_type="main"
            app:caui_round_radius="@dimen/caui_config_corner_radius_small"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>
        <com.incall.apps.caui.widget.button.roundbutton.CAUIRoundButton
            android:textSize="36sp"
            android:gravity="center"
            android:id="@+id/btn_independent_task_cancel"
            android:layout_width="344dp"
            android:layout_height="120dp"
            android:layout_marginBottom="64dp"
            android:text="@string/dialog_independent_new_version_cancel"
            android:layout_marginEnd="64dp"
            app:caui_round_btn_type="secondary"
            app:caui_round_radius="@dimen/caui_config_corner_radius_small"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
